import { AsyncReader } from '@sage/xtrem-async-helper';
import { AnyValue, AsyncResponse, isEnvVarTrue } from '@sage/xtrem-shared';
import * as express from 'express';
import { Express } from 'express';
import { EventEmitter } from 'node:events';
import { Counter, collectDefaultMetrics, register } from 'prom-client';

import { loggers } from '../loggers';

const logger = loggers.metrics;

export const allMetricsEnabled = isEnvVarTrue(process.env.XTREM_ALL_METRICS_ENABLED);
// node.js metrics may be redundant with what newrelic already collects. So we may collect them in dev but not prod.
const nodeJsMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_NODEJS_METRICS_ENABLED);
const ruleMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_RULE_METRICS_ENABLED);
const graphQlMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_GRAPHQL_METRICS_ENABLED);
const sqlMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_SQL_METRICS_ENABLED);

// collection the default node.js metrics
if (nodeJsMetricsEnabled) collectDefaultMetrics({ register });

export interface CustomCountersMetrics<KeyT extends object> {
    withMetrics<ResultT extends AnyValue>(key: KeyT, body: () => AsyncResponse<ResultT>): Promise<ResultT>;
    wrapReader<ElementT extends AnyValue>(key: KeyT, reader: AsyncReader<ElementT>): AsyncReader<ElementT>;
}

/**
 * @internal
 */
export class CustomCounters<KeyT extends object> implements CustomCountersMetrics<KeyT> {
    readonly #isEnabled: boolean;

    #counters: {
        calls: Counter;
        errors: Counter;
        totalMillis: Counter;
    };

    constructor(options: { labelNames: string[]; name: string; help: string; isEnabled: boolean }) {
        const { labelNames, name, help } = options;
        this.#isEnabled = options.isEnabled;
        this.#counters = {
            calls: new Counter({
                name: `xtrem_${name}_calls`,
                help: `Number of calls of ${help}`,
                labelNames,
            }),
            errors: new Counter({
                name: `xtrem_${name}_errors`,
                help: `Number of errors of ${help}`,
                labelNames,
            }),
            totalMillis: new Counter({
                name: `xtrem_${name}_total_millis`,
                help: `Total duration for ${help} (in milliseconds)`,
                labelNames,
            }),
        };
    }

    get isEnabled(): boolean {
        return this.#isEnabled;
    }

    private increment(key: object, millis: number, failed: boolean): void {
        this.#counters.calls.labels(key).inc(1);
        if (failed) this.#counters.errors.labels(key).inc(1);
        this.#counters.totalMillis.labels(key).inc(millis);
    }

    async withMetrics<ResultT extends AnyValue>(key: KeyT, body: () => AsyncResponse<ResultT>): Promise<ResultT> {
        if (!this.#isEnabled) return body();

        const startedAt = performance.now();
        let failed = false;
        try {
            return await body();
        } catch (err) {
            failed = true;
            throw err;
        } finally {
            this.increment(key, performance.now() - startedAt, failed);
        }
    }

    wrapReader<ElementT extends AnyValue>(key: KeyT, reader: AsyncReader<ElementT>): AsyncReader<ElementT> {
        if (!this.#isEnabled) return reader;

        const wrapped = Object.create(reader);
        // This wrapper is used to get metrics on SQL queries.
        // The SQL query is sent to the db server the first time we call read() so we have to measure the time taken
        // by the first read().
        wrapped.read = () => (reader.readCount === 0 ? this.withMetrics(key, () => reader.read()) : reader.read());

        return wrapped;
    }
}

export type StatementKind = 'select' | 'insert' | 'update' | 'upsert' | 'delete';

interface RulesLabels {
    nodeName: string;
    propertyName?: string;
    ruleName: string;
}

interface GraphQlLabels {
    nodeName: string;
    operationName: string;
    operationKind: string;
}

interface SqlLabels {
    nodeName: string;
    statementKind: StatementKind;
}

export class CustomMetrics {
    static readonly emitter = new EventEmitter();

    static readonly rules: CustomCountersMetrics<RulesLabels> = new CustomCounters<RulesLabels>({
        // Do not include tenant id for now. It would be nice but would make the matrix larger.
        name: 'rules',
        help: 'a business rule',
        labelNames: ['nodeName', 'propertyName', 'ruleName'],
        isEnabled: ruleMetricsEnabled,
    });

    static readonly graphql: CustomCountersMetrics<GraphQlLabels> = new CustomCounters<GraphQlLabels>({
        // Do not include tenant id for now. It would be nice but would make the matrix larger.
        name: 'graphql',
        help: 'a graphql operation',
        labelNames: ['nodeName', 'operationName', 'operationKind'],
        isEnabled: graphQlMetricsEnabled,
    });

    static readonly metadata: CustomCountersMetrics<GraphQlLabels> = new CustomCounters<GraphQlLabels>({
        // Do not include tenant id for now. It would be nice but would make the matrix larger.
        name: 'metadata',
        help: 'a metadata operation',
        labelNames: ['nodeName', 'operationName', 'operationKind'],
        isEnabled: graphQlMetricsEnabled,
    });

    static readonly sql: CustomCountersMetrics<SqlLabels> = new CustomCounters<SqlLabels>({
        // Do not include tenant id for now. It would be nice but would make the matrix larger.
        name: 'sql',
        help: 'a sql statement',
        labelNames: ['nodeName', 'statementKind'],
        isEnabled: sqlMetricsEnabled,
    });

    static get expressApp(): Express {
        const app = express();
        app.get('/', async (_req, res) => {
            logger.verbose(() => 'Received HTTP GET on /metrics endpoint');
            try {
                res.set('Content-Type', register.contentType);
                res.end(await register.metrics());
                this.emitter.emit('collected');
            } catch (ex) {
                logger.error(ex);
                res.status(500).send();
            }
        });
        return app;
    }
}
