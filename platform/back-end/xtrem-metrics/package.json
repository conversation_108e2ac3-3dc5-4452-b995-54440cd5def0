{"name": "@sage/xtrem-metrics", "description": "Xtrem metrics Package", "version": "57.0.22", "author": "sage", "license": "UNLICENSED", "keywords": ["metrics"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build/index.*", "build/package-definition.d.ts", "build/lib", "data"], "dependencies": {"@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "express": "^5.0.0", "lodash": "^4.17.21", "nanoid": "^3.3.8", "newrelic": "^12.0.0", "prom-client": "^15.1.2"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/express": "^5.0.0", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/newrelic": "^9.4.0", "@types/node": "^22.10.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "mocha": "^10.8.2", "prettier": "^3.3.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z 'build/**/*.js'", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:fix": "eslint --fix -c .eslintrc.js --ext .ts lib", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "test": "echo \"Error: no test specified for xtrem-dts-bundle\" && exit 0", "test:ci": "echo \"Error: no test specified for xtrem-dts-bundle\" && exit 0", "test:todo": "mocha --trace-warnings --recursive --exit \"build/test/**/*@(-|.)test.js\"", "test:todo:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-metrics.xml JUNIT_REPORT_NAME='xtrem-metrics' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}