import { Logger, sourceMapSetup } from '@sage/xtrem-log';
import {
    AppsConfig,
    AuthenticationServiceConfig,
    Config,
    Dict,
    SecurityConfig,
    ServerConfig,
    SizeLimits,
    SqlConfig,
    UiConfig,
    createDictionary,
    validator,
} from '@sage/xtrem-shared';
import Ajv, { ValidateFunction } from 'ajv';
import * as bytes from 'bytes';
import * as chokidar from 'chokidar';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import { load as yamlLoad } from 'js-yaml';
import * as _ from 'lodash';
import * as fsp from 'path';
import { SecureContextOptions } from 'tls';

sourceMapSetup();

const logger = Logger.getLogger(__filename, 'config');

interface DeprecatedConfig extends Config {
    sql?: SqlConfig;
    port?: number;
}

interface IndexedConfig extends Config {
    [index: string]: any;
}

/**
 * The result of loading a configuration
 */
interface LoadConfigResult {
    /** The loaded configuration */
    config: Config;
    /** the names of all the files that were loaded to build the configuration */
    filenames: string[];
}

interface FilesWatcher {
    files?: string[];
    watcher?: chokidar.FSWatcher;
}

export type SslCertificateOptions = Pick<SecureContextOptions, 'ca' | 'cert' | 'key'>;

type PemType = keyof SslCertificateOptions;

const defaultPort = { http: 8240, https: 443 };

const kiloBytes = 1024;
const megaBytes = 1024 * kiloBytes;
const gigaBytes = 1024 * megaBytes;

/**
 * The name of the many configuration files
 */
const knownFilenames = {
    configuration: 'xtrem-config.yml',
    security: 'xtrem-security.yml',
    apps: 'apps.yml',
};

export class ConfigManager {
    private static config: Config;

    private static fileWatcherMap: Dict<FilesWatcher> = createDictionary();

    static readonly emitter = new EventEmitter();

    // Config file paths loaded and we are watching
    private static configFilePaths?: string[] = [];

    private static _checkDeploymentMode(config: Config) {
        config.deploymentMode = config.deploymentMode || 'production';
        if (!/^(development|production)$/.test(config.deploymentMode)) {
            throw new Error(`invalid deploymentMode: ${config.deploymentMode}`);
        }
    }

    private static _checkIgnoreVendorProtection(config: Config) {
        if (config.ignoreVendorProtection) {
            // WARNING: ignoreVendorProtection is allowed even in production mode for special case
            logger.warn(
                "CAUTION: factory data protection is disabled because ignoreVendorProtection has been set to 'true'",
            );
        }
    }

    private static _checkGraphQlConfig(config: Config) {
        if (!config.graphql) config.graphql = {};
    }

    private static _checkLogLevels(config: Config) {
        const logDomains = config.logs?.domains;
        if (config.deploymentMode !== 'development' && logDomains) {
            const invalidKey = Object.keys(logDomains).find(
                key => !/^(error|warn|info|verbose)$/.test(logDomains[key].level),
            );
            if (invalidKey)
                throw new Error(
                    `${logDomains[invalidKey]?.level} log level not allowed in production (domain=${invalidKey})`,
                );
        }
    }

    private static _checkNewRelicConfig(config: Config) {
        if (
            config.newRelic &&
            (!config.newRelic.accountId ||
                !config.newRelic.applicationId ||
                !config.newRelic.licenceKey ||
                !config.newRelic.trustKey)
        ) {
            throw new Error("'newRelic' config object is defined but one of the properties is not set.");
        }
    }

    private static _checkStorageConfig(config: Config) {
        const sqlConfig = config.storage?.sql as IndexedConfig;
        if (sqlConfig) {
            const keys = ['user', 'database', 'sysUser', 'sysDatabase'] as (keyof SqlConfig)[];
            keys.forEach(k => {
                const value = sqlConfig[k];
                if (value) {
                    validator.isAlphaNumericName(value, { throw: { name: k } });
                }
            });
        } else if (!config.storage?.managedExternal) {
            throw new Error('Storage config missing');
        }
    }

    private static _checkAppName(name: string) {
        if (!/^[a-z][a-z0-9_]*$/.test(name)) throw new Error(`invalid app name (must be snake_case): ${name}`);
    }

    private static _checkAppsConfig(config: Config) {
        if (config.app) this._checkAppName(config.app);
        if (config.apps) Object.keys(config.apps).forEach(this._checkAppName);
    }

    private static _checkConfig(config: Config) {
        this._checkDeploymentMode(config);
        this._checkIgnoreVendorProtection(config);
        this._checkGraphQlConfig(config);
        this._checkLogLevels(config);
        this._checkNewRelicConfig(config);
        this._checkStorageConfig(config);
        this._checkAppsConfig(config);

        return config;
    }

    private static getDefaultSqlConfig(config: Config): SqlConfig {
        // In production mode, we need to keep full control of the configuration and especially for sys* parameters
        // that must be undefined in service mode
        if (config.deploymentMode === 'production') return {} as SqlConfig;

        return {
            hostname: '127.0.0.1', // force IPv4 - node 18 converts localhost to IPv6 (::1), which requires extra config on postgres
            port: 5432,
            database: 'postgres',
            user: 'postgres',
            password: 'secret',
            sysDatabase: 'postgres',
            sysUser: 'postgres',
            sysPassword: 'secret',
            poolMaxIdleSeconds: 60,
            connectionMaxRetries: 3,
            connectionRetryMillis: 2000,
            max: 20,
            maxUses: 7500,
            maxRetriesOnTransactionConflicts: 10,
        };
    }

    /**
     * Returns a default configuration
     */
    private static getDefaultConfig(): LoadConfigResult {
        return {
            config: { storage: { sql: ConfigManager.getDefaultSqlConfig({}) } },
            filenames: [],
        };
    }

    private static _load(startDir: string): LoadConfigResult {
        let dir = startDir;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const xtremPath = fsp.join(dir, knownFilenames.configuration);
            if (fs.existsSync(xtremPath)) {
                return this._loadConfigFilesFromPath(dir);
            }
            const ndir = fsp.join(dir, '..');
            if (ndir === dir) {
                logger.warn(`config MISSING: ${fsp.join(startDir, knownFilenames.configuration)}`);
                return ConfigManager.getDefaultConfig();
            }
            dir = ndir;
        }
    }

    private static _readConfigFile<T extends unknown>(path: string): T {
        return yamlLoad(fs.readFileSync(path, 'utf8')) as T;
    }

    /**
     * Read the configuration files from a given directory
     * Note: all the configuration files must be in the same folder
     */
    private static _readConfigFiles(dir: string, visited = {} as Dict<boolean>): LoadConfigResult {
        if (visited[dir]) throw new Error(`circularity in config extends: dir=${dir}`);
        visited[dir] = true;

        const path = fsp.join(dir, knownFilenames.configuration);
        if (!fs.existsSync(path)) {
            // Can happen when a xtrem-config file has an 'extends' property that points to a non-existing file
            return {
                config: ConfigManager.getDefaultConfig().config,
                filenames: [],
            };
        }
        logger.info(`Loading configuration from ${path}`);

        const result: LoadConfigResult = {
            config: yamlLoad(fs.readFileSync(path, 'utf8')) as Config,
            filenames: [path],
        };

        result.config.env = {
            isCI: [process.env.TF_BUILD].some(v => v && /^true$/i.test(v)),
        };
        const securityFilename = fsp.join(dir, knownFilenames.security);
        if (fs.existsSync(securityFilename)) {
            logger.info(`Loading security configuration from ${securityFilename}`);
            result.config.security = this._readConfigFile<SecurityConfig>(securityFilename);
            result.filenames.push(securityFilename);
        } else {
            logger.warn('security configuration missing');
        }

        let appsPath = fsp.join(dir, knownFilenames.apps);
        if (!fs.existsSync(appsPath)) appsPath = `/infra/${knownFilenames.apps}`;
        if (fs.existsSync(appsPath)) {
            logger.info(`Loading applications configuration from ${appsPath}`);
            result.config.apps = this._readConfigFile<{ apps: AppsConfig }>(appsPath).apps;
            result.filenames.push(appsPath);
        } else {
            logger.warn('applications configuration missing');
        }

        const uiPath = result.config.uiConfigPath || fsp.join(dir, 'xtrem-ui.yml');
        if (fs.existsSync(uiPath)) {
            result.config.ui = this._readConfigFile<UiConfig>(uiPath);
            result.filenames.push(uiPath);
        }

        if (result.config.extends) {
            const extendsDir = fsp.dirname(fsp.join(dir, result.config.extends));
            const extendsResult = this._readConfigFiles(extendsDir, visited);
            result.config = _.merge(extendsResult.config, result.config);
            result.filenames.push(...extendsResult.filenames);
        }
        return result;
    }

    /**
     * Load the configuration files from a given path
     * Note: all the configuration files must be in the same folder
     */
    private static _loadConfigFilesFromPath(dir: string): LoadConfigResult {
        const result = this._readConfigFiles(dir);

        result.config.originFolder = fsp.join(dir); // normalize folder

        // we need to ensure the deployment mode is set before calling the logger
        this._checkDeploymentMode(result.config);
        logger.info(`Loading configuration: deploymentMode='${result.config.deploymentMode}'`);

        Logger.reloadConfig(result.config);

        this._checkConfigJsonSchema(dir, result.config);
        this._setStorageConfig(result.config);
        this._setServerConfig(result.config);
        this._setAuthenticationServiceConfig(result.config);

        return result;
    }

    private static _checkConfigJsonSchema(dir: string, config: Config): void {
        const path = fsp.join(dir, 'xtrem-config-json-schema.json');

        if (fs.existsSync(path)) {
            const schema = this._readConfigFile<Config>(path);
            const validate: ValidateFunction = new Ajv().compile(schema);
            const valid = validate(config);

            if (!valid) {
                logger.warn(`${knownFilenames.configuration} config data is invalid according to its JSON Schema. \n
                                ${JSON.stringify(validate.errors, null, 4)}`);
            } else {
                logger.info(`${knownFilenames.configuration} config data is valid according to its JSON Schema.`);
            }
        } else {
            logger.warn(`config Json Schema MISSING: ${fsp.join(dir, 'xtrem-config-json-schema.json')}`);
        }
    }

    private static _setStorageConfig(config: Config) {
        // Fill default values for config.SqlConfig
        config.storage = config.storage || {};
        const deprecatedConfig = config as DeprecatedConfig;
        if (!config.storage?.managedExternal) {
            if (config.storage?.sql && deprecatedConfig.sql) {
                throw new Error(
                    "config conflict between 'storage.sql' and 'sql', please remove the deprecated root level 'sql' config",
                );
            }
            if (deprecatedConfig.sql) {
                logger.warn("'sql' parameter is deprecated at the root level, please move it into 'storage' parameter");
            }
            config.storage.sql = config.storage.sql || deprecatedConfig.sql;
            config.storage = {
                ...config.storage,
                sql: { ...ConfigManager.getDefaultSqlConfig(config), ...config.storage.sql },
            };
            delete deprecatedConfig.sql;
            if (config.storage.sql?.ssl && typeof config.storage.sql.ssl === 'object') {
                this._setSslPemOptions('storage.sql.ssl', config);
            }
        }
    }

    private static _setServerConfig(config: Config) {
        const deprecatedConfig = config as DeprecatedConfig;
        if (config.server?.port && deprecatedConfig.port) {
            throw new Error(
                "config conflict between 'server.port' and 'port', please remove the deprecated root level 'port' config",
            );
        }
        if (deprecatedConfig.port) {
            logger.warn("'port' parameter is deprecated at the root level, please move it into 'server' parameter");
        }

        config.server = config.server || ({} as ServerConfig);
        config.server.port =
            config.server.port || (config.server.ssl ? defaultPort.https : defaultPort.http) || deprecatedConfig.port;

        // requestFunnelSizeFactor default is 1 and the value must be greater or equal to 1
        config.server.requestFunnelSizeFactor = Math.max(config.server.requestFunnelSizeFactor || 1, 1);

        delete deprecatedConfig.port;
        if (config.server.ssl) {
            this._setSslPemOptions('server.ssl', config);
        }
    }

    private static _setAuthenticationServiceConfig(config: Config) {
        config.authentication = config.authentication || ({} as AuthenticationServiceConfig);
        if (config.authentication.ssl) {
            this._setSslPemOptions('authentication.ssl', config);
        }
    }

    private static sizeLimits: Dict<number> = {};

    static getSizeLimit(key: keyof SizeLimits): number {
        if (this.sizeLimits[key] != null) return this.sizeLimits[key];
        const sizeLimits = this.current.security?.sizeLimits;

        switch (key) {
            case 'maxRequestSize':
                this.sizeLimits[key] = bytes.parse(sizeLimits?.maxRequestSize || '100Mb') ?? 100 * megaBytes; // 104857600
                break;
            case 'maxStreamSize':
                this.sizeLimits[key] = bytes.parse(sizeLimits?.maxStreamSize || '32Mb') ?? 32 * megaBytes; // 33554432
                break;
            case 'maxUploadSize':
                this.sizeLimits[key] = bytes.parse(sizeLimits?.maxStreamSize || '1Gb') ?? 1 * gigaBytes; // 1073741824
                break;
            default:
                this.sizeLimits[key] = 0;
                break;
        }

        return this.sizeLimits[key];
    }

    private static _setSslPemOptions(propertyPath: string, config: Config): void {
        const shallowCopyPropertyPath = `${propertyPath}ShallowCopy`;
        const sslFromOptions = _.property<Config, SslCertificateOptions>(shallowCopyPropertyPath)(config);
        if (sslFromOptions != null) {
            throw new Error(`'${shallowCopyPropertyPath}' is not allowed in the configuration, please remove it`);
        }
        const sslOptions = _.property<Config, SslCertificateOptions>(propertyPath)(config) ?? {};
        // create a shallow copy of the sslOptions to keep the file path of the PEM files
        _.set(config, shallowCopyPropertyPath, { ...sslOptions });
        const ssl = sslOptions as Dict<string | Buffer>;
        const watchFiles: Dict<string> = {};
        const oldSslOptions = _.property<Config, SslCertificateOptions>(propertyPath)(this.config) ?? {};

        const tlsChange: Partial<SslCertificateOptions> = {};

        ['ca', 'cert', 'key']
            .filter(k => ssl[k] != null)
            .forEach((k: PemType) => {
                const pemArray = (Array.isArray(ssl[k]) ? ssl[k] : [ssl[k]]) as (string | Buffer)[];
                pemArray.forEach(pem => {
                    // load PEM file if not a PEM string
                    if (typeof pem === 'string' && !pem.startsWith('-----BEGIN ')) {
                        const pemData = this._tryloadPemFile(pem, k);
                        if (pemData != null) {
                            ssl[k] = pemData;
                            watchFiles[k] = pem;
                        }
                    }
                    // compare PEM string
                    if (ssl[k] !== oldSslOptions[k]) {
                        tlsChange[k] = ssl[k];
                    }
                });
            });

        this._notifyTlsChangeAndInstallWatcher(propertyPath, watchFiles, tlsChange);
    }

    private static _notifyTlsChangeAndInstallWatcher(
        propertyPath: string,
        watchFiles: Dict<string>,
        tlsChange: Partial<SslCertificateOptions>,
    ) {
        const emitTlsChange = _.debounce(() => {
            logger.info(() => `Notify TLS change for '${propertyPath}' on [${Object.keys(tlsChange)}]`);
            this.emitter.emit('tlsChange', propertyPath, tlsChange);
        }, 500);

        const reloadFiles = _.debounce(() => {
            logger.info(`Reload TLS files for '${propertyPath}'`);
            Object.keys(watchFiles).forEach(key => {
                (tlsChange as any)[key] = fs.readFileSync(watchFiles[key], 'utf8');
                logger.info(`Reloaded TLS ${key} file for '${propertyPath}' from '${watchFiles[key]}'`);
            });
            this.emitter.emit('tlsReload', propertyPath, watchFiles);
            emitTlsChange();
        }, 500);

        // if we have some change we notify them so that we can reload the security context of the related server
        if (Object.values(tlsChange).length > 0) {
            emitTlsChange();
        }

        const watchFilesPath = Object.values(watchFiles);
        let currentWatcher: FilesWatcher | undefined = this.fileWatcherMap[propertyPath];
        if (currentWatcher?.watcher != null) {
            if (_.isEqual(currentWatcher.files, watchFilesPath)) {
                // Nothing to do we are already watching the files
            } else {
                const watcher = currentWatcher.watcher;
                currentWatcher = undefined;
                delete this.fileWatcherMap[propertyPath];
                (async () => {
                    if (watcher) {
                        logger.info(`Closing TLS file watcher for '${propertyPath}'`);
                        await watcher.close();
                    }
                })()
                    .catch(err => {
                        logger.error(`Failed to close file watcher '${propertyPath}': ${err.message}`);
                    })
                    .finally(() => {});
            }
        }

        if (!currentWatcher?.watcher && watchFilesPath.length > 0) {
            logger.info(`Creating TLS file watcher for '${propertyPath}'`);
            this.fileWatcherMap[propertyPath] = {
                watcher: chokidar.watch(watchFilesPath, { persistent: true }).on('change', (path: string) => {
                    logger.info(`TLS file changed for '${propertyPath}': ${path}`);
                    reloadFiles();
                }),
                files: watchFilesPath,
            };
            emitTlsChange();
        }
    }

    private static _tryloadPemFile(path: string, pemType: PemType): string | null {
        logger.info(`Loading ${pemType} pem file from ${path}`);
        try {
            return fs.readFileSync(path, 'ascii').replace(/\r\n/g, '\n');
        } catch (err) {
            logger.error(`Failed to load ${pemType} pem file from ${path}: ${err.message}`);
            return null;
        }
    }

    static load(dirname: string, source?: string, extensionPath?: string): Config {
        if (this.config && this.config.extensionPath === extensionPath) return this.config;
        return this._loadConfiguration(dirname, source, extensionPath);
    }

    private static _loadConfiguration(dirname: string, source?: string, extensionPath?: string): Config {
        const result = this._load(dirname);

        // A config extension path was provided, load the config from the path and merge it to the root config
        if (extensionPath) {
            const configExtensionFilename = fsp.join(extensionPath, knownFilenames.configuration);
            const configExtensionSecurityFile = fsp.join(extensionPath, knownFilenames.security);
            // Only load extension if there is an xtrem-config.yml in the config extension location
            if (fs.existsSync(configExtensionFilename)) {
                const extendedConfig = yamlLoad(fs.readFileSync(configExtensionFilename, 'utf8'));
                result.filenames.push(configExtensionFilename);
                result.config = _.merge(result.config, extendedConfig);
            } else {
                logger.error(
                    () =>
                        `${knownFilenames.configuration} file not found in config extension location provided ${extensionPath}`,
                );
            }

            if (fs.existsSync(configExtensionSecurityFile)) {
                const extendedSecurityConfig = {
                    security: yamlLoad(fs.readFileSync(configExtensionSecurityFile, 'utf8')),
                };
                result.filenames.push(configExtensionSecurityFile);
                result.config = _.merge(result.config, extendedSecurityConfig);
            }
            result.config.extensionPath = extensionPath;
        }

        this.config = this._checkConfig(result.config);
        Logger.setAppName(this.config.app);

        // Disable logs first, to avoid polluting output with config manager logs.
        if (source === 'test' && this.config.logs?.disabledForTests) this.config.logs.disabled = true;
        if (this.config.logs?.disabled) {
            Logger.disable();
            // use console.warn to avoid noisy banner
            // eslint-disable-next-line no-console
            console.warn(`Logs are disabled by config (pid ${process.pid}, cwd ${process.cwd()})`);
        }

        this.sizeLimits = {};

        if (result.filenames.length > 0) {
            // Create a watcher so that the configuration can be reloaded when any of the configuration files change
            const files = _.uniq(result.filenames).filter(f => !!f);
            if (!_.isEqual(ConfigManager.configFilePaths, files)) {
                // use chokidar which is more perfomant and reliable than fs.watchFile and fs.watch
                chokidar.watch(files, { persistent: true }).on('change', (path: string) => {
                    logger.info(`Configuration changed: ${path}`);
                    this._loadConfiguration(dirname, source, extensionPath);
                });
                ConfigManager.configFilePaths = files;
            }
        }

        if (!this.config.security && (this.config.prodUi || this.config.deploymentMode === 'production')) {
            logger.warn(
                'Production user interface is configured without a security config, pages will not be fetched.',
            );
        }

        const sqlConfig = this.config.storage!.sql;

        if (sqlConfig) {
            const deprecatedKeys = ['driver', 'databaseDirs', 'databaseLayers', 'folderName'];
            deprecatedKeys
                .filter(key => Object.keys(sqlConfig).includes(key))
                .forEach(key => logger.warn(`Configuration parameter '${key}' is obsolete and will be ignored.`));

            logger.info(
                `SQL config: host=${sqlConfig.hostname}, port=${sqlConfig.port}, database=${sqlConfig.database}, user=${sqlConfig.user}`,
            );
        }

        if (this.config.deploymentMode === 'development' && process.env.XTREM_ENV == null) {
            // Default value for 'XTREM_ENV' environment variable.
            // This will disable some features on development machines:
            // - notifications on user creation,
            // - ...
            // Note : on AWS machines, the 'XTREM_ENV' variable will be set to other values
            process.env.XTREM_ENV = 'local';
            logger.info("'XTREM_ENV' environment variable was set to 'local'");
        }

        this.config.textStreamContentTypes = this.config.textStreamContentTypes || [];

        const allowedTextStreamContentTypeByDefault = [
            'application/xml',
            'application/json',
            'text/xml',
            'text/plain',
            'text/html',
            'text/css',
        ];
        const allowedBinaryStreamContentTypeByDefault = ['image/*', 'application/pdf'];

        allowedTextStreamContentTypeByDefault.forEach(contentType => {
            if (!this.config.textStreamContentTypes!.includes(contentType)) {
                logger.warn(`${contentType} was added to the list of allowed content-types of text stream`);
                this.config.textStreamContentTypes!.push(contentType);
            }
        });

        allowedBinaryStreamContentTypeByDefault.forEach(contentType => {
            if (!this.config.textStreamContentTypes!.includes(contentType)) {
                logger.warn(`${contentType} was added to the list of allowed content-types of binary stream`);
                this.config.textStreamContentTypes!.push(contentType);
            }
        });

        if (!this.config.serviceOptions) {
            /** if config.serviceOptions is not present we set it to workInProgress in dev mode,
            and to released in production mode. */
            this.config.serviceOptions = {
                level: this.config.deploymentMode === 'development' ? 'workInProgress' : 'released',
            };
            logger.info(`config.serviceOptions.level was set to '${this.config.serviceOptions.level}'`);
        }

        this.emitter.emit('loaded', this.config);
        return this.config;
    }

    static notLoadedError(): Error {
        return new Error('config not loaded!');
    }

    static get current(): Config {
        if (!this.config) throw this.notLoadedError();
        return this.config;
    }

    static get header(): string {
        if (!this.config) throw this.notLoadedError();
        return Buffer.from(JSON.stringify(this.config)).toString('base64');
    }

    static getSetting<T>(name: string, defaultValue: T) {
        const settings = this.current.settings;
        return settings && name in settings ? settings[name] : defaultValue;
    }

    static fixServicesConfig(): void {
        if (!this.config) {
            throw this.notLoadedError();
        }

        const keys = ['sysUser', 'sysPassword', 'sysDatabase'] as (keyof SqlConfig)[];
        const sqlConfig = this.config.storage?.sql as IndexedConfig;
        if (sqlConfig) {
            keys.forEach(k => {
                if (sqlConfig[k]) {
                    logger.warn(`storage.sql.${k} has been deleted because it is not allowed in services mode`);
                    delete sqlConfig[k];
                }
            });
        }
    }
}
