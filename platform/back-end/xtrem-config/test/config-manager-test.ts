// tslint:disable: no-unused-expression
import { assert } from 'chai';
import * as fs from 'node:fs/promises';
import { tmpdir } from 'node:os';
import * as fsp from 'node:path';
// import { setTimeout } from 'node:timers/promises';
import { Config } from '@sage/xtrem-shared';
import * as yaml from 'js-yaml';
import { ConfigManager } from '../lib/config-manager';
import { TestCertificateGenerator, generateCertificates } from './fixtures/test-certificate-generator';

describe('Config test', () => {
    const rootPath = fsp.join(__dirname, '..', '..', '..', '..');
    const tmpDir = fsp.join(__dirname, 'tmp');
    const tmpConfigFile = fsp.join(tmpDir, 'xtrem-config.yml');

    const prefix = fsp.join(tmpdir(), 'ssl-');

    const generateTestCertificates = async (): Promise<TestCertificateGenerator> => {
        const sslDir = await fs.mkdtemp(prefix);
        return generateCertificates(sslDir);
    };

    before(async () => {
        await fs.mkdir(tmpDir, { recursive: true });
        await fs.copyFile(fsp.join(rootPath, 'xtrem-config-azure.yml'), tmpConfigFile);
    });

    after(async () => {
        await fs.rm(tmpDir, { recursive: true });
    });

    it('Should throw if not loaded', () => {
        assert.Throw(() => {
            ConfigManager.current;
        }, 'config not loaded!');
    });

    it('can load config', () => {
        const config = ConfigManager.load(tmpConfigFile, 'test');
        assert.isNotNull(config);
        assert.deepEqual(config, ConfigManager.current);
        // hack to unload the config
        (ConfigManager as any).config = undefined;
        assert.Throw(() => {
            ConfigManager.current;
        }, 'config not loaded!');
    });

    it('should notify change to server.ssl ', async () => {
        const yamlConfig = await fs.readFile(tmpConfigFile, 'utf8');
        const rawConfig = yaml.load(yamlConfig) as Config;
        const certGenerator = await generateTestCertificates();

        ConfigManager.load(tmpConfigFile, 'test');

        assert.isNotNull(ConfigManager.current);

        // create the condition variable
        const tlsChange = new Promise(resolve => {
            ConfigManager.emitter.once('tlsChange', (propertyPath: string) => {
                resolve(propertyPath);
            });
        });

        rawConfig.server = {
            ssl: { ca: certGenerator.caCertFile, cert: certGenerator.serverCertFile, key: certGenerator.serverKeyFile },
        };

        // Rewriting the config should trigger the tlsChange event for change  we made on server.ssl
        await fs.writeFile(tmpConfigFile, yaml.dump(rawConfig), 'utf8');

        const propPath = await tlsChange;
        assert.strictEqual(propPath, 'server.ssl');

        // create the condition variable
        const tlsReload = new Promise(resolve => {
            ConfigManager.emitter.once('tlsReload', (propertyPath: string, watchFiles: string[]) => {
                resolve({ propertyPath, watchFiles });
            });
        });

        const certGenerator2 = await generateTestCertificates();

        // Rewriting the certificates files should trigger the tlsReload event for server.ssl
        await fs.copyFile(certGenerator2.caCertFile, certGenerator.caCertFile);
        await fs.copyFile(certGenerator2.serverCertFile, certGenerator.serverCertFile);
        await fs.copyFile(certGenerator2.serverKeyFile, certGenerator.serverKeyFile);
        const tlsReloadChanges: any = await tlsReload;
        assert.strictEqual(tlsReloadChanges.propertyPath, 'server.ssl');
        assert.deepEqual(tlsReloadChanges.watchFiles, rawConfig.server.ssl);

        // Cleanup
        // hack to unload the config
        (ConfigManager as any).config = undefined;
        await certGenerator.cleanup();
        await fs.rm(tmpConfigFile);
    });
});
