import * as childProc from 'child_process';
import * as fs from 'fs/promises';
import { nanoid } from 'nanoid';
import * as path from 'path';
import { SecureContextOptions } from 'tls';
import { promisify } from 'util';

const exec = promisify(childProc.exec);

type CertificateOptions = Pick<SecureContextOptions, 'cert' | 'ca' | 'key'>;

export class TestCertificateGenerator {
    readonly caKeyFile: string;

    readonly caCertFile: string;

    readonly serverKeyFile: string;

    readonly serverCertFile: string;

    readonly errors: string[] = [];

    #certificateOptions: CertificateOptions = {};

    #passphrase: string;

    constructor(
        readonly targetDir: string,
        passphrase: string,
    ) {
        this.caKeyFile = path.join(this.targetDir, 'ca.key');
        this.caCertFile = path.join(this.targetDir, 'ca.crt');
        this.serverKeyFile = path.join(this.targetDir, 'server.key');
        this.serverCertFile = path.join(this.targetDir, 'server.crt');
        this.#passphrase = passphrase;
    }

    static async getOpenSslVersion(): Promise<string | null> {
        try {
            const { stdout } = await exec('openssl version');
            return stdout;
        } catch {
            return null;
        }
    }

    get certificateOptions(): CertificateOptions {
        return this.#certificateOptions;
    }

    async createCaCert(): Promise<boolean> {
        try {
            await exec(`openssl genrsa -aes256 -passout pass:${this.#passphrase} -out ${this.caKeyFile} 4096`);
            await exec(
                `openssl req -new -x509 -days 10 -key ${this.caKeyFile} -out ${this.caCertFile} -passin pass:${
                    this.#passphrase
                } -subj "/O=Sage/CN=Xtrem Test CA"`,
            );
            this.#certificateOptions.ca = await fs.readFile(this.caCertFile, 'utf8');
            return true;
        } catch (err) {
            this.errors.push(err.message);
            return false;
        }
    }

    async createServerCert(): Promise<boolean> {
        try {
            const csr = path.join(this.targetDir, 'server.csr');
            await exec(`openssl genrsa -out ${this.serverKeyFile} 4096`);
            await exec(`openssl req -new -key ${this.serverKeyFile} -out ${csr} -subj "/O=Sage/CN=localhost"`);
            await exec(
                `openssl x509 -req -in ${csr} -passin pass:${this.#passphrase} -CA ${this.caCertFile} -CAkey ${
                    this.caKeyFile
                } -out ${this.serverCertFile} -CAcreateserial -days 10 -sha256`,
            );
            this.#certificateOptions.key = await fs.readFile(this.serverKeyFile, 'utf8');
            this.#certificateOptions.cert = await fs.readFile(this.serverCertFile, 'utf8');
            return true;
        } catch (err) {
            this.errors.push(err.message);
            return false;
        }
    }

    async cleanup(): Promise<void> {
        await fs.rm(this.targetDir, { recursive: true });
    }
}

export async function generateCertificates(dir: string): Promise<TestCertificateGenerator> {
    const certGenerator = new TestCertificateGenerator(dir, nanoid());
    if (await certGenerator.createCaCert()) {
        await certGenerator.createServerCert();
    }

    return certGenerator;
}
