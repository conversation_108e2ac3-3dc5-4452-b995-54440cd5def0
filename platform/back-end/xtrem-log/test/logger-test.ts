import { expect } from 'chai';
import * as sinon from 'sinon';
import * as tripleBeamSymbols from 'triple-beam';
import * as winston from 'winston';
import * as TransportStream from 'winston-transport';
import * as xtremLogger from '../lib/logger';

const FIXTURE_LOG_MESSAGE = 'FIXTURE LOG MESSAGE CONTENT';
const FIXTURE_DATE = new Date(Date.UTC(2019, 6, 23, 12, 34, 56, 789));
const FIXTURE_ERROR = new Error('FIXTURE ERROR');
const DASHES5 = '-'.repeat(5);

const getLogContent = (args: any[]) => args[0][tripleBeamSymbols.MESSAGE] as string;

describe('Test Xtrem logger', () => {
    let testee: xtremLogger.Logger;
    let loggerStub: sinon.SinonStub;
    let dateStub: sinon.SinonStub;
    let logSpy: sinon.SinonSpy;

    // BL: I couldn't any better way to get clean tests
    const resetLogger = () => {
        xtremLogger.Logger.reloadConfig({});
        testee.logLevel = 'info';
        (xtremLogger.Logger as any)._nextEventId = 0;
        logSpy.resetHistory();
    };

    before(() => {
        const originalCreateLogger = winston.createLogger;
        loggerStub = sinon.stub(winston, 'createLogger').callsFake((options: winston.LoggerOptions) => {
            logSpy = sinon.spy((options.transports as TransportStream[])[0], 'log');
            const logger = originalCreateLogger(options);
            return logger;
        });
    });

    beforeEach(() => {
        dateStub = sinon.stub(xtremLogger, 'getDate').returns(FIXTURE_DATE);
        testee = xtremLogger.Logger.getLogger('test.ts', 'test');
        resetLogger();
    });

    afterEach(() => {
        dateStub.restore();
    });

    after(() => {
        loggerStub.restore();
    });

    describe('app name', () => {
        it('should log app name if defined only', () => {
            xtremLogger.Logger.reloadConfig({ logs: { options: { json: true } } });

            xtremLogger.Logger.setAppName('app_test');

            logSpy.resetHistory();
            testee.info(FIXTURE_LOG_MESSAGE);

            expect(logSpy.callCount).to.eq(1);
            let logCall = logSpy.getCall(0);
            let info = JSON.parse(getLogContent(logCall.args));
            expect(info.app).to.eq('app_test');

            xtremLogger.Logger.setAppName(undefined);
            logSpy.resetHistory();
            testee.info(FIXTURE_LOG_MESSAGE);

            expect(logSpy.callCount).to.eq(1);
            logCall = logSpy.getCall(0);
            info = JSON.parse(getLogContent(logCall.args));
            expect(info.app).to.eq(undefined);
        });
    });

    describe('config', () => {
        it('should have "info" as the default level', () => {
            expect(testee.logLevel).to.eq('info');
        });

        it('should update the log level by domain', () => {
            const testee2 = xtremLogger.Logger.getLogger('test.ts', 'test2');
            expect(testee.logLevel).to.eq('info');
            expect(testee2.logLevel).to.eq('info');

            xtremLogger.Logger.reloadConfig({
                logs: {
                    domains: {
                        'sage/xtrem-log/test': {
                            level: 'error',
                        },
                        'sage/xtrem-log/test2': {
                            level: 'verbose',
                        },
                    },
                },
            });

            expect(testee.logLevel).to.eq('error');
            expect(testee2.logLevel).to.eq('verbose');
        });

        it('should correctly identify which log level is active', () => {
            testee.logLevel = 'debug';
            expect(testee.isActive('debug')).to.eq(true);
            expect(testee.isActive('verbose')).to.eq(true);
            expect(testee.isActive('info')).to.eq(true);
            expect(testee.isActive('warn')).to.eq(true);
            expect(testee.isActive('error')).to.eq(true);

            testee.logLevel = 'info';
            expect(testee.isActive('debug')).to.eq(false);
            expect(testee.isActive('verbose')).to.eq(false);
            expect(testee.isActive('info')).to.eq(true);
            expect(testee.isActive('warn')).to.eq(true);
            expect(testee.isActive('error')).to.eq(true);

            testee.logLevel = 'error';
            expect(testee.isActive('debug')).to.eq(false);
            expect(testee.isActive('verbose')).to.eq(false);
            expect(testee.isActive('info')).to.eq(false);
            expect(testee.isActive('warn')).to.eq(false);
            expect(testee.isActive('error')).to.eq(true);
        });
    });

    describe('levels', () => {
        describe('info', () => {
            it('should log simple string', () => {
                testee.info(FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                expect(getLogContent(logCall.args)).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );
            });

            it('should log simple callback string', () => {
                testee.info(() => FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                expect(getLogContent(logCall.args)).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );
            });

            it('should log asynchronous callback string', async () => {
                await testee.infoAsync(() => FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                expect(getLogContent(logCall.args)).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );
            });

            it('should log simple error with stack trace', () => {
                testee.info(FIXTURE_ERROR);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                const logContent = getLogContent(logCall.args);
                const logContentLines = logContent.split('\n');
                expect(logContentLines[0]).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | Error: FIXTURE ERROR`,
                );
                expect(logContentLines.length).to.be.greaterThan(10);
            });

            it('should not log any output if the level is higher than info', () => {
                testee.logLevel = 'warn';

                logSpy.resetHistory();

                testee.info(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(0);
            });
        });

        describe('warn', () => {
            it('should log simple string', () => {
                testee.warn(FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[33mWARN   \u001b[39m | \u001b[33mxtrem-log/test                \u001b[39m | \u001b[33mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | WARN    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log simple callback string', () => {
                testee.warn(() => FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[33mWARN   \u001b[39m | \u001b[33mxtrem-log/test                \u001b[39m | \u001b[33mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | WARN    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log asynchronous callback string', async () => {
                await testee.warnAsync(() => FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[33mWARN   \u001b[39m | \u001b[33mxtrem-log/test                \u001b[39m | \u001b[33mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | WARN    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log simple error with stack trace', () => {
                testee.warn(FIXTURE_ERROR);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                const logContent = getLogContent(logCall.args);
                const logContentLines = logContent.split('\n');
                if (process.stdout.isTTY) {
                    expect(logContentLines[0]).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[33mWARN   \u001b[39m | \u001b[33mxtrem-log/test                \u001b[39m | \u001b[33mError: FIXTURE ERROR`,
                    );
                } else {
                    expect(logContentLines[0]).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | WARN    | xtrem-log/test                 | Error: FIXTURE ERROR`,
                    );
                }
                expect(logContentLines.length).to.be.greaterThan(10);
            });

            it('should not log any output if the level is higher than warn', () => {
                testee.logLevel = 'error';
                logSpy.resetHistory();

                testee.warn(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(0);
            });
        });

        describe('error', () => {
            it('should log simple string', () => {
                testee.error(FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[31mERROR  \u001b[39m | \u001b[31mxtrem-log/test                \u001b[39m | \u001b[31mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | ERROR   | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log simple callback string', () => {
                testee.error(() => FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[31mERROR  \u001b[39m | \u001b[31mxtrem-log/test                \u001b[39m | \u001b[31mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | ERROR   | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log simple async string', async () => {
                await testee.errorAsync(() => FIXTURE_LOG_MESSAGE);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[31mERROR  \u001b[39m | \u001b[31mxtrem-log/test                \u001b[39m | \u001b[31mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | ERROR   | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log simple error with stack trace', () => {
                testee.error(FIXTURE_ERROR);

                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                const logContent = getLogContent(logCall.args);
                const logContentLines = logContent.split('\n');
                if (process.stdout.isTTY) {
                    expect(logContentLines[0]).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[31mERROR  \u001b[39m | \u001b[31mxtrem-log/test                \u001b[39m | \u001b[31mError: FIXTURE ERROR`,
                    );
                } else {
                    expect(logContentLines[0]).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | ERROR   | xtrem-log/test                 | Error: FIXTURE ERROR`,
                    );
                }
                expect(logContentLines.length).to.be.greaterThan(10);
            });
        });

        describe('verbose', () => {
            it('should not log by default', () => {
                testee.verbose(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(0);
            });

            it('should log when the log level is set to verbose or below', () => {
                testee.logLevel = 'verbose';
                logSpy.resetHistory();

                testee.verbose(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[94mVERBOSE\u001b[39m | \u001b[94mxtrem-log/test                \u001b[39m | \u001b[94mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | VERBOSE | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log asynchronously when the log level is set to verbose or below', async () => {
                testee.logLevel = 'verbose';
                logSpy.resetHistory();

                await testee.verboseAsync(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[94mVERBOSE\u001b[39m | \u001b[94mxtrem-log/test                \u001b[39m | \u001b[94mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | VERBOSE | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });
        });

        describe('debug', () => {
            it('should not log by default', () => {
                testee.debug(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(0);
            });

            it('should log when the log level is set to debug', () => {
                testee.logLevel = 'debug';
                logSpy.resetHistory();

                testee.debug(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[94mDEBUG  \u001b[39m | \u001b[94mxtrem-log/test                \u001b[39m | \u001b[94mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | DEBUG   | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });

            it('should log asynchronously when the log level is set to debug', async () => {
                testee.logLevel = 'debug';
                logSpy.resetHistory();

                await testee.debugAsync(() => FIXTURE_LOG_MESSAGE);
                expect(logSpy.callCount).to.eq(1);
                const logCall = logSpy.getCall(0);
                if (process.stdout.isTTY) {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | \u001b[94mDEBUG  \u001b[39m | \u001b[94mxtrem-log/test                \u001b[39m | \u001b[94mFIXTURE LOG MESSAGE CONTENT\u001b[39m`,
                    );
                } else {
                    expect(getLogContent(logCall.args)).to.eq(
                        `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | DEBUG   | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                    );
                }
            });
        });
    });

    describe('profiling', () => {
        it('should log when profiler success callback is called', () => {
            testee.logLevel = 'debug';
            logSpy.resetHistory();
            const loggerResult = testee.info(FIXTURE_LOG_MESSAGE);
            expect(logSpy.callCount).to.eq(1);
            expect(getLogContent(logSpy.getCall(0).args)).to.includes(
                `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
            );

            dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1234));
            loggerResult.success('FIXTURE TASK PROGRESS UPDATE.');
            expect(logSpy.callCount).to.eq(2);
            expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | INFO    | xtrem-log/test                 | Event #1 : FIXTURE TASK PROGRESS UPDATE. \u001b[39m(1234 ms)`,
            );
            dateStub.returns(new Date(FIXTURE_DATE.getTime() + 2345));
            loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');
            expect(logSpy.callCount).to.eq(3);
            expect(getLogContent(logSpy.getCall(2).args)).to.includes(
                `${DASHES5} | ${process.pid} | 000001 | 12:34:59.134 | INFO    | xtrem-log/test                 | Event #1 : FIXTURE TASK SUCCESSFULLY FINISHED. \u001b[39m(2345 ms)`,
            );
        });

        it('should log when profiler error callback is called', () => {
            const loggerResult = testee.info(FIXTURE_LOG_MESSAGE);
            expect(logSpy.callCount).to.eq(1);
            expect(getLogContent(logSpy.getCall(0).args)).to.includes(
                `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
            );

            dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1234));
            loggerResult.fail('FIXTURE TASK FAILED :-( ).');
            expect(logSpy.callCount).to.eq(2);
            if (process.stdout.isTTY) {
                expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | \u001b[31mERROR  \u001b[39m | \u001b[31mxtrem-log/test                \u001b[39m | \u001b[31mEvent #1 : FIXTURE TASK FAILED :-( ). (1234 ms)\u001b[39m`,
                );
            } else {
                expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | ERROR   | xtrem-log/test                 | Event #1 : FIXTURE TASK FAILED :-( ). (1234 ms)`,
                );
            }
        });

        describe('thresholds', () => {
            it('should log the execution time in white when it is acceptable', () => {
                testee.logLevel = 'debug';
                logSpy.resetHistory();
                const loggerResult = testee.info(FIXTURE_LOG_MESSAGE, {
                    lowThreshold: 1500,
                    highThreshold: 2000,
                });
                expect(logSpy.callCount).to.eq(1);
                expect(getLogContent(logSpy.getCall(0).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );

                dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1234));
                loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');
                expect(logSpy.callCount).to.eq(2);
                expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | INFO    | xtrem-log/test                 | Event #1 : FIXTURE TASK SUCCESSFULLY FINISHED. (1234 ms)`,
                );
            });

            it('should log the execution time in yellow when the execution is above the lower limit', () => {
                testee.logLevel = 'debug';
                logSpy.resetHistory();
                const loggerResult = testee.info(FIXTURE_LOG_MESSAGE, { lowThreshold: 1000, highThreshold: 2000 });
                expect(logSpy.callCount).to.eq(1);
                expect(getLogContent(logSpy.getCall(0).args)).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );

                dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1234));
                loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');
                expect(logSpy.callCount).to.eq(2);
                expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | INFO    | xtrem-log/test                 | Event #1 : FIXTURE TASK SUCCESSFULLY FINISHED. \u001b[33m(1234 ms)`,
                );
            });

            it('should log the execution time in red when the execution is above the higher limit', () => {
                testee.logLevel = 'debug';
                logSpy.resetHistory();
                const loggerResult = testee.info(FIXTURE_LOG_MESSAGE, { lowThreshold: 500, highThreshold: 1000 });
                expect(logSpy.callCount).to.eq(1);
                expect(getLogContent(logSpy.getCall(0).args)).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );

                dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1234));
                loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');
                expect(logSpy.callCount).to.eq(2);
                expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | INFO    | xtrem-log/test                 | Event #1 : FIXTURE TASK SUCCESSFULLY FINISHED. \u001b[31m(1234 ms)`,
                );
            });

            it('should log the execution time in red when only one limit is provided and it is exceeded', () => {
                testee.logLevel = 'debug';
                logSpy.resetHistory();
                const loggerResult = testee.info(FIXTURE_LOG_MESSAGE, { lowThreshold: 1000 });
                expect(logSpy.callCount).to.eq(1);
                expect(getLogContent(logSpy.getCall(0).args)).to.eq(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:56.789 | INFO    | xtrem-log/test                 | FIXTURE LOG MESSAGE CONTENT`,
                );

                dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1234));
                loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');
                expect(logSpy.callCount).to.eq(2);
                expect(getLogContent(logSpy.getCall(1).args)).to.includes(
                    `${DASHES5} | ${process.pid} | 000001 | 12:34:58.023 | INFO    | xtrem-log/test                 | Event #1 : FIXTURE TASK SUCCESSFULLY FINISHED. \u001b[31m(1234 ms)`,
                );
            });
        });

        describe('statistics', () => {
            beforeEach(() => {
                // Using random here to provide a fresh logger for each test
                testee = xtremLogger.Logger.getLogger('test.ts', `test${Math.random()}`);
                resetLogger();
            });

            it('should provide empty profiling statistics', () => {
                testee.info(FIXTURE_LOG_MESSAGE, { lowThreshold: 1000 });

                expect(testee.statistics).to.instanceOf(Object);
                expect(testee.statistics.count).to.eq(0);
                expect(testee.statistics.totalDuration).to.eq(0);
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                expect(testee.statistics.meanDuration).to.be.NaN;
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                expect(testee.statistics.standardDeviation).to.be.NaN;
            });

            it('should provide profiling statistics', () => {
                let loggerResult = testee.info(FIXTURE_LOG_MESSAGE);
                dateStub.returns(new Date(FIXTURE_DATE.getTime() + 1000));
                loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');

                expect(testee.statistics.count).to.eq(1);
                expect(testee.statistics.totalDuration).to.eq(1000);
                expect(testee.statistics.meanDuration).to.eq(1000);
                expect(testee.statistics.standardDeviation).to.eq(0);

                loggerResult = testee.info(FIXTURE_LOG_MESSAGE);
                dateStub.returns(new Date(FIXTURE_DATE.getTime() + 2000));
                loggerResult.success('FIXTURE TASK SUCCESSFULLY FINISHED.');

                expect(testee.statistics.count).to.eq(2);
                expect(testee.statistics.totalDuration).to.eq(2000);
                expect(testee.statistics.meanDuration).to.eq(1000);
                expect(testee.statistics.standardDeviation).to.eq(0);
            });
        });
    });
});
