import {
    AnyValue,
    AsyncResponse,
    Config,
    Dict,
    LoggerErrorH<PERSON>ler,
    LoggerIgnoreCallback,
    LoggerInterface,
    LoggingOptions,
    LogsConfig,
    ProfilerCallback,
} from '@sage/xtrem-shared';
import * as fs from 'fs';
import { format } from 'logform';
import * as fsp from 'path';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import * as Transport from 'winston-transport';
import { clsContext } from './async-local-storage';
import { Colors } from './colors';
import { ConsoleTransport, colorizeText } from './console-transport';

const rootLoggerSourceFilename = '$$';

const logLevelsOrders = {
    // WARNING : any changes to this object should be reflected to @sage/xtrem-shared/LogLevel
    off: 0,
    error: 1,
    warn: 2,
    info: 3,
    verbose: 4,
    debug: 5,
};

export type MessageOrCallback = string | Error | (() => string | Error);
export type MessageOrCallbackAsync = Promise<string | Error> | (() => string | Error | Promise<string | Error>);

/** @internal */
export type LogLevel = keyof typeof logLevelsOrders;

/** Default error handler for `logger.do`. Just rethrows */
export const rethrow: LoggerErrorHandler<any> = err => {
    throw err;
};

/** We need a function for testability */
/* istanbul ignore next */
export const getDate = () => new Date();

interface LoggerInfo extends winston.Logform.TransformableInfo {
    paddedEventId?: string;
    paddedFullDomain?: string;
    paddedLogLevel?: string;
    paddedDate?: string;
    message: string;
}

export class Logger implements LoggerInterface {
    private static _globalLogger: Dict<winston.Logger> = {};

    private static readonly _allLoggers: Dict<Logger> = {};

    private static readonly _allLoggersByDomain: Dict<Logger> = {};

    private static _nextEventId = 0;

    private static _startBannerDisplayed = false;

    private static logsConfig: LogsConfig | undefined;

    private static _logAsJson?: boolean;

    private static _app?: string;

    private static readonly consoleFormatter = format.printf((info: LoggerInfo) => {
        const context = clsContext()?.context?.context;

        let errorHint;
        if (info.level === 'error' && context?.request != null) {
            errorHint = context.getRequestHint?.();
        }
        if (Logger._logAsJson) {
            const payload = {
                tenantId: context?.tenantId,
                userId: context?.userId,
                originId: context?.originId,
                eventId: info.paddedEventId,
                datetime: info.paddedDate,
                logLevel: info.paddedLogLevel == null ? '' : info.paddedLogLevel.toString().trim(),
                domain: info.paddedFullDomain == null ? '' : info.paddedFullDomain.toString().trim(),
                errorHint,
                source: context?.source,
                message: info.message,
            } as Dict<string>;
            if (Logger._app != null) {
                payload.app = Logger._app;
            }
            /* istanbul ignore next */
            return JSON.stringify(payload);
        }
        return [
            context?.tenantId?.slice(-5) || '-'.repeat(5),
            process.pid,
            info.paddedEventId,
            info.paddedDate,
            ...[
                info.paddedLogLevel,
                info.paddedFullDomain,
                errorHint != null ? `[${errorHint}] ${info.message}` : info.message,
            ].map(arg => colorizeText(info.level as LogLevel, arg ?? '')),
        ].join(' | ');
    });

    private static readonly fileFormatter = format.printf((info: LoggerInfo) =>
        [
            process.pid,
            info.paddedEventId,
            info.paddedDate,
            info.paddedLogLevel,
            info.paddedFullDomain,
            Colors.clean(info.message),
        ].join(' | '),
    );

    private _logLevel: LogLevel = 'info';

    private initialized = false;

    private readonly moduleName: string | null;

    private readonly timer = {
        invocationCount: 0,
        totalDuration: 0,
        sumOfSquares: 0,
    };

    // Are all logs disabled?
    private static isDisabled = false;

    constructor(
        sourceFilename: string,
        private readonly domain: string,
    ) {
        if (!sourceFilename) throw new Error('Invalid call : __filename must be provided');
        if (sourceFilename === rootLoggerSourceFilename) {
            // rootLogger
            this.moduleName = '';
        } else {
            // extract domain from the package.json
            this.moduleName = Logger.getPackageName(fsp.dirname(sourceFilename));
        }
    }

    private static get globalLogger(): winston.Logger {
        return this._globalLogger[String(process.pid)];
    }

    private static set globalLogger(logger: winston.Logger) {
        this._globalLogger[String(process.pid)] = logger;
    }

    private nopFunction(): ProfilerCallback {
        return {
            success: () => {},
            fail: (message: string) => {
                this.error(message);
            },
        };
    }

    /**
     * Returns the name of the package that contains the folder.
     * The result will be sth like sage/xtrem-core
     * @param folder
     */
    private static getPackageName(folder: string): string | null {
        const packageFilename = 'package.json';
        let folderToTest = folder;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const path = fsp.join(folderToTest, packageFilename);
            if (fs.existsSync(path)) {
                try {
                    // eslint-disable-next-line global-require, import/no-dynamic-require
                    const pck = require(path);
                    const name = pck.name as string;
                    if (name.startsWith('@')) {
                        return name.slice(1); // remove the '@' prefix
                    }
                    return name;
                } catch {
                    return '';
                }
            }
            if (path === packageFilename) {
                // We have reached the top folder. No need to go further
                return '';
            }
            const parentFolder = fsp.join(folderToTest, '..');
            if (parentFolder === folderToTest) return '';
            folderToTest = parentFolder;
        }
    }

    /** internal */
    static get rootLogger(): Logger {
        return Logger.getLogger(rootLoggerSourceFilename, '');
    }

    public static reloadConfig(config: Config) {
        this.logsConfig = config.logs;
        Logger._logAsJson = config.deploymentMode === 'production' || this.logsConfig?.options?.json;
        if (config.env?.isCI) {
            // Disable the JSON logs when running on CI
            Logger._logAsJson = false;
            Logger.rootLogger.info(
                `json log has been disabled because the service is running on CI: TF_BUILD='${process.env.TF_BUILD}'`,
            );
        }

        let fileTransportCreated = false;
        let outputFolder;
        // We have to declare a static winston logger, all the xtrem loggers will log to this unique winston
        // logger (this way, we will only have one log file).
        // By default, logs are written in the folder that contains the config file.
        this.initGlobalLogger();
        if (this.globalLogger.transports.length === 1) {
            // For now, there is only the one transport : the console transport
            // Now that we have a config, we can add a file transport
            outputFolder =
                (this.logsConfig && this.logsConfig.outputFolder) || fsp.join(config.originFolder || __dirname, 'logs');
            try {
                fs.mkdirSync(outputFolder);
                // eslint-disable-next-line no-empty
            } catch {}
            this.globalLogger.add(
                new DailyRotateFile({
                    dirname: outputFolder,
                    filename: `${(this.logsConfig && this.logsConfig.filenamePrefix) || 'xtrem.server'}-${
                        process.pid
                    }-%DATE%.log`,
                    maxSize: 200 * 1024 * 1024, // 200 Mb
                    maxFiles: 30,
                    json: false,
                    format: this.fileFormatter,
                }),
            );
            fileTransportCreated = true;
        }
        const logsAreEmpty = !Logger._startBannerDisplayed;
        this.displayStartBanner();
        if (!logsAreEmpty) Logger.rootLogger.info('Configuration reloaded');
        if (fileTransportCreated)
            Logger.rootLogger.info(`Logs for process ${process.pid} will be written in folder : ${outputFolder}`);

        // Now, update the level of all the already created logs
        if (!this.logsConfig) return;
        const domains = this.logsConfig.domains || {};
        Object.keys(domains).forEach(domain => {
            if (this._allLoggersByDomain[domain]) {
                this._allLoggersByDomain[domain]._logLevel = domains[domain].level;
            }
        });
    }

    private static initGlobalLogger() {
        if (!Logger.globalLogger) {
            // Initialization of the first logger
            const transports: Transport[] = [
                new ConsoleTransport({
                    format: Logger.logsConfig?.options?.noColor ? this.fileFormatter : this.consoleFormatter,
                }),
            ];
            Logger.globalLogger = winston.createLogger({
                level: 'silly', // log levels are handled by our API
                transports,
            });
        }
    }

    private initIfNeeded() {
        if (this.initialized) return;
        this.initialized = true;
        Logger.initGlobalLogger();
        // Try to retrieve the logLevel from the config file
        if (Logger.logsConfig) {
            const domains = Logger.logsConfig.domains;
            const key = this.fullDomain;
            if (domains && domains[key]) {
                const domainLevel: LogLevel = domains[key].level;
                if (logLevelsOrders[domainLevel] === null) {
                    throw new Error(`Logger ${key} : invalid log level : ${domains[key].level}`);
                }

                this._logLevel = domains[key].level;
                if (logLevelsOrders[this._logLevel] < logLevelsOrders.info) {
                    Logger.rootLogger.warn(
                        `Logger '${key}' : level retrograded to '${this._logLevel}' by configuration.`,
                    );
                }
            }
        }
        Logger.displayStartBanner();
    }

    private static displayStartBanner() {
        if (this._startBannerDisplayed) return;
        this._startBannerDisplayed = true;
        Logger.rootLogger.info('************************ SERVER STARTED ************************');
    }

    private get fullDomain(): string {
        return this.moduleName ? `${this.moduleName}/${this.domain}` : '';
    }

    get logLevel(): LogLevel {
        this.initIfNeeded();
        return this._logLevel;
    }

    set logLevel(level: LogLevel) {
        this.initIfNeeded();
        if (logLevelsOrders[level] < logLevelsOrders[this._logLevel] && this.initialized) {
            Logger.rootLogger.warn(`Logger '${this.fullDomain}' : level retrograded to '${level}'`);
        }
        this._logLevel = level;
    }

    /**
     * Creates a new logger.
     * @param sourceFilename  should be __filename
     * @param domain
     */
    static getLogger(sourceFilename: string, domain: string): Logger {
        const key = `${Logger.getPackageName(sourceFilename)}/${domain}`;
        let logger = Logger._allLoggers[key];
        if (!logger) {
            logger = new Logger(sourceFilename, domain);
            Logger._allLoggers[key] = logger;
            Logger._allLoggersByDomain[logger.fullDomain] = logger;
        }
        return logger;
    }

    /**
     * Logs a message with a provided log level
     */
    private _log(logLevel: LogLevel, messageOrCallback: MessageOrCallback, options: LoggingOptions): ProfilerCallback {
        this.initIfNeeded();
        if (Logger.isDisabled) return this.nopFunction();
        if (logLevelsOrders[this._logLevel] < logLevelsOrders[logLevel]) return this.nopFunction();
        if (typeof messageOrCallback === 'function')
            this._internalLog({
                logLevel,
                messageOrError: messageOrCallback(),
                ignoreCallback: options.ignoreCallback,
            });
        else this._internalLog({ logLevel, messageOrError: messageOrCallback, ignoreCallback: options.ignoreCallback });
        return this.getLogFunctionCallback(logLevel, options);
    }

    /**
     * Logs a message with a provided log level - async version
     */
    private async _logAsync(
        logLevel: LogLevel,
        messageOrCallback: MessageOrCallbackAsync,
        options: LoggingOptions,
    ): Promise<ProfilerCallback> {
        this.initIfNeeded();
        if (Logger.isDisabled) return this.nopFunction();
        if (logLevelsOrders[this._logLevel] < logLevelsOrders[logLevel]) return this.nopFunction();
        if (typeof messageOrCallback === 'function') {
            this._internalLog({
                logLevel,
                messageOrError: await messageOrCallback(),
                ignoreCallback: options.ignoreCallback,
            });
        } else
            this._internalLog({
                logLevel,
                messageOrError: await messageOrCallback,
                ignoreCallback: options.ignoreCallback,
            });
        return this.getLogFunctionCallback(logLevel, options);
    }

    /**
     * Logs a message with a provided log level
     */
    log(logLevel: LogLevel, messageProvider: () => string, options: LoggingOptions = {}): ProfilerCallback {
        return this._log(logLevel, messageProvider, options);
    }

    /**
     * Logs a message with a provided log level - async version
     */
    logAsync(
        logLevel: LogLevel,
        messageProvider: Promise<string> | (() => Promise<string>),
        options: LoggingOptions = {},
    ): Promise<ProfilerCallback> {
        return this._logAsync(logLevel, messageProvider, options);
    }

    /**
     * Logs a message with the 'info' level (level=3)
     */
    info(messageOrCallback: MessageOrCallback, options: LoggingOptions = {}): ProfilerCallback {
        return this._log('info', messageOrCallback, options);
    }

    /**
     * Logs a message with the 'info' level (level=3) - async version
     */
    infoAsync(messageOrCallback: MessageOrCallbackAsync, options: LoggingOptions = {}): Promise<ProfilerCallback> {
        return this._logAsync('info', messageOrCallback, options);
    }

    /**
     * Logs a message with the 'warn' level (level=2)
     */
    warn(messageOrCallback: MessageOrCallback, options: LoggingOptions = {}): ProfilerCallback {
        return this._log('warn', messageOrCallback, options);
    }

    /**
     * Logs a message with the 'warn' level (level=2) - async version
     */
    warnAsync(messageOrCallback: MessageOrCallbackAsync, options: LoggingOptions = {}): Promise<ProfilerCallback> {
        return this._logAsync('warn', messageOrCallback, options);
    }

    /**
     * Logs a message with the 'debug' level (level=5)
     */
    debug(messageProvider: () => string, options: LoggingOptions = {}): ProfilerCallback {
        return this._log('debug', messageProvider, options);
    }

    /**
     * Logs a message with the 'debug' level (level=5)
     */
    debugAsync(
        messageProvider: () => string | Promise<string>,
        options: LoggingOptions = {},
    ): Promise<ProfilerCallback> {
        return this._logAsync('debug', messageProvider, options);
    }

    /**
     * Logs a message with the 'verbose' level (level=4)
     */
    verbose(messageProvider: () => string, options: LoggingOptions = {}): ProfilerCallback {
        return this._log('verbose', messageProvider, options);
    }

    /**
     * Logs a message with the 'verbose' level (level=4) - async version
     */
    verboseAsync(
        messageProvider: () => string | Promise<string>,
        options: LoggingOptions = {},
    ): Promise<ProfilerCallback> {
        return this._logAsync('verbose', messageProvider, options);
    }

    /**
     * Logs a message with the 'error' level (level=1)
     */
    error(messageOrCallback: MessageOrCallback, options: LoggingOptions = {}): ProfilerCallback {
        return this._log('error', messageOrCallback, options);
    }

    /**
     * Logs a message with the 'error' level (level=1) - async version
     * @param messageOrCallback
     */
    errorAsync(messageOrCallback: MessageOrCallbackAsync, options: LoggingOptions = {}): Promise<ProfilerCallback> {
        return this._logAsync('error', messageOrCallback, options);
    }

    private getLogFunctionCallback(logLevel: LogLevel, options: LoggingOptions): ProfilerCallback {
        const profilingStartDate = getDate().getTime();
        const eventId = Logger._nextEventId;
        return {
            success: (message?: string) => {
                const duration = getDate().getTime() - profilingStartDate;
                this.timer.invocationCount += 1;
                this.timer.totalDuration += duration;
                this.timer.sumOfSquares += duration * duration;
                let colorizedDuration = `(${duration} ms)`;
                if (options.lowThreshold != null) {
                    if (options.highThreshold == null) {
                        // only one threshold, consider it as a maximum duration
                        if (duration > options.lowThreshold) colorizedDuration = Colors.red(colorizedDuration);
                    } else if (duration > options.highThreshold) colorizedDuration = Colors.red(colorizedDuration);
                    // 2 thresholds : consider them as a range [lowThreshold..highThreshold]
                    else if (duration > options.lowThreshold) colorizedDuration = Colors.yellow(colorizedDuration);
                } else colorizedDuration = Colors.default(colorizedDuration);
                if (this.logLevel === 'debug') {
                    const msg = `Event #${eventId}${message ? ` : ${message}` : ' is over'}`;
                    this._internalLog({ logLevel, messageOrError: `${msg} ${colorizedDuration}`, eventId });
                }
            },
            fail: (message?: string) => {
                const duration = getDate().getTime() - profilingStartDate;
                const msg = `Event #${eventId}${message ? ` : ${message}` : ' failed'}`;
                this._internalLog({ logLevel: 'error', messageOrError: `${msg} (${duration} ms)`, eventId });
            },
        };
    }

    isActive(logLevel: LogLevel): boolean {
        return logLevelsOrders[this._logLevel] >= logLevelsOrders[logLevel];
    }

    private static _messageOrErrorToString(messageOrError: string | Error): string {
        if (messageOrError instanceof Error) {
            if (messageOrError.stack) return messageOrError.stack;
            return `${messageOrError.name}: ${messageOrError.message}`;
        }
        return messageOrError;
    }

    private _internalLog(options: {
        logLevel: LogLevel;
        messageOrError: string | Error;
        eventId?: number;
        /**
         * The callback to determine if a message should be ignored
         */
        ignoreCallback?: LoggerIgnoreCallback;
    }) {
        function padRight(str: string, pad: number): string {
            return str.padEnd(pad).substring(0, pad);
        }
        function padLeft(str: string, pad: number): string {
            return str.padStart(pad, '0').substring(0, pad);
        }

        const messageToLog = Logger._messageOrErrorToString(options.messageOrError);

        if (options.ignoreCallback && options.ignoreCallback(messageToLog)) return;

        // Note 1 : if we use timestamps of Winston tranports, the timestamp in the console may differ (some ms) from
        // the timestamp written in the log file. That's why we declare a common timestamp that will be used by all
        // the transports.
        // Note 2 : all the items that will be used by the formatter must be padded so that logs are well aligned.
        // eslint-disable-next-line no-plusplus
        const evtId = options.eventId || ++Logger._nextEventId;

        const entryMetadata = {
            paddedEventId: padLeft(`${evtId}`, 6),
            paddedFullDomain: padRight(this.fullDomain.replace(/^sage\//, ''), 30),
            paddedLogLevel: padRight(options.logLevel.toString().toUpperCase(), 7),
            paddedDate: getDate().toISOString().substring(11, 23), // No need to log the date, the log files are prefixed with date
        };

        Logger.globalLogger.log(options.logLevel.toString(), messageToLog, entryMetadata);
    }

    get statistics() {
        const average = (x: number) => (this.timer.invocationCount === 0 ? Number.NaN : x / this.timer.invocationCount);
        const mean = average(this.timer.totalDuration);
        const meanOfSquares = average(this.timer.sumOfSquares);
        return {
            count: this.timer.invocationCount,
            totalDuration: this.timer.totalDuration,
            meanDuration: mean,
            standardDeviation: Math.sqrt(meanOfSquares - mean * mean),
        };
    }

    /**
     * result = do(fn, onError);
     *
     * Executes `fn()` silently.
     * But if `fn()` fails, logs the stacktrace and rethrows.
     *
     * `onError(err)` is an optional callback which will be invoked if `fn()` fails.
     * By default, `onError` rethrows the error, but you can rethrow a different error, typically
     * to mask low level details, or add some context to the error.
     * You can also use `onError` to stop the error propagation and return a special value.
     */
    do<T>(fn: () => T, onError: LoggerErrorHandler<T> = rethrow) {
        try {
            return fn();
        } catch (err) {
            this.error(err.stack);
            return onError(err);
        }
    }

    /**
     * Async variant of do(fn, onError)
     */
    async doAsync<T extends AnyValue | void>(
        fn: () => AsyncResponse<T>,
        options: {
            onError?: LoggerErrorHandler<T>;
            /**
             * The callback to determine if a message should be ignored
             */
            ignoreCallback?: LoggerIgnoreCallback;
        } = {},
    ): Promise<T> {
        try {
            return await fn();
        } catch (err) {
            this.error(err.stack, { ignoreCallback: options.ignoreCallback });
            if (options.onError) return options.onError(err);
            throw err;
        }
    }

    static setAppName(app: string | undefined) {
        this._app = app;
    }

    /**
     * Logger.disable()
     *
     * Disables all logs.
     * This method is called before running unit tests when logs/disabledForTests is true.
     */
    static disable() {
        Logger.isDisabled = true;
    }

    static isLogAsJson(): boolean {
        return !!Logger._logAsJson;
    }

    static noBanner() {
        Logger._startBannerDisplayed = true; // do not display the start banner
    }
}
