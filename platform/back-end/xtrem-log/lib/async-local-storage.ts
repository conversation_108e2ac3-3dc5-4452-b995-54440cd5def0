// These definitions come from xtrem-async-helper
// I copied them here because xtrem-log is shared with front-end packages and

import { AsyncResponse } from '@sage/xtrem-shared';
import { AsyncLocalStorage } from 'async_hooks';

// we don't want xtrem-async-helper in webpack.
export type AnyValue = boolean | string | number | object | null | undefined | AnyValue[] | void;

const asyncLocalStorage = new AsyncLocalStorage();

export function withClsContext<T extends AnyValue | void>(fn: () => AsyncResponse<T>, context: {}): Promise<T> {
    return asyncLocalStorage.run({ context }, fn) as Promise<T>;
}

export function clsContext<T = any>(): T {
    return asyncLocalStorage.getStore() as T;
}
