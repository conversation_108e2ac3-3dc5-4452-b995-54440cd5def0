/* eslint-disable no-console */
import * as tripleBeamSymbols from 'triple-beam';
import * as TransportStream from 'winston-transport';
import { Colors } from './colors';
import { LogLevel } from './logger';

/** @internal */
export function colorizeText(level: LogLevel, text: string) {
    /* istanbul ignore next */
    if (!process.stdout.isTTY) {
        return text;
    }

    switch (level) {
        case 'error':
            return Colors.red(text);
        case 'warn':
            return Colors.yellow(text);
        case 'verbose':
        case 'debug':
            return Colors.lightBlue(text);
        default:
            return text;
    }
}

/** @internal */
export class ConsoleTransport extends TransportStream {
    constructor(options = {}) {
        super(options);
    }

    override log(info: any, callback: any) {
        global.setImmediate(() => this.emit('logged', info));
        if (info[tripleBeamSymbols.LEVEL] === 'error') console.error(info[tripleBeamSymbols.MESSAGE]);
        else console.log(info[tripleBeamSymbols.MESSAGE]);
        if (callback) callback();
    }
}
