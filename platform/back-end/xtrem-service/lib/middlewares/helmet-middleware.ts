import { ConfigManager, Dict } from '@sage/xtrem-core';
import { Express, NextFunction, Request } from 'express';
import helmet from 'helmet';
import { ServerResponse } from 'http';
import * as _ from 'lodash';
import { loggers } from '../loggers';

const logger = loggers.service;

const defaultPendoSubscriptionId = '5069687185997824';

// Pendo requires the US/Worldwide CSP directives listed in:
// https://support.pendo.io/hc/en-us/articles/************-Content-Security-Policy-CSP-#guide-delivery-settings-0-0
const wellKnownUrl = {
    pendo: 'https://cdn.pendo.io',
    pendoStaticStorage: 'https://pendo-io-static.storage.googleapis.com',
    pendoSubIdStaticStorage: 'https://pendo-static-{{SUB_ID}}.storage.googleapis.com',
    pendoApp: 'https://app.pendo.io',
    pendoData: 'https://data.pendo.io',
    sageIdStaging: 'https://id-shadow.sage.com',
    sageIdProd: 'https://id.sage.com',
    sageFontCdn: 'https://fonts.sage.com',
    newRelic: 'https://bam.nr-data.net',
    newRelicCdn: 'https://js-agent.newrelic.com',
    youTube: 'https://www.youtube.com',
};

export interface RequestWithNonce extends Request {
    nonce?: string;
}

export function configureHelmetApp(expressApp: Express): void {
    const config = ConfigManager.current;
    const documentationUrlOrigin = config.documentationServiceUrl ? new URL(config.documentationServiceUrl).origin : '';
    const s3BucketUrlPrefix = config.s3Storage?.s3BucketUrlPrefix;
    const securityConfig = config.security;
    const redirectUrl = securityConfig?.redirectUrl ? new URL(securityConfig.redirectUrl) : null;
    const loginUrlOrigin = securityConfig?.loginUrl ? new URL(securityConfig?.loginUrl).origin : '';
    const renewalUrlOrigin = securityConfig?.renewalUrl ? new URL(securityConfig?.renewalUrl).origin : '';
    const allowedUrls: string[] = securityConfig
        ? _.uniq(
              [
                  loginUrlOrigin,
                  loginUrlOrigin.replace('http://', 'https://'),
                  renewalUrlOrigin,
                  renewalUrlOrigin.replace('http://', 'https://'),
              ].filter(url => !!url),
          )
        : [];

    if (config.copilot?.serviceUrl) {
        allowedUrls.push(new URL(config.copilot.serviceUrl).origin);
    }

    if (config.copilot?.oauthEndpointUrl) {
        allowedUrls.push(new URL(config.copilot.oauthEndpointUrl).origin);
    }

    if (config.deploymentMode === 'development') allowedUrls.push(`ws://websocket.localhost:8240`);

    if (redirectUrl) {
        allowedUrls.push(`wss://websocket.${redirectUrl.hostname.split('.').slice(1).join('.')}`);
        if (config.deploymentMode === 'development')
            allowedUrls.push(`ws://websocket.${redirectUrl.hostname.split('.').slice(1).join('.')}:8240`);
    }

    if (s3BucketUrlPrefix) {
        allowedUrls.push(s3BucketUrlPrefix);
    }

    // Helmet CSP and HSTS rules can be disabled and/or configured by editing the xtrem-security.yml file
    const helmetConfig = securityConfig?.services?.helmet;
    // Default is now to have csp enabled
    const enableCsp = !helmetConfig?.disabledOptions?.csp;
    if (!enableCsp) {
        logger.warn('Helmet: CSP (Content Security Policy) is disabled');
    }
    const strictTransportSecurity =
        redirectUrl?.protocol === 'https:' && !helmetConfig?.disabledOptions?.hsts ? helmetConfig?.hsts || true : false;
    if (!strictTransportSecurity) {
        logger.warn('Helmet: HSTS (HTTP Strict Transport Security) is disabled');
    }
    const defaultDirectives = helmet.contentSecurityPolicy.getDefaultDirectives();

    const pendoSubIdStaticStorage = wellKnownUrl.pendoSubIdStaticStorage.replace(
        '{{SUB_ID}}',
        config.pendo?.subscriptionId || defaultPendoSubscriptionId,
    );

    const commonWhitelistedOrigins = [
        "'self'",
        'data:',
        wellKnownUrl.pendo,
        wellKnownUrl.pendoStaticStorage,
        pendoSubIdStaticStorage,
        wellKnownUrl.pendoApp,
    ];
    const baseDirectives = {
        // 'scriptSrcAttr' is not supported by all browsers so we only add 'scriptSrc'
        // Pendo requirement: 'unsafe-inline' 'unsafe-eval' app.pendo.io pendo-io-static.storage.googleapis.com cdn.pendo.io
        //   pendo-static-SUB_ID.storage.googleapis.com data.pendo.io;
        scriptSrc: [
            "'unsafe-eval'",
            "'unsafe-inline'", // required for Pendo
            ...commonWhitelistedOrigins,
            pendoSubIdStaticStorage,
            wellKnownUrl.pendoData,
            wellKnownUrl.sageIdStaging,
            wellKnownUrl.sageIdProd,
            wellKnownUrl.newRelic,
            wellKnownUrl.newRelicCdn,
        ],
        // Pendo requirement: cdn.pendo.io app.pendo.io pendo-static-SUB_ID.storage.googleapis.com data.pendo.io youtube.com;
        imgSrc: [
            ...commonWhitelistedOrigins,
            pendoSubIdStaticStorage,
            wellKnownUrl.pendoData,
            wellKnownUrl.youTube,
            'blob:',
        ],
        workerSrc: ['blob:', 'data:', "'self'"],
        // style-src 'unsafe-inline' app.pendo.io cdn.pendo.io pendo-static-SUB_ID.storage.googleapis.com;
        styleSrc: [
            "'unsafe-inline'", // required for Pendo,
            ...commonWhitelistedOrigins,
            wellKnownUrl.sageFontCdn,
        ],
        objectSrc: ["'self'", 'blob:'],
        // Pendo requirement: app.pendo.io, youtube.com;
        frameSrc: [...commonWhitelistedOrigins, loginUrlOrigin, documentationUrlOrigin, wellKnownUrl.youTube, 'blob:'],
        // fontSrc: ['https://fonts.sage.com'],

        // Pendo requirement: app.pendo.io data.pendo.io pendo-static-SUB_ID.storage.googleapis.com;
        connectSrc: [
            "'self'",
            'blob:',
            wellKnownUrl.pendoApp,
            wellKnownUrl.pendoData,
            pendoSubIdStaticStorage,
            wellKnownUrl.newRelic,
            ...allowedUrls,
        ],
        // Pendo doc says it requires: frame-ancestors app.pendo.io;
        // frame-ancestors is like the x-frame-options. so far, we do not want to allow pendo to include xtrem in it
        // frameAncestors: ["'self'", wellKnownUrl.pendoApp],

        // Pendo requirement: child-src app.pendo.io;
        childSrc: ["'self'", wellKnownUrl.pendoApp],

        upgradeInsecureRequests: strictTransportSecurity ? defaultDirectives['upgrade-insecure-requests'] : null,
    } as Dict<null | string[]>;

    const cspDirectives = helmetConfig?.csp?.directives || {};
    Object.entries(cspDirectives).forEach(([k, v]) => {
        if (v == null) return;
        baseDirectives[k] = [...(baseDirectives[k] || []), ...v];
    });

    const directives = _.cloneDeep(baseDirectives);
    const cspMiddleware = (req: RequestWithNonce, res: ServerResponse, next: NextFunction): void => {
        helmet.contentSecurityPolicy({
            useDefaults: true,
            directives,
        })(req, res, next);
    };

    expressApp.use(helmet({ strictTransportSecurity, contentSecurityPolicy: enableCsp, hidePoweredBy: true }));

    if (enableCsp) expressApp.use(cspMiddleware);
}
