import { Application, fileExists, Logger } from '@sage/xtrem-core';
import * as express from 'express';
import { Request, Response } from 'express';
import * as path from 'path';

const logger = Logger.getLogger(__filename, 'xtrem-ui-plugin-service');

/** The plugin name is sent as a URL parameter in base64 encoded format in order not to mess with url encoding */
const getPackageName = (req: Request) => {
    const buff = Buffer.from(req.params.pluginName, 'base64');
    const packageName = buff.toString('ascii');
    if (packageName.startsWith('/') || packageName.includes('.')) {
        throw new Error(`Invalid package name: ${packageName}`);
    }

    return packageName;
};

const findPluginPath = (application: Application, pluginName: string) => {
    const applicationDependencies = application.getPackages();
    // eslint-disable-next-line no-restricted-syntax
    for (const applicationDependency of applicationDependencies) {
        try {
            return path.dirname(require.resolve(`${pluginName}/package.json`, { paths: [applicationDependency.dir] }));
        } catch {
            // Intentionally left empty.
        }
    }

    throw new Error(`Plugin package is not found: ${pluginName}`);
};

/**
 * This middleware serves resources for UI plugins. A package may consist of a number of resources, including various
 * images, fonts etc, therefore for plugin packages we host the entire build folder.
 * @param application
 */
export const xtremUiPluginsMiddleware = (application: Application): express.Express => {
    const app = express();

    // Resolve the "main" file of the requested plugin
    app.get('/:pluginName', (req: Request, res: Response) => {
        try {
            if (req.params.pluginName) {
                const pluginName = getPackageName(req);
                const pluginFolder = findPluginPath(application, pluginName);
                // eslint-disable-next-line global-require, import/no-dynamic-require
                const addOnPackageContent = require(`${pluginFolder}/package.json`);

                // Ensure that the request belongs to an xtremPlugin package and prevent harvesting of other packages.
                if (addOnPackageContent.xtremPlugin) {
                    res.redirect(`/plugins/${req.params.pluginName}/${addOnPackageContent.main}`);
                    return;
                }
            }
            res.status(404).send();
        } catch (e) {
            logger.warn(`Failed to resolve plugin entry file. ${req.path} (FYI: plugin name is base64 encoded).`);
            logger.warn(e);
            res.status(404).send();
        }
    });

    // Return the resources
    app.get('/:pluginName{/*path}', (req: Request, res: Response) => {
        try {
            // Ensure that the requested path does not contain .. path and for a build folder.
            if (!(req.params.pluginName && req.path.includes('build') && !req.path.includes('..'))) {
                res.status(404).send();
                return;
            }
            const pluginName = getPackageName(req);
            const pluginFolder = findPluginPath(application, pluginName);

            // eslint-disable-next-line global-require, import/no-dynamic-require
            const addOnPackageContent = require(`${pluginFolder}/package.json`);

            // Ensure that the request belongs to an xtremPlugin package and prevent harvesting of other packages.
            if (!addOnPackageContent.xtremPlugin) {
                res.status(404).send();
                return;
            }

            const fileRelativePath = req.path.replace(`/${req.params.pluginName}/`, '');
            const absoluteFilePath = path.resolve(pluginFolder, fileRelativePath);
            (async () => {
                if (await fileExists(absoluteFilePath)) {
                    res.setHeader('X-XTREM-PLUGIN-VERSION', addOnPackageContent.version);
                    res.sendFile(absoluteFilePath);
                } else {
                    res.status(404).send();
                }
            })().catch(err => {
                logger.error(err);
                res.status(500).send();
            });
        } catch (e) {
            logger.warn(`Failed to return requested plugin file: ${req.path} (FYI: plugin name is base64 encoded).`);
            logger.warn(e);
            res.status(404).send();
        }
    });

    return app;
};
