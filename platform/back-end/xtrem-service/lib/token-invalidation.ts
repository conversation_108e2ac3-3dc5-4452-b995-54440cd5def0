import { getInvalidatedTokenInstance } from '@sage/xtrem-access-token';
import { JwtClaims } from '@sage/xtrem-core';
import { Config } from '@sage/xtrem-shared';
import { loggers } from './loggers';

const logger = loggers.service;
const isLocalEnv = (): boolean => !process.env.XTREM_ENV || process.env.XTREM_ENV === 'local';

export class TokenInvalidationService {
    private static _isStarted = false;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static start(config: Config): void {
        if (this._isStarted) {
            return;
        }
        if (isLocalEnv()) {
            logger.warn(
                "Cannot start token invalidation service: 'XTREM_ENV' environment variable not set or set to 'local'",
            );
            return;
        }
        if (config.security?.services?.tokenInvalidation?.active === false) {
            logger.warn('Cannot start token invalidation service: Deactivated by config');
            return;
        }
        if (!config.security?.loginUrl) {
            logger.error(
                "Cannot start token invalidation service: Login service must be configured - Missing 'loginUrl'",
            );
            return;
        }
        if (!config.clusterId) {
            logger.error('Cannot start token invalidation service: Cluster Id is missing');
            return;
        }

        try {
            // start fetching token list
            getInvalidatedTokenInstance().startListening({
                logger,
                tableName: process.env.XTREM_INVALIDATED_TOKEN_TABLE_NAME || 'invalidated-token-table-name',
                frequencyInSec: 15,
                cluster: config.clusterId,
            });
            this._isStarted = true;
            logger.info('Token invalidation service started');
        } catch (e) {
            logger.error(`Cannot start token invalidation service: ${e.message}`);
        }
    }

    static stop(): void {
        if (!this._isStarted) {
            return;
        }
        getInvalidatedTokenInstance().stopListening();
        logger.info('Token invalidation service stopped');
    }

    static isInvalidatedToken(accessToken: JwtClaims): boolean {
        if (!this._isStarted) {
            // assume it has not been invalidated
            return false;
        }
        return !accessToken.jti || !getInvalidatedTokenInstance().isJTIValid(accessToken.jti);
    }
}
