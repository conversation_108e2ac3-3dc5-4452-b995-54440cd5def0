import { ConfigManager, Context, Test, getTestFixtures } from '@sage/xtrem-core';
import { DevelopmentConfig } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Express } from 'express';
import * as sinon from 'sinon';
import * as request from 'supertest';
import { Response as TestResponse } from 'supertest';
import { getApplication } from './fixtures/test-app';
import { startTestHttpServer } from './utils';

const fixtures = getTestFixtures();

describe('Test xtrem service config middleware', () => {
    let expressApp: Express | undefined;

    function executeRequest(url: string, expect: number): Promise<TestResponse> {
        if (!expressApp) throw new Error('expressApp not found');
        const req = request(expressApp).get(url);
        return new Promise<TestResponse>((resolve, reject) => {
            // eslint-disable-next-line no-void
            void req.expect(expect).end((err, response) => {
                if (err) reject(err instanceof Error ? err : new Error(String(err)));
                else resolve(response);
            });
        });
    }

    before(async () => {
        fixtures.updateContext();
        const application = await getApplication();
        expressApp = await startTestHttpServer(application);
    });

    after(async () => {
        const application = await getApplication();
        const graphqlHttpServer = application.graphqlHttpServer;
        if (graphqlHttpServer)
            await new Promise<void>((resolve, reject) => {
                graphqlHttpServer.close(err => {
                    if (err) reject(err);
                    else resolve();
                });
            });
    });

    it('can ping', () => executeRequest('/ping', 200));

    it('check graphql', async () => {
        const result = (await executeRequest('/explorer/', 200)).body;
        assert.isObject(result);
        assert.strictEqual(Object.keys(result).length, 0, 'empty object');
    });

    it('cannot submit a graphql request for a tenant that is not provisioned on the app', async () => {
        const config = ConfigManager.current as DevelopmentConfig;
        const oldTenantId = config.tenantId;
        const oldAuth = config.auth;
        const oldApp = config.app;
        config.tenantId = Test.defaultTenantId;
        config.auth = { login: '<EMAIL>', tenantId: Test.defaultTenantId, auth0: 'api|0000000000' };
        config.app = 'test';

        const stub = sinon.stub(Context.tenantManager, 'listTenantsIds').resolves(['9'.repeat(21)]);
        const result = (await executeRequest('/api/', 200)).body;
        assert.isObject(result);
        assert.deepEqual(result, {
            data: {},
            errors: [
                {
                    extensions: {
                        diagnoses: [
                            {
                                severity: 3,
                                message: 'test: Tenant does not exist',
                            },
                        ],
                    },
                },
            ],
        });
        stub.restore();
        config.tenantId = oldTenantId;
        config.app = oldApp;
        config.auth = oldAuth;
    });

    it('check static assets are served', async () => {
        const resp = await executeRequest('/runtime-bundle.js', 200);
        assert.strictEqual(resp.header['content-type'], 'text/javascript; charset=utf-8');
    });
});
