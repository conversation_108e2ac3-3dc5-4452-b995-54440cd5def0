import { ConfigManager, Dict, getTestFixtures, Test } from '@sage/xtrem-core';
import { SecurityConfig } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Express, NextFunction, Request, Response } from 'express';
import * as jwt from 'jsonwebtoken';
import { AddressInfo } from 'net';
import * as request from 'supertest';
import { Response as TestResponse } from 'supertest';
import { getApplication } from './fixtures/test-app';
import { startTestHttpServer } from './utils';

const fixtures = getTestFixtures();

const jwks = {
    keys: [
        {
            kty: 'RSA',
            use: 'sig',
            kid: 'sig-1626191928',
            alg: 'RS256',
            n: '0NLulFVo08ZdSc3283AVFNURtms51_xdBvem8831PWqwpijPTkc9QmZDRsXhTMw3trTKwfC1vi08uym7ZUUuuZNb9Yse_rpXGj9cfvc0Xtmvo1zRwBYw9Lq9IPZ2NOjhtIRU1RGgrelpXz7agSC-4awAw1z3wy1ISLPTz1KhSosPKwRYZEdi7xg8ZD-ubNvNgFrkPefDK6InrZg8ncqEHA4UsCDXH_p7OU12i666Gw8nWzF_JBwU8KHDrRaBLYAzGe9wg0356YVAbraB1X7rdP9iCmf3U73iFgQcLA6wRdfmVrTvyPMSc3lh0YEtxlKQ8Y5iTx2VDAULI2hRrLbKwtjwhcElW2YzcpMJssIEbZRrrqd4QddWf92WOSrg67WTTu5RK_-yQcTXrJFPlZD4akY04X5eH29-DRz362Bh-ukPwKuOlIsEB-Zjp6ynGiblB61k3X4UUADMnHUrXx8aRpcvD1hXPE5WRmOV8PLA_ny65Ep90DdNUe4-bWoIpo7lEeUIEFSHu9p3BV_C-0V93X3zViQJbACUy2Gmx1NMNLjpC-hU5wci9nrsMXUaprP861r4injpgdXJhWkhDm5973OcAElXW-lPZmPZtgy9kQmolfhkVmL3O2WTSjpwQLspv8kNMLPX32ME6xWAXLIeAih-myy1wn4XyDwA5LTj_SU',
            e: 'AQAB',
        },
    ],
};

const pk = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

const loginTenants = {
    tenants: ['xtrem2.localhost.dev-sagextrem.com', 'xtrem3.localhost.dev-sagextrem.com'],
};

type TestSecurityConfig = Pick<SecurityConfig, Exclude<keyof SecurityConfig, 'renewalThreshold' | 'renewalUrl'>>;

interface RequestOptions {
    authorized?: boolean;
    url?: string;
    method?: 'GET' | 'POST';
    headers?: Dict<any>;
    authOptions?: AuthHeaderOptions | null;
    config?: ConfigOptions;
}
interface AuthHeaderOptions {
    useBearer?: boolean;
    payload?: object;
    secret?: jwt.Secret;
    signOptions?: jwt.SignOptions;
    audience?: string;
    app?: string;
    apps?: string[];
}

interface ConfigOptions {
    security?: TestSecurityConfig;
    app?: string;
}

function makeHeaderWithAuth(initHeaders: Dict<any>, options?: AuthHeaderOptions): Dict<any> {
    const headers = { ...initHeaders };
    const claims = {
        iss: 'connect.localhost.dev-sagextrem.com',
        aud: options?.audience ?? 'sdmo-xtrem.localhost.dev-sagextrem.com',
        sub: '<EMAIL>',
        app: options?.app,
        apps: options?.apps,
        ...options?.payload,
    };
    const token = jwt.sign(
        claims,
        options?.secret || { key: pk, passphrase: 'xtrem' },
        options?.signOptions || { algorithm: 'RS256' },
    );
    if (options?.useBearer) {
        headers.authorization = `Bearer ${token}`;
    } else {
        const tokenParts = token.split('.');
        const tokenSign = tokenParts.pop();
        headers.cookie = [`access_token=${tokenParts.join('.')}`, `access_token_sign=${tokenSign}`];
    }
    return headers;
}

describe('Test xtrem service auth middleware', () => {
    let listeningPort = 0;
    let expressApp: Express | undefined;

    function executeRequest(
        url: string,
        expect: number,
        headers?: Dict<any>,
        method?: 'GET' | 'POST',
        data?: any,
    ): Promise<TestResponse> {
        if (!expressApp) throw new Error('expressApp not found');
        const req = method === 'POST' ? request(expressApp).post(url).send(data) : request(expressApp).get(url);
        Object.entries(headers ?? {}).forEach(([name, value]) => req.set(name, value));
        return new Promise<TestResponse>((resolve, reject) => {
            // eslint-disable-next-line no-void
            void req.expect(expect).end((err, response) => {
                if (err) reject(err instanceof Error ? err : new Error(String(err)));
                else resolve(response);
            });
        });
    }

    async function withConfig(options: ConfigOptions | null, body: () => Promise<void>): Promise<void> {
        const oldSecurity = ConfigManager.current.security;
        const oldApp = ConfigManager.current.app;
        const security = options ? (options.security ?? {}) : null;
        const app = options?.app;
        const config = ConfigManager.current;
        config.security =
            security == null
                ? undefined
                : {
                      renewalThreshold: 300,
                      renewalUrl: `http://localhost:${listeningPort}`,
                      jwksUrl: `http://localhost:${listeningPort}/.auth-unit-test/.well-known/jwks.json`,
                      issuer: 'connect.localhost.dev-sagextrem.com',
                      audience: 'sdmo-xtrem.localhost.dev-sagextrem.com',
                      loginUrl: `http://localhost:${listeningPort}/.auth-unit-test/login`,
                      ...security,
                  };
        config.app = app;
        try {
            await body();
        } finally {
            config.security = oldSecurity;
            config.app = oldApp;
        }
    }

    function testLoginRedirect(url: string, headers?: Dict<any>): Promise<void> {
        return withConfig({}, async () => {
            const resp = await executeRequest('/explorer/', 302, headers);
            assert.isObject(resp.body);
            assert.isObject(resp.headers);
            assert.strictEqual(resp.headers.location, ConfigManager.current.security?.loginUrl);
            assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
        });
    }

    function testUnauthorized(url: string, headers?: Dict<any>, method: 'GET' | 'POST' = 'GET'): Promise<void> {
        return checkRequest({
            authorized: false,
            url,
            headers,
            method,
            config: {},
        });
    }

    function checkRequest(requestOptions: RequestOptions): Promise<void> {
        const { authorized = true, url = '/api/', method = 'POST', authOptions, config = null } = requestOptions;

        const defaultHeaders: Dict<string> = { accept: 'application/json' };
        if (method === 'POST') defaultHeaders['content-type'] = 'application/json';
        const baseHeaders = requestOptions.headers ?? defaultHeaders;
        const headers = authOptions == null ? baseHeaders : makeHeaderWithAuth(baseHeaders, authOptions);
        return withConfig(config, async () => {
            const resp = await executeRequest(url, authorized ? 200 : 401, headers, method, {
                query: '{__schema {types { name }}}',
            });
            if (authorized) {
                assert.isObject(resp.body);
                assert.isNotNull(resp.body.data);
                assert.isNotNull(resp.body.extensions);
            } else {
                assert.deepEqual(resp.body, {
                    $diagnoses: [
                        {
                            $severity: 'error',
                            $message: `unauthorized: ${method} ${url}`,
                            $statusCode: 401,
                        },
                    ],
                });
            }
        });
    }

    before(async () => {
        ConfigManager.load(__dirname);
        // overrides the default config to have the expected values for the tests
        Object.assign(ConfigManager.current, {
            deploymentMode: 'development',
            auth: {},
            apps: {},
            prodUi: false,
            packages: {},
            app: undefined,
        });
        fixtures.updateContext();
        const application = await getApplication();
        expressApp = await startTestHttpServer(application, {
            beforeAuth: {
                jwks: (req: Request, res: Response, next: NextFunction) => {
                    if (req.path === '/.auth-unit-test/.well-known/jwks.json') {
                        res.send(jwks);
                    } else if (req.path === '/.auth-unit-test/login') {
                        res.send(loginTenants);
                    } else {
                        next();
                    }
                },
            },
        });
        listeningPort = (application.graphqlHttpServer?.address() as AddressInfo)?.port;
    });

    after(async () => {
        const application = await getApplication();
        const graphqlHttpServer = application.graphqlHttpServer;
        if (graphqlHttpServer)
            await new Promise<void>((resolve, reject) => {
                graphqlHttpServer.close(err => {
                    if (err) reject(err);
                    else resolve();
                });
            });
    });

    describe('Without security config', () => {
        it('no security - can ping', () =>
            withConfig(null, async () => {
                await executeRequest('/ping', 200);
            }));

        it('no security - can get jwks', () =>
            withConfig(null, async () => {
                const resp = await executeRequest('/.auth-unit-test/.well-known/jwks.json', 200);
                assert.deepEqual(resp.body, jwks);
            }));

        it('no security - can get login', () =>
            withConfig(null, async () => {
                const resp = await executeRequest('/.auth-unit-test/login', 200);
                assert.deepEqual(resp.body, loginTenants);
            }));

        it('no security - can request explorer', () =>
            withConfig(null, async () => {
                const resp = await executeRequest('/explorer/', 200);
                assert.isObject(resp.body);
                assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
            }));
    });

    describe('With security config single app', () => {
        it('security - can ping unauthenticated', () =>
            withConfig({}, async () => {
                await executeRequest('/ping', 200);
            }));

        it('security - can get jwks.json unauthenticated', () =>
            withConfig({}, async () => {
                const resp = await executeRequest('/.auth-unit-test/.well-known/jwks.json', 200);
                assert.deepEqual(resp.body, jwks);
            }));

        it('security - can request login route unauthenticated', () =>
            withConfig({}, async () => {
                const resp = await executeRequest('/.auth-unit-test/login', 200);
                assert.deepEqual(resp.body, loginTenants);
            }));

        it('security - unauthenticated request should be redirected to login url', async () => {
            await testLoginRedirect('/');
            await testLoginRedirect('/', { accept: '*/*' });
            await testLoginRedirect('/explorer/');
            await testLoginRedirect('/explorer/', { accept: '*/*' });
        });

        it('security - unauthenticated request on /api should return 401', async () => {
            await testUnauthorized('/api/', { accept: 'application/json' }, 'POST');
        });

        it('security - request with expired jwt should be redirected to login url', async () => {
            // create a token with an expired date
            const iat = Date.now() / 1000 - 600;
            const headers = makeHeaderWithAuth({}, { payload: { iat, exp: iat + 300 } });

            await testLoginRedirect('/', headers);
            await testLoginRedirect('/', { ...headers, accept: '*/*' });
            await testLoginRedirect('/explorer/', headers);
            await testLoginRedirect('/explorer/', { ...headers, accept: '*/*' });
        });

        it('security - request with expired jwt on /api should return 401', async () => {
            // create a token with an expired date
            const iat = Date.now() / 1000 - 600;
            const headers = makeHeaderWithAuth({ accept: 'application/json' }, { payload: { iat, exp: iat + 300 } });

            await testUnauthorized('/api/', headers, 'POST');
            await testUnauthorized('/metadata/', headers, 'POST');
        });

        it('security - can request with valid auth', async () => {
            const headers = makeHeaderWithAuth({});

            await withConfig({}, async () => {
                const resp = await executeRequest('/explorer/', 200, headers);
                assert.isObject(resp.body);
                assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
            });
        });

        it('security - cannot request with invalid auth', async () => {
            const headers = makeHeaderWithAuth({}, { secret: 'bad-secret', signOptions: {} });

            await testUnauthorized('/', headers);
            await testUnauthorized('/', { ...headers, accept: '*/*' });
            await testUnauthorized('/explorer/', headers);
            await testUnauthorized('/explorer/', { ...headers, accept: '*/*' });
        });

        it('security - bad audience should be redirected to login url', async () => {
            const headers = makeHeaderWithAuth({}, { payload: { aud: 'xtrem2.localhost.dev-sagextrem.com' } });

            await testLoginRedirect('/', headers);
            await testLoginRedirect('/', { ...headers, accept: '*/*' });
            await testLoginRedirect('/explorer/', headers);
            await testLoginRedirect('/explorer/', { ...headers, accept: '*/*' });
        });

        it('security - bad audience on /api should return 401', async () => {
            const headers = makeHeaderWithAuth(
                { accept: 'application/json' },
                { payload: { aud: 'xtrem2.localhost.dev-sagextrem.com' } },
            );

            await testUnauthorized('/api/', headers, 'POST');
        });

        it('security - bad issuer should be unauthorized', async () => {
            const headers = makeHeaderWithAuth({}, { payload: { iss: 'login.invalid.com' } });

            await testUnauthorized('/', headers);
            await testUnauthorized('/', { ...headers, accept: '*/*' });
            await testUnauthorized('/explorer/', headers);
            await testUnauthorized('/explorer/', { ...headers, accept: '*/*' });
            await testUnauthorized('/api/', headers, 'POST');
        });
        it('security - valid jwt coming from login service without pref should redirect to login', async () => {
            const headers = makeHeaderWithAuth({}, { payload: { auth0: 'auth0|05788cb' } });

            await testLoginRedirect('/', headers);
            await testLoginRedirect('/', { ...headers, accept: '*/*' });
            await testLoginRedirect('/explorer/', headers);
            await testLoginRedirect('/explorer/', { ...headers, accept: '*/*' });
        });
        it('security - valid jwt coming from login service with pref should return 200', async () => {
            const headers = makeHeaderWithAuth({}, { payload: { auth0: 'auth0|05788cb', pref: 7 } });

            await withConfig({}, async () => {
                const resp = await executeRequest('/explorer/', 200, headers);
                assert.isObject(resp.body);
                assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
            });
        });
        it('security - valid jwt coming from api gateway service with pref should return 401', async () => {
            const headers = makeHeaderWithAuth({}, { payload: { auth0: 'api|05788cb', pref: 7 } });

            await testUnauthorized('/', headers);
            await testUnauthorized('/explorer/', headers);
            await testUnauthorized('/api/', headers, 'POST');
        });
        it('security - Bearer auth should return 200', async () => {
            const headers = makeHeaderWithAuth({}, { useBearer: true, payload: { auth0: 'api|05788cb' } });

            await withConfig({}, async () => {
                const resp = await executeRequest('/explorer/', 200, headers);
                assert.isObject(resp.body);
                assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
            });
        });
        it('security - Bearer auth with bad auth0 should return 401', async () => {
            const headers = makeHeaderWithAuth({}, { useBearer: true, payload: { auth0: 'auth0|05788cb' } });

            await testUnauthorized('/', headers);
            await testUnauthorized('/explorer/', headers);
            await testUnauthorized('/api/', headers, 'POST');
        });
    });

    describe('With security config Bizapps', () => {
        it('bizapps security - can ping unauthenticated', () =>
            withConfig({ security: { audience: '*-xtrem.localhost.dev-sagextrem.com' } }, async () => {
                await executeRequest('/ping', 200);
            }));

        it('bizapps security - can request with valid auth', async () => {
            // audience used both in the jwt token and security config to allow bizapps authentication
            const audience = '*-xtrem.localhost.dev-sagextrem.com';
            // simulate a request from showcase-sales app to showcase-stocks app
            await checkRequest({
                authorized: true,
                authOptions: {
                    audience,
                    app: 'showcase-sales',
                    apps: ['showcase-sales', 'showcase-stocks'],
                },
                config: { security: { audience }, app: 'showcase-stocks' },
            });
        });

        it('bizapps security - unauthenticated request on /api should return 401', async () => {
            const audience = '*-xtrem.localhost.dev-sagextrem.com';
            await checkRequest({
                authorized: false,
                config: { security: { audience }, app: 'showcase-stocks' },
            });
        });

        it('bizapps security - missing app should return 401', async () => {
            const audience = '*-xtrem.localhost.dev-sagextrem.com';
            await checkRequest({
                authorized: false,
                authOptions: {
                    audience,
                    apps: ['showcase-sales', 'showcase-stocks'],
                },
                config: { security: { audience }, app: 'showcase-stocks' },
            });
        });

        it('bizapps security - missing apps should return 401', async () => {
            const audience = '*-xtrem.localhost.dev-sagextrem.com';
            await checkRequest({
                authorized: false,
                authOptions: {
                    audience,
                    app: 'showcase-sales',
                },
                config: { security: { audience }, app: 'showcase-stocks' },
            });
        });

        it('bizapps security - bad app should return 401', async () => {
            const audience = '*-xtrem.localhost.dev-sagextrem.com';
            await checkRequest({
                authorized: false,
                authOptions: {
                    audience,
                    app: 'showcase-sales',
                    apps: ['showcase-sales', 'showcase-foo'],
                },
                config: { security: { audience }, app: 'showcase-stocks' },
            });
        });
    });

    describe('Dev routes', () => {
        it('dev login - unsecure login should return 302', async () => {
            const oldUnsecureDevLogin = process.env.UNSECURE_DEV_LOGIN;
            process.env.UNSECURE_DEV_LOGIN = '1';
            const url = new URL('http://foo');
            url.pathname = '/unsecuredevlogin';
            url.searchParams.append('tenant', Test.defaultTenantId);
            url.searchParams.append('user', '<EMAIL>');
            const urlPath = [url.pathname, url.search].join('');
            await executeRequest(urlPath, 302);
            process.env.UNSECURE_DEV_LOGIN = oldUnsecureDevLogin;
        });
    });
});
