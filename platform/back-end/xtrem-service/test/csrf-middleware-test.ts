import { Config<PERSON>anager, Dict, getTestFixtures, Test } from '@sage/xtrem-core';
import { DevelopmentConfig } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Express, Request, Response } from 'express';
import * as request from 'supertest';
import { Response as TestResponse } from 'supertest';
import { csrfMiddleware } from '../lib/middlewares/csrf-middleware';
import { getApplication } from './fixtures/test-app';
import { startTestHttpServer } from './utils';

const fixtures = getTestFixtures();

describe('Test xtrem service csrf middleware', () => {
    let expressApp: Express | undefined;
    function httpGet(url: string, expect: number): Promise<TestResponse> {
        if (!expressApp) throw new Error('expressApp not found');
        const req = request(expressApp).get(url);
        return new Promise<TestResponse>((resolve, reject) => {
            // eslint-disable-next-line no-void
            void req.expect(expect).end((err, response) => {
                if (err) reject(err instanceof Error ? err : new Error(String(err)));
                else resolve(response);
            });
        });
    }

    function httpPost(
        url: string,
        data: object | string,
        expect: number,
        headers?: Dict<string>,
    ): Promise<TestResponse> {
        if (!expressApp) throw new Error('expressApp not found');
        const req = request(expressApp)
            .post(url)
            .set(headers || {})
            .send(data);
        return new Promise<TestResponse>((resolve, reject) => {
            // eslint-disable-next-line no-void
            void req.expect(expect).end((err, response) => {
                if (err) reject(err instanceof Error ? err : new Error(String(err)));
                else resolve(response);
            });
        });
    }

    function checkBodyWithDiagnoses(body: any, statusCode: number) {
        assert.isObject(body);
        assert.isArray(body.$diagnoses);
        assert.isObject(body.$diagnoses[0]);
        assert.strictEqual(body.$diagnoses[0].$statusCode, statusCode);
    }

    before(async () => {
        fixtures.updateContext();
        const application = await getApplication();
        expressApp = await startTestHttpServer(application, {
            afterAuth: {
                csrfMiddleware,
                explorer: (req: Request, res: Response) => {
                    res.send({});
                },
                api: (req: Request, res: Response) => {
                    res.send({});
                },
            },
        });
    });

    after(async () => {
        const application = await getApplication();
        const graphqlHttpServer = application.graphqlHttpServer;
        if (graphqlHttpServer)
            await new Promise<void>((resolve, reject) => {
                graphqlHttpServer.close(err => {
                    if (err) reject(err);
                    else resolve();
                });
            });
    });

    it('can run GET request on /explorer', async () => {
        const resp = await httpGet('/explorer/', 200);
        assert.isObject(resp.body);
        assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
    });

    it('cannot run GET request on /explorer with query string', async () => {
        const resp = await httpGet('/explorer/?query=mutation%20%7B%7D', 400);
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('can GET request on /api', async () => {
        const resp = await httpGet('/api', 200);
        assert.isObject(resp.body);
        assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
    });

    it('cannot GET request on /api with query string', async () => {
        const resp = await httpGet('/api?query=mutation%20%7B%7D', 400);
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('cannot POST request on /api with query string', async () => {
        const resp = await httpPost('/api?query=mutation%20%7B%7D', {}, 400);
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('can POST request on /api with json body payload', async () => {
        const resp = await httpPost('/api', { query: {} }, 200);
        assert.isObject(resp.body);
        assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
    });

    it('can POST request through proxy using x-forwarded-host', async () => {
        const resp = await httpPost('/api', { query: {} }, 200, {
            origin: 'http://127.0.0.1',
            'x-forwarded-host': '127.0.0.1',
        });
        assert.isObject(resp.body);
        assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
    });

    it('can POST request from customer app using api token', async () => {
        const config = ConfigManager.current as DevelopmentConfig;
        const oldAuth = config.auth;
        config.auth = { login: '<EMAIL>', tenantId: Test.defaultTenantId, auth0: 'api|0000000000' };

        const resp = await httpPost('/api', { query: {} }, 200, {
            origin: 'http://customer.com',
        });
        assert.isObject(resp.body);
        assert.strictEqual(Object.keys(resp.body).length, 0, 'empty object');
        config.auth = oldAuth;
    });

    it('cannot POST request from customer app using a non api token', async () => {
        const config = ConfigManager.current as DevelopmentConfig;
        const oldAuth = config.auth;
        config.auth = {
            login: Test.defaultEmail,
            tenantId: Test.defaultTenantId,
            auth0: 'auth0|7bda53083aef4b4780ecbb73a318a966',
        };

        const resp = await httpPost('/api', { query: {} }, 400, {
            origin: 'http://customer.com',
        });
        checkBodyWithDiagnoses(resp.body, 400);
        config.auth = oldAuth;
    });

    it('cannot POST request through proxy using mismatching x-forwarded-host', async () => {
        const resp = await httpPost('/api', { query: {} }, 400, {
            origin: 'http://127.0.0.1',
            'x-forwarded-host': 'foo.com',
        });
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it("cannot POST request on /api with non 'text/plain' content-type", async () => {
        const resp = await httpPost('/api', JSON.stringify({ query: {} }), 400, { 'content-type': 'text/plain' });
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('cannot POST request on /api with a different origin', async () => {
        const resp = await httpPost('/api', { query: {} }, 400, { origin: 'http://hacking.com' });
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it("cannot POST request on /api with 'null' origin and empty referrer", async () => {
        const resp = await httpPost('/api', { query: {} }, 400, { origin: 'null' });
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('cannot POST request on /api with a different referrer', async () => {
        const resp = await httpPost('/api', { query: {} }, 400, { referer: 'http://hacking.com' });
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('cannot POST request on /api with flash request', async () => {
        const resp = await httpPost('/api', { query: {} }, 400, { 'x-requested-with': 'ShockwaveFlash/16.0.0.305' });
        checkBodyWithDiagnoses(resp.body, 400);
    });

    it('cannot POST request on with a payload that is too large', async () => {
        // 413 - PayloadTooLargeError: request entity too large
        const resp = await httpPost('/api?query=mutation%20%7B%7D', { foo: `${'é'.repeat(104857601)}` }, 413);
        assert.isObject(resp.body);
        assert.equal((resp.error as Error).message, 'cannot POST /api?query=mutation%20%7B%7D (413)');
    });
});
