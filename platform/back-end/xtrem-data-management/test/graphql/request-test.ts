import { Application, asyncArray, graphQlApp, initTables, QueryPage, Test, Uuid } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as express from 'express';
import { fixtures } from '../fixtures';

import request = require('supertest');

const { datatypesData, graphqlPageNodes, setup, createApplicationWithApi } = fixtures;
const testApp = express();

describe('Test graphql middleware', () => {
    let application: Application;
    before(async () => {
        application = await createApplicationWithApi(
            {
                nodes: {
                    TestSysVendor: fixtures.importedNodes.TestSysVendor,
                    TestUser: fixtures.importedNodes.TestUser,
                    TestDatatypes: fixtures.importedNodes.TestDatatypes,
                    // hack: there is a persistency of the application among the tests of a package
                    // so for now we need to add all the factories used by all tests. (TO REVIEW LATER)
                    TestReferred: fixtures.importedNodes.TestReferred,
                    TestReferring: fixtures.importedNodes.TestReferring,
                },
            },
            'xtrem_data_management_test',
        );
        await setup({ application });
        await initTables([{ nodeConstructor: fixtures.importedNodes.TestDatatypes, data: datatypesData }]);

        await Test.withContext(context => Test.initializeManagers(context));

        testApp.use(Test.fixRequestMiddleWare);
        testApp.use(graphQlApp(application));
    });
    after(() => Test.cleanUp(application));

    it('can execute a basic query', async () => {
        const result = (
            await new Promise<{
                body: {
                    data: {
                        xtremCore: {
                            testDatatypes: {
                                query: QueryPage<InstanceType<typeof fixtures.importedNodes.TestDatatypes>>;
                            };
                        };
                    };
                };
            }>((resolve, reject) => {
                // eslint-disable-next-line no-void
                void request(testApp)
                    .get('/?query={ xtremCore { testDatatypes { query { edges { node { id } } } } } }')
                    .expect(200)
                    .end((err, res) => (err ? reject(err instanceof Error ? err : new Error(err)) : resolve(res)));
            })
        ).body;
        assert.isObject(result);
        assert.isObject(result.data);
        assert.isObject(result.data.xtremCore);
        const datatypes = graphqlPageNodes(result.data.xtremCore.testDatatypes.query);
        assert.equal(datatypes.length, datatypesData.length);
        await asyncArray(datatypes).forEach(async (t, i) => {
            assert.isObject(t);
            assert.equal(await t.id, i);
        });
    });

    it('can execute a mutation', async () => {
        const uuid = Uuid.generate();
        const id = datatypesData.length + 2;
        const result = (
            await new Promise<{
                body: {
                    data: {
                        xtremCore: {
                            testDatatypes: { create: { id: number; stringVal: string; uuidVal: string } };
                        };
                    };
                };
            }>((resolve, reject) => {
                // eslint-disable-next-line no-void
                void request(testApp)
                    .post(
                        `/?query=mutation { xtremCore { testDatatypes { create(data: {
                id: ${datatypesData.length + 2},
                stringVal: "created_${id}"
                uuidVal: "${uuid}"
            })  { id, stringVal, uuidVal  } } } }`,
                    )
                    .expect(200)
                    .end((err, res) => (err ? reject(err instanceof Error ? err : new Error(err)) : resolve(res)));
            })
        ).body;
        assert.isObject(result);
        assert.isObject(result.data);
        assert.isObject(result.data.xtremCore);
        assert.isObject(result.data.xtremCore.testDatatypes);
        const created = result.data.xtremCore.testDatatypes.create;
        assert.isObject(created);
        assert.equal(created.id, id);
        assert.equal(created.stringVal, `created_${id}`);
        assert.equal(created.uuidVal, `${uuid}`);
    });
});
