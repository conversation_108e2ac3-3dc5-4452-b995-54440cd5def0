import { graphQlApp, Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import * as express from 'express';
import { fixtures } from '../fixtures';
import { DiagnoseResult, Result } from '../fixtures/interfaces/restricted-properties';
import { setup } from '../fixtures/setup';

import request = require('supertest');

const { createApplicationWithApi, graphqlPageNodes, initTables, restoreTables, referringData } = fixtures;
const testApp = express();

const queryWithFilter = async (filter: object): Promise<Result> => {
    const filterAsString = JSON.stringify(filter).replace(/"/g, '\\"');
    const query = `{
        xtremCore {
            testReferring {
                query(filter:"${filterAsString}") {
                    edges {
                        node {
                            code
                            reference {
                                code
                                restricted
                            }
                        }
                    }
                }
            }
        }
    }`;

    const result = (
        await new Promise<{ body: Result }>((resolve, reject) => {
            // eslint-disable-next-line no-void
            void request(testApp)
                .get(`/?query=${query}`)
                .expect(200)
                .end((err, res) => (err ? reject(err instanceof Error ? err : new Error(err)) : resolve(res)));
        })
    ).body;

    return result;
};

const testFilterQuery = async (
    filter: object,
    expectedNumberOfResults: number,
    expectedRestrictedFilters: DiagnoseResult[],
) => {
    const result = await queryWithFilter(filter);
    assert.isObject(result);
    assert.isObject(result.data);
    assert.isObject(result.data.xtremCore);
    const referring = graphqlPageNodes(result.data.xtremCore.testReferring.query);
    // Check that filter hasn't indeed been applied
    assert.equal(referring.length, expectedNumberOfResults);
    // Check that restricted properties are added to result.extensions
    assert.isObject(result.extensions);
    assert.isArray(result.extensions.diagnoses);
    expectedRestrictedFilters.forEach((restrictedFilter: DiagnoseResult) => {
        expect(result.extensions.diagnoses).to.deep.contain(
            restrictedFilter,
            `${JSON.stringify(restrictedFilter)} not found in ${JSON.stringify(result.extensions.diagnoses)} `,
        );
    });
};

describe('Test graphql filters', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            {
                nodes: {
                    TestReferred: fixtures.importedNodes.TestReferred,
                    TestReferring: fixtures.importedNodes.TestReferring,
                },
            },
            'xtrem_data_management_test',
        );
        await setup(application);
        await initTables([
            { nodeConstructor: fixtures.importedNodes.TestReferred, data: fixtures.referredData },
            { nodeConstructor: fixtures.importedNodes.TestReferring, data: fixtures.referringData },
        ]);
        await Test.withContext(context => Test.initializeManagers(context));
        testApp.use(Test.fixRequestMiddleWare);
        testApp.use(graphQlApp(application));
    });

    after(() => restoreTables());

    it('cannot filter by nested restricted properties', async () => {
        const filter = {
            reference: {
                restricted: {
                    _eq: 'restricted1',
                },
            },
        };
        await testFilterQuery(filter, referringData.length, [
            {
                severity: ValidationSeverity.warn,
                path: ['reference', 'restricted'],
                message: 'The property in the filter is unavailable or unauthorized.',
            },
        ]);
    });

    it('cannot filter by multiple restricted properties', async () => {
        const filter = {
            restricted: {
                _eq: 'restricted1',
            },
            reference: {
                restricted: {
                    _eq: 'restricted1',
                },
            },
        };
        await testFilterQuery(filter, referringData.length, [
            {
                severity: ValidationSeverity.warn,
                path: ['restricted'],
                message: 'The property in the filter is unavailable or unauthorized.',
            },
            {
                severity: ValidationSeverity.warn,
                path: ['reference', 'restricted'],
                message: 'The property in the filter is unavailable or unauthorized.',
            },
        ]);
    });

    it('can filter discarding restricted properties', async () => {
        const filter = {
            code: {
                _ne: 'PAR2',
            },
            restricted: {
                _eq: 'restricted1',
            },
            reference: {
                restricted: {
                    _eq: 'restricted1',
                },
                code: {
                    _eq: 'REF1',
                },
            },
        };
        await testFilterQuery(filter, 2, [
            {
                severity: ValidationSeverity.warn,
                path: ['restricted'],
                message: 'The property in the filter is unavailable or unauthorized.',
            },
            {
                severity: ValidationSeverity.warn,
                path: ['reference', 'restricted'],
                message: 'The property in the filter is unavailable or unauthorized.',
            },
        ]);
    });
});
