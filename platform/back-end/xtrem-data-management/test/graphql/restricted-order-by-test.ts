import { asyncArray, graphQlApp, restoreTables, Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import * as express from 'express';
import { fixtures } from '../fixtures';
import { DiagnoseResult, Result } from '../fixtures/interfaces/restricted-properties';
import { setup } from '../fixtures/setup';

import request = require('supertest');

const { graphqlPageNodes, createApplicationWithApi, initTables, referringData } = fixtures;
const { TestReferred, TestReferring } = fixtures.nodes;

const testApp = express();

const queryWithOrderBy = async (orderBy: object): Promise<Result> => {
    const orderByString = JSON.stringify(orderBy).replace(/"/g, '\\"');
    const query = `{
        xtremCore {
            testReferring {
                query(orderBy:"${orderByString}") {
                    edges {
                        node {
                            code
                            reference {
                                code
                                restricted
                            }
                        }
                    }
                }
            }
        }
    }`;

    const result = (
        await new Promise<{ body: Result }>((resolve, reject) => {
            // eslint-disable-next-line no-void
            void request(testApp)
                .get(`/?query=${query}`)
                .expect(200)
                .end((err, res) => (err ? reject(err instanceof Error ? err : new Error(err)) : resolve(res)));
        })
    ).body;

    return result;
};

const testOrderByQuery = async (
    orderBy: object,
    referringOrderedCodes: string[],
    expectedRestrictedOrderByConditions: DiagnoseResult[],
) => {
    const result = await queryWithOrderBy(orderBy);
    assert.isObject(result);
    assert.isObject(result.data);
    assert.isObject(result.data.xtremCore);
    const referrings = graphqlPageNodes(result.data.xtremCore.testReferring.query);
    assert.equal(referrings.length, referringData.length);
    // Check result order
    await asyncArray(referrings).forEach(async (referring, index) => {
        assert.equal(await referring.code, referringOrderedCodes[index]);
    });
    // Check that reserved order-by properties are present in result.extensions
    assert.isObject(result.extensions);
    assert.isArray(result.extensions.diagnoses);
    expectedRestrictedOrderByConditions.forEach((expectedRestrictedOrderByCondition: DiagnoseResult) => {
        expect(result.extensions.diagnoses).to.deep.contain(
            expectedRestrictedOrderByCondition,
            `${JSON.stringify(expectedRestrictedOrderByCondition)} not found in ${JSON.stringify(
                result.extensions.diagnoses,
            )} `,
        );
    });
};

describe('Test graphql order by', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            {
                nodes: { TestReferred, TestReferring },
            },
            'xtrem_data_management_test',
        );

        await setup(application);
        await Test.withContext(context => Test.initializeManagers(context));
        testApp.use(Test.fixRequestMiddleWare);
        testApp.use(graphQlApp(application));
        await initTables([
            { nodeConstructor: fixtures.importedNodes.TestReferred, data: fixtures.referredData },
            { nodeConstructor: fixtures.importedNodes.TestReferring, data: fixtures.referringData },
        ]);
    });

    after(() => restoreTables());

    it('cannot order by restricted properties', async () => {
        const orderBy = {
            restricted: -1,
        };
        await testOrderByQuery(
            orderBy,
            ['PAR1', 'PAR2', 'PAR3'],
            [
                {
                    path: ['restricted'],
                    severity: ValidationSeverity.warn,
                    message: 'The property in the sort order is unavailable or unauthorized.',
                },
            ],
        );
    });

    it('cannot order by multiple restricted properties', async () => {
        const orderBy = {
            restricted: -1,
            reference: {
                restricted: -1,
            },
        };
        await testOrderByQuery(
            orderBy,
            ['PAR1', 'PAR2', 'PAR3'],
            [
                {
                    path: ['restricted'],
                    severity: ValidationSeverity.warn,
                    message: 'The property in the sort order is unavailable or unauthorized.',
                },
                {
                    path: ['reference', 'restricted'],
                    severity: ValidationSeverity.warn,
                    message: 'The property in the sort order is unavailable or unauthorized.',
                },
            ],
        );
    });

    it('can order results discarding restricted properties', async () => {
        const orderBy = {
            code: -1,
            restricted: -1,
            reference: {
                restricted: -1,
                code: -1,
            },
        };
        await testOrderByQuery(
            orderBy,
            ['PAR3', 'PAR2', 'PAR1'],
            [
                {
                    path: ['restricted'],
                    severity: ValidationSeverity.warn,
                    message: 'The property in the sort order is unavailable or unauthorized.',
                },
                {
                    path: ['reference', 'restricted'],
                    severity: ValidationSeverity.warn,
                    message: 'The property in the sort order is unavailable or unauthorized.',
                },
            ],
        );
    });
});
