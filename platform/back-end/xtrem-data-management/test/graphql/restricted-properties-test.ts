import { asyncArray, graphQlApp, restoreTables, Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import * as express from 'express';
import { fixtures } from '../fixtures';
import { Result } from '../fixtures/interfaces/restricted-properties';
import { setup } from '../fixtures/setup';

import request = require('supertest');

const { createApplicationWithApi, initTables, graphqlPageNodes } = fixtures;
const testApp = express();

describe('Test graphql properties', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            {
                nodes: {
                    TestReferred: fixtures.importedNodes.TestReferred,
                    TestReferring: fixtures.importedNodes.TestReferring,
                },
            },
            'xtrem_data_management_test',
        );
        await setup(application);
        await Test.withContext(context => Test.initializeManagers(context));
        testApp.use(Test.fixRequestMiddleWare);
        testApp.use(graphQlApp(application));
        await initTables([
            { nodeConstructor: fixtures.importedNodes.TestReferred, data: fixtures.referredData },
            { nodeConstructor: fixtures.importedNodes.TestReferring, data: fixtures.referringData },
        ]);
    });
    after(() => restoreTables());

    it('cannot read restricted properties', async () => {
        const query = `{
            xtremCore {
                testReferring {
                    query {
                        edges {
                            node {
                                code
                                restricted
                                reference {
                                    code
                                    restricted
                                }
                            }
                        }
                    }
                }
            }
        }`;

        const result = (
            await new Promise<{ body: Result }>((resolve, reject) => {
                // eslint-disable-next-line no-void
                void request(testApp)
                    .get(`/?query=${query}`)
                    .expect(200)
                    .end((err, res) => (err ? reject(err instanceof Error ? err : new Error(err)) : resolve(res)));
            })
        ).body;

        assert.isObject(result);
        assert.isObject(result.data);
        assert.isObject(result.data.xtremCore);
        const referrings = graphqlPageNodes(result.data.xtremCore.testReferring.query);

        // Check that restricted properties have been set to null
        await asyncArray(referrings).forEach(async referring => {
            assert.isNotNull(await referring.code);
            assert.isNull(await referring.restricted);
            expect(await referring.reference).to.have.keys('code', 'restricted');
            assert.isNotNull(await (await referring.reference)?.code);
            assert.isNull(await (await referring.reference)?.restricted);
        });

        // Check that restricted properties are added to the 'extensions' object
        assert.isObject(result.extensions);
        assert.isArray(result.extensions.diagnoses);
        expect(result.extensions.diagnoses).to.deep.contain({
            severity: ValidationSeverity.error,
            path: ['xtremCore', 'testReferring', 'query', 'edges', 'node', 'restricted'],
            message: 'The property is unavailable.',
        });
        expect(result.extensions.diagnoses).to.deep.contain({
            severity: ValidationSeverity.error,
            path: ['xtremCore', 'testReferring', 'query', 'edges', 'node', 'reference', 'restricted'],
            message: 'The property is unavailable.',
        });
    });
});
