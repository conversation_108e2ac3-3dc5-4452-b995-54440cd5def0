import { async<PERSON><PERSON>y, ConfigManager, fileExists, S3Bucket, S3Helper, Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import { tmpdir } from 'os';
import * as fsp from 'path';
import * as sinon from 'sinon';
import { TenantService } from '../../lib/tenant/tenant-service';
import { fixtures } from '../fixtures';
import { resetSchema } from './tenant-test-utils';

const sandbox = sinon.createSandbox();

const { initTables, referredData, referringData, restoreTables, setup, createApplicationWithApi } = fixtures;
const { TestDatatypes, TestReferred, TestReferring, TestDocument, TestDocumentLine, ComputedDocumentLine } =
    fixtures.nodes;

const datatypesData = [...fixtures.datatypesData];
const newData = { ...datatypesData[datatypesData.length - 1] };
newData._id += 1;
newData.id += 1;
// eslint-disable-next-line @typescript-eslint/quotes
newData.textStream = TextStream.fromString(`with multiple lines\nline '1'\nline "2"\nline "3'`);
datatypesData.push(newData);

async function checkResult(tenantId: string, options?: { isEmpty?: boolean }): Promise<void> {
    const { isEmpty = false } = options ?? {};
    // data exists in TestDatatypes. So we have moved the data from 777777777777777777777 to 999999999999999999999
    await Test.application.withReadonlyContext(tenantId, async context => {
        const results = await context.query(TestDatatypes).toArray();
        assert.equal(results.length, isEmpty ? 0 : datatypesData.length);
        await asyncArray(results).forEach(async (t, i) => {
            assert.equal(await t.id, i);
            assert.equal(await t.integerVal, datatypesData[i].integerVal);
            assert.equal(await t.booleanVal, datatypesData[i].booleanVal);
            assert.equal((await t.textStream).value, datatypesData[i].textStream.value);
        });
    });
}

async function checkReferenceResult(tenantId: string, data: typeof fixtures.referringData): Promise<void> {
    // data exists in TestDatatypes. So we have moved the data from 777777777777777777777 to 999999999999999999999
    await Test.application.withReadonlyContext(tenantId, async context => {
        const results = await context.query(TestReferring).toArray();
        assert.equal(results.length, data.length);
        await asyncArray(results).forEach(async (t, i) => {
            assert.equal(await t.code, data[i].code);
            assert.equal(await t.description, data[i].description);
            assert.equal((await t.reference)?._id, data[i].reference);
            assert.deepEqual(
                (await t.referenceArray)?.map(r => r._id),
                data[i].referenceArray,
            );
        });
    });
}

const getZipPath = (tenantId: string, exportId: string) => {
    return fsp.join(Test.application.dir, 'data/exports', tenantId, `${exportId}.zip`);
};

describe('import tenant tests', () => {
    describe('basic import tenant tests', () => {
        before(async () => {
            const application = await createApplicationWithApi(
                { nodes: { TestDatatypes } },
                'xtrem_data_management_test',
            );
            await setup({ application, stubResetTables: false });
            await resetSchema(application);
        });

        beforeEach(() => initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]));

        it('can import all tenant data of tests schema from zip', async () => {
            // exclude invalid test tables
            const excludedTables = ['test_foreign_nullable_1'];
            const [tenantId, exportId] = [Test.defaultTenantId, 'test-export'];
            await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });
            const zipPath = getZipPath(tenantId, exportId);
            const newTenantId = '9'.repeat(21);
            const newCustomerId = '9'.repeat(8);

            await checkResult(newTenantId, { isEmpty: true });
            await TenantService.importTenant(Test.application, {
                tenant: { id: newTenantId },
                location: zipPath,
                customer: {
                    id: newCustomerId,
                },
            });
            // data exists in TestDatatypes. So we have moved the data from 777777777777777777777 to 999999999999999999999
            await checkResult(newTenantId);
        });

        it('can import all tenant data of tests schema from zip with app name prefix', async () => {
            ConfigManager.current.app = 'xtrem_data_management_test';
            // exclude invalid test tables
            const excludedTables = ['test_foreign_nullable_1'];
            const [tenantId, exportId] = [Test.defaultTenantId, 'test-export'];
            await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });
            const zipPath = getZipPath(tenantId, exportId);
            const newTenantId = '9'.repeat(21);
            const newCustomerId = '9'.repeat(8);

            await checkResult(newTenantId, { isEmpty: true });
            await TenantService.importTenant(Test.application, {
                tenant: { id: newTenantId },
                location: zipPath,
                customer: {
                    id: newCustomerId,
                },
            });
            // data exists in TestDatatypes. So we have moved the data from 777777777777777777777 to 999999999999999999999
            await checkResult(newTenantId);
            delete ConfigManager.current.app;
        });

        it('cannot import tenant data for existing tenant', () =>
            Test.withContext(async context => {
                Test.initializeManagers(context);

                const [tenantId, exportId, customerId] = [Test.defaultTenantId, 'test-export', '0'.repeat(8)];
                const zipPath = getZipPath(tenantId, exportId);

                await assert.isRejected(
                    TenantService.importTenant(Test.application, {
                        tenant: { id: tenantId },
                        location: zipPath,
                        customer: { id: customerId },
                    }),
                    `Tenant ${tenantId} already exists.`,
                );
            }));

        it('can import all tenant data of tests schema from S3(mocked)', async () => {
            sandbox
                .stub(S3Bucket.prototype, 'putObject')
                // eslint-disable-next-line require-await
                .returns((async () => Promise.resolve({ ETag: '', VersionId: '', $metadata: {} }))());
            // eslint-disable-next-line require-await
            sandbox.stub(S3Helper, 'download').callsFake(async () => Promise.resolve(undefined));

            // exclude invalid test tables
            const excludedTables = ['test_foreign_nullable_1'];
            const [tenantId, exportId] = [Test.defaultTenantId, 'test-export'];
            await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });

            // copy the zip to tmp dir
            const tempZip = fsp.join(tmpdir(), `${exportId}.zip`);
            const zipPath = getZipPath(tenantId, exportId);

            fs.copyFileSync(zipPath, tempZip);
            const s3Uri = `s3://xtrem-dev-eu-global/tenants/${exportId}.zip`;
            // 999999999999999999999 already exists so we use 888888888888888888888 in this test
            const newTenantId = '8'.repeat(21);
            const newCustomerId = '8'.repeat(8);
            await Test.withContext(() =>
                TenantService.importTenant(Test.application, {
                    tenant: { id: newTenantId },
                    location: s3Uri,
                    customer: { id: newCustomerId, name: 'testCustomer' },
                }),
            );

            // check cleanup
            assert.isFalse(await fileExists(tempZip));

            await checkResult(newTenantId);
            sandbox.restore();
        });

        afterEach(() => restoreTables());
    });
    describe('import tenant tests with references', () => {
        before(async () => {
            const application = await createApplicationWithApi(
                {
                    nodes: {
                        TestReferred,
                        TestReferring,
                        TestDocument,
                        TestDocumentLine,
                        ComputedDocumentLine,
                    },
                },
                'xtrem_data_management_test',
            );
            await setup({ application, stubResetTables: false });
            await resetSchema(application);
        });

        beforeEach(() =>
            initTables([
                { nodeConstructor: TestReferred, data: referredData },
                { nodeConstructor: TestReferring, data: referringData },
            ]),
        );

        // XT-6027
        it('can import all tenant data of tests schema from zip with nullable references', async () => {
            // put a row with a null for the reference
            const id = referringData.length + 1;
            await Test.withCommittedContext(async context => {
                const node = await context.create(TestReferring, {
                    code: `PAR${id}`,
                    description: `referring ${id}`,
                    restricted: `restricted${id}`,
                });
                await node.$.save();
            });

            // exclude invalid test tables
            const [tenantId, exportId, customerId] = [Test.defaultTenantId, 'test-export-ref', '0'.repeat(8)];
            await TenantService.exportTenant(Test.application, { tenantId, exportId });
            const zipPath = getZipPath(tenantId, exportId);
            const newTenantId = '9'.repeat(21);
            await TenantService.importTenant(Test.application, {
                tenant: { id: newTenantId },
                customer: { id: customerId },
                location: zipPath,
            });
            // data exists in TestDatatypes. So we have moved the data from 777777777777777777777 to 999999999999999999999
            await checkReferenceResult(newTenantId, [
                ...referringData,
                ...([
                    {
                        code: `PAR${id}`,
                        description: `referring ${id}`,
                        restricted: `restricted${id}`,
                    },
                ] as typeof fixtures.referringData),
            ]);
        });

        afterEach(() => restoreTables());
    });
});
