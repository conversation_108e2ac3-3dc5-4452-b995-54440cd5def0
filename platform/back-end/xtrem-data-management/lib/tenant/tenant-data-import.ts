import {
    AnyR<PERSON>ord,
    AnyValue,
    Application,
    asyncArray,
    AsyncResponse,
    checkAndSyncValuesHash,
    Context,
    CoreHooks,
    Datetime,
    Decompress,
    Dict,
    fileExists,
    ModifyTableSqlContext,
    NodeFactory,
    ReadTableSqlContext,
    rootUserEmail,
    S3Helper,
    S3ObjectInfo,
    SequenceSqlContext,
    SqlContext,
    SqlNaturalKeyUtils,
    SystemError,
} from '@sage/xtrem-core';
import { copyFrom, CopyOptions, TableDefinition } from '@sage/xtrem-postgres';
import { integer } from '@sage/xtrem-shared';
import { assert } from 'console';
import { CsvError, parse } from 'csv-parse';
import * as fs from 'fs';
import { createReadStream } from 'fs';
import { kebabCase, snakeCase, uniq } from 'lodash';
import { tmpdir } from 'os';
import * as fsp from 'path';
import { createInterface } from 'readline';
import * as semver from 'semver';
import { Readable, Transform } from 'stream';
import { ColumnInfo, TableInfo } from './schema-info';
import { TenantDataLocation } from './tenant-data-location';
import { dbConstant } from './tenant-data-transform';
import { defaultCopy, logger, TenantDataUtils } from './tenant-data-utils';
import {
    ColumnsDistribution,
    TenantDataMetadata,
    TenantDataOptions,
    TenantImportAnatomy,
    TenantImportConfig,
} from './tenant-interfaces';

interface ZipInfo extends S3ObjectInfo {
    basename: string;
    version?: string;
}

/**
 * A cache of natural keys, for shared tables
 * _ids, indexed by factoryName / naturalKey
 */
type SharedTablesNaturalKeysCache = Dict<Dict<number>>;

type LoadTableOptions = TenantDataOptions & { sharedTablesNaturalKeysCache: SharedTablesNaturalKeysCache };

type RowTransformFunction = (row: Dict<any>) => AsyncResponse<any>;

interface InjectTenantId {
    columnName?: string;
    columnValue?: string;
}

interface ParseMetadata {
    headersDone: boolean;
    headers: string[];
    originalHeaders: string[];
    mapOut: Dict<(value: any) => any>;
    chunks: string[];
    count: number;
}

/** @internal */
export class TenantDataImport {
    private location: string;

    private readonly userMapping: Dict<string | number> = {};

    private readonly cache: Dict<Dict<any>> = {};

    constructor(
        public readonly application: Application,
        private readonly importConfig: TenantImportConfig,
    ) {
        this.location = this.importConfig.location;
    }

    private get hasTenantIncluded(): boolean {
        return !!this.importConfig.hasTenantIncluded;
    }

    private get tenantId(): string {
        return this.importConfig.tenant.id;
    }

    /**
     * Download the target file
     * @param targetFilename
     */
    async download(targetFilename: string): Promise<void> {
        const profiler = logger.info(`Downloading from ${this.location}`);
        try {
            await S3Helper.download(S3Helper.parseS3Uri(this.location), targetFilename);
            profiler.success('Download complete');
        } catch (err) {
            profiler.fail(`Failed to download ${this.location}, ${err.message}`);
            throw err;
        }
    }

    /**
     * Decompress provided archived file
     * @param archiveFullFilename
     * @param targetPath
     * @returns
     */
    static async decompress(archiveFullFilename: string, targetPath: string): Promise<string[]> {
        const profiler = logger.info(`Extracting ${archiveFullFilename} to ${targetPath}`);
        try {
            const filePaths = (await Decompress.decompressZipToFolder(archiveFullFilename, targetPath)).map(filename =>
                fsp.join(targetPath, filename),
            );

            filePaths.forEach(filePath => logger.info(filePath));

            profiler.success(`Extracted ${filePaths.length} files`);

            return filePaths;
        } catch (err) {
            profiler.fail(`Failed to extract ${archiveFullFilename} to ${targetPath}, ${err.message}`);
            throw err;
        }
    }

    /**
     * Read metadata.json from list of files paths provided and return the metadata object
     * @param filePaths
     * @returns
     */
    private static getAnatomy(filePaths: string[]): TenantImportAnatomy {
        const metadataPath = filePaths.find(filePath => fsp.basename(filePath) === 'metadata.json');

        if (!metadataPath) throw new Error('metadata.json is missing');

        return { dir: fsp.dirname(metadataPath), metadata: JSON.parse(fs.readFileSync(metadataPath).toString()) };
    }

    /**
     * Validate that the current package versions and the metadata package versions are the same
     * to ensure the schema export from and the current schema are then same.
     * @param context
     * @param metadata
     */
    private validatePackageVersions(metadata: TenantDataMetadata): void {
        let packageIssues = 0;
        const packages = this.application.getPackages();
        metadata.packages?.forEach(pack => {
            const logIssue = (message: string): void => {
                packageIssues += 1;
                logger.error(() => message);
            };

            const currentPackVersion = packages.find(pkg => pkg.name === pack.name);

            if (!currentPackVersion) {
                logger.warn(
                    () =>
                        `Package ${pack.name} with version ${pack.version} was skipped in export because it's not present in the Bundle Manager.`,
                );
                return;
            }

            const isReleased = currentPackVersion.isReleased;

            const { version, sqlSchemaVersion } = currentPackVersion;
            const versionToCheck = sqlSchemaVersion || version;

            const packVersionDiff = semver.diff(pack.version, versionToCheck);
            if (packVersionDiff != null) {
                //  If the package is released then we need verify that there is only a patch level difference in version
                //  If the package is not released then no difference in version is allowed at all
                if ((isReleased && packVersionDiff !== 'patch') || !isReleased) {
                    logIssue(
                        `Package versions not aligned: ${pack.name}, exported version ${pack.version} vs. ${versionToCheck}`,
                    );
                }
            }
        });

        if (packageIssues > 0) throw new Error('Cannot import data with mis-aligned package versions.');
    }

    /**
     * Pre load csv header
     * @param filePath - file path of the CSV to parse
     * @param table - The corresponded table info for the current CSV file.
     * @param metadata - The CSV parse metadata.
     */
    private static preLoadCsvHeader(
        filePath: string,
        table: TableInfo,
        metadata: ParseMetadata,
    ): Promise<string | undefined> {
        const mapOutJson = (value: any): any => {
            if (!value.length) return '';
            return value === '{}' ? value : `"${value.replace(/"/g, '""')}"`;
        };

        const mapOutText = (value: any): any => {
            const quoted = /^"(.*)"$/.exec(value);
            const val = quoted !== null ? quoted[1] : value;
            return !val.length ? '""' : `"${val.replace(/"/g, '""')}"`;
        };

        // Read the first line to get headers and set the mapping
        // We need this because the copy from clause is built with those explicit headers
        return new Promise((resolve, reject) => {
            const lineReader = createInterface({
                input: createReadStream(filePath),
            });
            let firstLine: string | undefined;
            lineReader.on('line', line => {
                firstLine = line;
                lineReader.close();
            });
            lineReader.on('close', () => {
                if (firstLine == null) {
                    resolve(firstLine);
                    return;
                }
                parse(
                    `${firstLine}\n`,
                    {
                        delimiter: ';',
                        columns: headers => {
                            // Exclude columns that are not in the schema
                            metadata.headers = metadata.headers.concat(headers);
                            metadata.headers = metadata.headers.filter(header => table.columnsByName[header]);
                            metadata.originalHeaders = headers;
                            const { mapOut } = metadata;
                            metadata.headers.forEach(header => {
                                const column = table.columnsByName[header];
                                if (['jsonb', 'ARRAY'].includes(column.dataType)) {
                                    mapOut[header] = mapOutJson;
                                } else if (['text', 'character varying'].includes(column.dataType)) {
                                    mapOut[header] = mapOutText;
                                }
                            });
                            // return the original headers to parse the file data "as is"
                            return headers;
                        },
                    },
                    (err: CsvError | undefined) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(firstLine);
                        }
                    },
                );
            });
        });
    }

    /**
     * Pre load parsing logic for a given streamReader based on provided header and row conditions.
     * @param filePath - A stream reader created from the CSV file we are currently parsing.
     * @param table - The corresponded table info for the current CSV file.
     * @param rowTransformFunctions - Transform callbacks to be applied for the rows.
     * @param injectTenantId - Transform callback to be applied for the rows.
     */
    private static async preLoadCsvParse(
        importConfig: TenantImportConfig,
        filePath: string,
        table: TableInfo,
        rowTransformFunctions: RowTransformFunction[] = [],
        injectTenantId: InjectTenantId = {},
    ): Promise<{ reader: Readable; headers: string[] }> {
        const chunkSize = importConfig.chunkSize || 100;
        const metadata: ParseMetadata = {
            headersDone: false,
            headers: injectTenantId.columnName && injectTenantId.columnValue ? [injectTenantId.columnName] : [],
            originalHeaders: [],
            mapOut: {},
            chunks: [],
            count: 0,
        };

        const firstLine = await this.preLoadCsvHeader(filePath, table, metadata);
        if (firstLine == null) {
            throw new Error(`${filePath} is an empty file`);
        }

        const reader = createReadStream(filePath)
            // go through the csv parser to get records
            .pipe(
                parse({
                    delimiter: ';',
                    columns: true,
                }),
            )
            // then transform the records into the expect lines for the SQL copy stream
            .pipe(
                new Transform({
                    writableObjectMode: true,

                    transform(_chunk, _encoding, done) {
                        const { headers, chunks, mapOut } = metadata;
                        if (!metadata.headersDone) {
                            chunks.push(headers.join(';'));
                            metadata.headersDone = true;
                        }

                        const chunk: Dict<any> =
                            injectTenantId.columnName && injectTenantId.columnValue
                                ? { [injectTenantId.columnName]: injectTenantId.columnValue, ..._chunk }
                                : _chunk;

                        rowTransformFunctions.forEach(transformCallback => transformCallback(chunk));
                        if (chunk.$invalid) {
                            return done(null);
                        }

                        const row = Object.keys(chunk)
                            .map(header => (mapOut[header] ? mapOut[header](chunk[header]) : chunk[header]))
                            .join(';');

                        metadata.count += 1;
                        // accumulate rows
                        chunks.push(row);
                        if (chunks.length > chunkSize) {
                            metadata.chunks = [];
                            // write rows
                            return done(null, `${chunks.join('\n')}\n`);
                        }
                        return done(null);
                    },

                    flush() {
                        const { chunks } = metadata;
                        if (chunks.length > 0) {
                            metadata.chunks = [];
                            // write remaining rows
                            this.push(`${chunks.join('\n')}\n`);
                        }
                        // end the readable stream
                        this.push(null);
                    },
                }),
            );
        return { reader, headers: metadata.headers };
    }

    /**
     * Load tenant table data from specified file
     * @param context
     * @param table
     * @param options
     * @returns
     */
    private async _loadTable(table: TableInfo, options: LoadTableOptions): Promise<number> {
        if (table.name === 'sys_csv_checksum') {
            // TODO Restore when the update of the sequences will have been moved after the load of data, in a separate DDL context
            // For now, if we load data for sys_csv_checksum, the update of sequences will lock
            return 0;
        }
        assert(options.filename != null);

        const ext = fsp.extname(options.filename) ? '' : '.csv';
        const filename = `${options.filename}${ext}`;
        const filePath = fsp.join(options.rootPath, filename);
        logger.info(`Filepath - ${filePath}`);
        if (!(await fileExists(filePath))) {
            logger.info(`Filepath does not exist - ${filePath}`);
            return 0;
        }

        const { hasTenantIncluded } = this;

        const copyHeaders = options?.copy?.headers;
        // temp tables cannot have a schema prefix as they are created in there own internal session schema in Postgres
        const tableName = table.isTemp ? table.name : `${this.application.schemaName}.${table.name}`;
        logger.info(`Loading ${filePath} into table ${tableName} with header [${copyHeaders}]`);

        const rootUserId = await this.withCommittedContext(context => context.userId as integer);
        const fixData = TenantDataImport.fixData(rootUserId, copyHeaders);
        const rowTransformFunctions = [fixData];
        let injectTenantId: InjectTenantId = {};
        const userTableName = snakeCase(CoreHooks.sysManager.getUserNode().name);
        const snakeCaseFilename = filename.replace(/-/g, '_');

        if (!hasTenantIncluded) {
            const tenantColumnName = this.getTenantColumnName(table, filePath);
            if (tenantColumnName && this.tenantId) {
                // If the table has a tenant column, we add the fill with tenant transform function
                injectTenantId = {
                    columnName: tenantColumnName,
                    columnValue: this.tenantId,
                };
            }
        } else if (!copyHeaders?.length) {
            logger.info(`No column to set for table ${table.name}`);
            return 0;
        }

        if (filename === 'user-preferences.csv') {
            rowTransformFunctions.push(this.duplicateUserPreferenceTransformation());
        } else if ([`${userTableName}.csv`, `${userTableName}__nullable.csv`].includes(snakeCaseFilename)) {
            rowTransformFunctions.push(this.userTransformation(rootUserId));
        }

        const sharedFactoriesByColumn = table.columns.reduce((total, column) => {
            if (!column.isReference) return total;
            const factory = this.application.tryGetFactoryByTableName(column.targetTableName!);
            if (factory && factory.isSharedByAllTenants) {
                total[column.name] = factory;
            }
            return total;
        }, {} as Dict<NodeFactory>);
        if (Object.keys(sharedFactoriesByColumn).length > 0) {
            rowTransformFunctions.push(
                await this._resolveNaturalKeysForSharedTablesTransformation(
                    table.name,
                    sharedFactoriesByColumn,
                    options.sharedTablesNaturalKeysCache,
                ),
            );
        }

        const userReferences = table.columns
            .filter(column => column.isReference && column.targetTableName === userTableName)
            .map(c => c.name);
        if (userReferences.length > 0) {
            rowTransformFunctions.push(this.fixUserReferencesTransformation(userReferences));
        }
        logger.info(`Pre-loading table ${tableName}`);
        const { reader, headers } = await TenantDataImport.preLoadCsvParse(
            this.importConfig,
            filePath,
            table,
            rowTransformFunctions,
            injectTenantId,
        );
        logger.info(`SQL copy into table ${tableName}`);
        // In any case
        const count = await this.copyFromStream(reader, tableName, {
            ...defaultCopy,
            ...options?.copy,
            headers,
        });
        logger.info(`Loaded table ${tableName} with ${count} row(s)`);
        return count;
    }

    private getTenantColumnName(table: TableInfo, filePath: string): string | undefined {
        logger.info(`Adding _tenantId/tenantId ${this.tenantId} to ${filePath}`);

        // Retrieve the tenant columns from the table
        const tenantColumnNames = table.columns.filter(col => col.name === 'tenant_id' || col.name === '_tenant_id');
        if (tenantColumnNames.length > 1) {
            // Throw an error if there are multiple tenant columns within the current table (not permitted).
            throw new Error(`table '${table.name}' must not contain both 'tenant_id' and '_tenant_id' columns`);
        }

        return tenantColumnNames[0]?.name;
    }

    /**
     * @internal
     * Copy from a stream to a table
     * @see https://www.postgresql.org/docs/current/sql-copy.html
     * @param context
     * @param reader
     * @param tableName
     * @param options
     */
    async copyFromStream(reader: Readable, tableName: string, options: CopyOptions): Promise<number> {
        try {
            const sql = `COPY ${tableName}${
                options.headers?.length ? `(${options.headers.map(h => SqlContext.escape(h)).join(',')})` : ''
            } FROM STDIN`;
            const withHeader = options?.header ?? true;
            const delimiter = options?.delimiter || ';';
            const copyOptions = [
                `FORMAT ${options?.format || 'CSV'}`,
                `DELIMITER '${delimiter}'`,
                `HEADER ${withHeader}`,
            ];

            SqlContext.logger.verbose(() => `${sql} (${copyOptions.join(', ')})`);

            return await this.withCommittedContext(
                context =>
                    new Promise<number>((resolve, reject) => {
                        const writer = context.transaction.connection.query(
                            copyFrom(`${sql} (${copyOptions.join(', ')})`, {}),
                        );
                        writer.on('error', (err: any) => {
                            logger.error(err.stack ?? err.message);
                            reader.destroy(err);
                            reject(err instanceof Error ? err : new Error(err));
                        });
                        reader.on('error', err => {
                            logger.error(err.stack ?? err.message);
                            writer.destroy(err);
                            reject(err instanceof Error ? err : new Error(err));
                        });
                        writer.on('finish', () => {
                            // Any cast is required because the rowCount property is not exposed
                            // but it is really set like for any other query results
                            resolve((writer as any).rowCount);
                        });
                        reader.pipe(writer);
                    }),
            );
        } catch (err) {
            if (err.detail) {
                // 'detail' may contain some very useful infos (for instance, when a FK is violated, detail really helps)
                err.message = `${err.message}: ${err.detail}`;
            }
            if (err.where) {
                // 'where' may contain the line involved
                // The format of the string is like the following:
                // COPY address_base, line 206: ""111111111111111111111";21792;"BusinessEntityAddress";1;"1fb3ff8f7922c569";"AMCKD HSTQBB;418 KQPA ZH..."
                const where = (/^([^:]+ line \d+):/.exec(err.where) ?? [])[1];
                if (where != null) {
                    err.message = `${err.message} [on ${where}]`;
                }
            }
            if (err.code?.startsWith('23')) {
                logger.error(() => err.message);
            }
            throw new SystemError(err.message, err);
        }
    }

    private async createTempTable(table: TableInfo): Promise<TableInfo> {
        const primaryKeycolumnNames = ['_id'];
        const { hasTenantIncluded } = this;
        if (hasTenantIncluded) primaryKeycolumnNames.unshift('_tenant_id');
        // This has to be reviewed we are doing thing twice because it is used in the contect of a tenant import or data management
        const columnsInUniqueIndex = new Set<string>();
        table.indexes?.forEach(ind => {
            if (!ind.isUnique) return;
            ind.columnNames.forEach(name => columnsInUniqueIndex.add(name));
        });

        const nullableColumns = table.columns.filter(
            col => col.isNullable && col.isReference && !columnsInUniqueIndex.has(col.name),
        );
        const nonNullableSelfReferences = table.columns.filter(col => !col.isNullable && col.isSelfReference);

        const keyColumns = table.columns.filter(col => primaryKeycolumnNames.includes(col.name));

        const factory = this.application.tryGetFactoryByTableName(table.name);

        if (factory?.naturalKey) {
            factory.naturalKey.forEach(naturalKeyPropName => {
                const naturalKeyProp = factory.findProperty(naturalKeyPropName);

                if (naturalKeyProp && naturalKeyProp.columnName) {
                    const naturalKeyColumn = table.columns.find(col => col.name === naturalKeyProp.columnName);
                    if (naturalKeyColumn) keyColumns.push(naturalKeyColumn);
                }
            });
        }

        const tempTableName = `${table.name}_tmp`;
        const tempTableColumns = uniq([...keyColumns, ...nullableColumns, ...nonNullableSelfReferences]);
        const tableDef = await new ReadTableSqlContext(this.application).readSchema(table.name, {
            skipForeignKeys: true,
            skipSecurity: true,
            skipSequences: true,
        });
        const colNames = tempTableColumns.map(c => c.name);
        const wellKnownReferences = ['_vendor', '_layer'];
        const tempColumns = tableDef.columns?.filter(
            col => wellKnownReferences.includes(col.name) || colNames.includes(col.name),
        );

        const tempTableDef: TableDefinition = {
            schemaName: this.application.schemaName,
            tableName: tempTableName,
            columns: tempColumns,
            primaryKey: { columns: primaryKeycolumnNames },
        };

        await this.withCommittedContext<void>(context =>
            new ModifyTableSqlContext(this.application, tempTableDef).createTemporaryTable(context),
        );

        return {
            name: tempTableName,
            columns: tempTableColumns,
            indexes: table.indexes,
            isTemp: true,
            columnsByName: tempTableColumns.reduce((r, k) => {
                r[k.name] = k;
                return r;
            }, {} as Dict<ColumnInfo>),
        };
    }

    withCommittedContext<T extends AnyValue | void>(body: (context: Context) => AsyncResponse<T>): Promise<T> {
        return this.application.asRoot.withCommittedContext(this.tenantId, body, {
            disableAllCrudNotifications: true, // disable all CRUD notification for this transaction
            description: () => `importFromDir(${this.tenantId})`,
        });
    }

    /**
     * Import data
     * @param context the context (already set to the right tenantId)
     */
    async import(): Promise<void> {
        const tempDir = tmpdir();
        const isS3Uri = TenantDataUtils.isS3Uri(this.location);
        const archiveFilename = fsp.basename(this.location);
        let archiveFullFilename = isS3Uri ? fsp.join(tempDir, archiveFilename) : this.location;
        const ext = fsp.extname(archiveFilename);
        const { keepFiles } = this.importConfig;

        if (isS3Uri && !ext.length) {
            // Let's pick the most appropriate file:
            const xtremVersion = this.application.rootAbout.version;
            const dataLocation = new TenantDataLocation({
                tenantId: this.tenantId,
                location: this.location,
                version: xtremVersion,
            });

            const listOfZipFiles = (await S3Helper.listObjects(S3Helper.parseS3Uri(this.location)))
                .map((s3Object: S3ObjectInfo) => {
                    const basename = fsp.basename(s3Object.key);
                    const version = /--(\d+\.\d+\.\d+)--/.exec(basename)?.[1];
                    return { ...s3Object, basename, version };
                })
                .filter((zipInfo: ZipInfo) => {
                    if (
                        zipInfo.version == null ||
                        !semver.valid(zipInfo.version) ||
                        (dataLocation.appPrefix && !zipInfo.basename.startsWith(dataLocation.appPrefix))
                    ) {
                        return false;
                    }
                    return this.application.mainPackage.isReleased
                        ? // Make sure that the zip'version matches the application major version
                          semver.major(xtremVersion) === semver.major(zipInfo.version)
                        : // Make sure that the zip'version matches exactly the application version
                          xtremVersion === zipInfo.version;
                })
                .sort((elt1: ZipInfo, elt2: ZipInfo) =>
                    elt1.lastModified! > elt2.lastModified! ? -1 : elt1.lastModified! < elt2.lastModified! ? 1 : 0,
                );

            if (!listOfZipFiles.length) {
                throw new Error(
                    `${this.location} doesn't contain any export that matches the ${
                        this.application.mainPackage.isReleased ? ' released ' : ' '
                    }version ${xtremVersion}`,
                );
            }
            const zipFilename = fsp.basename(listOfZipFiles[0].key);
            archiveFullFilename = fsp.join(tempDir, zipFilename);
            this.location = `${this.location}/${zipFilename}`;
        }

        const unzipDir = fsp.join(
            tempDir,
            'xtrem-tenant-import-data',
            this.tenantId,
            archiveFilename.slice(0, -ext.length || undefined),
        );

        // Download ZIP from S3 if the location is an S3 URI
        if (isS3Uri) {
            await this.download(archiveFullFilename);
        }

        // Decompress ZIP
        const filePaths = await TenantDataImport.decompress(archiveFullFilename, unzipDir);

        // Get import anatomy including metadata.json and the actual directory where the files are located
        // It might be different from the unzipDir because of subdirectories created during the decompress process
        const { dir, metadata } = TenantDataImport.getAnatomy(filePaths);

        const profiler = logger.info(`Importing ${archiveFullFilename} from ${dir}`);

        this.validatePackageVersions(metadata);

        try {
            // this will load the files exported without nullable column values
            await this.importFromDir(metadata, dir);
        } finally {
            try {
                // Always cleanup except if keepFiles option is set.
                // The Zip file will be deleted only if it has been retrieved from an s3 location.
                if (!keepFiles) TenantDataUtils.cleanup(unzipDir, isS3Uri ? archiveFullFilename : null);
            } catch (err) {
                logger.error(err);
            }
        }

        await this.withCommittedContext<void>(
            // This will create the service option states for the new tenant
            async context => {
                await context.serviceOptionManager.createOrUpgradeServiceOptionStates(context);

                // invalidate any cached data for the given tenant to force reload service options on all instances
                await context.application.invalidateGlobalCache(context);
            },
        );

        // Fix the _valuesHash for all content addressable nodes in the imported tenant from anonymized data
        await checkAndSyncValuesHash(this.application, this.tenantId, logger);

        const applicationTables = (metadata.tables || []).filter(t =>
            this.application.tryGetFactoryByTableName(t.name),
        );

        //  Fix table _id sequences
        await asyncArray(applicationTables).forEach(table =>
            new SequenceSqlContext(this.application, table.name).fixAutoIncrementSequence(this.tenantId, '_id'),
        );

        profiler.success('Importing complete');
    }

    // Added loadTables to please sonarCloud that doesn't like complexity
    private async loadTables(
        metadata: TenantDataMetadata,
        targetDir: string,
        tables: TableInfo[],
        options: {
            columnsDistributionMap?: Dict<ColumnsDistribution>;
            sharedTablesNaturalKeysCache: SharedTablesNaturalKeysCache;
        },
    ): Promise<void> {
        await asyncArray(tables).forEach(async table => {
            const headers = options.columnsDistributionMap?.[table.name].notNullColumns;
            if (options.columnsDistributionMap && !headers) return;
            const filenames = metadata.files?.filter(file => {
                return fsp.basename(file).startsWith(`${kebabCase(table.name)}${fsp.extname(fsp.basename(file))}`);
            });
            await asyncArray(filenames || []).forEach(async filename => {
                const opts = {
                    rootPath: targetDir,
                    filename,
                    targetDir,
                    copy: { header: true, headers },
                    sharedTablesNaturalKeysCache: options.sharedTablesNaturalKeysCache,
                } as LoadTableOptions;
                await this._loadTable(table, opts);
            });
        });
    }

    private async importFromDir(
        metadata: TenantDataMetadata,
        targetDir: string,
        columnsDistributionMap?: Dict<ColumnsDistribution>,
    ): Promise<void> {
        if (!metadata) throw new Error('missing metadata');
        const { hasTenantIncluded } = this;

        const importElements = (metadata.tables || [])
            .map(t => ({ factory: this.application.tryGetFactoryByTableName(t.name), table: t }))
            .filter(
                e =>
                    e.factory?.tableName != null &&
                    (!e.factory.isPlatformNode || (e.factory.isPlatformNode && e.factory.isPlatformNodeExportable)),
            );
        const applicationTables = importElements.map(e => e?.table);

        // TEMP HACK to be removed in v29
        // The _vendor column has not been correctly removed from some tables:
        applicationTables
            .filter(table => ['user_group', 'user_navigation', 'user_preferences'].includes(table.name))
            .forEach(table => {
                // Let's remove it:
                delete table.columnsByName._vendor;
                table.columns = table.columns.filter(col => !!table.columnsByName[col.name]);
            });

        const sharedTablesNaturalKeysCache: SharedTablesNaturalKeysCache = {};

        await this.loadTables(metadata, targetDir, applicationTables, {
            columnsDistributionMap,
            sharedTablesNaturalKeysCache,
        });

        // If there is nullable column in the table then a file with --nullable in the name is created, which contains
        // the nullable column and key data
        await asyncArray(applicationTables)
            .filter(table => {
                const filename = metadata.files?.find(
                    file =>
                        fsp.basename(file) === `${kebabCase(table.name)}--nullable${fsp.extname(fsp.basename(file))}`,
                );
                return !!filename && fileExists(fsp.join(targetDir, filename));
            })
            .forEach(async table => {
                const columnsDistribution = columnsDistributionMap?.[table.name] || {};
                const headers = columnsDistribution.nullableColumns;
                if (columnsDistributionMap && !headers) return;
                const factory = this.application.tryGetFactoryByTableName(table.name);
                if (factory?.naturalKey) {
                    factory.naturalKey
                        .slice()
                        .reverse()
                        .forEach(naturalKeyPropName => {
                            const naturalKeyProp = factory.findProperty(naturalKeyPropName);
                            if (naturalKeyProp?.columnName) headers?.unshift(naturalKeyProp.columnName);
                        });
                }
                headers?.unshift(...(columnsDistribution.keyColumns || []));
                const filename = `${kebabCase(table.name)}--nullable`;
                // create temp table, a temp table will be automatically dropped at the end of the session or transaction
                const tempTableInfo = await this.createTempTable(table);
                // load temp table data
                const opts = {
                    rootPath: targetDir,
                    filename,
                    metadata,
                    hasTenantIncluded,
                    copy: { header: true, headers },
                    sharedTablesNaturalKeysCache,
                } as LoadTableOptions;
                await this._loadTable(tempTableInfo, opts);

                let tenantIdJoin = '';
                if (hasTenantIncluded && tempTableInfo.columnsByName._tenant_id) {
                    tenantIdJoin = 'AND tab._tenant_id=tmp._tenant_id';
                } else {
                    tenantIdJoin =
                        table.columns
                            ?.filter(col => ['tenant_id', '_tenant_id'].includes(col.name))
                            .map(col => `AND tab.${col.name}='${this.tenantId}'`)
                            .join(' ') || '';
                }
                // bulk update statement
                const setClause = tempTableInfo.columns
                    .filter(col => col.name !== '_id')
                    .map(col => ` "${col.name}"=tmp.${col.name}`)
                    .join(',');

                const updateStatement = `UPDATE ${this.application.schemaName}.${
                    table.name
                } AS tab SET${setClause} FROM (SELECT ${tempTableInfo.columns
                    .map(col => `"${col.name}"`)
                    .join(',')} FROM ${
                    tempTableInfo.name
                }) AS tmp WHERE tmp._id = tab._id ${tenantIdJoin}; DROP TABLE ${tempTableInfo.name};`;

                await this.withCommittedContext(context => context.executeSql(updateStatement, []));
                // LATER await context.application.getFactoryByTableName(table.name).invalidateCache(context);
            });
    }

    static async getImportMetadata(
        context: Context,
        columnsDistributionMap: Dict<ColumnsDistribution>,
    ): Promise<TenantDataMetadata> {
        const metadata: TenantDataMetadata = { exportId: '', tables: [], files: [] };
        await TenantDataUtils.visitTenantTables(
            context,
            (table: TableInfo) => {
                const columnsDistribution = columnsDistributionMap[table.name];
                if (!columnsDistribution) return;
                const nullableColumns = columnsDistribution?.nullableColumns ?? [];
                metadata.tables?.push(table);
                const filename = kebabCase(table.name);
                metadata.files?.push(`${filename}.csv`);
                if (nullableColumns?.length > 0) metadata.files?.push(`${filename}--nullable.csv`);
            },
            {
                includeAll: true,
            },
        );
        return metadata;
    }

    /**
     * filters out the duplicate user preferences.
     * @param context
     * @param tenantImport
     */
    private duplicateUserPreferenceTransformation(): RowTransformFunction {
        const { cache } = this;
        if (!cache.userPreferences) {
            cache.userPreferences = {} as Dict<boolean>;
        }
        const rowCache = cache.userPreferences;
        return (userPref: any): any => {
            if (!rowCache[userPref.user]) {
                rowCache[userPref.user] = true;
            } else {
                userPref.$invalid = true;
            }
        };
    }

    /**
     * Returns the transformation that has to be applied to convert the natural key of a record form a shared table
     * to the _id of the record.
     */
    private async _resolveNaturalKeysForSharedTablesTransformation(
        tableName: string,
        sharedFactoriesByColumn: Dict<NodeFactory>,
        sharedTablesNaturalKeysCache: SharedTablesNaturalKeysCache,
    ): Promise<RowTransformFunction> {
        await this.withCommittedContext<void>(async context => {
            await asyncArray<[string, NodeFactory]>(Object.entries(sharedFactoriesByColumn)).forEach(
                async ([columnName, factory]) => {
                    if (factory.naturalKey == null || factory.naturalKey.length === 0) {
                        throw new Error(
                            `Could not import ${tableName}. The column '${columnName}' refers to a shared factory ${factory.name} but it does not have any natural key`,
                        );
                    }
                    if (sharedTablesNaturalKeysCache[factory.name] != null) {
                        // The cache already contains data for this factory
                        return;
                    }
                    const table = factory.table;
                    const sqlPartWithNaturalKeys = SqlNaturalKeyUtils.getSqlQueryPartsWithNaturalKeys(
                        context.schemaName,
                        factory.table,
                        factory.naturalKey.map(propertyName => table.columnsByPropertyName[propertyName].columnName),
                        {
                            tableAlias: 't0',
                        },
                    );
                    const sqlParts: string[] = [];
                    const columnsToQuery = sqlPartWithNaturalKeys.columns.map(col => col.aliased);
                    columnsToQuery.unshift('t0._id AS _id');
                    sqlParts.push(`SELECT ${columnsToQuery.join(',')}`);
                    sqlParts.push(`FROM ${context.schemaName}.${factory.tableName} t0`);
                    if (sqlPartWithNaturalKeys.joins.length) sqlParts.push(sqlPartWithNaturalKeys.joins.join(''));
                    const rows = await context.executeSql<AnyRecord[]>(sqlParts.join(' '), sqlPartWithNaturalKeys.args);
                    const factoryCache: Dict<number> = {};
                    rows.forEach(row => {
                        const naturalKeyWithSeparators = sqlPartWithNaturalKeys.columns
                            .map(col => row[col.unaliased])
                            .join('|');
                        factoryCache[naturalKeyWithSeparators] = row._id as number;
                    });
                    sharedTablesNaturalKeysCache[factory.name] = factoryCache;
                },
            );
        });
        return (row: any): any => {
            // Resolve the natural keys
            Object.entries(sharedFactoriesByColumn).forEach(([columnName, factory]) => {
                const naturalKeyWithSeparators = row[columnName] as string;
                if (naturalKeyWithSeparators === '' || naturalKeyWithSeparators == null) return;
                const id = sharedTablesNaturalKeysCache[factory.name][naturalKeyWithSeparators];
                if (id == null) {
                    if (/^\d+$/.test(naturalKeyWithSeparators)) {
                        // It's OK, the natural key has already been mapped to the right _id
                        // This will happen when we load the layers (setup, test, demo, ...) as the
                        // CSV files we are loading are the result of the merge of
                        // xxx.csv + xxx-nullable.csv files and the naturalKeys were
                        // already translated to _ids
                        return;
                    }
                    throw new Error(
                        `Could not resolve '${naturalKeyWithSeparators}' for ${tableName}.${columnName} (targetFactory was ${factory.name})`,
                    );
                }
                row[columnName] = id;
            });
        };
    }

    /**
     * Filters the user table for the root user and create a mapping for it.
     * As we provisioned the root user on the tenant creation, we exclude it from the import
     * but we create a mapping of the reference in order to fix other tables having that reference id
     * @returns the row transformation callback
     */
    private userTransformation(rootUserId: string | number): RowTransformFunction {
        const { userMapping } = this;
        if (!rootUserId) {
            throw new Error('root user must exist and have been loaded before loading user data');
        }
        return (user: any): boolean => {
            if (user.email === rootUserEmail) {
                userMapping[user._id] = rootUserId;
                // mark it as invalid so that it will be excluded
                user.$invalid = true;
            } else if (user.is_demo_persona === dbConstant.true && user.first_name?.toLowerCase() === 'persona') {
                user.is_active = dbConstant.false;
            }
            return true;
        };
    }

    /**
     * Fixes the user references
     *
     * @param userReferenceColumnNames
     * @returns the row transformation callback
     */
    private fixUserReferencesTransformation(userReferenceColumnNames: string[]): RowTransformFunction {
        const { userMapping } = this;
        return (row: any): boolean => {
            // eslint-disable-next-line no-restricted-syntax
            for (const col of userReferenceColumnNames) {
                const userId = userMapping[row[col]];
                if (userId != null) {
                    row[col] = userId;
                }
            }
            return true;
        };
    }

    /**
     * @param context
     * Fixes data applies to any data to be imported:
     *  - it fixes the vendor id
     *  - it sets a default value to _create_stamp if null
     *  - it sets a default value to _update_stamp if null
     */
    private static fixData(rootUserId: string | number, headers?: string[]): RowTransformFunction {
        return (data: any): boolean => {
            if (!data._create_user && headers?.includes('_create_user')) {
                data._create_user = rootUserId;
            }
            if (!data._update_user && headers?.includes('_update_user')) {
                data._update_user = rootUserId;
            }
            ['_create_stamp', '_update_stamp'].forEach(stamp => {
                if (data[stamp] !== undefined && !data[stamp].length && headers?.includes(stamp)) {
                    data[stamp] = Datetime.now();
                }
            });
            return true;
        };
    }
}
