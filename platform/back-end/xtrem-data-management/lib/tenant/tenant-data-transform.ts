import {
    CoreHooks,
    Dict,
    NodeFactory,
    Property,
    adminDemoPersona,
    randomizeUrl,
    unsafeRandomizeCharacters,
} from '@sage/xtrem-core';
import { createDictionary } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { customAlphabet } from 'nanoid';
import { Transform, TransformCallback } from 'stream';
import { loggers } from '../util/loggers';

// 46 thousand years needed, in order to have a 1% probability of at least one collision if we generate 1000 IDs per hour
// see https://zelark.github.io/nano-id-cc/
const shortNanoid = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 16);

export const dbConstant = {
    false: '0',
    true: '1',
};

/** @internal */
export const logger = loggers.tenant;

/** @internal */
export type TransformCsvLine = (lineObject: Dict<string>, properties?: Property[]) => Dict<string>;

/** @internal */
export interface CsvTransformMetadata {
    headers?: string[];
    mapOut: Dict<(value: any) => any>;
    count: number;
}

export interface TransformCsvOptions {
    userTransformContext: UserTransformContext;
    anonymize: boolean;
    chunkSize: number;
}

/** @internal */
export interface CsvFormatter {
    toJson: (value: any) => any;

    toRange: (value: any) => any;

    toText: (value: any) => any;
}

/** @internal */
export function getCsvFormatters(delimiter: string): CsvFormatter {
    const shouldQuoteRegex = new RegExp(`["${delimiter}\n\r]`);
    return {
        toJson: (value: any): any => {
            if (!value.length) return '';
            return value === '{}' ? value : `"${value.replace(/"/g, '""')}"`;
        },

        toRange: (value: any): any => {
            if (!value.length) return '';
            return shouldQuoteRegex.test(value) ? `"${value.replace(/"/g, '""')}"` : value;
        },

        toText: (value: any): any => {
            if (!value) return '""';
            return shouldQuoteRegex.test(value) ? `"${value.replace(/"/g, '""')}"` : value;
        },
    };
}

export class UserTransformContext {
    readonly tableName: string;

    readonly userById = createDictionary<any>();

    readonly referencedIds = createDictionary<boolean>();

    constructor(readonly supportsPersona: boolean) {
        this.tableName = _.snakeCase(CoreHooks.sysManager.getUserNode().name);
    }
}

class CsvLineTransformer {
    constructor(
        readonly factory: NodeFactory,
        readonly properties: Property[],
        readonly options: TransformCsvOptions,
    ) {}

    getAllTransformers(): TransformCsvLine[] {
        const transformers: TransformCsvLine[] = [];
        const { factory, properties, options } = this;
        const anonymize = !!options.anonymize;
        const { userTransformContext } = options;
        const userTableName = userTransformContext.tableName;
        if (factory.tableName === userTableName) {
            transformers.push(CsvLineTransformer.transformUserCsvLine(options.userTransformContext));
        }
        const userReferenceColumns = factory.properties
            .filter(p => p.isReferenceProperty() && p.targetFactory.tableName === userTableName)
            .map(p => p.columnName)
            .filter(col => col != null);
        if (userReferenceColumns.length > 0) {
            transformers.push(
                CsvLineTransformer.collectUserReference(userReferenceColumns, userTransformContext.referencedIds),
            );
        }

        if (!anonymize || !properties) {
            return transformers;
        }
        if (properties.some(p => typeof p.decorator.anonymizeValue === 'function'))
            transformers.push(CsvLineTransformer.transformCustomAnonymize());

        if (properties.some(p => p.decorator.anonymizeMethod === 'perCharRandom'))
            transformers.push(CsvLineTransformer.transformPerCharacterAnonymize());

        if (properties.some(p => p.decorator.anonymizeMethod === 'url'))
            transformers.push(CsvLineTransformer.transformAnonymizeUrl());

        return transformers;
    }

    private static transformPerCharacterAnonymize(): TransformCsvLine {
        return (line: Dict<string>, properties: Property[]): Dict<string> => {
            properties.forEach(prop => {
                if (prop.decorator.anonymizeMethod === 'perCharRandom' && prop.columnName) {
                    const value = unsafeRandomizeCharacters(line[prop.columnName]);
                    line[prop.columnName] = value;
                }
            });
            return line;
        };
    }

    private static transformAnonymizeUrl(): TransformCsvLine {
        return (line: Dict<string>, properties: Property[]): Dict<string> => {
            properties.forEach(prop => {
                if (prop.decorator.anonymizeMethod === 'url' && prop.columnName) {
                    const value = randomizeUrl(line[prop.columnName]);
                    line[prop.columnName] = value;
                }
            });
            return line;
        };
    }

    private static transformCustomAnonymize(): TransformCsvLine {
        return (line: Dict<string>, properties: Property[]): Dict<string> => {
            properties.forEach(prop => {
                if (typeof prop.decorator.anonymizeValue === 'function' && prop.columnName) {
                    const anonymizeValue = prop.decorator.anonymizeValue as (value: string) => string | null;
                    const value = anonymizeValue.call(null, line[prop.columnName]);
                    line[prop.columnName] = value || '';
                }
            });
            return line;
        };
    }

    private static transformUserCsvLine(userTransformContext: UserTransformContext): TransformCsvLine {
        const { supportsPersona, userById } = userTransformContext;
        return (line: Dict<string>): Dict<string> => {
            const user = userById?.[line._id];
            line.is_first_admin_user = dbConstant.false;
            if (
                supportsPersona &&
                (user ?? line).user_type === 'application' &&
                // When processing the nullables we do not have the is_demo_persona but the email has changed
                (line.is_demo_persona !== dbConstant.true || (user && user.email !== line.email))
            ) {
                const lastName = shortNanoid();
                // replace non existing user by an inactive persona
                const replacement = user ?? {
                    email: `persona.${lastName}@localhost.domain`,
                    is_first_admin_user: dbConstant.false,
                    first_name: 'Persona',
                    last_name: lastName,
                    is_active: dbConstant.false,
                    photo: '', // do not disclose it!
                    is_administrator: dbConstant.false, // we do not allow multiple admin personas
                    is_demo_persona: dbConstant.true,
                    is_api_user: dbConstant.false,
                    intacct_id: '', // do not disclose it!
                    record_no: '',
                    _source_id: '',
                    _custom_data: '{}',
                };

                // change only existing columns
                _.forOwn(line, (value, key) => {
                    line[key] = replacement[key] ?? value;
                });
            }
            if (line.email === adminDemoPersona.email) {
                // ensure the admin persona is correctly set
                line.is_active = dbConstant.true;
                line.is_administrator = dbConstant.true;
                line.is_demo_persona = dbConstant.true;
            } else if (line.is_demo_persona === dbConstant.true && line.is_administrator === dbConstant.true) {
                // we do not allow multiple admin personas
                line.is_administrator = dbConstant.false;
                line.is_active = dbConstant.false;
            }
            if (userById && user == null) {
                userById[line._id] = { ...line };
            }
            return line;
        };
    }

    private static collectUserReference(
        userReferenceColumns: string[],
        referencedIds: Dict<boolean>,
    ): TransformCsvLine {
        return (line: Dict<string>): Dict<string> => {
            // eslint-disable-next-line no-restricted-syntax
            for (const col of userReferenceColumns) {
                if (!_.isEmpty(line[col]) && !referencedIds[line[col]]) {
                    referencedIds[line[col]] = true;
                }
            }
            return line;
        };
    }
}

export interface CsvTransformerHandler {
    metadata: CsvTransformMetadata;
    transformer?: Transform;
}

/** @internal */
export function getCsvTransformerHandler(
    factory: NodeFactory,
    properties: Property[],
    options: TransformCsvOptions,
): CsvTransformerHandler {
    const handler: CsvTransformerHandler = {
        metadata: {
            mapOut: {},
            count: 0,
        },
    };

    const lineTransformers = new CsvLineTransformer(factory, properties, options).getAllTransformers();

    if (lineTransformers.length === 0) {
        return handler;
    }

    handler.transformer = new TransformCsv(handler.metadata, lineTransformers, properties, options);
    return handler;
}

class TransformCsv extends Transform {
    #headersDone = false;

    #chunks: string[] = [];

    constructor(
        private readonly metadata: CsvTransformMetadata,
        private readonly lineTransformers: TransformCsvLine[],
        private readonly properties: Property[],
        private readonly options: TransformCsvOptions,
    ) {
        super({ writableObjectMode: true });
    }

    _ensureHeaders(): string[] {
        const { headers } = this.metadata;
        if (headers == null) {
            throw new Error('The CSV content must be parsed before being transformed');
        }
        if (!this.#headersDone) {
            this.#chunks.push(headers.join(';'));
            this.#headersDone = true;
        }
        return headers;
    }

    _flushData(windowsSize: number, cb: TransformCallback): void {
        const chunks = this.#chunks;
        if (chunks.length > windowsSize) {
            this.#chunks = [];
            // write lines
            cb(null, `${chunks.join('\n')}\n`);
            return;
        }
        cb();
    }

    override _transform(row: Dict<any>, _encoding: string, cb: TransformCallback): void {
        const { mapOut } = this.metadata;
        const chunks = this.#chunks;
        const headers = this._ensureHeaders();

        this.lineTransformers.forEach(rowTransformer => {
            rowTransformer(row, this.properties);
        });

        const line = headers.map(header => (mapOut[header] ? mapOut[header](row[header]) : row[header])).join(';');

        this.metadata.count += 1;
        // accumulate lines
        chunks.push(line);
        this._flushData(this.options.chunkSize, cb);
    }

    // eslint-disable-next-line class-methods-use-this
    override _flush(cb: TransformCallback): void {
        this._ensureHeaders();
        this._flushData(0, cb);
    }
}
