import * as artillery from 'artillery';
import * as debugging from 'debug';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as glob from 'glob';
import * as jsYaml from 'js-yaml';
import * as _ from 'lodash';
import { nanoid } from 'nanoid';
import * as fsp from 'path';
import * as hooks from '../hooks';
import { resolveFunc } from '../hooks';

export const debug = debugging('plugin:xtrem');

export const PLUGIN_NAME = 'xtrem';

export interface ScriptConfig {
    _environment: string;
    config: {
        plugins: XtremBenchmarkConfig;
        target: string;
        processor: {
            [name: string]: any;
        };
        payload: any[];
        environments: {
            [name: string]: {
                [name: string]: any;
                [PLUGIN_NAME]: {
                    loginManager: string;
                };
            };
        };
        variables: [{ [name: string]: any }];
    };
    scenarios: {
        [name: string]: any;
        [PLUGIN_NAME]: {
            resources: string[];
        };
        flow: any;
    }[];
}

export interface XtremBenchmarkConfig {
    [PLUGIN_NAME]: {
        processHandlebarsInResources: boolean;
        loginManager: string;
    };
}

function $randomNumber(min: number, max: number): number {
    return _.random(min, max);
}

function $randomString(length: number): string {
    return Math.random().toString(36).substring(2, length);
}

export class Plugin {
    constructor(
        public readonly script: ScriptConfig,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _eventEmitter: EventEmitter,
    ) {
        if (!script.config || !script.config.plugins || !script.config.plugins[PLUGIN_NAME]) {
            throw new Error('No plugin config found');
        }
        debug(`received config: ${JSON.stringify(script.config.plugins[PLUGIN_NAME], null, 4)}`);
        if (!this.script.scenarios) {
            throw new Error('No scenario found');
        }
        this.init();
    }

    init(): void {
        this.parseScenarios();
        this.loadHooks();
        this.loadHandlebarsProcessor();
        this.loadHelpers();
        this.loadLoginManager();
        this.resolveConfigVariables();
    }

    private createContext(): artillery.ScenarioContext {
        const { script } = this;
        const _uid = nanoid();
        const $env = process.env as Record<string, string>;
        return {
            _uid,
            vars: {
                target: script.config.target,
                $uuid: _uid,
                $environment: script._environment,
                $processEnvironment: $env,
                $env,
            },
            funcs: {
                $randomNumber,
                $randomString,
                $template: (input: string) => input,
            },
        } as unknown as artillery.ScenarioContext;
    }

    resolveConfigVariables(): void {
        Object.keys(this.script.config.variables || []).forEach((variable: any) => {
            // @ts-expect-error bad typing to be fixed
            this.script.config.variables[variable] = resolveFunc(this.script.config.variables[variable]);
        });
        const environments = Object.keys(this.script.config.environments);
        environments.forEach(env => {
            Object.keys(this.script.config.environments[env].variables || []).forEach((variable: any) => {
                this.script.config.environments[env].variables[variable] = resolveFunc(
                    this.script.config.environments[env].variables[variable],
                );
            });
        });
    }

    resolveResources(paths: string[]): string[] {
        let resources: string[] = [];
        paths.forEach(resource => {
            const fullPath = Plugin.template(resource, this.createContext());
            resources = [...resources, ...glob.sync(fullPath).map(file => fsp.resolve(file))];
        });
        return resources;
    }

    loadHooks(): void {
        this.script.config.processor = this.script.config.processor || {};
        this.script.config.processor = { ...this.script.config.processor, ...hooks };
        debug(`default hooks: ${Object.keys(hooks)}`);
    }

    parseScenarios(): void {
        const getResourceFile = (keyValue: string, resourceList: string[]): string => {
            const file = resourceList.filter((resource: any) => {
                return fsp.parse(resource).base === keyValue;
            });
            if (file.length === 0) throw new Error(`resource not found for ${keyValue}`);
            if (file.length > 1) throw new Error(`multiple resources for ${keyValue}`);
            return file[0];
        };
        let resources: string[] = [];
        const parser = (obj: any): void => {
            Object.keys(obj).forEach(key => {
                if (obj[key] !== null && typeof obj[key] === 'object') {
                    parser(obj[key]);
                }
                let keyValue = obj[key];
                if (key === PLUGIN_NAME && keyValue.resources) {
                    resources = this.resolveResources(keyValue.resources);
                }

                if (typeof keyValue === 'string') {
                    let resourceFile;
                    const fileExtenstion = fsp.parse(keyValue).ext;
                    switch (fileExtenstion) {
                        case '.yml':
                        case '.yaml':
                            // allow template in yaml file path
                            keyValue = Plugin.template(keyValue, this.createContext());
                            obj[key] = Plugin.readScript(keyValue);
                            parser(obj[key]);
                            break;
                        case '.graphql':
                            if (key === 'query') {
                                resourceFile = getResourceFile(keyValue, resources);
                                obj[key] = fs.readFileSync(resourceFile).toString();
                            }
                            break;
                        default:
                        // do nothing;
                    }
                }
            });
        };
        parser(this.script.scenarios);
        debug(`generated scenario: ${this.script.scenarios}`);
    }

    loadLoginManager(): void {
        Object.keys(this.script.config.environments || {})
            .filter(environment => environment === this.script._environment)
            .forEach(environment => {
                if (this.script.config.environments[environment][PLUGIN_NAME]?.loginManager) {
                    const fncName = Plugin.template(
                        this.script.config.environments[environment][PLUGIN_NAME].loginManager,
                        this.createContext(),
                    );

                    const loginManager = Object.keys(hooks).find(key => key === fncName);

                    if (!loginManager) {
                        throw new Error(`login manager ${fncName} not exists`);
                    }

                    (hooks as any)[loginManager](this.script);
                    debug(`login manager loaded scenario: ${loginManager}`);
                }
            });
    }

    loadHandlebarsProcessor() {
        this.script.scenarios.forEach(scenario => {
            if (this.script.config.plugins[PLUGIN_NAME].processHandlebarsInResources)
                scenario.beforeRequest = [...(scenario.beforeRequest || []), hooks.processHandlebars.name];
        });
    }

    loadHelpers() {
        this.script.scenarios.forEach(scenario => {
            scenario.beforeRequest = [...(scenario.beforeRequest || []), hooks.xtremPluginFunctionsBeforeRequest.name];
            scenario.beforeScenario = [
                ...(scenario.beforeScenario || []),
                hooks.xtremPluginFunctionsBeforeScenario.name,
            ];
        });
    }

    static readScript(path: string) {
        const content = fs.readFileSync(path);
        return jsYaml.load(content.toString());
    }

    static template(o: any, context: artillery.ScenarioContext) {
        return (global as any).artillery.util.template(o, context);
    }
}
