import * as artillery from 'artillery';
import axios from 'axios';
import { ScriptConfig } from '../../../core/plugin';

export function UnsecureDevLogin(script: ScriptConfig) {
    script.scenarios.forEach(scenario => {
        scenario.flow.unshift({ function: getAccessToken.name });
        scenario.beforeRequest = [...(scenario.beforeRequest || []), setAccessTokenCookie.name];
    });
}

export function getCookie(response: any, cookieName: string): string {
    const cookie = response.headers['set-cookie'].join();
    const re = new RegExp(`(?<=${cookieName}=)[^;]*`);
    try {
        return cookie.match(re)[0];
    } catch {
        throw new Error(`Token ${cookieName} does not exist`);
    }
}

export function getAccessToken(
    userContext: artillery.ScenarioContext,
    _event: artillery.EventEmitter,
    done: (err?: Error) => artillery.Next,
) {
    const environment = userContext.vars.$environment;
    const email = userContext.vars.email;
    const tenant = userContext.vars.tenant || '777777777777777777777';
    const url =
        environment === 'local'
            ? `${userContext.vars.target}/unsecuredevlogin?tenant=${tenant}&user=${email}`
            : `https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=${userContext.vars.cluster}&tenant=${tenant}&user=${email}&app=sdmo`;
    // Using the classic .then().catch() as artillery seems to having issues with async/await and returning promises.
    axios
        .get(url, { maxRedirects: 0, validateStatus: status => status === 302 })
        .then(res => {
            const response = res as any;
            const accessToken = getCookie(response, 'access_token');
            const accessTokenSign = getCookie(response, 'access_token_sign');
            userContext.vars.access_token = accessToken;
            userContext.vars.access_token_sign = accessTokenSign;
            return done();
        })
        // See artillery.io doc "Calling the next() callback with an error object will stop the execution of the current VU."
        .catch(error => done(new Error(`Error fetching access token: ${error.stack}`)));
}

export function setAccessTokenCookie(
    req: artillery.RequestParams,
    userContext: artillery.ScenarioContext,
    _event: artillery.EventEmitter,
    next: (err?: Error) => artillery.Next,
) {
    if (!req.headers) req.headers = {};
    // disable cookieJar to avoid redirect to login page.
    (req as any).cookieJar = undefined;
    req.headers = {
        ...req.headers,
        'content-type': 'application/json',
        Cookie: `access_token=${userContext.vars.access_token}; access_token_sign=${userContext.vars.access_token_sign}`,
    };
    return next();
}
