import { assert, expect } from 'chai';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as fsp from 'path';
import { Plugin as ArtilleryPlugin } from '../lib/core/plugin';
import * as hooks from '../lib/hooks/index';
import { getCookie } from '../lib/hooks/index';
import { generateId } from '../lib/hooks/internal/xtrem-function';
import { fullScript as script } from './fixtures/scripts';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
(global as any).artillery = { util: { template: (obj: string, context: any) => obj } };
const plugin = new ArtilleryPlugin(script as any, new EventEmitter());
const userContext = { vars: script.config.variables, $environment: 'cluster-ci', funcs: {} };
const response = {
    statusCode: 400,
    request: { uri: { host: 'localhost', pathname: '/api' } },
    headers: { 'set-cookie': ['access_token=ACCESS_TOKEN; access_token_sign=ACCESS_TOKEN_SIGN'] },
} as any;

describe('Artillery plugin test', () => {
    it('processor should contain all function in hooks', () => {
        assert.deepEqual(Object.keys(plugin.script.config.processor), Object.keys(hooks));
    });
    it('processor should contain all function in hooks', () => {
        plugin.script.scenarios.forEach(scenario => {
            expect(scenario.beforeRequest).to.include(hooks.xtremPluginFunctionsBeforeRequest.name);
            expect(scenario.beforeScenario).to.include(hooks.xtremPluginFunctionsBeforeScenario.name);
        });
    });
    it('should include login manager', () => {
        expect(Object.keys(plugin.script.config.processor)).to.include(hooks.UnsecureDevLogin.name);
        plugin.script.scenarios.forEach(scenario => {
            expect(scenario.beforeRequest).to.include(hooks.setAccessTokenCookie.name);
            expect(scenario.flow).to.deep.include({ function: hooks.getAccessToken.name });
        });
    });
    it('should parse graphql files', () => {
        expect(plugin.script.scenarios[2].flow[1].get.json).to.include({
            query: fs.readFileSync(fsp.join(process.env.PWD!, 'test', 'fixtures', 'simple-query.graphql'), 'utf8'),
        });
    });
    it('should parse yaml files', () => {
        expect(plugin.script.scenarios[3].flow[1].get).to.include({ name: 'from yaml' });
    });
    it('should execute xtrem-functions', () => {
        hooks.xtremPluginFunctionsBeforeScenario(userContext as any, new EventEmitter(), () => true as any);
        assert.isTrue(
            hooks.xtremPluginFunctionsBeforeRequest({}, userContext as any, new EventEmitter(), () => true as any),
        );
        assert.isTrue(
            hooks.xtremPluginFunctionsBeforeScenario(userContext as any, new EventEmitter(), () => true as any),
        );
        const func = generateId(script.config.variables.generateId[0]);
        // eslint-disable-next-line no-console
        console.log('func:', func);
        expect(hooks.resolveFunc(generateId(script.config.variables.generateId[0]))).to.include('PREFIX_');
        assert.deepEqual(plugin.script.config.variables['arrayVal' as any], ['1', '2', '3', '4']);
        assert.equal(plugin.script.config.variables['emptyParams' as any], ['empty']);
    });
    it('default hooks handler should be callable', () => {
        assert.isTrue(hooks.beforeRequestHandler({} as any, userContext as any, new EventEmitter(), () => true as any));
        assert.isTrue(hooks.beforeScenarioHandler({} as any, new EventEmitter(), () => true as any));
        assert.isTrue(
            hooks.afterResponseHandler({} as any, response, {} as any, new EventEmitter(), () => true as any),
        );
        assert.isTrue(hooks.afterScenarioHandler(userContext as any, new EventEmitter(), () => true as any));

        assert.throw(() => hooks.throwErrorOnErrorHandler({} as any, response));
        assert.isTrue(
            hooks.logResponseOnErrorHandler(
                {} as any,
                response,
                userContext as any,
                new EventEmitter(),
                () => true as any,
            ),
        );
    });
    it('shoudl call login manager', () => {
        assert.equal(getCookie(response, 'access_token'), 'ACCESS_TOKEN');
        assert.equal(getCookie(response, 'access_token_sign'), 'ACCESS_TOKEN_SIGN');
        assert.throw(() => getCookie(response, 'not_a_token'), 'Token not_a_token does not exist');
        assert.isTrue(hooks.setAccessTokenCookie({} as any, userContext as any, new EventEmitter(), () => true as any));
        assert.equal(hooks.getAccessToken.name, 'getAccessToken');
    });
    it('shoudl call processHandlebars', () => {
        assert.isTrue(hooks.processHandlebars({} as any, userContext as any, new EventEmitter(), () => true as any));
    });
});
