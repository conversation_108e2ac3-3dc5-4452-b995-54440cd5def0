import { Logger } from '@sage/xtrem-log';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as path from 'path';
import { generatePdfBuffer, GeneratePdfDataOptions } from '../index.js';

Logger.noBanner();

const logger = Logger.getLogger(__filename, 'cli');

let baseDir = process.cwd();

/**
 * CLI entry point for generating a PDF from HTML content.
 * Usage: node cli.js <productName> <reportName> <populatedBodyContent> <paperFormat> <pageOrientation>
 */
export async function runCli() {
    const args = parseArgs();
    const { fromDir } = args;

    if (!fromDir) {
        logger.error('Usage: node cli.js [--from-dir <self-content-directory>]');
        process.exit(1);
    }
    // use the fromDir as the base directory for resolving file paths
    baseDir = path.resolve(fromDir);

    try {
        const timings = {
            start: performance.now(),
            loadOptions: 0,
            generatePdf: 0,
            total: 0,
        };
        // Load options from a hard-coded JSON file relatively to baseDir
        const optionsFile = 'file://report-options.json';
        const options = loadOptionsFromFile(optionsFile);
        // set the output path to a file in the baseDir based on productName and reportName
        options.outputPath = path.join(baseDir, _.camelCase(`${options.productName}--${options.reportName}.pdf`));
        timings.loadOptions = performance.now() - timings.start;
        const pdfBuffer = await generatePdfBuffer(options);
        timings.generatePdf = performance.now() - timings.loadOptions - timings.start;
        timings.total = performance.now() - timings.start;
        logger.info(
            `PDF generation completed in ${timings.total.toFixed(2)} ms (${timings.loadOptions.toFixed(2)} ms to load options, ${timings.generatePdf.toFixed(2)} ms to generate PDF)`,
        );
        logger.info(`Pdf file '${options.outputPath}' with size (${pdfBuffer.length} bytes)`);
    } catch (error) {
        logger.error(`Error generating PDF: ${error.message}`);
        process.exit(1);
    }
}

function parseArgs(): Record<string, any> {
    return process.argv.slice(2).reduce(
        (r, argument) => {
            let arg = argument.trim();
            if (/^--\w/.test(arg)) {
                arg = arg.substring(2);
                const camelName = arg.replace(/-(\w)/g, (m, p1) => p1.toUpperCase());
                r[camelName] = true;
                r.$currentOpt = camelName;
            } else if (r.$currentOpt) {
                r[r.$currentOpt] = arg;
                delete r.$currentOpt;
            } else {
                r._.push(arg);
            }
            return r;
        },
        { _: [] } as any,
    );
}

function loadOptionsFromFile(filePath: string): GeneratePdfDataOptions {
    // Load options from a JSON file
    try {
        const optionsFilePath = resolveFilePath(filePath);
        // Ensure the base directory is set to the directory of the options file
        baseDir = path.dirname(optionsFilePath);
        const content = loadContentFromFile(optionsFilePath, 'Loading options');
        const options = JSON.parse(content, optionsReviver) as GeneratePdfDataOptions;
        if (typeof options !== 'object' || !options) {
            throw new Error('Invalid options file format. Expected a JSON object.');
        }
        if (!options.productName || !options.reportName || !options.populatedBodyContent) {
            throw new Error(
                'Missing required fields in options file: productName, reportName, or populatedBodyContent.',
            );
        }
        if (!options.paperFormat) {
            options.paperFormat = 'a4'; // Default value if not provided
        }
        if (!options.pageOrientation) {
            options.pageOrientation = 'portrait'; // Default value if not provided
        }
        if (!options.reportOptions) {
            options.reportOptions = {}; // Default value if not provided
        }
        // ensure reportOptions has closeBrowser set to true so that the proccess exits after generating the PDF
        options.reportOptions.closeBrowser = true;

        return options;
    } catch (error) {
        logger.error(`Error loading options from file ${filePath}: ${error.message}`);
        process.exit(1);
        return {} as GeneratePdfDataOptions; // This line is unreachable but keeps TypeScript happy
    }
}

function optionsReviver(key: string, value: any): any {
    // Custom reviver function for JSON.parse to handle file paths in options instead of raw content
    if (
        ['populatedBodyContent', 'populatedHeaderContent', 'populatedFooterContent', 'populatedAttachment'].includes(
            key,
        )
    ) {
        if (typeof value === 'string' && value.startsWith('file://')) {
            // If the value starts with 'file://', load the content from the file
            return loadContentFromFile(resolveFilePath(value));
        }
    }
    return value;
}

function loadContentFromFile(filePath: string, prefix = 'Loading content'): string {
    logger.info(`${prefix} from file: ${filePath}`);
    // Load content from a file
    try {
        return fs.readFileSync(filePath, 'utf-8').trim();
    } catch (error) {
        logger.error(`Error ${prefix.toLowerCase()} from file ${filePath}: ${error.message}`);
        process.exit(1);
        return ''; // This line is unreachable but keeps TypeScript happy
    }
}

function resolveFilePath(filePath: string): string {
    // Resolve a file path to an absolute path
    if (!filePath) {
        throw new Error('File path is required.');
    }
    let file = filePath;
    if (file.startsWith('file://')) {
        file = file.replace(/^file:\/\//, '');
    }
    // Resolve to absolute path and prevent path traversal
    const resolvedPath = path.join(baseDir, file);
    if (!resolvedPath.startsWith(baseDir)) {
        throw new Error(`Invalid file path: ${file} (path traversal detected)`);
    }
    if (!fs.existsSync(resolvedPath)) {
        throw new Error(`File ${resolvedPath} does not exist.`);
    }
    return resolvedPath;
}
