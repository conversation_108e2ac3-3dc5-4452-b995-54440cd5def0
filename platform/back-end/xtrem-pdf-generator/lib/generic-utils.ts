import { constants, promises as fs } from 'fs';

export const WIZARD_PREVIEW_TEMPLATE_NAME = 'wizardPreviewTemplate';

export const checkFileExists = async (filePath: string): Promise<boolean> => {
    try {
        await fs.access(filePath, constants.F_OK);
        return true;
    } catch {
        return false;
    }
};

export function sleepMillis(ms: number): Promise<void> {
    return new Promise(resolve => {
        setTimeout(resolve, ms);
    });
}
