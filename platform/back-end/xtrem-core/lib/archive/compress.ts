import { AnyValue } from '@sage/xtrem-async-helper';
import { SystemError } from '@sage/xtrem-shared';
import { Archiver } from 'archiver';
import * as fs from 'fs';
import { Writable } from 'stream';
import { ZlibOptions } from 'zlib';
import { fileExists } from '../file-utils';
import { loggers } from '../runtime/loggers';

export interface ZipOptions {
    comment?: string; // Sets the zip archive comment.
    forceLocalTime?: boolean; // Forces the archive to contain local file times instead of UTC.
    forceZip64?: boolean; // Forces the archive to contain ZIP64 headers.
    store?: boolean; // Sets the compression method to STORE.
    zlib?: ZlibOptions; // Passed to zlib to control compression.
}

export abstract class Compress {
    private static _createArchiver(...args: AnyValue[]): Archiver {
        // eslint-disable-next-line global-require
        return require('archiver')(...args);
    }

    static async zipDirectory(sourceDir: string, targetZip: string, options?: ZipOptions): Promise<void> {
        if (!(await fileExists(sourceDir)))
            throw new SystemError(`${sourceDir}: cannot zip: source dir does not exist`);
        await Compress.zipDirectoryToStream(sourceDir, fs.createWriteStream(targetZip), options);
    }

    static async zipDirectoryToStream(sourceDir: string, output: Writable, options?: ZipOptions): Promise<void> {
        await new Promise<void>((resolve, reject) => {
            const archive = Compress._createArchiver('zip', options);

            output.on('close', resolve);
            output.on('end', resolve);

            archive.on('error', reject);

            archive.pipe(output);
            archive.directory(sourceDir, false);
            archive.finalize().catch(err => loggers.compress.error(err));
        });
    }

    static async zipFile(fullpath: string, filename: string, outFilename: string, options?: any): Promise<void> {
        await new Promise<void>((resolve, reject) => {
            const output = fs.createWriteStream(outFilename);
            const archive = Compress._createArchiver('zip', options);

            output.on('close', resolve);

            archive.on('error', reject);

            archive.pipe(output);

            archive.append(fs.createReadStream(fullpath), { name: filename });
            archive.finalize().catch(err => loggers.compress.error(err));
        });
    }
}
