/** @packageDocumentation @module runtime */
import { AnyR<PERSON><PERSON>, AnyValue, getAsync, UnPromised } from '@sage/xtrem-async-helper';
import { Decimal } from '@sage/xtrem-decimal';
import { AsyncResponse, Dict, integer } from '@sage/xtrem-shared';
import { Collection, datetime, OrderBy } from '.';
import { InternalAttachmentAssociationData } from '../application/attachment-manager';
import { InternalNoteAssociationData } from '../application/note-manager';
import { NodeState } from '../node-state';
import { Property } from '../properties/property';
import { Context, NodeDeleteOptions } from '../runtime/context';
import type { NodeFactory } from '../runtime/node-factory';
import { SyncInfo } from '../synchronization/sync-info';
import { NodePayloadData, NodeUpdateData } from './create-data';
import { AnyNode, Node } from './node';
import { NodeStatus } from './node-status';

export interface NodeSaveOptions {
    controlOnly?: boolean;
    useUpsert?: boolean;
    /**
     * Should the save be deferred at the end of the transaction ?
     */
    deferred?: boolean;

    /**
     * Flushes the deferred actions at the end of the save
     * @see context.flushDeferredActions()
     */
    flushDeferredActions?: boolean;
}

export interface NodePayloadOptions<T> {
    omitDefaultValues?: boolean;
    inputValuesOnly?: boolean;
    withIds?: boolean;
    withoutCustomData?: boolean;
    thunkOnly?: boolean;
    propertyNames?: PayloadSelector<T>;
    returnEnumsAsNumber?: boolean;
    convertScalarValue?: (value: AnyValue) => AnyValue;
    withLayer?: boolean;
    withTimeStamps?: boolean;
    withNaturalKeyWhenThunk?: boolean;
    limitCollections?: number;
}

export type PayloadSelector<T> = {
    [K in keyof T]?: T[K] extends Promise<infer U> ? (U extends Node | null ? PayloadSelector<U> : boolean) : T[K];
};

export type PropertyName<T extends Node> = Exclude<keyof T, symbol | number>;

/** System properties and methods accessible from `node.$` */
export class Node$<NodeT extends Node> {
    /** @internal */
    readonly state: NodeState;

    /** @internal */
    constructor(state: NodeState) {
        this.state = state;
    }

    /** returns the node's status */
    get status(): NodeStatus {
        return this.state.nodeStatus;
    }

    /** Is the node readonly? */
    get isReadonly(): boolean {
        return this.state.isReadonly;
    }

    /**
     * Is the node effectively readonly?
     *
     * Writable states are put in readonly mode during the execution of some rules
     * (defaultValue, updatedValue, control, controlDelete).
     * This accessor returns true while these rules are executed, even if the node is writable.
     */
    get isEffectivelyReadonly(): boolean {
        return this.state.isEffectivelyReadonly;
    }

    /** Is the node writable? (opposite of isReadonly) */
    get isWritable(): boolean {
        return this.state.isWritable;
    }

    /** Was the node created (rather than read)  */
    get isNew(): boolean {
        return this.state.isNew;
    }

    get isOnlyForDefaultValues(): boolean {
        return this.state.isOnlyForDefaultValues;
    }

    /** return the decrypted value */
    decryptValue<K extends keyof NodeT>(propertyName: K): Promise<string> {
        return this.context.vault.decrypt(this.state.values[propertyName as string] as string);
    }

    /** the _context_ in which the node was created. */
    get context(): Context {
        return this.state.context;
    }

    /** The state of the object at the beginning of the transaction, before any changes made to it */
    get old(): Promise<NodeT> {
        return this.state.old as Promise<NodeT>;
    }

    /** The factory that provides metadata for the node instance */
    get factory(): NodeFactory {
        return this.state.factory;
    }

    /**
     * The node's ID, a unique integer for all nodes of the same node class.
     *
     * Same as `node._id`
     */
    get id(): number {
        return this.state.id;
    }

    /**
     * The node's source ID, an ID that relates the record to the source system.
     *
     * Same as `node._sourceId`
     */
    get sourceId(): Promise<string> {
        return this.state.getValue('_sourceId');
    }

    /**
     * The node's layer.
     *
     * Same as `node._layer`
     */
    get layer(): Promise<number> {
        return this.state.getValue('_layer');
    }

    /**
     * The node's sort value. Only valid on vital child nodes
     */
    get sortValue(): Promise<integer> {
        return this.state.sortValue;
    }

    async setSortValue(value: integer): Promise<void> {
        await this.state.setSortValue(value);
    }

    /** The _createUser of the node */
    get createdBy(): Promise<Node | number> {
        return this.state.createdBy;
    }

    /** The _updateUser of the node */
    get updatedBy(): Promise<Node | number> {
        return this.state.updatedBy;
    }

    /** The _createStamp of the node */
    get createStamp(): Promise<datetime> {
        return this.state.createStamp;
    }

    /** The _updateStamp of the node */
    get updateStamp(): Promise<datetime> {
        return this.state.updateStamp;
    }

    /** The _syncTick of the node */
    get syncTick(): Promise<Decimal> {
        return this.state.syncTick;
    }

    /** The _syncInfo of the node */
    get syncInfo(): Promise<SyncInfo> {
        return this.state.syncInfo;
    }

    /**
     * The vendor. Only valid if the node has a _vendor property
     */
    get vendor(): Promise<Node | null> {
        return this.state.vendor;
    }

    get etag(): Promise<string> {
        return this.state.etag;
    }

    get customData(): Promise<object> {
        return this.state.customData;
    }

    get valuesHash(): Promise<string> {
        return this.state.valuesHash;
    }

    get attachments(): Collection<InternalAttachmentAssociationData & Node> {
        return this.state.attachments;
    }

    get notes(): Collection<InternalNoteAssociationData & Node> {
        return this.state.notes;
    }

    getCursorValue(orderBy: OrderBy<NodeT>): Promise<string> {
        return this.state.getCursorValue(orderBy);
    }

    /** Controls the node before saving it. */
    control(options?: NodeSaveOptions): Promise<boolean> {
        return this.state.control(options);
    }

    /**
     * Saves a newly created node or an updated node to the database.
     * If the nodes has vital children, they are also saved.
     * This method executes all the validation rules before saving.
     * It throws and does not modify the database if some validation rules raise an error.
     */
    async save(options?: NodeSaveOptions): Promise<void> {
        await this.state.save(options);
    }

    /**
     * `node.$.save` with a boolean return value.
     * Returns true if node has been saved, false if some validation rules failed.
     */
    trySave(options?: NodeSaveOptions): Promise<boolean> {
        return this.state.trySave(options);
    }

    async update(data: NodeUpdateData<NodeT>): Promise<void> {
        await this.set(data);
        await this.save();
    }

    /**
     * Deletes the node.
     *
     * All the nodes that are related via _vital_ references or collections will also be deleted.
     *
     * This call triggers the `delete` rules on the node decorators and throws an error if any
     * rule forbids the deletion.
     * An error will also be thrown if the deletion violates foreign key constraints in the database
     * (for example an attempt to delete a product which is referenced from sales documents).
     */
    delete(options?: NodeDeleteOptions): Promise<void> {
        return this.state.delete(options);
    }

    /**
     * Tries to delete the node.
     *
     * Similar to `delete()` but returns a true value in case of success and a false value otherwise,
     * instead of throwing an exception.
     */
    tryDelete(options?: NodeDeleteOptions): Promise<boolean> {
        return this.state.tryDelete(options);
    }

    /**
     * Sets multiple property values in a single call.
     * @param data the values, as a plain object. `data` may contain values for collection properties, as arrays of plain objects
     *
     * Note: `node.$.set(data)` only assigns the values. It does not save the node to the database.
     * To save the node, call `node.$.save()`.
     *
     * @example
     * ```
     * user.$.set({ lastName: 'Smith', children: [{name: 'Mary' }, {name: 'Tim'}] })
     * ```
     */
    async set(data: NodeUpdateData<NodeT>): Promise<void> {
        this.context.prefetcher.visit(this.factory, true, data as AnyRecord);
        await this.state.set(data as NodeUpdateData<Node>);
    }

    /**
     * Gets the value of a property, given the property name
     */
    async getValue<
        ValT extends AnyValue = AnyValue,
        K extends PropertyName<NodeT> | '' = '',
        ResultT = K extends PropertyName<NodeT> ? UnPromised<NodeT[K]> & ValT : ValT,
    >(
        propertyName: K | string, // be lenient and accept any string, for generic calls
    ): Promise<(ResultT & ValT) | undefined> {
        // TODO: we should use findProperty here, which throws if the property is not found.
        // But returning undefined ensures compat with some existing unit tests.
        // const property = this.factory.findProperty(propertyName);
        const property = this.factory.propertiesByName[propertyName];
        if (!property) return undefined;
        return (await this.state.getPropertyValue(property)) as ResultT & ValT;
    }

    getRawPropertyValue(propertyName: string): AnyValue {
        return this.state.values[propertyName];
    }

    getRawPropertyValues(): Dict<AnyValue> {
        return Object.freeze({ ...this.state.values });
    }

    isValueDeferred(propertyName: string): boolean {
        return this.state.isPropertyValueDeferred(this.factory.findProperty(propertyName));
    }

    /**
     * Get the natural key value as a string (concatenated with '|' if composite).
     * Throws if the node factory does not have a natural key.
     */
    getNaturalKeyValue(): Promise<string> {
        return this.state.getNaturalKeyValue();
    }

    /** The key token, a string that uniquely identifies a node within its factory */
    get keyToken(): string {
        return this.state.keyToken;
    }

    /**
     * Does the node have a property with a given name
     */
    hasProperty(propertyName: string): boolean {
        return !!this.factory.propertiesByName[propertyName];
    }

    /**
     * Returns whether the property is frozen
     */
    isPropertyFrozen(property: Property): AsyncResponse<boolean> {
        return this.state.isPropertyFrozen(property);
    }

    /**
     * The node's property values as a plain TypeScript object which may be serialized
     * with JSON.stringify().
     */
    async payload(options?: NodePayloadOptions<NodeT>): Promise<NodePayloadData<NodeT>> {
        return (await this.state.payload(options as NodePayloadOptions<AnyNode>)) as NodePayloadData<NodeT>;
    }

    /**
     * Untyped variant of this.payload(options?). For compat with exising code.
     */
    async payloadAsAny<V extends Dict<any> = Dict<any>>(options?: NodePayloadOptions<NodeT>): Promise<V> {
        // TODO: investigate `unknown` cast introduced during typescript 4.6 upgrade
        return (await this.payload(options)) as unknown as V;
    }

    async isPropertyEnabled<K extends keyof NodeT>(propertyName: K): Promise<boolean> {
        const prop = this.factory.propertiesByName[propertyName as string];
        if (!(await this.context.isEnabledByServiceOptions(prop))) {
            return false;
        }
        return true;
    }

    /**
     * Return a duplicate of the node with negative _id values to create a copy
     * Where the node is a vitalCollectionChild, remove sort value and keep parent _id
     */
    async duplicate(data?: AnyRecord): Promise<NodeT> {
        return (await this.state.duplicate(data)).node as NodeT;
    }

    /**
     * Return the value of a property from a path
     */
    get(path: string): Promise<AnyValue> {
        return getAsync(this.state.node as AnyNode, path);
    }

    /**
     * Special method for x3-gateway to clear the caches
     */
    clearCaches(): void {
        this.state.references.clear();
        this.state.referenceArrays.clear();
        this.state.collections.clear();
    }

    /**
     * Special method used only by ItemSite.
     * TODO: investigate why applicative code really needs this and remove if possible.
     */
    get isInsideCollection(): boolean {
        return !!this.state.collection;
    }
}
