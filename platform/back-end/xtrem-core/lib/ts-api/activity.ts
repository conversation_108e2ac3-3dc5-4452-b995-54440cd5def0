import { Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import * as fsp from 'path';
import { Application } from '../application';
import { ActivityManager } from '../application/activity-manager';
import { StaticThis } from '../decorators';
import { ForeignNodeProperty } from '../properties/foreign-node-property';
import { NodeFactory } from '../runtime';
import { SystemProperties } from '../runtime/system-properties';
import { ActivityExtension } from './activity-extension';
import { Node } from './node';

/**
 * Grant the permissions on a set of activities
 */
export interface PermissionGrant {
    permissions: string[];
    on: (() => Activity)[];
}

/**
 * Grant the operations from a set of nodes
 */
export interface OperationGrant {
    operations: string[];
    on?: (() => { new (): Node })[];
}

export class InternalBaseGrant {
    constructor(readonly type: string) {}

    /**
     * Key that uniquely identifies the grant
     */
    // eslint-disable-next-line class-methods-use-this
    get key(): string {
        return '';
    }

    /**
     * This is an instance of a InternalOperationGrant
     * @returns
     */
    isOperationGrant(): this is InternalOperationGrant {
        return this.type === 'operationGrant';
    }

    /**
     * This is an instance of a InternalPermissionGrant
     * @returns
     */
    isPermissionGrant(): this is InternalPermissionGrant {
        return this.type === 'permissionGrant';
    }
}

export class InternalOperationGrant extends InternalBaseGrant {
    constructor(
        readonly operation: string,
        readonly node: string,
    ) {
        super('operationGrant');
    }

    /**
     * Key that uniquely identifies the grant
     */
    override get key(): string {
        return `${this.node}.${this.operation}`;
    }
}

export class InternalPermissionGrant extends InternalBaseGrant {
    constructor(
        readonly permission: string,
        readonly activity: string,
    ) {
        super('permissionGrant');
    }

    /**
     * Key that uniquely identifies the grant
     */
    override get key(): string {
        return `${this.activity}.${this.permission}`;
    }
}

/**
 * Represents the structure of a flattened permission
 * {read: ['SalesOrder','SalesOrderLine']}
 */
export type FlattenedPermission = Dict<string[]>;

/**
 * Base of the definition passed into the Activity and activity extension constructors
 */
export interface BaseActivityDefinition {
    permissions: string[];
    operationGrants?: Dict<OperationGrant[]>;
    permissionGrants?: Dict<PermissionGrant[]>;
}

/**
 * definition passed into the Activity constructor
 */
export interface ActivityDefinition extends BaseActivityDefinition {
    description: string;
    node: () => { new (): Node };
    __filename: string;
}

export class Activity {
    #package: string;

    #name: string;

    // SECURITY NOTE: Always use Object.create(null) to prevent from prototype pollutions
    #internalGrants: Dict<Dict<InternalOperationGrant | InternalPermissionGrant>> = Object.create(null);

    #permissions: string[];

    #permissionGrants: Dict<PermissionGrant[]> | undefined;

    #operationGrants: Dict<OperationGrant[]> | undefined;

    constructor(private readonly definition: ActivityDefinition) {
        this.#permissions = definition.permissions;
        this.#permissionGrants = definition.permissionGrants;
        this.#operationGrants = definition.operationGrants;
    }

    /**
     * Description of activity
     */
    get description(): string {
        return this.definition.description;
    }

    /**
     * Node related to the activity
     */
    get node(): StaticThis<Node> {
        return this.definition.node();
    }

    /**
     * Activity name derived from the file name
     */
    get name(): string {
        if (!this.#name) this.#name = _.camelCase(fsp.basename(this.definition.__filename).replace(/\.(js|ts)$/, ''));
        return this.#name;
    }

    /**
     * list of permission of the activity
     */
    get permissions(): string[] {
        return this.#permissions;
    }

    /**
     * list of operationGrants of the activity
     */
    get operationGrants(): Dict<OperationGrant[]> | undefined {
        return this.#operationGrants;
    }

    /**
     * list of operationGrants of the activity
     */
    get permissionGrants(): Dict<PermissionGrant[]> | undefined {
        return this.#permissionGrants;
    }

    /**
     * The root package of the activity
     */
    get package(): string {
        return this.#package;
    }

    /* @internal */
    set package(packageName: string) {
        this.#package = packageName;
    }

    // SECURITY NOTE: Always use Object.create(null) to prevent from prototype pollutions
    #flattenedPermission: Dict<FlattenedPermission> = Object.create(null);

    /* @internal */
    set flattenedPermissions(value: Dict<FlattenedPermission>) {
        this.#flattenedPermission = value;
    }

    /** Returns a flattened version of the permissions. For instance, in the following example:
     *  - lookup grants lookup on the current node, but also on Sales and Purchases.
     *  - read grants read on the current node, but also on Journal, and grants lookup on Journal and Sales.
     * {
     *       lookup: {
     *           lookup: [Sales, Purchases]
     *       },
     *       read: {
     *           read: [Journal],
     *           lookup: [Journal, Sales],
     *       }
     * },
     */
    get flattenedPermissions(): Dict<FlattenedPermission> {
        return this.#flattenedPermission;
    }

    mergeExtension(activityExtension: ActivityExtension): void {
        activityExtension.permissions.forEach(permission => {
            if (!this.#permissions.includes(permission)) this.#permissions.push(permission);
        });

        this.#permissionGrants = _.mergeWith(this.#permissionGrants, activityExtension.permissionGrants, (a, b) => {
            if (_.isArray(a) || _.isArray(b)) {
                return _.uniqWith([...(a || [])].concat(b || []), _.isEqual);
            }
            return undefined;
        });

        this.#operationGrants = _.mergeWith(this.#operationGrants, activityExtension.operationGrants, (a, b) => {
            if (_.isArray(a) || _.isArray(b)) {
                return _.uniqWith([...(a || [])].concat(b || []), _.isEqual);
            }
            return undefined;
        });
    }

    /**
     * Add a grant to the grants list if it does not already exist in the list
     * / @internal */
    addGrant(permission: string, grant: InternalOperationGrant | InternalPermissionGrant): boolean {
        if (this.#internalGrants[permission] == null) {
            // SECURITY NOTE: Always use Object.create(null) to prevent from prototype pollutions
            this.#internalGrants[permission] = Object.create(null);
        }

        const permissionMap = this.#internalGrants[permission];
        if (permissionMap[grant.key] == null) {
            permissionMap[grant.key] = grant;
            return true;
        }

        return false;
    }

    get internalGrants(): Dict<Dict<InternalOperationGrant | InternalPermissionGrant>> {
        return this.#internalGrants;
    }

    private addNodeOperation(operationFactory: NodeFactory, operationName: string): void {
        if (ActivityManager.checkOperation(this.name, operationName, operationFactory)) {
            this.addGrant(operationName, new InternalOperationGrant(operationName, operationFactory.name));

            // For the operations 'create', 'update', 'delete' the 'read' operation is implicitly granted
            // For the operation 'read' the 'lookup' operation is implicitly granted
            // read and lookup are added as activity grants to the the current activity
            // As we resolve activity grants at the end, all the relevant node grants will be filled, without the need
            // of iterating the grant list again
            if (['create', 'update', 'delete', 'read'].includes(operationName)) {
                if (operationName !== 'read') {
                    this.addGrant(operationName, new InternalPermissionGrant('read', this.name));
                }
                this.addGrant(operationName, new InternalPermissionGrant('lookup', this.name));
            }
        }
    }

    private fillFactoryGrants(factory: NodeFactory): void {
        const { operationGrants, permissionGrants, permissions } = this;
        permissions.forEach(permission => this.addNodeOperation(factory, permission));

        if (operationGrants) {
            // Add all operations granted by operationGrants
            Object.keys(operationGrants).forEach(operationGrantKey => {
                this.addNodeOperation(factory, operationGrantKey);
                operationGrants[operationGrantKey].forEach(operationGrant => {
                    const currentOperations = operationGrant.operations;
                    currentOperations.forEach(operation => {
                        (operationGrant.on || [() => this.node]).forEach(node => {
                            this.addGrant(operationGrantKey, new InternalOperationGrant(operation, node().name));
                        });
                    });
                });
            });
        }

        if (permissionGrants) {
            // fill the activity grants
            Object.keys(permissionGrants).forEach(permissionGrantKey => {
                if (ActivityManager.checkOperation(this.name, permissionGrantKey, factory))
                    this.addGrant(permissionGrantKey, new InternalOperationGrant(permissionGrantKey, factory.name));
                permissionGrants[permissionGrantKey].forEach(permissionGrant => {
                    const currentPermissions = permissionGrant.permissions;
                    currentPermissions.forEach(permission => {
                        permissionGrant.on.forEach(activity => {
                            this.addGrant(permissionGrantKey, new InternalPermissionGrant(permission, activity().name));
                        });
                    });
                });
            });
        }

        // If the permissions contain node operations of the activity node they are implicitly granted
        this.permissions.forEach(permission => {
            if (ActivityManager.checkOperation(this.name, permission, factory)) {
                this.addGrant(permission, new InternalOperationGrant(permission, this.node.name));
            }
        });
    }

    /**
     * First pass to fill the activity grant list based on the activity definition
     *  @internal */
    fillInternalGrants(application: Application): void {
        const factory = application.getFactoryByName(this.node.name);

        this.fillFactoryGrants(factory);

        // If factory is abstract, grant lookup to all subnodes of this base node
        if (factory.isAbstract) {
            application
                .getAllFactories()
                .filter(nodeFactory => nodeFactory.isSubNodeOf(factory.name))
                .forEach(subNodeFactory => {
                    this.fillFactoryGrants(subNodeFactory);
                });
        }
    }

    /**
     * Second pass this method fills the inherited grants (Grants inherited by the entries in the current list of grants)
     * It will be continuously called, checking and adding any new grants and will return false once it has not added any new grants to the list,
     * flagging that the cycle of calls should end, breaking saturation loop.
     * @internal */
    fillInheritedInternalGrants(application: Application, lookupFactoriesMap: Dict<NodeFactory[]>): boolean {
        const allGrants = Object.entries(this.#internalGrants);
        // eslint-disable-next-line no-restricted-syntax
        for (const [permission, permissionGrants] of allGrants) {
            const grants = Object.values(permissionGrants).filter(grant => grant.isOperationGrant());

            if (this.addOperationGrants(application, Object.values(grants), permission, lookupFactoriesMap)) {
                return true;
            }
        }
        return false;
    }

    private addOperationGrants(
        application: Application,
        operationGrants: InternalOperationGrant[],
        permission: string,
        lookupFactoriesMap: Dict<NodeFactory[]>,
    ): boolean {
        // eslint-disable-next-line no-restricted-syntax
        for (const grant of operationGrants) {
            const factory = application.getFactoryByName(grant.node);
            if (['create', 'update', 'delete', 'read', 'lookup'].includes(grant.operation)) {
                // For operations 'create', 'update', 'delete', read and lookup are implicitly granted
                // For operation 'read', lookup is implicitly granted
                if (grant.operation !== 'lookup') {
                    if (
                        grant.operation !== 'read' &&
                        this.addGrant(permission, new InternalOperationGrant('read', grant.node))
                    ) {
                        return true;
                    }
                    if (this.addGrant(permission, new InternalOperationGrant('lookup', grant.node))) {
                        return true;
                    }
                }
                if (this.addExtendedVitalFactoriesGrants(factory, grant, permission)) {
                    return true;
                }
            }
            // Lookup does not grant lookup on foreign node properties
            if (grant.operation !== 'lookup') {
                // lookup on the nodes of foreign node properties are inherited

                const foreignFactories =
                    lookupFactoriesMap[factory.name] ??
                    factory.properties
                        .filter(
                            prop =>
                                prop.isForeignNodeProperty() &&
                                ((prop.isReferenceProperty() && prop.canLookup) || !prop.isReferenceProperty()) &&
                                !SystemProperties.isUserManagementProperty(prop) &&
                                !SystemProperties.isAttachmentsProperty(prop) &&
                                !SystemProperties.isNotesProperty(prop) &&
                                !prop.isVitalParent,
                        )
                        .map(prop => (prop as ForeignNodeProperty).targetFactory);

                if (!lookupFactoriesMap[factory.name]) lookupFactoriesMap[factory.name] = foreignFactories;
                // eslint-disable-next-line no-restricted-syntax
                for (const foreignFactory of foreignFactories) {
                    if (this.addGrant(permission, new InternalOperationGrant('lookup', foreignFactory.name))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Adds grants from all children node on the vital graph
     * @param factory
     * @param grant
     * @param permission
     * @returns `true` if the grants were already added
     */
    private addExtendedVitalFactoriesGrants(
        factory: NodeFactory,
        grant: InternalOperationGrant,
        permission: string,
    ): boolean {
        // eslint-disable-next-line no-restricted-syntax
        for (const vitalChildFactory of factory.extendedVitalFactories) {
            // Vital children -
            // For operations 'create', 'update', 'delete', read and lookup are implicitly granted
            // For operation 'read', lookup is implicitly granted
            if (this.addGrant(permission, new InternalOperationGrant(grant.operation, vitalChildFactory.name))) {
                return true;
            }

            switch (grant.operation) {
                case 'lookup':
                    // Nothing further to grant
                    break;
                case 'read':
                    // grant lookup only
                    if (this.addGrant(permission, new InternalOperationGrant('lookup', vitalChildFactory.name))) {
                        return true;
                    }
                    break;
                default:
                    // grant read and lookup
                    if (
                        this.addGrant(permission, new InternalOperationGrant('read', vitalChildFactory.name)) ||
                        this.addGrant(permission, new InternalOperationGrant('lookup', vitalChildFactory.name))
                    ) {
                        return true;
                    }
                    break;
            }
        }
        return false;
    }

    getLocalizedTitleKey(): string {
        return `${this.#package}/activity__${_.snakeCase(this.name)}__name`;
    }

    getPermissionLocalizedTitleKey(permission: string): string {
        return `${this.#package}/permission__${_.snakeCase(permission)}__name`;
    }
}
