/* eslint-disable class-methods-use-this */
import { AsyncResponse, LogicError } from '@sage/xtrem-shared';
import { WorkflowManagerInterface } from './workflow-manager-interface';
import { WorkflowStepDescriptor, WorkflowStepInterface } from './workflow-step-descriptor';

export class WorkflowManagerStub implements WorkflowManagerInterface {
    registerWorkflowStepConstructor(packageName: string, workflowStep: WorkflowStepInterface): never {
        throw new LogicError(`Cannot register workflow step ${packageName}.${workflowStep.descriptor.key} in stub`);
    }

    getWorkflowStepDescriptors(): AsyncResponse<WorkflowStepDescriptor[]> {
        return [];
    }

    getWorkflowStepDescriptor(key: string): never {
        throw new LogicError(`Cannot find workflow step ${key} in stub`);
    }

    getWorkflowTopics(): string[] {
        return [];
    }

    runTest(): never {
        throw new LogicError('Cannot runTest workflow in stub');
    }

    start(): never {
        throw new LogicError('Cannot start workflow engine in stub');
    }
}
