import { AsyncResponse, asyncArray, funnel } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import {
    ClientArtifactUsedLiterals,
    LocalizedLiteral,
    appendLiterals,
    getLiteral,
    getLocaleFromHeader,
} from '@sage/xtrem-i18n';
import { AccessStatus, Dict, MetaCustomFieldsArray, MetaCustomizableNodesArray, SystemError } from '@sage/xtrem-shared';
import * as fs from 'fs/promises';
import { camelCase, kebabCase, snakeCase, startCase, trim, union, uniq } from 'lodash';
import { minimatch } from 'minimatch';
import * as path from 'path';
import { Application } from '../application';
import { fileExists } from '../file-utils';
import { expandNodeNames } from '../graphql/metadata-schema/metadata-schema-utils';
import { Context } from '../runtime/context';
import { CoreHooks, NodeExportTemplate } from '../runtime/core-hooks';
import { loggers } from '../runtime/loggers';
import { TextStream, getNameWithoutPackage } from '../types';
import {
    ApplicationArtifact,
    ArtifactType,
    ClientArtifactMetadata,
    ClientArtifactType,
    ClientService,
    JsPackArtifact,
    MetaPackArtifact,
    NodeAccess,
    PageArtifact,
} from './pages/client-service';

enum StorageStatus {
    notStarted,
    done,
}

export enum ArtifactDataTypeEnum {
    json = 1,
    js = 2,
    csv = 3,
    ts = 4,
    meta = 5,
}

export type ArtifactDataType = keyof typeof ArtifactDataTypeEnum;

export interface PackArtifactInterface {
    packageName: string;
    path: string;
    content: TextStream | ClientArtifactMetadata;
    dataType: ArtifactDataType;
    condition: (pack: PackArtifactInterface) => boolean;
    type?: string;
}

export const generateEnumKey = (e: string): string => {
    const parts = e.split('/');
    return `${parts[0]}/${parts[1]}/enums__${snakeCase(parts[2])}`;
};

function pathExtname(filepath: string): string {
    const extension = path.extname(filepath);
    return extension ? `${pathExtname(path.basename(filepath, extension))}${extension}` : extension;
}

async function readArtifactContent(filepath: string): Promise<TextStream> {
    return TextStream.fromString(await fs.readFile(filepath, 'utf-8'));
}

export function getArtifactDataType(fileName: string): ArtifactDataType | undefined {
    switch (pathExtname(fileName)) {
        case '.csv':
            return 'csv';
        case '.json':
            return 'json';
        case '.ts':
            return 'ts';
        case '.meta.json':
            return 'meta';
        case '.js':
            return 'js';
        default:
            break;
    }
    return undefined;
}

export interface ArtifactFilter {
    packageOrPage: string;
    exactMatch: boolean;
    pageNode?: string;
}

export class ArtifactManager {
    static progress = StorageStatus.notStarted;

    // SECURITY NOTE: Always use Object.create(null) to prevent from prototype pollutions
    private static _applicationArtifactsByType: Dict<Dict<ApplicationArtifact | null>> = Object.create(null);

    private static _packArtifactByKey: Dict<PackArtifactInterface> = Object.create(null);

    private static ensureArtifactsCachedFunnel = funnel(1);

    static clearStored(): void {
        this._applicationArtifactsByType = Object.create(null) as Dict<Dict<ApplicationArtifact>>;
        this.progress = StorageStatus.notStarted;
    }

    /**
     * Ensure that all pack artifacts of the application are cached in memory prior to retrieval
     * This is fired once per container with an adivisory lock to avoid the artifact being inserted twice into cache.
     *
     * @param application
     */
    private static async ensureArtifactsCached(context: Context): Promise<void> {
        if (this.progress === StorageStatus.done) {
            return;
        }
        await this.ensureArtifactsCachedFunnel(async () => {
            // We don't want the resolving of pages metadata and the resolving of the sitemap to execute this function in parallel
            if (this.progress !== StorageStatus.done) {
                const artifactTypes = [
                    ClientArtifactType.stickers,
                    ClientArtifactType.pages,
                    ClientArtifactType.pageExtensions,
                    ClientArtifactType.pageFragments,
                    ClientArtifactType.widgets,
                ];

                await asyncArray(artifactTypes).forEach(artifactType => this.cacheArtifacts(context, artifactType));
                this.progress = StorageStatus.done;
            }
        });
    }

    /**
     * Cache a specific artifact type of the application from the file system to memory
     * @param context
     * @param artifactType
     */
    private static async cacheArtifacts(context: Context, artifactType: ClientArtifactType): Promise<void> {
        this._applicationArtifactsByType[artifactType] =
            this._applicationArtifactsByType[artifactType] ?? ({} as Dict<ArtifactType>);

        await asyncArray(await this.readClientArtifactsFromFiles(context.application, artifactType)).forEach(artifact =>
            this.cacheArtifact(context, artifactType, artifact),
        );
    }

    private static cacheArtifact(
        context: Context,
        artifactType: ClientArtifactType,
        artifact: ApplicationArtifact | null,
        key?: string,
    ): void {
        // If the artifact is a page, then initialize the basic artifact information into memory
        // so that we can retrieve this simple data about the page fast.
        // We initialize this here as it is more efficient to load this information in bulk rather than querying
        // the artifacts of every request.
        const artifactKey = artifact?.key || key;
        if (!artifactKey) {
            throw new SystemError(`Cannot store artifact of type ${artifactType}: no key provided`);
        }
        this._applicationArtifactsByType[artifactType] =
            this._applicationArtifactsByType[artifactType] ?? ({} as Dict<ArtifactType>);
        // we store null artifact to have it already resolved
        this._applicationArtifactsByType[artifactType][artifactKey] = artifact;
        if (artifact) {
            const packArtifact = this.getPackArtifactInterface(artifact);
            this._packArtifactByKey[`${artifact.packageName}${artifact.artifactFilePath}`] = packArtifact;
        }
    }

    private static getPackArtifactInterface(artifact: ApplicationArtifact): PackArtifactInterface {
        const dataType = getArtifactDataType(artifact.artifactFilePath);
        return {
            packageName: artifact.packageName,
            path: artifact.artifactFilePath,
            content: artifact.content,
            dataType,
            type: artifact.type,
        } as PackArtifactInterface;
    }

    static getArtifactTitle(
        context: Context,
        artifactType: ClientArtifactType,
        artifactEntry: ApplicationArtifact,
    ): string {
        const packageName = path.dirname(artifactEntry.key);
        const locale = getLocaleFromHeader(context.request.headers);
        const fileName = `${kebabCase(artifactEntry.key.replace(/^.*[\\/]/, ''))}.js`;
        return artifactType !== ClientArtifactType.strings && !artifactType.endsWith('extensions')
            ? getLiteral(`${packageName}/${artifactType}__${snakeCase(path.parse(fileName).name)}____title`, locale)
                  .content
            : '';
    }

    static getArtifactDescription(
        context: Context,
        artifactType: ClientArtifactType,
        artifactEntry: ApplicationArtifact,
    ): string {
        const packageName = path.dirname(artifactEntry.key);
        const locale = getLocaleFromHeader(context.request.headers);
        const fileName = `${kebabCase(artifactEntry.key.replace(/^.*[\\/]/, ''))}.js`;
        try {
            return artifactType !== ClientArtifactType.strings && !artifactType.endsWith('extensions')
                ? getLiteral(
                      `${packageName}/${artifactType}__${snakeCase(path.parse(fileName).name)}____description`,
                      locale,
                  ).content
                : '';
        } catch {
            return '';
        }
    }

    static addUsedStringsKey(literals: ClientArtifactUsedLiterals | undefined, usedStringKeys: string[]): void {
        if (!literals) {
            return;
        }
        let count = usedStringKeys.length;
        const strings = literals.strings;
        if (strings != null) {
            // eslint-disable-next-line no-restricted-syntax
            for (const k of strings) {
                usedStringKeys[count++] = k;
            }
        }
        const enums = literals.enums;
        if (enums != null) {
            // eslint-disable-next-line no-restricted-syntax
            for (const k of enums) {
                usedStringKeys[count++] = generateEnumKey(k);
            }
        }
    }

    /**
     * Resolve the literals for the artifact, including the literals from the extensions and fragments
     * @param context
     * @param artifactType
     * @param artifact
     * @returns
     */
    static async resolveArtifactStrings(
        context: Context,
        artifactType: ClientArtifactType,
        artifact: ApplicationArtifact,
    ): Promise<LocalizedLiteral[]> {
        const usedStringKeys: string[] = [];
        this.addUsedStringsKey(artifact.literals, usedStringKeys);

        if (artifactType === ClientArtifactType.pages) {
            const extensions = await ArtifactManager.getExtensionsForPage(
                context,
                artifact.key,
                ClientArtifactType.pages,
            );
            // eslint-disable-next-line no-restricted-syntax
            for (const extension of extensions) {
                this.addUsedStringsKey(extension.literals, usedStringKeys);
            }

            const fragments = await this.getArtifactPageFragments(context, artifact);

            Object.values(fragments).forEach(fragment => {
                this.addUsedStringsKey(fragment.literals, usedStringKeys);
            });
        }

        if (artifact.plugins) {
            // eslint-disable-next-line no-restricted-syntax
            for (const extension of artifact.plugins) {
                usedStringKeys.push(extension);
            }
        }

        const locale = getLocaleFromHeader(context.request.headers);

        const resolvedLiterals: LocalizedLiteral[] = [];
        const uniqKeys = uniq(usedStringKeys);
        // eslint-disable-next-line no-restricted-syntax
        for (const key of uniqKeys) {
            appendLiterals(key, locale, resolvedLiterals);
        }

        return resolvedLiterals;
    }

    private static async getArtifacts(
        context: Context,
        artifactType: ClientArtifactType,
        filter?: Partial<PackArtifactInterface>,
    ): Promise<ApplicationArtifact[]> {
        return asyncArray(await this.getJsBundles(context, filter, artifactType))
            .map(jsBundle => this.completeWithJsonMeta(context, artifactType, jsBundle))
            .toArray();
    }

    private static async getJsBundles(
        context: Context,
        filter?: Partial<PackArtifactInterface>,
        artifactType?: ClientArtifactType,
    ): Promise<JsPackArtifact[]> {
        const startPath = path.join('lib', artifactType || '').replace(/\\/g, '/');
        const packArtifactFilter = {
            ...filter,
            dataType: 'js',
            condition: pack => {
                return artifactType ? pack.path.startsWith(startPath) : true;
            },
        } as Partial<PackArtifactInterface>;
        const packArtifacts = await this.queryPackArtifacts(context, packArtifactFilter);

        const convertToPackArtifact = (packArtifact: PackArtifactInterface): JsPackArtifact => {
            const file = camelCase(packArtifact.path.replace(/^.*[\\/]/, '').replace(/\.[^/.]+$/, ''));
            const className = `${file.charAt(0).toUpperCase()}${file.slice(1)}`;
            const key = `${packArtifact.packageName}/${className}`;
            if (!TextStream.isTextStream(packArtifact.content)) {
                throw new SystemError(
                    `artifact '${packArtifact.packageName}/${packArtifact.path}' must be of type 'TextStream', got '${packArtifact.content.constructor.name}'`,
                );
            }
            const content = packArtifact.content;
            return {
                packageName: packArtifact.packageName,
                artifactFilePath: packArtifact.path,
                type: packArtifact.type,
                content,
                // plugins: (content as any).plugins,
                // literals: (content as any).literals,
                // menuItem: (content as any).menuItem,
                className,
                key,
            };
        };
        return packArtifacts.map(pack => convertToPackArtifact(pack));
    }

    private static async completeWithJsonMeta(
        context: Context,
        artifactType: ClientArtifactType,
        jsBundle: JsPackArtifact,
    ): Promise<ApplicationArtifact> {
        const filter = {
            packageName: jsBundle.packageName,
            path: jsBundle.artifactFilePath.replace('.js', '.meta.json'),
        };
        const metaBundles = await this.getMetaBundles(context, artifactType, filter);
        if (metaBundles.length !== 1) {
            throw new Error(
                `Couldn't identify meta data for the artifact ${jsBundle.key} packageName:${jsBundle.packageName} path:${jsBundle.artifactFilePath}`,
            );
        }

        const artifact = { ...jsBundle, ...metaBundles[0] } as ApplicationArtifact;

        return artifact;
    }

    static getParsedContent(packArtifact: PackArtifactInterface): ClientArtifactMetadata {
        if (TextStream.isTextStream(packArtifact.content)) {
            packArtifact.content = JSON.parse(packArtifact.content.toString()) as ClientArtifactMetadata;
        }
        return packArtifact.content;
    }

    /**
     * Resolves to the customizable nodes based on the vital tree of the page node.
     * Used by the client side to have the list of customizable nodes relevant to the page.
     * @param context
     * @param artifact
     * @returns
     */
    static resolvePageCustomizableNodesWizard(
        context: Context,
        artifact: ApplicationArtifact,
    ): MetaCustomizableNodesArray | undefined {
        return artifact.pageNode
            ? context.application
                  .tryToGetFactoryByName(getNameWithoutPackage(artifact.pageNode))
                  ?.getVitalTree()
                  .filter(node => node.isCustomizable)
                  .map(f => {
                      return {
                          node: f.name,
                          fullName: f.fullName,
                      };
                  })
            : undefined;
    }

    /**
     * A list of customizable nodes for the page, constructed from ALL the nodes used on the page.
     * @param context
     * @param artifact
     * @returns
     */
    static async resolvePageCustomizableNodes(
        context: Context,
        artifact: ApplicationArtifact,
    ): Promise<MetaCustomizableNodesArray> {
        return (await this.getArtifactNodes(context, artifact))
            .map(key => {
                return {
                    node: getNameWithoutPackage(key),
                    fullName: key,
                };
            })
            .filter(node => context.application.tryToGetFactoryByName(node.node)?.isCustomizable);
    }

    /**
     * Get a dictionary of custom fields for the page, based on ALL the nodes used on the page.
     * @param context
     * @param artifact
     * @returns
     */
    static async resolvePageCustomFields(
        context: Context,
        artifact: ApplicationArtifact,
    ): Promise<MetaCustomFieldsArray | undefined> {
        const customizableNodes = await this.resolvePageCustomizableNodes(context, artifact);

        let customFields: MetaCustomFieldsArray | undefined;
        const customFieldsDict =
            customizableNodes.length > 0
                ? await CoreHooks.customizationManager.getCustomFields(
                      context,
                      customizableNodes?.map(item => item.fullName) || [],
                      { includeMutableChildren: true },
                  )
                : undefined;

        if (customFieldsDict) {
            customFields = Object.keys(customFieldsDict).map(key => {
                return { name: key, properties: customFieldsDict[key] };
            });
        }

        return customFields;
    }

    /**
     * Get the export templates for the page, based on ALL the nodes used on the page.
     * @param context
     * @param artifact
     * @returns
     */
    static async resolvePageExportTemplatesByNode(
        context: Context,
        artifact: ApplicationArtifact,
    ): Promise<NodeExportTemplate[]> {
        const nodeNames = await this.getArtifactNodes(context, artifact);
        const exportTemplatesDict = await CoreHooks.importExportManager.getNodeExportTemplates(context, nodeNames);
        return Object.keys(exportTemplatesDict).map(key => {
            return {
                name: key,
                exportTemplates: exportTemplatesDict[key],
            };
        });
    }

    static async resolveHasRecordPrintingTemplates(context: Context, artifact: ApplicationArtifact): Promise<boolean> {
        const printingManager = CoreHooks.createPrintingManager(context.application);
        const result = await printingManager?.hasRecordPrintingTemplates?.(context, artifact.key);
        return result ?? false;
    }

    static resolvePageAccess(context: Context, artifact: ApplicationArtifact): AsyncResponse<AccessStatus> {
        return ClientService.getPageAccess(context, artifact.pageAccess, {
            authorizationCode: artifact.authorizationCode,
        });
    }

    static async resolveAccess(context: Context, artifact: ApplicationArtifact): Promise<NodeAccess[]> {
        const nodes = await this.getArtifactNodes(context, artifact);

        let result: NodeAccess[] = [];
        let requiresAttachment = false;
        await asyncArray(expandNodeNames(context.application, nodes.map(getNameWithoutPackage), 3)).forEach(
            async nodeName => {
                const factory = context.application.tryToGetFactoryByName(nodeName);

                if (factory) {
                    if (factory.hasAttachments) {
                        requiresAttachment = true;
                    }
                    const factoryAccess = await ClientService.getFactoryAccess(context, factory);

                    result = union(result, factoryAccess);
                }
            },
        );

        if (requiresAttachment) {
            const attachmentsNode = CoreHooks.getAttachmentManager().getAttachmentNode();
            const attachmentsNodeFactory =
                context.application.factoriesManager.getFactoryByConstructor(attachmentsNode);
            const attachmentFactoryAccess = await ClientService.getFactoryAccess(context, attachmentsNodeFactory);
            result = union(result, attachmentFactoryAccess);
        }

        return result;
    }

    /**
     * Convert the pack artifact to a meta pack artifact.
     * @param context
     * @param packArtifact
     * @param artifactType
     * @returns
     */
    private static convertToPackArtifact(
        context: Context,
        packArtifact: PackArtifactInterface,
        artifactType: ClientArtifactType,
    ): MetaPackArtifact {
        const data = this.getParsedContent(packArtifact);
        const name = path.parse(packArtifact.path.replace('.meta.json', '.js')).name;
        let title: string;
        // We need a try and catch for the instances where UI file is not an artifact
        // TODO: optimize later, with a mechanism to check that artifact file is actually a UI artifact and not a file like index.ts
        try {
            title = trim(
                context.localize(`${packArtifact.packageName}/${artifactType}__${snakeCase(name)}____title`, ''),
            );
            // eslint-disable-next-line no-empty
        } catch {}

        const pageNodeName = (data.pageNode && getNameWithoutPackage(data.pageNode)) || '';
        const pageFactory = context.application.tryToGetFactoryByName(pageNodeName);

        const duplicateBindings = pageFactory?.properties
            .filter(prop => prop.duplicateRequiresPrompt)
            .map(p => {
                return p.name;
            });

        /**
         * Here we must be mindful not to add fields that are not static to application instance
         * if we need to add fields that are relevant to the user/tenant/context we should do a specific resolver for the
         * fields on the GraphQL type.
         * Examples: user access, custom fields, export templates etc. We have added these as explicitly defined resolvers on the pages
         * GraphQl query type.
         */
        return {
            title: title! || startCase(name),
            authorizationCode: data?.authorizationCode,
            pageAccess: data?.pageAccess,
            pageNode: data?.pageNode,
            extensionAccess: data?.extensionAccess,
            description: data?.description,
            listIcon: data?.listIcon,
            category: data?.category,
            type: packArtifact.type ?? data?.type,
            extends: data?.extends,
            plugins: data?.plugins,
            literals: data?.literals,
            menuItem: data?.menuItem,
            nodes: data?.nodes,
            duplicateBindings,
            fragments: data?.fragments,
            group: data?.group,
        };
    }

    private static async getMetaBundles(
        context: Context,
        artifactType: ClientArtifactType,
        filter?: Partial<PackArtifactInterface>,
    ): Promise<MetaPackArtifact[]> {
        const found = await this.queryPackArtifacts(context, {
            ...filter,
            dataType: 'meta',
        });

        return found.map(pack => this.convertToPackArtifact(context, pack, artifactType));
    }

    /**
     * Retrieves the artifact information for the specified key
     * The key is the <package name>/<artifact name>
     * Example: `@sage/xtrem-sales/SalesOrder`
     * @param context
     * @param key
     * @param locale
     * @returns
     */
    static async getPageArtifact(context: Context, key: string): Promise<PageArtifact | null> {
        return (await this.getArtifact(context, key, ClientArtifactType.pages)) as PageArtifact;
    }

    /**
     * Retrieves the artifact information for the specified key
     * The key is the <package name>/<artifact name>
     * Example: `@sage/xtrem-sales/SalesOrder`
     * @param context
     * @param key
     * @param locale
     * @returns
     */
    private static async getArtifact(
        context: Context,
        key: string,
        artifactType: ClientArtifactType,
    ): Promise<ArtifactType> {
        if (!key) return null;
        this._applicationArtifactsByType[artifactType] =
            this._applicationArtifactsByType[artifactType] ?? ({} as Dict<ArtifactType>);

        const artifactsInfo = this._applicationArtifactsByType[artifactType];
        // The basic page artifact information is not cached
        // Then we try to retrieve the artifact and set the cache.
        if (artifactsInfo[key] === undefined) {
            const artifact = await this.readClientArtifact(context, artifactType, {
                exactMatch: false,
                packageOrPage: key,
            });

            this.cacheArtifact(context, artifactType, artifact, key);
        }

        return artifactsInfo[key];
    }

    /**
     * This list search in the file system
     * @param key
     * @param artifactType
     */
    static async listAllClientArtifactsFromStorage(
        context: Context,
        artifactType: ClientArtifactType,
    ): Promise<ApplicationArtifact[]> {
        await ArtifactManager.ensureArtifactsCached(context);
        const artifacts = [
            // Naming is not helpful at all here - clarify
            // Second one does not seem to do much but adds metadata.json stuff - clarify
            ...(await ArtifactManager.getPackArtifacts(context, artifactType)),
        ];
        return artifacts;
    }

    /**
     * Find the artifacts for the packages that are active for the current tenant.
     */
    private static async getPackArtifacts(
        context: Context,
        artifactType: ClientArtifactType,
    ): Promise<ApplicationArtifact[]> {
        let artifacts: ApplicationArtifact[] = [];

        const activePackages = (await context.getActivePackageNames()).sort();

        await asyncArray(activePackages).forEach(async packageName => {
            const filter = { packageName };
            artifacts = artifacts.concat(await this.getArtifacts(context, artifactType, filter));
        });

        return artifacts;
    }

    /**
     * Read from file system
     * @param key
     * @param artifactType
     */
    static async readClientArtifact(
        context: Context,
        artifactType: ClientArtifactType,
        filter: ArtifactFilter,
    ): Promise<ApplicationArtifact | null> {
        const fileName = `${kebabCase(filter.packageOrPage.replace(/^.*[\\/]/, ''))}.js`;
        const artifactFilePath = path.join('lib', artifactType, fileName).replace(/\\/g, '/');
        const packageName = path.dirname(filter.packageOrPage);
        await ArtifactManager.ensureArtifactsCached(context);
        const artifactFilter = { packageName, path: artifactFilePath, dataType: 'js' } as any;
        const artifacts = (await ArtifactManager.getArtifacts(context, artifactType, artifactFilter)).filter(
            artifact => !filter.pageNode || artifact.pageNode === filter.pageNode,
        );

        if (artifacts.length) {
            return artifacts[0];
        }
        return null;
    }

    /**
     * Get extensions by page key
     * @param key
     * @param artifactType
     */
    static async getExtensionsForPage(
        context: Context,
        key: string,
        artifactType: ClientArtifactType,
    ): Promise<ApplicationArtifact[]> {
        if (artifactType !== ClientArtifactType.pages) {
            throw new Error('Extensions only supported for pages');
        }
        const user = await context.user;
        return context.getCachedValue({
            category: 'ARTIFACT',
            key: `USER:${user?._id || '*'}:EXTENSIONS:${key}`,

            getValue: async () => ({ value: await this.getArtifactExtensions(context, key, artifactType) }),
            cacheInMemory: true,
            ttlInSeconds: 1800,
        });
    }

    static async getArtifactExtensions(
        context: Context,
        key: string,
        artifactType: ClientArtifactType,
    ): Promise<ApplicationArtifact[]> {
        if (artifactType !== ClientArtifactType.pages) {
            throw new Error(`Extensions only supported for pages, and not ${artifactType} (${key})`);
        }
        const allExtensions: ApplicationArtifact[] = [];
        const activePackages = await context.getActivePackageNames();

        await asyncArray(activePackages).forEach(async packageId => {
            const packagePageExtensions = await this.getArtifacts(context, ClientArtifactType.pageExtensions, {
                packageName: packageId,
            });

            await asyncArray(packagePageExtensions).forEach(async artifact => {
                if (
                    artifact.extends === key &&
                    (await ClientService.getPageAccess(context, artifact?.extensionAccess, {
                        authorizationCode: artifact?.authorizationCode,
                    })) === 'authorized'
                ) {
                    allExtensions.push(artifact);
                }
            });
        });

        return allExtensions;
    }

    /**
     * Get the page fragments for the page
     * @param context
     * @param artifact
     * @returns
     */
    static async getArtifactPageFragments(
        context: Context,
        artifact: ApplicationArtifact,
    ): Promise<Dict<ApplicationArtifact>> {
        const allPageFragments = this._applicationArtifactsByType[ClientArtifactType.pageFragments] || {};

        let fragments = artifact.fragments || [];
        const extensions = await this.getArtifactExtensions(context, artifact.key, artifact.type as ClientArtifactType);

        extensions.forEach(extension => {
            if (extension.fragments) fragments = union(fragments, extension.fragments);
        });

        return fragments.reduce((acc, fragment) => {
            const framentArtifact = allPageFragments[fragment];
            if (framentArtifact == null) throw new Error(`Fragment ${fragment} not found`);
            acc[fragment] = framentArtifact;
            return acc;
        }, {} as Dict<ApplicationArtifact>);
    }

    static async getArtifactNodes(context: Context, artifact: ApplicationArtifact): Promise<string[]> {
        let nodes = [...(artifact.nodes || [])];
        const fragments = await ArtifactManager.getArtifactPageFragments(context, artifact);

        Object.values(fragments).forEach(fragment => {
            if (fragment.nodes) {
                nodes = union(nodes, fragment.nodes);
            }
        });

        const extensions = await ArtifactManager.getArtifactExtensions(context, artifact.key, ClientArtifactType.pages);

        extensions.forEach(extension => {
            if (extension.nodes) {
                nodes = union(nodes, extension.nodes);
            }
        });

        return nodes;
    }

    static async queryPackArtifacts(
        context: Context,
        filter: Partial<PackArtifactInterface>,
    ): Promise<PackArtifactInterface[]> {
        await this.ensureArtifactsCached(context);

        const artifacts = Object.values(this._packArtifactByKey).filter(
            a =>
                (filter.packageName == null || filter.packageName === a.packageName) &&
                (filter.path == null || filter.path === a.path) &&
                (filter.dataType == null || filter.dataType === a.dataType) &&
                (typeof filter.condition !== 'function' || filter.condition(a)),
        );
        return artifacts;
    }

    /**
     * In the ui config there can be inclusion and exclusion globs provided,
     *  we can use these checks to see if the artifact path conforms to the inclusion/exclusion rules
     * Exclusions will take precedence over inclusions
     * @param artifactPath
     * @returns
     */
    private static checkArtifactWithConfig(artifactPath: string): boolean {
        const artifactConfig = ConfigManager.current.ui;
        if (artifactConfig != null) {
            if (artifactConfig.exclude && artifactConfig.exclude.some(exlusion => minimatch(artifactPath, exlusion))) {
                loggers.application.debug(() => `Not loading artifact ${artifactPath} as per exclusion config`);
                return false;
            }

            if (artifactConfig.include) {
                if (artifactConfig.include.some(inclusion => minimatch(artifactPath, inclusion))) {
                    loggers.application.debug(() => `Loading artifact ${artifactPath} as per inclusion config`);
                    return true;
                }
                loggers.application.debug(() => `Not loading artifact ${artifactPath} as per inclusion config`);
                return false;
            }
        }
        return true;
    }

    /**
     * Iterate through all xtrem application packages and read their client artifacts.
     * @param applications
     * @param artifactType
     */
    private static async readClientArtifactsFromFiles(
        application: Application,
        artifactType: ClientArtifactType,
    ): Promise<ApplicationArtifact[]> {
        const applicationArtifacts: ApplicationArtifact[] = [];
        await asyncArray(application.getPackages()).forEach(async pack => {
            const artifactsPath = path.resolve(pack.dir, path.join('build', 'lib', artifactType));

            if (await fileExists(artifactsPath)) {
                let artifacts = await fs.readdir(artifactsPath);
                artifacts = artifacts.filter(fileName => fileName.endsWith('.meta.json'));

                await asyncArray(artifacts)
                    .filter(fileName =>
                        this.checkArtifactWithConfig(path.join(artifactsPath, fileName.replace('.meta.json', '.js'))),
                    )
                    .forEach(async fileName => {
                        const jsFilePath = fileName.replace('.meta.json', '.js');
                        if (await fileExists(path.resolve(artifactsPath, jsFilePath))) {
                            const filePathMetadata = path.resolve(artifactsPath, fileName);
                            const jsonMetadata = JSON.parse(
                                await fs.readFile(filePathMetadata, 'utf-8'),
                            ) as ClientArtifactMetadata;

                            const addArtifact = async (file: string): Promise<void> => {
                                const filePathContent = path.resolve(artifactsPath, file);
                                const artifactFilePath = path.join('lib', artifactType, file).replace(/\\/g, '/');

                                applicationArtifacts.push({
                                    ...jsonMetadata,
                                    packageName: pack.name,
                                    content: await readArtifactContent(filePathContent),

                                    key: `${pack.name}/${jsonMetadata.className}`,
                                    artifactFilePath,
                                    type: artifactType,
                                });
                            };

                            await addArtifact(fileName);
                            await addArtifact(fileName.replace('.meta.json', '.js'));
                        }
                    });
            }
        });
        return applicationArtifacts;
    }
}
