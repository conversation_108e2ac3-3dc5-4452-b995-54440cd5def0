import { Dict } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import { loggers } from '../runtime/loggers';
import { Application } from './application';

const logger = loggers.application;

/** *
 * For CRUD notifications
 *  - topic is <node_name>/<created or updated or deleted>
 *  - payload is empty
 *
 * For custom notifications
 *  - topic is <node_node>/<event name>
 *  - payload must be agreed between UI and server
 */
export interface UiBroadcastMessage {
    tenantId: string;
    category: string;
    payload?: string;
}

/**
 * A bucket to hold pending messages to be broadcasted.
 * It is used to collect messages during a transaction and then broadcast them after the transaction is committed.
 * @internal
 */
export class UiBroadcasterBucket {
    #pendingMessages: Dict<UiBroadcastMessage> = {};

    get pendingMessages(): Dict<UiBroadcastMessage> {
        return this.#pendingMessages;
    }

    /**
     * Add a broadcast message
     */
    addMessage(tenantId: string, category: string, payload?: string): void {
        const content = `${tenantId} / ${category} / ${payload}`;
        const hash = crypto.createHash('sha256').update(content).digest('base64');
        this.#pendingMessages[hash] = { tenantId, category, payload };
    }

    /**
     * On CRUD operations the messages are kept at the context level. Only after the transaction is
     * successfully committed we add the CRUD messages to the pendingMessages in order to broadcast them
     */
    addBucket(bucket: UiBroadcasterBucket): void {
        Object.keys(bucket.pendingMessages).forEach(key => {
            const message = bucket.pendingMessages[key];
            this.#pendingMessages[key] = message;
        });
    }

    clear(): void {
        if (Object.keys(this.#pendingMessages).length === 0) return;
        this.#pendingMessages = {};
    }
}

/**
 * A broadcaster to send UI messages to all the users.
 * It collects messages in a bucket and broadcasts them periodically.
 * The messages are collected during a transaction and then broadcasted after the transaction is committed.
 * The broadcasting is done periodically, so that we can avoid sending too many messages at once
 * and it avoids having duplicate messages.
 */
export class UiBroadcaster {
    readonly #timeout: number;

    #bucket: UiBroadcasterBucket = new UiBroadcasterBucket();

    #timer: NodeJS.Timeout | null = null;

    constructor(
        private readonly application: Application,
        private readonly options?: { timeout?: number },
    ) {
        this.#timeout = this.options?.timeout ?? 1000;
        this.startBackgroundProcessing();
    }

    private get pendingMessages(): Dict<UiBroadcastMessage> {
        return this.#bucket.pendingMessages;
    }

    broadcast(tenantId: string, category: string, payload?: string): void {
        if (!tenantId) {
            throw new Error('Cannot broadcast message: No valid tenantId found.');
        }
        if (!category) {
            throw new Error('Cannot broadcast message: No valid category found.');
        }
        this.#bucket.addMessage(tenantId, category, payload);
        // if the timer is not running, we start it
        if (!this.#timer) {
            this.startBackgroundProcessing();
        }
    }

    addBucket(bucket: UiBroadcasterBucket): void {
        if (!bucket) {
            throw new Error('Cannot add bucket: No valid bucket found.');
        }
        this.#bucket.addBucket(bucket);
        // if the timer is not running, we start it
        if (!this.#timer) {
            this.startBackgroundProcessing();
        }
    }

    /**
     * Broadcast pending messages to all the users
     */
    private startBackgroundProcessing(): void {
        this.#timer = setInterval(() => {
            if (!this.#timer) return; // if the timer is already cleared, we do not proceed
            // keep the reference to pendingMessages to avoid resetting it while broadcasting
            const pendingMessages = this.pendingMessages;
            if (Object.keys(pendingMessages).length === 0) return;
            this.resetPendingMessages();

            // Use an async IIFE to handle the promise rejection and exception
            // This is necessary because setInterval does not handle async functions directly
            (async () => {
                const allMessages = Object.values(pendingMessages).map(message =>
                    this.application.notificationManager.broadcast(message),
                );
                await Promise.all(allMessages);
                Application.emitter.emit('uiBroadcastCompleted');
            })().catch(error => {
                const message = error instanceof Error ? error.message : String(error);
                logger.error(`Error in UI broadcaster: ${message}`);
                Application.emitter.emit('uiBroadcastError', message);
            });
        }, this.#timeout);
    }

    private resetPendingMessages(): void {
        this.#bucket.clear();
    }

    /** @internal */
    close(): void {
        if (this.#timer) {
            clearInterval(this.#timer);
            this.#timer = null;
        }
        this.resetPendingMessages();
    }
}
