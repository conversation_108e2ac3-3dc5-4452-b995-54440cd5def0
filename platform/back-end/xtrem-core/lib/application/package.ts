/* eslint-disable  import/no-dynamic-require, global-require */
/** @packageDocumentation @module runtime */
import { AnyValue, AsyncResponse, dynamicImport } from '@sage/xtrem-async-helper';
import {
    Dict,
    LogicError,
    PackageJsonAbout,
    PackageJsonFile,
    RootMenuItem,
    SubMenuItem,
    XtremOptions,
    getPackageName,
    getPackageQueueName,
} from '@sage/xtrem-shared';
import * as fs from 'fs';
import { merge, uniq } from 'lodash';
import * as fsp from 'path';
import { StaticThis, getDecorators } from '../decorators/decorator-utils';
import { NotificationTopic } from '../interop';
import { NodeFactory } from '../runtime';
import { loggers } from '../runtime/loggers';
import { PropertyDecorator } from '../runtime/property';
import {
    ExternalEnumDecorator,
    ExternalNodeDecorator,
    ExternalNodeExtensionDecorator,
    ExternalPropertyDecorator,
    Node,
} from '../ts-api';
import { Activity } from '../ts-api/activity';
import { ActivityExtension } from '../ts-api/activity-extension';
import { DataType, DataTypeOptions, EnumDataType } from '../types';
import { WorkflowStepInterface } from '../workflow/workflow-step-descriptor';
import { Application } from './application';
import { ServiceOption } from './service-option';

const logger = loggers.application;

export interface PackageApi {
    // TODO: XT-803 improve typing here
    nodes?: Dict<any>;
    nodeExtensions?: Dict<any>;
    enums?: Dict<any>;
    enumExtensions?: Dict<any>;
    serviceOptions?: Dict<ServiceOption>;
    menuItems?: Dict<SubMenuItem | RootMenuItem>;
    activities?: Dict<Activity>;
    activityExtensions?: Dict<ActivityExtension>;
    dataTypes?: Dict<DataType<AnyValue, DataTypeOptions>>;
    notificationTopics?: Dict<NotificationTopic>;
    workflowSteps?: Dict<WorkflowStepInterface>;
    startService?: (application: Application) => AsyncResponse<void>;
}

export interface PackageCreateOptions {
    // Only set when building the application for xtrem-core unit-tests
    buildDir?: string;
    // Only set when building the application for xtrem-core unit-tests
    api?: PackageApi;
    // Is the package a third party extension
    isAddOn?: boolean;
}

export { PackageJsonAbout, PackageJsonFile, XtremOptions, getPackageName, getPackageQueueName };

export class Package {
    #name: string | null;

    #queue: string | undefined;

    private buildDir: string;

    private _dir: string;

    public get dir(): string {
        return this._dir;
    }

    api: PackageApi;

    private _packageJson: PackageJsonFile;

    // TODO: make private again
    factories: NodeFactory[];

    // TODO: make private again
    _nodeExtensions: StaticThis<Node>[] = [];

    // All the dependencies
    #allDependencies: Package[];

    private constructor(
        readonly application: Application,
        name: string | null,
        public options: PackageCreateOptions,
    ) {
        if (!options.buildDir) throw new Error('A build directory must be provided to create an Application.');

        this.#name = name;
        this.buildDir = options.buildDir || '';
        if (options.api) this.api = options.api;

        this._dir = fsp.join(this.buildDir, '..');
    }

    private async init(): Promise<Package> {
        const path = fsp.join(this.dir, 'package.json');
        const stub = this.#name ? this.application.packageLoader.packageStubsByName[this.#name] : null;
        if (stub) {
            this._packageJson = stub.content;
        } else {
            this._packageJson = require(path);
        }
        const packageName = getPackageName(this._packageJson);
        if (packageName !== this.#name) {
            this.#name = packageName;
        }
        const main = this._packageJson.main || 'build/index.js';
        if (this.options.api && !this._packageJson.xtrem) {
            // Hack for xtrem-core unit-tests
            // package.json is xtrem-core
            this._packageJson.isMainUnitTest = true;
        } else if (
            this._packageJson.xtrem &&
            (!this._packageJson.main || (this._packageJson.xtrem.isMain && !this._packageJson.xtrem.hasListeners)) &&
            !this.isModule
        ) {
            // This is an umbrella package.
            // No API in this package, all the artifacts come from the dependencies.
            this.api = {};
        } else if (this.isModule) {
            const required = await dynamicImport(`${this.dir}/build/index.js`);
            this.api = merge({}, required, this.options.api || {});
        } else {
            let resolved;
            try {
                resolved = require.resolve(this.dir);
            } catch {
                // ignore
            }
            const required = resolved ? require(resolved) : (require.cache[main]?.exports ?? require(this.dir));
            this.api = merge({}, required, this.options.api || {});
        }

        this.checkXtremAttribute(this._packageJson, () => `xtrem property in ${path} should be an object`);

        // TODO: We need a better way to identify that the application is created from a directory that is not a package.
        // -   For instance, in the image: the cli commands are launched from the root of the application: all the
        //     packages are in the node_modules directory. The main app created is not a package.
        // -   For now, we rely on the fact that the attribute "main" will not be set in the app's package.json
        //     if not a package.
        if (!this._packageJson.xtrem && !this._packageJson.isMainUnitTest) {
            throw new Error(`${this._packageJson.name}: 'xtrem' key is missing in package.json`);
        }

        if (this._packageJson.xtrem?.isMain && !this._packageJson.xtrem?.appName) {
            // TODO: enable later
            // throw new Error(`${this._packageJson.name}: 'xtrem.appName' key is missing in package.json`);
        }

        this._packageJson.main = this._packageJson.main || main;
        return this;
    }

    static create(application: Application, name: string | null, options: PackageCreateOptions): Promise<Package> {
        return new Package(application, name, options).init();
    }

    get isSealed(): boolean {
        return !!this._packageJson?.xtrem?.isSealed;
    }

    /**
     * @disabled_internal
     * indicate if the package is hidden (technical) or not. Hidden packages cannot be deactivated
     */
    get isHidden(): boolean {
        // We will mark add-ons as hidden and therefore active implicitly
        // Enabling and disabling of add-on functionality will be controlled by service options
        return !!this._packageJson?.xtrem?.isHidden || this.isAddOn;
    }

    /**
     * Indicate if the package should be active by default.
     */
    get isActiveByDefault(): boolean {
        return !this._packageJson?.xtrem?.isInactiveByDefault;
    }

    /**
     * indicate if the package is released.
     */
    get isReleased(): boolean {
        return !!this._packageJson?.xtrem?.isReleased;
    }

    /**
     * indicate if the package is released.
     */
    get sqlSchemaVersion(): string | undefined {
        return this._packageJson?.xtrem?.sqlSchemaVersion;
    }

    /**
     * indicate if the package is a main package.
     */
    get isMain(): boolean {
        return !!this._packageJson?.xtrem?.isMain;
    }

    /**
     * Is the packages a service which handles SQS notifications and/or messages.
     * Only service packages have SQS queues.
     */
    get isService(): boolean {
        return !!this._packageJson?.xtrem?.isService;
    }

    /**
     * Is the packages from a third party extension
     */
    get isAddOn(): boolean {
        return !!this.options?.isAddOn;
    }

    /** Is the package an ESM module */
    get isModule(): boolean {
        return this._packageJson.type === 'module';
    }

    /**
     * Indicates if the package can have listeners registered
     */
    get hasListeners(): boolean {
        return (
            !!this._packageJson?.xtrem?.hasListeners ||
            this.isService ||
            this.isAddOn ||
            this.factories.some(
                factory => factory.notificationListeners.length > 0 || factory.messageListeners.length > 0,
            )
        );
    }

    get queue(): string {
        if (this.#queue) return this.#queue;
        this.#queue = getPackageQueueName(this._packageJson);
        return this.#queue;
    }

    get dependencies(): Dict<string> {
        return this._packageJson.dependencies || {};
    }

    get directDependencies(): Package[] {
        return Object.keys(this.dependencies)
            .map(dep => this.application.packagesByName[dep])
            .filter(pack => !!pack);
    }

    get apiDependencies(): string[] {
        return uniq([
            ...this.allDependencies.map(pack => `${pack.name}-api`),
            ...Object.keys(this.packageJson.devDependencies || {}).filter(key =>
                /@sage\/[a-z][a-z0-9-]*-api$/.test(key),
            ),
        ]);
    }

    /** Returns a list of all the dependencies of the current package, and their dependencies, etc.
     * @param app: application object used to retrieve the full package list.
     */
    get allDependencies(): Package[] {
        if (this.#allDependencies) return this.#allDependencies;

        const packagesByName = this.application.packagesByName;

        const dependencies = new Set<Package>();
        const scan = (pack: Package): void => {
            if (pack.dependencies) {
                Object.keys(pack.dependencies)
                    .filter(dep => packagesByName[dep] && !dependencies.has(packagesByName[dep]))
                    .forEach(dep => {
                        dependencies.add(packagesByName[dep]);
                        scan(packagesByName[dep]);
                    });
            }
        };
        scan(this);
        this.#allDependencies = [...dependencies];
        return this.#allDependencies;
    }

    // eslint-disable-next-line class-methods-use-this
    private checkXtremAttribute = (pack: PackageJsonFile, errorProvider: () => string): void => {
        if (pack && pack.xtrem && typeof pack.xtrem !== 'object') {
            throw new Error(errorProvider());
        }
    };

    get packageJson(): PackageJsonFile {
        if (!this._packageJson) {
            throw new LogicError('lazy loading of package.json');
        }
        return this._packageJson;
    }

    get name(): string {
        // was this.packageJson.name before
        return this.#name ?? getPackageName(this.packageJson);
    }

    get version(): string {
        return this.packageJson.version;
    }

    // TODO: XT-803 cleanup these 2 names
    get packageName(): string {
        // Note: DO NOT REMOVE the hack on package.xtrem.packageName - it is needed for unit tests on upgrades
        // (to force the same package name on xtrem-upgrade-test-v1 and xtrem-upgrade-test-v2)
        // We have to keep the 'as any' because we don't want to polute the PackageXtrem interface with this hack
        return this.name;
    }

    static getPackageJson(dir: string): PackageJsonFile {
        return require(fsp.join(dir, 'package.json'));
    }

    get xtremOptions(): XtremOptions {
        return this.packageJson?.xtrem || {};
    }

    /** Require the code of the Application and loads the application api attribute. */
    loadDecorators(): void {
        if (this.api) return;
        try {
            this.api = require(this.buildDir) as PackageApi;
        } catch (e) {
            logger.error(e.stack);
        }
    }

    /**
     * Returns the list of nodeExtensions created by the package
     */
    get nodeExtensions(): StaticThis<Node>[] {
        return this._nodeExtensions;
    }

    get activities(): Dict<Activity> {
        return this.api.activities || {};
    }

    get activitiesExtensions(): Dict<ActivityExtension> {
        return this.api.activityExtensions || {};
    }

    get serviceOptions(): Dict<ServiceOption> {
        return this.api.serviceOptions || {};
    }

    static decoratorFromFactory(factory: NodeFactory): ExternalNodeDecorator {
        return {
            ...factory.nodeDecorator,
            isVitalChild: factory.isVitalChild,
            isVitalReferenceChild: factory.isVitalReferenceChild,
            isVitalCollectionChild: factory.isVitalCollectionChild,
            properties: factory.properties as ExternalPropertyDecorator[],
            queries: factory.queries,
            mutations: factory.mutations,
            storage: factory.storage,
            externalStorageManager: factory.externalStorageManager,
            indexes: factory.indexes,
            tableName: factory.tableName,
            super: factory.baseFactory?.name,
        };
    }

    getNodeDecorators(): ExternalNodeDecorator[] {
        return this.factories.map(f => Package.decoratorFromFactory(f));
    }

    getEnumDescriptors(): ExternalEnumDecorator[] {
        const enums = this.api.enums;
        if (!enums) return [];
        return Object.keys(enums)
            .filter(k => !(enums[k] instanceof DataType))
            .map(k => {
                const enu = enums[k];
                return {
                    name: k,
                    values: Object.keys(enu)
                        .filter(key => !/^\d/.test(key))
                        .reduce((result, key) => ({ ...result, [key]: enu[key] }), {} as Dict<number>),
                };
            });
    }

    private enumDataTypes: EnumDataType[];

    getEnumDataTypes(): EnumDataType[] {
        if (this.enumDataTypes) return this.enumDataTypes;
        const enums = this.api.enums;
        if (!enums) return [];
        this.enumDataTypes = Object.keys(enums)
            .filter(e => enums[e] instanceof EnumDataType)
            .map(e => {
                const dataType = enums[e] as EnumDataType;
                dataType.pack = this.name;
                dataType.name = e;
                return dataType;
            });

        return this.enumDataTypes;
    }

    getExtensionDecorators(): ExternalNodeExtensionDecorator[] {
        const extensions = this.api.nodeExtensions;
        if (!extensions) return [];
        return Object.keys(extensions || {})
            .map(name => {
                const extension = extensions[name];
                if (typeof extension === 'function') {
                    const decorators = getDecorators(extension);
                    const factory = this.application.getFactoryByConstructor(decorators.nodeExtension.extends());
                    // decorators.properties are _property decorators_. We have to map them to _properties_.
                    const properties = ((decorators.properties || []) as PropertyDecorator[])
                        .filter(prop => !prop.isOverride)
                        .map(prop => factory.findProperty(prop.name));

                    return {
                        ...decorators.nodeExtension,
                        name: this.application.getFactoryByConstructor(decorators.nodeExtension.extends()).name,
                        properties,
                        queries: decorators.queries,
                        mutations: decorators.mutations,
                        notificationListeners: decorators.notificationListeners,
                        messageListeners: decorators.messageListeners,
                    };
                }
                logger.warn(`${name}: Skipping decorator detection`);
                return null;
            })
            .filter(v => !!v) as ExternalNodeExtensionDecorator[];
    }

    getLocalizedTitleKey(): string {
        return `${this.name}/package__name`;
    }
}

export abstract class XtremAboutHelper {
    /* @internal */
    static load(application: Application, packageName = ''): PackageJsonAbout {
        const packageFilename = 'package.json';
        let packageJsonAbout: PackageJsonAbout = {
            name: packageName,
            version: '',
            author: '',
            description: '',
            license: '',
            buildStamp: '',
        };
        let folderToTest: string = application.mainPackage.dir;

        // eslint-disable-next-line no-constant-condition
        while (true) {
            const path = fsp.join(folderToTest, packageFilename);
            const lockPath = fsp.join(folderToTest, 'pnpm-lock.yaml');
            if (fs.existsSync(path)) {
                try {
                    const content = JSON.parse(fs.readFileSync(path, 'utf8')) as PackageJsonAbout;
                    packageJsonAbout = Object.keys(packageJsonAbout).reduce((result, key) => {
                        let val = (content as any)[key] || '';
                        if (typeof val === 'object') {
                            val = val.name || '';
                        }
                        return { ...result, [key]: val || '' };
                    }, {} as any) as PackageJsonAbout;

                    if (packageName && content.name === packageJsonAbout.name) {
                        // It's the package we are looking for:
                        break;
                    }
                } catch {
                    break;
                }
            }
            if (fs.existsSync(lockPath)) {
                // We have reached the root folder with the pnpm-lock.yaml file. No need to go further
                break;
            }
            if (path === packageFilename) {
                // We have reached the top folder. No need to go further
                break;
            }
            const parentFolder = fsp.join(folderToTest, '..');
            if (parentFolder === folderToTest) {
                // We have reached the top folder. No need to go further
                break;
            }
            folderToTest = parentFolder;
        }
        return packageJsonAbout;
    }
}

export class PackageValidationContext {
    warnings: Set<string> = new Set();
}
