import { Dashboard, DashboardItem, Dict } from '@sage/xtrem-shared';
import { Context } from '../runtime';

export interface DashboardManager {
    getSelectedDashboard(context: Context, group?: string): Promise<Dashboard | null>;
    setSelectedDashboard(context: Context, selectedDashboardId: string, group?: string): Promise<Dashboard>;
    updateDashboardItemSettings(context: Context, dashboardItemId: string, settings: object): Promise<void>;
    updateDashboardLayout(context: Context, dashboardItemId: string, layout: Dashboard): Promise<Dashboard>;
    addDashboardItem(context: Context, dashboardId: string, item: Partial<DashboardItem>): Promise<void>;
    getDashboardList(context: Context, group?: string): Promise<Dict<string>>;
    createDashboard(context: Context, group?: string): Promise<Dashboard>;
    cloneDashboard(context: Context, dashboardId: string): Promise<Dashboard>;
    deleteDashboard(context: Context, dashboardItemId: string): Promise<void>;
    getFactoryDashboardList(context: Context, group?: string): Promise<Dashboard[]>;
    getWidgetCategory(context: Context, widget: { _id: string; id: string }): Promise<{ key: string; title: string }>;
    getWidgetCategories(context: Context): Promise<{ key: string; title: string }[]>;
    canEditDashboards(context: Context): Promise<boolean>;
}

export class DashboardManagerStub implements DashboardManager {
    // eslint-disable-next-line class-methods-use-this
    getWidgetCategory = (): Promise<{ key: string; title: string }> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    getWidgetCategories = (): Promise<[{ key: string; title: string }] | []> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    getSelectedDashboard = (): Promise<Dashboard | null> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    setSelectedDashboard = (): Promise<Dashboard> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    updateDashboardItemSettings = (): Promise<void> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    updateDashboardLayout = (): Promise<Dashboard> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    addDashboardItem = (): Promise<void> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    getDashboardList = (): Promise<Dict<string>> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    createDashboard = (): Promise<Dashboard> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    cloneDashboard = (): Promise<Dashboard> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    deleteDashboard = (): Promise<void> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    getFactoryDashboardList = (): Promise<Dashboard[]> => {
        throw new Error('Dashboard manager is not registered.');
    };

    // eslint-disable-next-line class-methods-use-this
    canEditDashboards = (): Promise<boolean> => {
        throw new Error('Dashboard manager is not registered');
    };
}
