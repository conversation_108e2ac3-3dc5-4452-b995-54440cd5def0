import { EventEmitter } from 'events';
import { loggers } from '../runtime/loggers';

export const gracefulShutdown = new EventEmitter();

const logger = loggers.core;

process.on('SIGTERM', () => {
    gracefulShutdown.emit('stop');
    logger.info('Graceful shutdown has been initiated');
});

gracefulShutdown.on('stopped', () => {
    logger.info(`Graceful shutdown complete - terminating process ${process.pid}`);
    // 5s Grace period to allow messages to flush to log
    setTimeout(() => {
        process.exit(0);
    }, 5000);
});
