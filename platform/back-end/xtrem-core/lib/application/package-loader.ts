import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { initializeLocalizationResolver } from '@sage/xtrem-i18n';
import { Dict, LogicError, SystemError, createDictionary } from '@sage/xtrem-shared';
import * as fs from 'fs';
import { uniq } from 'lodash';
import * as fsp from 'path';
import { loggers } from '../runtime/loggers';
import { ExternalEnumDecorator } from '../ts-api';
import { testPackageExclusions } from '../utils';
import { Application } from './application';
import { Package, PackageCreateOptions, PackageJsonFile, PackageValidationContext } from './package';

export interface PackageStub {
    name: string;
    dir: string;
    content: PackageJsonFile;
}

const logger = loggers.nodeFactory;

const emptyPackageJson = {
    name: '',
    version: '0.0.0',
    description: '',
    author: '',
    license: '',
    main: '',
    exports: {},
    type: '',
    isMainUnitTest: false,
    dependencies: {},
    devDependencies: {},
    peerDependencies: {},
};

// List of wellknown package scopes that should not be loaded as packages (To enhance later)
const packageScopesBlackList = ['@aws-sdk', '@aws-crypto', '@swc', '@types'];

export class PackageLoader {
    readonly packagesByName = createDictionary<Package>();

    readonly packageStubsByName = createDictionary<PackageStub>();

    constructor(private application: Application) {}

    private static getPackageStubFromPaths(packageName: string, paths: string[]): PackageStub | null {
        try {
            const packPath = require.resolve(`${packageName}/package.json`, { paths });
            const packDir = fsp.dirname(packPath);
            return {
                name: packageName,
                dir: packDir,
                // eslint-disable-next-line import/no-dynamic-require, global-require
                content: require(packPath),
            };
        } catch {
            // second chance to find the package.json file using a find-up strategy
            const stub = paths.reduce((r, dir) => {
                if (r) return r;
                const packPath = PackageLoader.findUpPackageJson(dir, packageName);
                if (!packPath) return r;
                const packDir = fsp.dirname(packPath);
                const content = JSON.parse(fs.readFileSync(packPath, 'utf-8'));
                return {
                    name: packageName,
                    dir: packDir,
                    content,
                };
            }, null);
            if (stub == null) {
                throw new SystemError(`Could not find package.json for ${packageName} in paths: ${paths}`);
            }
            return stub;
        }
    }

    private static findUpPackageJson(dir: string, packageName: string): string | null {
        let currentDir = dir;
        while (currentDir !== '/') {
            const filePath = fsp.join(currentDir, 'node_modules', packageName, 'package.json');
            if (fs.existsSync(filePath)) {
                return filePath;
            }
            currentDir = fsp.join(currentDir, '..');
        }
        return null;
    }

    private getPackageStubOfDependency(packageName: string, dir: string): PackageStub {
        if (!this.packageStubsByName[packageName]) {
            const mainAppDir = this.application.dir;

            const resolutionPaths = [dir];
            if (process.env.NODE_ENV !== 'production') {
                // When main package is an add-on in development
                uniq([mainAppDir, dir]).forEach(p => {
                    let addOnPath = fsp.join(p, '..', '..');
                    if (fsp.basename(addOnPath) !== 'node_modules') {
                        addOnPath = fsp.join(addOnPath, 'node_modules');
                    }
                    resolutionPaths.push(addOnPath);
                });
            }

            const stub = PackageLoader.getPackageStubFromPaths(packageName, resolutionPaths) ?? {
                name: packageName,
                dir: fsp.join(dir, 'node_modules', packageName),
                content: { ...emptyPackageJson, name: packageName },
            };
            this.packageStubsByName[packageName] = stub;
        }
        return this.packageStubsByName[packageName];
    }

    /**
     * Check if a package name is valid for being loaded in the application
     * @param packageName
     * @returns
     */
    private isValidPackageName(packageName: string): boolean {
        let valid =
            !this.application.nonPackageDependencies[packageName] &&
            packageName[0] === '@' &&
            !/(xtrem|etna)-ui$/.test(packageName) &&
            !/eslint/.test(packageName);
        // so far so good it is a scoped package, but it is in the black list
        if (valid) {
            const scope = packageName.split('/')[0];
            valid = !packageScopesBlackList.includes(scope);
        }
        if (!valid) {
            this.application.nonPackageDependencies[packageName] = true;
            return false;
        }
        return true;
    }

    /**
     * Returns whether a package contains some nodes and must be taken into account when compunting dependencies
     * @param packageName
     * @param dir
     */
    private isPackageWithNodes(packageName: string, packageJson: PackageJsonFile | undefined): boolean {
        if (/^@sage\/test[\w\d-]+-app$/.test(packageName)) return true; // <= le hack
        if (!this.isValidPackageName(packageName)) return false;

        const pack = this.packagesByName[packageName];

        if (pack) {
            return pack.packageJson.xtrem != null && !pack.packageJson.xtrem?.isFrontEndApp;
        }

        if (!packageJson) {
            this.application.nonPackageDependencies[packageName] = true;
            return false;
        }

        if (packageJson.xtrem != null && !packageJson.xtrem?.isFrontEndApp) {
            return true;
        }

        this.application.nonPackageDependencies[packageName] = true;
        return false;
    }

    private getDependencyBuildDir(dependency: string, dir: string, knownPaths: string[] = []): string {
        const stub = this.packageStubsByName[dependency];
        if (stub) {
            return fsp.join(stub.dir, 'build');
        }
        // the following array of paths also takes into account potentially hoisted dependencies
        const paths = uniq([dir, fsp.join(dir, '../..'), ...knownPaths]);
        const fileToRequire = require.resolve(`${dependency}/package.json`, { paths });
        return fsp.join(fsp.dirname(fileToRequire), 'build');
    }

    /** Checks if there are any name collisions between enums and nodes */
    private static checkForNameCollisions(pack: Package): void {
        pack.getEnumDescriptors().forEach((enumInstance: ExternalEnumDecorator) => {
            const enumName = enumInstance.name;
            if (pack.application.nodeAndEnumNames[enumName] === 'enum') {
                throw new SystemError(`Enum names cannot be duplicated. The enum name: ${enumName} was already used.`);
            } else if (pack.application.nodeAndEnumNames[enumName] === 'node') {
                throw new SystemError(
                    `Enum names cannot match node names. The enum name: ${enumName} was already used as a node name.`,
                );
            }
            pack.application.nodeAndEnumNames[enumName] = 'enum';
        });
    }

    private _verifyPackage(pack: Package): void {
        if (this.application.skipVerifications) return;
        // TODO: we are filtering out xtrem-communication because we had to mark it with isService: true to get
        // a notification queue for its unit tests, and we don't want to have a warning on all the packages
        // which depend on xtrem-communication.
        // We have to find a better way to manage notification queues that are used only by unit tests.
        const serviceDependencies = [pack, ...pack.allDependencies].filter(
            p => p.isService && p.name !== '@sage/xtrem-communication',
        );
        if (serviceDependencies.length > 1 && !pack.isMain) {
            const message = `${pack.name}: invalid dependency graph: several services found: ${serviceDependencies.map(
                p => p.name,
            )}`;
            // TODO: throw an error here
            logger.warn(message);
            // throw new LogicError(message);
        }

        // We only allow enum extensions on external applications and platform unit tests.
        const allowEnumExtensions =
            ConfigManager.current.storage?.managedExternal ||
            testPackageExclusions.includes(pack.application.mainPackage.name);

        if (!allowEnumExtensions && Object.keys(pack.api.enumExtensions || {}).length > 0) {
            throw new LogicError(`${pack.name}: enum extensions are only allowed on external applications.`);
        }

        PackageLoader.checkForNameCollisions(pack);
    }

    private async createPackage(name: string, options: PackageCreateOptions): Promise<Package> {
        const pack = await Package.create(this.application, name, options);
        this.packagesByName[pack.name] = pack;
        this._verifyPackage(pack);
        pack.loadDecorators();
        return pack;
    }

    /**
     * Scan package dependencies and load application packages
     * @param dir
     * @param knownPaths
     * @param processedPackages
     * @param packageValidationContext
     * @param options
     * @returns
     */
    private async scanPackage(
        dir: string,
        knownPaths: string[],
        processedPackages: Dict<boolean>,
        packageValidationContext: PackageValidationContext,
        options?: { isAddOn?: boolean },
    ): Promise<void> {
        const pack = Package.getPackageJson(dir);
        knownPaths.push(dir);

        const dependencies = Object.keys(pack.dependencies || {});

        const peerDependencies = Object.keys(pack.peerDependencies || {});

        const packDependencies = uniq([...dependencies, ...peerDependencies]);

        // Special condition to load the routing service package for unit tests, so that we can start service with
        // the application event emitter on event 'testListen'
        if (
            this.application.applicationType === 'test' &&
            pack.devDependencies != null &&
            Object.keys(pack.devDependencies).includes('@sage/xtrem-routing')
        ) {
            packDependencies.push('@sage/xtrem-routing');
        }

        if (packDependencies.length === 0) return;
        await asyncArray(packDependencies).forEach(async dep => {
            if (processedPackages[dep] || !this.isValidPackageName(dep)) {
                processedPackages[dep] = true;
                return;
            }
            const depStub = this.getPackageStubOfDependency(dep, dir);
            const depContent = depStub.content;
            // If the package is an application package then scan its dependencies and add the package to the application
            if (this.isPackageWithNodes(dep, depContent)) {
                await this.scanPackage(depStub.dir, knownPaths, processedPackages, packageValidationContext);
                if (!processedPackages[dep]) {
                    const buildDir = this.getDependencyBuildDir(dep, this.application.dir, knownPaths);
                    await this.createPackage(dep, { ...options, buildDir });
                    processedPackages[dep] = true;
                }
            } else {
                processedPackages[dep] = true;
            }
        });
    }

    /**
     * Create add-on packages of application
     */
    private async createAddOnPackages(
        knownPaths: string[],
        processedPackages: Dict<boolean>,
        packageValidationContext: PackageValidationContext,
    ): Promise<void> {
        if (this.application.addOnPackagePaths.length) {
            await asyncArray(this.application.addOnPackagePaths).forEach(async addOnPackagePath => {
                const addOnPackage = Package.getPackageJson(addOnPackagePath);
                const addOnName = addOnPackage.name;
                if (processedPackages[addOnName]) return;
                knownPaths.push(addOnPackagePath);
                if (this.isPackageWithNodes(addOnName, addOnPackage)) {
                    // We pass in the add on directory as the first parameter as the name is insufficient
                    // as the add on package is not loaded in the require cache as yet and it is not in main application
                    // node_modules
                    const addOnBuildDir = this.getDependencyBuildDir(
                        addOnPackagePath,
                        this.application.dir,
                        knownPaths,
                    );

                    processedPackages[addOnName] = true;

                    await this.scanPackage(
                        fsp.join(addOnBuildDir, '..'),
                        knownPaths,
                        processedPackages,
                        packageValidationContext,
                        {
                            isAddOn: true,
                        },
                    );
                    // Create package implicitly marked as an add-on as the standard application packages are already
                    // processed therefore any "new" packages loaded here are add-ons
                    await this.createPackage(addOnName, { buildDir: addOnBuildDir, isAddOn: true });
                } else {
                    processedPackages[addOnName] = true;
                }
            });
        }
    }

    private createFactories(packageValidationContext: PackageValidationContext): void {
        const packages = Object.values(this.packagesByName);
        this.application.factoriesManager.createAllFactories(packages, packageValidationContext);
        packages.forEach(pack => {
            this.application.activityManager.createActivities(pack);
        });
    }

    /** @internal Recursively create Application objects for the current Application's package dependencies */
    async createPackages(): Promise<void> {
        // The graph is read in-depth-first.
        const processedPackages = {} as Dict<boolean>;
        const packageValidationContext = new PackageValidationContext();
        const knownPaths: string[] = [];
        const t0 = performance.now();
        let profiler = logger.verbose(() => 'Loading packages');
        await this.scanPackage(this.application.dir, knownPaths, processedPackages, packageValidationContext);
        profiler.success();

        this.packagesByName[this.application.mainPackage.name] = this.application.mainPackage;
        processedPackages[this.application.mainPackage.name] = true;

        // Add-on packages needs to be loaded before creating the factories
        profiler = logger.verbose(() => 'Loading add on packages');
        await this.createAddOnPackages(knownPaths, processedPackages, packageValidationContext);
        profiler.success();

        profiler = logger.verbose(() => 'Creating application factories');
        this.createFactories(packageValidationContext);
        packageValidationContext.warnings.forEach(warning => logger.warn(`${warning}`));
        profiler.success();

        this.application.factoriesManager.registerListeners();

        Object.freeze(this.packagesByName);

        profiler = logger.verbose(() => 'Intializing localization resolver');
        initializeLocalizationResolver(Object.values(this.packagesByName), this.application.dir);
        profiler.success();
        logger.info(
            `Loaded ${Object.keys(this.packagesByName).length} packages in ${Math.round((performance.now() - t0) * 100) / 100}ms`,
        );
    }
}
