import { Dict, LogicError, SystemError } from '@sage/xtrem-shared';
import { MessageListener, NotificationListener, PlainOperationDecorator } from '../decorators';
import { StaticThis, decoratorsSymbol } from '../decorators/decorator-utils';
import { loggers } from '../runtime/loggers';
import { FactoryDecorators, NodeFactory } from '../runtime/node-factory';
import { Node, NodeExtension } from '../ts-api';
import { Application } from './application';
import { Package, PackageValidationContext } from './package';

const logger = loggers.nodeFactory;

export class NodeFactoriesManager {
    private _allFactoriesByName: Dict<NodeFactory> = {};

    private _allFactoriesByTableName: Dict<NodeFactory> = {};

    constructor(private application: Application) {}

    /** @internal - to be called when a NodeFactory needs to be created with full initialization. */
    getFactoryByConstructor(clas: StaticThis<Node>): NodeFactory {
        const constructor = clas as any;
        const decorators = constructor[decoratorsSymbol];

        const factory = this._allFactoriesByName[decorators.node.name];
        if (!factory) throw new Error(`${decorators.node.name}: no factory for Node constructor`);
        return factory;
    }

    /** Checks if there are any name collisions between nodes and enums */
    private checkForNameCollisions(decorators: FactoryDecorators): void {
        if (this.application.nodeAndEnumNames[decorators.node.name] === 'enum') {
            throw new SystemError(
                `Node names cannot match an Enum name. The node name: ${decorators.node.name} was already used as an enum name`,
            );
        } else if (this.application.nodeAndEnumNames[decorators.node.name] === 'node') {
            throw new SystemError(
                `Node names cannot be duplicated. The node: ${decorators.node.name} was already used`,
            );
        }
        this.application.nodeAndEnumNames[decorators.node.name] = 'node';
    }

    /** Creates a NodeFactory instance out of a node. */
    private getOrCreateNodeFactory(pack: Package, clas: StaticThis<Node>): NodeFactory {
        const constructor = clas as any;
        const decorators = constructor[decoratorsSymbol];

        let factory = this._allFactoriesByName[decorators.node.name];
        if (factory) {
            throw new LogicError(
                `Factory ${factory.name} was already created by package ${factory.package.name} but package ${pack.name} tried to create it.`,
            );
        }

        this.checkForNameCollisions(decorators);
        factory = new NodeFactory(pack, clas, decorators);
        this._allFactoriesByName[decorators.node.name] = factory;
        return factory;
    }

    /**
     * @internal
     *
     * Returns the list of node factories for the given list of constructors
     *
     * @param constructors
     */
    private getOrCreateNodeFactories(pack: Package, constructors: StaticThis<Node>[]): NodeFactory[] {
        const trueConstructors = (constructors || []).filter(
            nodeConstructor =>
                nodeConstructor.prototype instanceof Node && !(nodeConstructor.prototype instanceof NodeExtension),
        );
        return trueConstructors.map(nodeConstructor => this.getOrCreateNodeFactory(pack, nodeConstructor));
    }

    /**
     * @internal
     *
     * Load into the application factories the node factories for the given list of constructors
     *
     * */
    createNodeFactories(pack: Package, constructors: StaticThis<Node>[]): NodeFactory[] {
        const factories = this.getOrCreateNodeFactories(pack, constructors);
        pack.factories = pack.factories || [];
        pack.factories.push(...factories);
        return factories;
    }

    // Create the non-subNode factories of the current application.
    private createNonSubNodes(pack: Package): NodeFactory[] {
        const nonSubNodes = Object.values(pack.api.nodes || {}).filter(item => {
            const constructor = item;
            const decorators = constructor[decoratorsSymbol];
            return decorators && !decorators.superDecorators;
        });
        return this.createNodeFactories(pack, nonSubNodes);
    }

    // Create the subNode factories of the current application.
    private createSubNodes(pack: Package): NodeFactory[] {
        const getReadySubNodes = (): StaticThis<Node>[] => {
            // Return subNodes which super class has already been created.
            return Object.values(pack.api.nodes || {}).filter(item => {
                const constructor = item;
                const decorators = constructor[decoratorsSymbol];
                if (!decorators || !decorators.superDecorators) return false;
                const subNodeFactory = this._allFactoriesByName[decorators.node.name];
                const superNodeFactory = this._allFactoriesByName[decorators.superDecorators.node.name];
                if (
                    superNodeFactory &&
                    superNodeFactory.package.isSealed &&
                    pack.application.applicationType !== 'test'
                )
                    throw new SystemError(
                        `Wrongful attempt to extend node ${superNodeFactory.name} from a sealed package.`,
                    );
                return superNodeFactory && !subNodeFactory;
            });
        };

        let readySubNodes: StaticThis<Node>[] = getReadySubNodes();
        const subNodes: NodeFactory[] = [];
        while (readySubNodes.length) {
            subNodes.push(...this.createNodeFactories(pack, readySubNodes));
            readySubNodes = getReadySubNodes();
        }

        // Check for subnodes where base class was not found
        const notFoundSubNodes = Object.values(pack.api.nodes || {}).filter(item => {
            const constructor = item;
            const decorators = constructor[decoratorsSymbol];
            const notFound =
                decorators &&
                decorators.superDecorators &&
                !this._allFactoriesByName[decorators.superDecorators.node.name];
            if (notFound) {
                logger.error(
                    `Base class ${decorators.superDecorators.node.name} not found for ${decorators.node.name}`,
                );
            }
            return notFound;
        });
        if (notFoundSubNodes.length) throw new SystemError('Some subNodes were not created due to missing base nodes');

        return subNodes;
    }

    private static setOperationsDefiningPackage(pack: Package): void {
        const setPackage = (decorator: {
            queries?: Dict<PlainOperationDecorator>;
            mutations?: Dict<PlainOperationDecorator>;
            notificationListeners?: Dict<PlainOperationDecorator>;
            messageListeners?: Dict<PlainOperationDecorator>;
        }): void => {
            Object.values(decorator.queries || {}).forEach(query => {
                query.definingPackage = pack;
            });
            Object.values(decorator.mutations || {}).forEach(mutation => {
                mutation.definingPackage = pack;
            });
            Object.values(decorator.notificationListeners || {}).forEach(listener => {
                listener.definingPackage = pack;
            });
            Object.values(decorator.messageListeners || {}).forEach(listener => {
                listener.definingPackage = pack;
            });
        };

        Object.values(pack.api.nodes || {}).forEach(node => {
            setPackage(node[decoratorsSymbol] || {});
        });
        Object.values(pack.api.nodeExtensions || {}).forEach(extension => {
            setPackage(extension[decoratorsSymbol] || {});
        });
    }

    /** Add the extensions decorators brought by the current application to its dependencies' node factories. */
    private addExtensionDecorators(pack: Package): void {
        if (pack.api.nodeExtensions == null || Object.keys(pack.api.nodeExtensions).length === 0) return;
        const nodeExtensions = Object.values(pack.api.nodeExtensions);
        const factories = pack.application.getAllFactories();
        nodeExtensions.forEach(extension => {
            const extendedNode = extension[decoratorsSymbol].nodeExtension.extends();
            pack._nodeExtensions.push(extension);
            extension[decoratorsSymbol].package = pack;

            // Get a factory's subNodes
            const getSubFactories = (superFactory: NodeFactory): NodeFactory[] => {
                return factories.filter(item => {
                    const superDecorators = item.decorators.superDecorators;
                    if (!superDecorators) return false;
                    return this._allFactoriesByName[superDecorators.node.name] === superFactory;
                });
            };

            // We add the extension to:
            // - the node N explicitly extended by the extension
            // - the subNodes of this node N.
            let factoriesToExtend = [pack.application.getFactoryByConstructor(extendedNode)];

            while (factoriesToExtend.length) {
                const subFactories: NodeFactory[] = [];
                factoriesToExtend.forEach(factory => {
                    factory.addExtensionDecorator(extension);
                    const newSubFactories = getSubFactories(factory);
                    subFactories.push(...newSubFactories);
                });
                factoriesToExtend = subFactories;
            }
        });
    }

    /**
     * @internal
     */
    createAllFactories(packages: Package[], packageValidationContext: PackageValidationContext): void {
        packages.forEach(pack => {
            this.createFactories(pack);
        });
        packages.forEach(pack => {
            this.initFactories(pack);
        });
        this.completeFactories(packageValidationContext);
    }

    /**
     * @internal
     */
    private createFactories(pack: Package): void {
        logger.verbose(() => `Creating factories for package ${pack.name}`);

        // Create NodeFactory instances for each of the "non subNodes" of the new applications.
        //   Non subNodes: nodes that don't inherit from a superNode.
        const nonSubNodes = this.createNonSubNodes(pack);

        // Create NodeFactory instances for subNodes.
        // when creating a subNode, we ensure its superNodes' factories have all already been created before.
        const subNodes = this.createSubNodes(pack);

        // set definingPackage on all operations
        NodeFactoriesManager.setOperationsDefiningPackage(pack);

        // Create main node and superNode properties for subNodes
        nonSubNodes.forEach(factory => factory.createMainNodeProperties(pack));
        subNodes.forEach(factory => factory.createSuperNodeProperties(pack));

        // Create subNodes properties in the following order:
        //   - First the properties defined in the subNodes' superNodes
        //   - Then those defined in the subNode itself.
        //   - Extend the properties that were defined in a superNode and redefined in another superNode or
        //     in the subNode itself.
        subNodes.forEach(factory => factory.createSubNodeProperties(pack));
    }

    private initFactories(pack: Package): void {
        // Add extension decorators to newly created factories. Don't modify the factories in any other way.
        // If an extension extends a superNode, also add it to its subNodes.
        this.addExtensionDecorators(pack);

        const allFactories = Object.values(this._allFactoriesByName);
        const packFactories = pack.factories;
        // const dependentFactories = allFactories.filter(f => !packFactories.includes(f));

        // Merge subNode decorators for each of the created factories.
        // Merges the header, queries and mutations of the superClass into the subClass.
        packFactories
            .filter(factory => !!factory.decorators.superDecorators)
            .forEach(factory => factory.mergeSubNodes());

        // Merge extension decorators for each of the created factories.
        allFactories.forEach(factory => factory.mergeExtensions(pack));

        // Set subFactories (must be done before analyzing references and collections)
        allFactories.forEach(factory => factory.setSubFactories());
    }

    private completeFactories(packageValidationContext?: PackageValidationContext): void {
        const allFactories = Object.values(this._allFactoriesByName);

        // Create subNode overrides after all package extensions were loaded
        // This needs to happen here to support overriding of extended properties
        allFactories
            .filter(factory => factory.decorators.superDecorators)
            .forEach(factory => factory.createSubNodeOverrides());

        allFactories.forEach(factory => {
            if (factory.storage === 'external' && factory.externalStorageManager) {
                factory.externalStorageManager.factory = factory;
            }
        });

        // Operations to complement the newly created node factories.
        allFactories.forEach(factory => factory.complement(packageValidationContext));

        // Checks the factories and their properties are correct.
        allFactories.forEach(factory => factory.verify());

        // Checks the joins, in a separate pass to cope with circular references
        allFactories.forEach(factory => factory.verifyJoins());
    }

    registerListeners(): void {
        Object.values(this._allFactoriesByName).forEach(factory => {
            const registerListener = (listener: MessageListener | NotificationListener): void => {
                listener.register(factory);
            };
            factory.notificationListeners.forEach(registerListener);
            factory.messageListeners.forEach(registerListener);
        });
    }

    /** @internal */
    getFactoryByName(name: string): NodeFactory {
        const factory = this.tryToGetFactoryByName(name);
        if (!factory) {
            throw new Error(`Factory '${name}' could not be found.`);
        }
        return factory;
    }

    /** @internal */
    tryToGetFactoryByName(name: string): NodeFactory | undefined {
        const cachedFactory = this._allFactoriesByName[name];
        if (cachedFactory) return cachedFactory;
        const factory = this.application.getAllFactories().find(f => f.name === name);
        if (factory) {
            this._allFactoriesByName[name] = factory;
        }
        return factory;
    }

    /** @internal */
    getFactoryByTableName(name: string): NodeFactory {
        const cachedFactory = this._allFactoriesByTableName[name];
        if (cachedFactory) return cachedFactory;
        const factory = this.application.getSqlPackageFactories().find(f => f.tableName === name);
        if (!factory) {
            throw new Error(`Factory of table '${name}' could not be found.`);
        }
        this._allFactoriesByTableName[name] = factory;
        return factory;
    }
}
