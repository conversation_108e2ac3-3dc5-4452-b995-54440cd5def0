/* eslint-disable  import/no-dynamic-require, global-require */
/** @packageDocumentation @module runtime */
import { AnyValue, asyncArray, AsyncResponse, dynamicImport, funnel } from '@sage/xtrem-async-helper';
import { Filter } from '@sage/xtrem-client';
import { ConfigManager } from '@sage/xtrem-config';
import { createDictionary, Dict, integer, limitSqsQueueName, LogicError, Maybe } from '@sage/xtrem-shared';
import { topoSort } from '@sage/xtrem-toposort';
import { createHash } from 'crypto';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as fsExtra from 'fs-extra';
import * as glob from 'glob';
import { GraphQLSchema } from 'graphql';
import * as http from 'http';
import { load } from 'js-yaml';
import { camelCase, merge, pick, uniq } from 'lodash';
import * as fsp from 'path';
import { GlobalCache } from '../cache/global-cache';
import { StaticThis } from '../decorators';
import { decoratorsSymbol } from '../decorators/decorator-utils';
import { SchemaBuilder } from '../graphql/schema-builder';
import { NotificationTopic } from '../interop';
import { Property } from '../properties';
import { globalRunningContext, monitoredFunnel } from '../runtime';
import { CollationCache } from '../runtime/collation-cache';
import { Context, ContextOptions, CreateAdminUserOptions, rootUserEmail, UserData } from '../runtime/context';
import { CoreHooks } from '../runtime/core-hooks';
import { GlobalLock } from '../runtime/global-lock';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../runtime/node-factory';
import * as systemDataTypes from '../runtime/system-data-types';
import { sortFactories } from '../runtime/utils';
import { SqlStatementCache } from '../sql/mapper/sql-statement-cache';
import { Table } from '../sql/schema';
import { DatabaseSqlContext } from '../sql/sql-context/database-sql-context';
import { AnyNode, Node } from '../ts-api';
import { Activity } from '../ts-api/activity';
import { DataType, DataTypeOptions, EnumDataType, ReferenceDataType } from '../types';
import { testPackageExclusions } from '../utils';
import { WorkflowManagerInterface } from '../workflow/workflow-manager-interface';
import { ActivityManager } from './activity-manager';
import { ClientSettingsManager } from './client-settings-manager';
import { CsvChecksumManager } from './csv-checksum-manager';
import { HotUpgradeManager } from './hot-upgrade-manager';
import { NodeFactoriesManager } from './node-factories-manager';
import { NotificationManager } from './notification-manager';
import { Package, PackageCreateOptions, PackageJsonAbout, PackageJsonFile, XtremAboutHelper } from './package';
import { PackageLoader } from './package-loader';
import { PackageManager } from './package-manager';
import { ServiceOption } from './service-option';
import { ServiceOptionManager } from './service-option-manager';
import { UiBroadcaster } from './ui-broadcaster';
import './unhandled-error-monitor';

Error.stackTraceLimit = Math.max(Error.stackTraceLimit, 100);

export interface Dependency {
    name: string;
    version: string;
}

/**
 * Type of application:
 *                          +---------+-------+------+----------+-----+
 *                          | service | batch | test | dev-tool | job |
 * +------------------------+---------+-------+------+----------+-----+
 * | graphQL API            |    X    |   -   |   X  |    -     |  -  |
 * +------------------------+---------+-------+------+----------+-----+
 * | system broadcasts      |    X    |   X   |   X  |    -     |  X  |
 * +------------------------+---------+-------+------+----------+-----+
 * | applicative queues     |  in dev |   X   |   X  |    -     |  -  |
 * +------------------------+---------+-------+------+----------+-----+
 */
export type ApplicationType =
    /**
     * example: pnpm run start
     */
    | 'service'
    /**
     * example: xtrem test, ....
     */
    | 'test'
    /**
     * example: xtrem compile, ....
     */
    | 'dev-tool'
    /**
     * example: xtrem manage, schema, tenant, ....
     */
    | 'admin-tool'
    /**
     * example: upgrade operations, ...
     */
    | 'job';

export interface ApplicationCreateOptions extends PackageCreateOptions {
    applicationType?: ApplicationType;

    schemaName?: string;

    // directory of the test-application.ts file (test only)
    testApplicationDir?: string;

    startOptions?: ApplicationStartServicesOptions;
}

export interface ApplicationConstructorOptions extends ApplicationCreateOptions {
    schemaName: string;
}

export const allowedStartChannels = <const>['graphql', 'listeners', 'routing'];
export type StartChannel = (typeof allowedStartChannels)[number];
export interface ApplicationStartServicesOptions {
    services: string[];
    channels: StartChannel[];
    webSockets?: string[];
    /**
     * Names of queues to explicitly start
     * If not set, all the queues will be started
     */
    queues?: string[];
    configExtensionLocation?: string;
}

type WithContextFunction = <T extends AnyValue | void>(
    tenantId: string | null,
    body: (context: Context) => AsyncResponse<T>,
    options?: ContextOptions,
) => Promise<T>;

export interface WithContext {
    withCommittedContext: WithContextFunction;
    withUncommittedContext: WithContextFunction;
    withReadonlyContext: WithContextFunction;
}

const logger = loggers.application;

const requestFunnelSize = (): number =>
    (ConfigManager.current.storage?.sql?.max || ConfigManager.current.storage?.maxConnections || 20) *
    (ConfigManager.current.server?.requestFunnelSizeFactor || 1);

const testApiCache = createDictionary<any>();

export class Application {
    #mainPackage: Package;

    #rootAbout: PackageJsonAbout;

    #about: PackageJsonAbout;

    #workspaceDir: string;

    #workspaceHash: string;

    #hotUpgradeManager: HotUpgradeManager;

    /**
     * The manager in charge of the checksums of loaded CSV files
     */
    #csvChecksumManager: CsvChecksumManager;

    /** @internal */
    readonly packageLoader = new PackageLoader(this);

    readonly factoriesManager = new NodeFactoriesManager(this);

    readonly activityManager = new ActivityManager(this);

    #serviceOptionManager: ServiceOptionManager;

    #packageManager: PackageManager;

    #notificationManager: NotificationManager;

    #clientSettingsManager: ClientSettingsManager;

    #workflowManager: WorkflowManagerInterface;

    #uiBroadcaster: UiBroadcaster;

    /** @internal */
    readonly nonPackageDependencies: Dict<boolean> = {};

    #platformPackages: Dependency[];

    #sqlPackageFactories: NodeFactory[];

    #pluginDependencies: Dict<string> = {};

    static readonly emitter = new EventEmitter();

    /** @internal */
    globalCache = new GlobalCache();

    /** @internal */
    globalLock = new GlobalLock(this);

    /* Used to prevent name collisions among nodes and enums */
    readonly nodeAndEnumNames: Dict<'node' | 'enum'> = {};

    readonly dataTypes: Dict<DataType<AnyValue, DataTypeOptions>> = {};

    readonly notificationTopics: Dict<NotificationTopic> = {};

    private graphqlSchemaFunnel = funnel(1);

    private graphqlSchemas: Dict<GraphQLSchema> = {};

    readonly requestFunnel = monitoredFunnel('graphqlRequest', requestFunnelSize());

    private static hasInstance = false;

    /**
     * Should some verifications be done (check on properties, on factories, ...)
     * @internal
     */
    readonly skipVerifications: boolean;

    /**
     * Cache of SQL statements
     * @internal
     */
    readonly sqlStatementCache = new SqlStatementCache();

    /** The external http server, set when the application is started with 'graphql' inside `startOptions.channels` */
    graphqlHttpServer?: http.Server;

    /** Is the application ready to process requests */
    #isReady = false;

    /** @internal */
    constructor(readonly options: ApplicationConstructorOptions) {
        if (process.env.XTREM_SKIP_FACTORY_CHECKS === 'true' || process.env.XTREM_SKIP_FACTORY_CHECKS === '1') {
            this.skipVerifications = true;
            logger.warn(
                'XTREM_SKIP_FACTORY_CHECKS is set: some checks will be skipped on packages/factories/properties',
            );
        }

        options.applicationType = options.applicationType || 'dev-tool';
        globalRunningContext.configure(options?.applicationType);

        if (Application.hasInstance) {
            logger.warn('Main application already set!');
        }
        Application.hasInstance = true;
    }

    async init(): Promise<Application> {
        this.#mainPackage = await Package.create(this, null, this.options);
        await this.tryLoadTestApi();

        logger.info(
            `[${this.schemaName}] starting application ${this.mainPackage.name} from folder ${this.mainPackage.dir}`,
        );

        // Create packages
        await this.packageLoader.createPackages();

        // Register the workflow manager before registering the workflow steps
        this.#workflowManager = CoreHooks.createWorkflowManager(this);

        // Register datatypes
        this.registerMiscArtifacts();

        this.#packageManager = CoreHooks.createPackageManager(this);
        this.#serviceOptionManager = CoreHooks.createServiceOptionManager(this);
        this.#notificationManager = CoreHooks.createNotificationManager(this);
        this.#clientSettingsManager = CoreHooks.createClientSettingsManager(this);

        this.#uiBroadcaster = new UiBroadcaster(this, {
            timeout: ConfigManager.current.uiBroadcastTimeout,
        });

        if (this.applicationType === 'service') this.initFactoryCacheLogs();
        return this;
    }

    static create(options: ApplicationConstructorOptions): Promise<Application> {
        return new Application(options).init();
    }

    get mainPackage(): Package {
        return this.#mainPackage;
    }

    get serviceOptionManager(): ServiceOptionManager {
        return this.#serviceOptionManager;
    }

    get packageManager(): PackageManager {
        return this.#packageManager;
    }

    get notificationManager(): NotificationManager {
        return this.#notificationManager;
    }

    get clientSettingsManager(): ClientSettingsManager {
        return this.#clientSettingsManager;
    }

    get workflowManager(): WorkflowManagerInterface {
        return this.#workflowManager;
    }

    get uiBroadcaster(): UiBroadcaster {
        return this.#uiBroadcaster;
    }

    /**
     * Returns the test nodes API if the application is run from a test/fixtures directory
     * that contains a test-application.ts file.
     * Returns an empty object otherwise.
     */
    private async tryLoadTestApi(): Promise<void> {
        if (process.env.XTREM_USE_TEST_APPLICATION !== '1') return;

        const testApplicationDir = fsp.join(this.dir, 'test/fixtures');
        if (!fs.existsSync(testApplicationDir)) return;

        const testApplicationTsPath = fsp.join(testApplicationDir, 'test-application.ts');
        if (!fs.existsSync(testApplicationTsPath)) return;

        logger.info(`Loading test artifacts from ${testApplicationDir}`);

        this.options.testApplicationDir = testApplicationDir;

        const testApplicationJsPath = testApplicationTsPath
            .replace('test/fixtures', 'build/test/fixtures')
            .replace('.ts', '.js');
        const testApi = testApiCache[testApplicationJsPath] || (await dynamicImport(testApplicationJsPath));
        testApiCache[testApplicationJsPath] = testApi;
        merge(this.mainPackage.api, testApi);

        const testApplicationOptionsPath = fsp.join(this.dir, 'test/fixtures/test-application-options.yml');
        if (fs.existsSync(testApplicationOptionsPath)) {
            const testApplicationOptions = load(fs.readFileSync(testApplicationOptionsPath, 'utf8'));
            merge(this.options, testApplicationOptions);
        }
    }

    registerDataType(pack: Package, name: string, dataType: DataType<AnyValue, DataTypeOptions>): void {
        if (this.dataTypes[name])
            throw new Error(`Datatype ${name} in ${pack.name} already declared in ${this.dataTypes[name]?.pack}`);
        dataType.pack = dataType.pack ?? pack.name;
        dataType.name = dataType.name ?? name;
        this.dataTypes[name] = dataType;
    }

    // Temporary method to auto-generated missing reference data types
    // TODO: remove when we have reference data types for all published nodes
    private registerDefaultReferenceDataType(pack: Package, name: string, nodeConstructor: StaticThis<Node>): void {
        if (typeof nodeConstructor !== 'function') return;
        const factory = this.getFactoryByName(name);
        if (!factory.isPublished) return;
        const dataTypeName = camelCase(name);
        let dataType = this.dataTypes[dataTypeName];
        if (dataType) {
            if (dataType.type !== 'reference') {
                logger.warn(
                    `Cannot define default reference data type ${dataTypeName} in ${pack.name}: name conflicts with ${dataType.type} data type defined in ${dataType.pack}`,
                );
            }
            return;
        }
        const paths = [] as string[];
        const fillPaths = (f: NodeFactory, prefix = '', factories: string[] = []): void => {
            factories.push(f.name);
            const manageProperty = (prop: Property): void => {
                let propertyPath = prefix ? `${prefix}.${prop.name}` : prop.name;
                if (prop.isReferenceProperty()) {
                    if (factories.includes(prop.targetFactory.name)) paths.push(prefix ? `${prefix}._id` : '_id');
                    else fillPaths(prop.targetFactory, propertyPath, factories);
                } else {
                    if (prop.isTextStreamProperty() || prop.isBinaryStreamProperty()) {
                        propertyPath = `${propertyPath}.value`;
                    }
                    paths.push(propertyPath);
                }
            };
            const naturalKey = f.isVitalChild
                ? f.naturalKey?.filter(key => key !== f.vitalParentProperty.name && key !== '_sortValue')
                : f.naturalKey;
            if (!naturalKey || naturalKey.length === 0) {
                const lookupAccessProperties = f.properties.filter(
                    prop =>
                        prop.lookupAccess &&
                        !prop.isSystemProperty &&
                        !prop.isCollectionProperty() &&
                        !prop.isReferenceArrayProperty() &&
                        prop.name !== '_sortValue' &&
                        !f.naturalKey?.includes(prop.name) &&
                        !prop.isVitalParent,
                );
                if (lookupAccessProperties.length > 0) {
                    lookupAccessProperties.forEach(prop => {
                        manageProperty(prop);
                    });
                } else if (f.keyPropertyNames.length > 0) {
                    // For external factories key properties are required so we can use them in the lookup
                    f.keyPropertyNames.forEach(propertyName => {
                        const p = f.findProperty(propertyName);
                        manageProperty(p);
                    });
                } else {
                    paths.push(prefix ? `${prefix}._id` : '_id');
                }
            } else {
                naturalKey.forEach(propertyName => {
                    const p = f.findProperty(propertyName);
                    if (p.isSystemProperty || p.name === '_sortValue') return;
                    manageProperty(p);
                });
            }
        };
        fillPaths(factory);
        if (paths.length === 0) paths.push('_id');
        dataType = new ReferenceDataType<AnyNode>({
            reference: () => factory.nodeConstructor as unknown as StaticThis<AnyNode>,
            lookup: {
                valuePath: paths[0],
                helperTextPath: paths[0],
                columnPaths: paths,
            },
        });
        dataType.name = dataTypeName;
        dataType.pack = pack.name;
        this.dataTypes[dataTypeName] = dataType;
    }

    registerNotificationTopic(pack: Package, name: string, topic: NotificationTopic): void {
        if (this.notificationTopics[name])
            throw new LogicError(
                `Notification topic ${name} in ${pack.name} already declared in ${this.notificationTopics[name]?.definingPackage.name}`,
            );
        topic.completeRegistration(name, pack);
        this.notificationTopics[name] = topic;
        topic.verify();
    }

    findNotificationTopic(name: string): NotificationTopic {
        const topic = this.notificationTopics[name];
        if (!topic) throw new LogicError(`Notification topic ${name} not found`);
        return topic;
    }

    registerMiscArtifacts(): void {
        // Register system data types
        Object.entries(systemDataTypes).forEach(([name, dataType]) => {
            if (dataType?.type) this.registerDataType(this.mainPackage, name, dataType);
        });

        this.getPackages().forEach(pack => {
            Object.entries(pack.api.dataTypes || {}).forEach(([name, dataType]) => {
                if (dataType?.type) this.registerDataType(pack, name, dataType);
            });
            Object.entries(pack.api.nodes || {}).forEach(([name, nodeConstructor]) => {
                const decorators = nodeConstructor[decoratorsSymbol];
                if (decorators) this.registerDefaultReferenceDataType(pack, name, nodeConstructor);
            });

            pack.getEnumDataTypes().forEach(dataType => {
                if (dataType instanceof EnumDataType && dataType.name)
                    this.registerDataType(pack, dataType.name, dataType);
            });

            Object.entries(pack.api.notificationTopics || {}).forEach(([name, topic]) => {
                this.registerNotificationTopic(pack, name, topic);
            });
            Object.values(pack.api.workflowSteps || {}).forEach(workflowStep => {
                this.workflowManager.registerWorkflowStepConstructor(pack.name, workflowStep);
            });

            Object.entries(this.dataTypes).forEach(([, dataType]) => {
                if (dataType instanceof ReferenceDataType) {
                    dataType.setReferenceNodePackage(pack);
                }
            });
        });

        // All dataTypes of the application are registered, updates enum dataType values with enum extensions
        Object.values(this.dataTypes).forEach(dataType => {
            if (dataType instanceof EnumDataType && dataType.rootDataType != null) {
                // We only allow extension of enum dataType on external applications and platform unit tests.
                const allowEnumExtensions =
                    ConfigManager.current.storage?.managedExternal ||
                    testPackageExclusions.includes(this.mainPackage.name);
                if (!allowEnumExtensions) {
                    throw new LogicError(
                        `${dataType.name}: enum extensions are only allowed on external applications.`,
                    );
                }
                dataType.mergeExtension();
            }
        });
    }

    /**
     * Returns the manager in charge of hot-upgrades
     */
    get hotUpgradeManager(): HotUpgradeManager {
        if (!this.#hotUpgradeManager) {
            this.#hotUpgradeManager = CoreHooks.createHotUpgradeManager(this);
        }
        return this.#hotUpgradeManager;
    }

    get isReady(): boolean {
        return this.#isReady;
    }

    setReady(): void {
        this.#isReady = true;
    }

    /**
     * The manager in charge of the checksums of loaded CSV files
     */
    get csvChecksumManager(): CsvChecksumManager {
        if (!this.#csvChecksumManager) {
            this.#csvChecksumManager = CoreHooks.createCsvChecksumManager(this);
        }
        return this.#csvChecksumManager;
    }

    get applicationType(): ApplicationType | undefined {
        return this.options.applicationType;
    }

    #addOnPackagePaths: string[];

    /**
     * Get the add-on package paths
     */
    get addOnPackagePaths(): string[] {
        const useMultiWorkerService = process.env.XTREM_USE_MULTI_WORKER === 'true';
        // worker id of the process
        const workerId = process.env.XTREM_WORKER_ID;
        if (useMultiWorkerService && !workerId) return [];

        if (this.#addOnPackagePaths) return this.#addOnPackagePaths;

        // add-on path from the config
        const addOnsConfigPath = ConfigManager.current.addOns?.folder;
        // default add-on path relative to the application root folder
        const defaultPath = fsp.join(process.cwd(), 'add-ons');
        // The root path to look for add-ons
        const addOnsRootPath = addOnsConfigPath ?? defaultPath;
        // Set the current working folder;
        // If we are in a worker process, the add-ons must be in a sub-folder of the root add-ons paths in a folder
        // bearing the worker id
        // Otherwise we look in the add-ons root path directly
        const cwd = workerId ? fsp.join(addOnsRootPath, workerId) : addOnsRootPath;

        logger.info(`Looking for add-ons in: ${cwd}`);
        // we need to filter out node_modules for now as the glob ignore option does not work
        // See https://github.com/isaacs/node-glob/issues/570
        const packageJsonPaths = glob
            .sync('./**/package.json', { cwd, realpath: true, absolute: true, ignore: 'node_modules/**' })
            .filter(p => !/node_modules/.test(p));
        let addOnsPackages = packageJsonPaths.map(p => {
            const packageJson = require(p);
            return { name: packageJson.name, dependsOn: Object.keys(packageJson.dependencies), path: fsp.dirname(p) };
        });

        // convert to format needed for toposort
        addOnsPackages = addOnsPackages.map(a => {
            return { ...a, dependsOn: a.dependsOn.filter(dep => addOnsPackages.find(adp => adp.name === dep)) };
        });
        // We need to toposort the add-ons so that they are loaded in the correct order and to ensure their are no
        // cycles between add-on packages.
        const sortedAddOnsPackages = topoSort(addOnsPackages);
        this.#addOnPackagePaths = sortedAddOnsPackages.map(a => a.path);
        this.#addOnPackagePaths.forEach(addOnPath => logger.info(`Add-on package will be load from: ${addOnPath}`));

        // If there are add-ons we need create symlinks for:
        //  - the root node_modules
        //  - cross add-on package dependencies
        // This is needed as when the add-on package is required the root node-modules is not in context if the add-ons are in
        // a directory that is not within the application directory
        // We need the symlinks between add-on packages, as add-ons are not in the root application node_modules
        if (this.#addOnPackagePaths.length > 0) {
            const createSymlink = (target: string, symLinkPath: string): void => {
                logger.info(`Creating symlink  ${symLinkPath} -> ${target}`);
                // We need to use junctions for windows as symlinks are not supported
                // we don't need to clean up the symlinks as the create will overwrite them
                fsExtra.createSymlinkSync(target, symLinkPath, 'junction');
            };

            const sourceNodeModules = fsp.join(process.cwd(), 'node_modules');
            if (fs.existsSync(sourceNodeModules)) {
                createSymlink(sourceNodeModules, fsp.join(cwd, 'node_modules'));
            }

            sortedAddOnsPackages.forEach(a => {
                const targetDepNodeModules = fsp.join(a.path, 'node_modules');
                if (!fs.existsSync(targetDepNodeModules)) fs.mkdirSync(targetDepNodeModules);

                a.dependsOn.forEach(dep => {
                    const depPack = sortedAddOnsPackages.find(adp => adp.name === dep);
                    if (depPack) {
                        createSymlink(depPack.path, fsp.join(targetDepNodeModules, depPack.name));
                    }
                });
            });
        }

        return this.#addOnPackagePaths;
    }

    get schemaName(): string {
        return this.options.schemaName;
    }

    /** Only for testing purposes */
    /** @internal */
    set schemaName(schemaName: string) {
        this.options.schemaName = schemaName;
    }

    get activities(): Dict<Activity> {
        return this.activityManager.getActivities();
    }

    get serviceOptionsByName(): Dict<ServiceOption> {
        return this.serviceOptionManager.serviceOptionsByName;
    }

    findServiceOption(name: string): ServiceOption {
        const serviceOption = this.serviceOptionsByName[name];
        if (!serviceOption) throw new Error(`${name}: invalid service option name`);
        return serviceOption;
    }

    /**
     * List of packages started in the current container.
     */
    private _servicePackagesStarted?: Package[];

    private _startOptions: ApplicationStartServicesOptions;

    get startOptions(): ApplicationStartServicesOptions {
        if (this._startOptions) return this._startOptions;

        const mainService = this.mainPackage.name;
        const communicationService = '@sage/xtrem-communication';
        const routingService = '@sage/xtrem-routing';
        const schedulerService = '@sage/xtrem-scheduler';
        const workflowService = '@sage/xtrem-workflow';
        const interopService = '@sage/xtrem-interop';
        let addMainService = true;
        const managedExternal = ConfigManager.current.storage?.managedExternal;

        const fixServiceDomain = (service: string): string => {
            return service.startsWith('@') ? service : `@sage/xtrem-${service}`;
        };

        // Initialize start options
        this._startOptions = {
            channels: this.options?.startOptions?.channels || ['graphql'],
            queues: this.options.startOptions?.queues,
            services: [],
        };

        const addService = (service: string): void => {
            // If the application is not manage externally and the service being added is not the routing or communication service
            // we do not add the of the main service implicitly at the end
            if (
                ![routingService, communicationService, schedulerService, workflowService, interopService].includes(
                    service,
                )
            )
                addMainService = false;
            if (!this._startOptions.services.includes(service)) this._startOptions.services.push(service);
        };

        if (this.options.startOptions?.services?.length) {
            this.options.startOptions.services
                .map(service => fixServiceDomain(service))
                .forEach(service => addService(service));
        }

        // If listeners channel is requested then we add the communication service
        if (!managedExternal && this._startOptions.channels.includes('routing')) {
            addService(routingService);
            addService(schedulerService);
        }

        if (!managedExternal && this._startOptions.channels.includes('listeners')) {
            addService(communicationService);
            addService(workflowService);
        }

        if (!managedExternal) {
            addService(interopService);
        }

        // Implicit adding the main service if no other service is started
        if (managedExternal || addMainService) {
            addService(mainService);
        }

        /**
         * Scenario 1 :
         *  pnpm run start
         *  No start options are passed and the service is not managed externally.
         *  Outcome- We start the routing service, listener service, graphql service and listen to the main package tree.
         *
         * Scenario 2 :
         *  pnpm run start
         *  No start options are passed and the service is managed externally. (X3)
         *  Outcome- We start the graphql service.
         *
         * Scenario 3 :
         *  start options has the routing or listeners channels without services,
         *  pnpm run start --channels='routing'
         *  pnpm run start --channels='listeners'
         *  pnpm run start --channels='listeners,routing'
         *  Outcome- If managed external (x3) we start the graphql service. If not managed external then we start relevant
         *           communication service (routing/listeners) and listen to the main package tree. The graphql service is not started.
         *
         * Scenario 4 :         *
         *  pnpm run start --channels='routing' --services='reporting'
         *  pnpm run start --channels='listeners' --services='reporting'
         *  pnpm run start --channels='listeners,routing' --services='reporting'
         *  start options has the routing or listeners channels with services that are not routing or communication,
         *  Outcome- If managed external (x3) we do not start the graphql service (they should not use channels).
         *           If not managed external then we start relevant communication service (routing/listeners)
         *           and listen to the the services. The graphql service is not started.
         *
         * Scenario 5 :
         *  pnpm run start --services='reporting'
         *  start options has no channels with services that are not routing or communication,
         *  Outcome- We start graphql service. If not managed external then we start relevant communication service (routing and listeners)
         *           and listen to the the services provided.
         */

        return this._startOptions;
    }

    get servicePackagesStarted(): Package[] {
        if (!this._servicePackagesStarted) {
            const explicitlyIncludedPackages = this.getPackages().filter(pack => {
                if (this.startOptions.services?.length) return this.startOptions.services.includes(pack.name);
                return true;
            });

            // If a package is started by the container, all of its dependencies needs to be includes.
            const allPackages = new Set<Package>();
            explicitlyIncludedPackages.forEach(pack => {
                allPackages.add(pack);
                // get array of the entire dependency tree of service package
                // if service package dependency tree is
                // foo
                //  -> bar
                //   -> foo2
                //  -> foo2
                // then the packages collected will be [foo,bar,foo2]
                // We only start the services of dependencies if the package started is a main package
                if (pack.isMain) pack.allDependencies.filter(dep => dep.isService).forEach(dep => allPackages.add(dep));
            });
            this._servicePackagesStarted = [...allPackages];
        }
        return this._servicePackagesStarted;
    }

    /**
     * emit events to start listeners
     * @param otherTopics  topics to emit other than the standard `listen` and `ready`, used for unit tests.
     */
    startListeners(otherTopics?: string[]): void {
        Application.emitter.emit('listen', this);
        if (otherTopics)
            otherTopics.forEach(topic => {
                Application.emitter.emit(topic, this);
            });
        // listeners are installed, advertise that we are ready
        Application.emitter.emit('ready', this);
    }

    /**
     * Limits the length of an SQS queue name to 75 characters, 80 with the .fifo extension.
     * If the name is already 75 characters or less, it is returned as is.
     * If the name is longer than 75 characters, it is truncated and appended with "---" in the middle.
     * @param name - The original queue name.
     * @returns The modified queue name.
     */
    static limitSqsQueueName(name: string): string {
        return limitSqsQueueName(name);
    }

    /**
     * Generates the raw SQS queue name based on the provided name.
     *
     * For unit tests, the queue name will be the package name with the routing queue name appended.
     * In multi-app mode, the queue name is prefixed with the app name if it is not already prefixed.
     *
     * @param name - The name used to generate the queue name.
     * @returns The generated raw SQS queue name.
     */
    private rawSqsQueueName(name: string): string {
        // For unit tests we generate the elasticMq config and a notification queue is added per package
        // The queue name will be the package name with the routing queue name appended
        // e.g. routing is 2088ef5--test-xtrem-sales-routing, when running the sale unit tests
        if (this.applicationType === 'test') {
            return `${this.workspaceId}--test-${this.name.split('/').pop()}-${name}`;
        }
        let sqsName = name;
        const config = ConfigManager.current;
        // In multi-app mode we prefix the queue name with the app name (converted to kebab-case),
        // if it is not already prefixed
        const app = ConfigManager.current.app?.replace(/_/g, '-');
        if (app && !name.startsWith(`${app}--`)) {
            sqsName = `${app}--${name}`;
        }

        // In development mode, we prefix the queue name with the workspaceId
        if (config.deploymentMode === 'development') {
            if (name.startsWith(`${this.workspaceId}--`)) {
                throw new LogicError(
                    `Queue name '${name}' must not be prefixed with workspace id '${this.workspaceId}--'`,
                );
            }
            sqsName = `${this.workspaceId}--${sqsName}`;
        }

        // Otherwise we just return the sqsName
        return sqsName;
    }

    /**
     * Returns the SQS queue name for the given name.
     * @param name - The name of the queue.
     * @returns The SQS queue name.
     */
    sqsQueueName(name: string): string {
        return Application.limitSqsQueueName(this.rawSqsQueueName(name));
    }

    getPluginDependencies(): Dict<string> {
        return { ...this.#pluginDependencies };
    }

    /** @internal */
    static resetMain(): void {
        this.hasInstance = false;
    }

    /** @disabled_internal */
    /**
     * Returns the factories that have their own table in the database.
     * The returned factories are sorted so that referenced factories
     * are always before the factories that reference them with non nullable references.
     */
    getSqlPackageFactories(): NodeFactory[] {
        if (!this.#sqlPackageFactories || this.options.applicationType === 'test') {
            this.#sqlPackageFactories = sortFactories(
                this.getAllFactories().filter(factory => factory.storage === 'sql'),
            );
        }
        return this.#sqlPackageFactories;
    }

    get name(): string {
        return this.mainPackage.name;
    }

    /**
     * Returns the short name of an application without the sage prefix
     * for instance, will return 'xtrem-services-main' for the @sage/xtrem-services-main application
     */
    get shortName(): string {
        return this.name.substring(this.name.lastIndexOf('/') + 1);
    }

    get packageName(): string {
        return this.mainPackage.packageName;
    }

    get dir(): string {
        return this.mainPackage.dir;
    }

    get tmpDir(): string {
        return fsp.join(this.dir, 'tmp');
    }

    get workspaceId(): string {
        if (this.#workspaceHash) {
            return this.#workspaceHash;
        }
        this.#workspaceHash = createHash('md5').update(this.workspaceDir).digest('hex').substring(0, 7);
        return this.#workspaceHash;
    }

    get workspaceDir(): string {
        if (this.#workspaceDir) {
            return this.#workspaceDir;
        }
        let dirName = this.dir;
        while (dirName) {
            const pnpmLock = fsp.resolve(dirName, 'pnpm-lock.yaml');
            const packageLock = fsp.resolve(dirName, 'package-lock.json');
            if (fs.existsSync(pnpmLock) || fs.existsSync(packageLock)) {
                this.#workspaceDir = dirName;
                return this.#workspaceDir;
            }
            const parent = fsp.dirname(dirName);
            if (parent === dirName) {
                break;
            }
            dirName = parent;
        }
        throw new Error('detection of root directory failed');
    }

    get version(): string {
        return this.mainPackage.packageJson.version;
    }

    get about(): PackageJsonAbout {
        if (!this.#about) {
            this.#about = XtremAboutHelper.load(this, this.mainPackage.name);
        }
        return this.#about;
    }

    get rootAbout(): PackageJsonAbout {
        if (!this.#rootAbout) {
            this.#rootAbout = XtremAboutHelper.load(this);
        }
        return this.#rootAbout;
    }

    checkIfPluginPackage(depPackageContent: PackageJsonFile | null): void {
        if (depPackageContent?.xtremPlugin) {
            this.#pluginDependencies[depPackageContent.name] = depPackageContent.version;
        }
    }

    /**
     * Validate all Activities loaded from packages
     */
    validateActivities(): void {
        const validateActivity = (activityName: string): void => {
            const activity = this.activities[activityName];

            if (activity.description === '') {
                throw new Error(`Activity ${activityName} has no description defined.`);
            }

            // permissionGrants must have permissions defined, so that services access allocation can have a list to select from
            if (activity.permissions.length === 0) {
                throw new Error(`Activity ${activityName} has no permissions defined.`);
            }

            if (activity.operationGrants) {
                Object.values(activity.operationGrants).forEach(operationGrants => {
                    operationGrants.forEach(operationGrant => {
                        (operationGrant.on || [() => activity.node]).forEach(node => {
                            const grantFactory = this.getFactoryByConstructor(node());
                            operationGrant.operations.forEach(operation =>
                                ActivityManager.checkOperation(activityName, operation, grantFactory, { throws: true }),
                            );
                        });
                    });
                });
            }
        };

        Object.keys(this.activities).forEach(activityName => {
            validateActivity(activityName);
        });

        if (!ConfigManager.current.storage?.managedExternal) {
            // Check all published non-abstract nodes in activity references
            const noActivityNodes: string[] = [];
            this.getAllFactories().forEach(factory => {
                if (factory.isPublished && !factory.isAbstract && !factory.authorizedBy) {
                    // Check if the node has an associated activity
                    if (!this.activityManager.isNodeAccessControlled(factory)) {
                        noActivityNodes.push(factory.name);
                    }
                }
            });

            if (noActivityNodes.length > 0) {
                logger.warn(`The following nodes are not associated with any activity: ${noActivityNodes.join(', ')}`);
            }
        }
    }

    getPackages(): Package[] {
        return Object.values(this.packageLoader.packagesByName);
    }

    get packagesByName(): Dict<Package> {
        return this.packageLoader.packagesByName;
    }

    findPackage(name: string): Package {
        const pack = this.packagesByName[name];
        if (!pack) throw new LogicError(`Package not found: ${name}`);
        return pack;
    }

    /**
     * Gets all platform packages required by the application packages
     */
    getPlatformPackages(): Dependency[] {
        if (this.#platformPackages) return this.#platformPackages;

        const packagesByName: Dict<Package> = {};
        this.getPackages().forEach((pack: Package) => {
            packagesByName[pack.name] = pack;
        });
        const isDevMode = ConfigManager.current.deploymentMode === 'development';
        const dependencies = {} as Dict<PackageJsonFile>;
        const tryGetPackageJson = (path: string): Maybe<PackageJsonFile> => {
            try {
                return Package.getPackageJson(path);
            } catch (e) {
                if (e.code !== 'MODULE_NOT_FOUND') throw e;
                return undefined;
            }
        };

        const scan = (dir: string, pack: PackageJsonFile): void => {
            const packageJsonDependencies = Object.keys(pack.dependencies || {});

            const packageJsonPeerDependencies = Object.keys(pack.peerDependencies || {});

            const packageJsonPackDependencies = uniq([...packageJsonDependencies, ...packageJsonPeerDependencies]);
            if (packageJsonPackDependencies.length) {
                packageJsonPackDependencies
                    .filter(dep => dep.startsWith('@sage/'))
                    .forEach(dep => {
                        if (dependencies[dep]) return;
                        if (packagesByName[dep]) {
                            dependencies[dep] = packagesByName[dep].packageJson;
                            scan(packagesByName[dep].dir, packagesByName[dep].packageJson);
                        } else {
                            let paths = [
                                // prod mode path like:
                                // .../xtrem/node_modules/@sage/xtrem-cli/../../@sage/xtrem-service/
                                fsp.join(dir, '../..', dep),
                                // dev mode path like:
                                // .../xtrem/platform/system/xtrem-system/node_modules/@sage/xtrem-cli/node_modules/@sage/xtrem-service/
                                fsp.join(dir, 'node_modules', dep),
                            ];
                            if (isDevMode) paths = paths.reverse();
                            const resolvedPackage = {} as { packageJson?: PackageJsonFile; path?: string };
                            paths.some(path => {
                                resolvedPackage.path = path;
                                resolvedPackage.packageJson = tryGetPackageJson(path);
                                return resolvedPackage.packageJson != null;
                            });
                            if (resolvedPackage.packageJson && resolvedPackage.path) {
                                dependencies[dep] = resolvedPackage.packageJson;
                                scan(resolvedPackage.path, resolvedPackage.packageJson);
                            }
                        }
                    });
            }
        };
        this.getPackages().forEach((pack: Package) => {
            scan(pack.dir, pack.packageJson);
        });

        this.#platformPackages = Object.values(dependencies)
            .filter(dep => !dep.xtrem && !dep.xtremPlugin)
            .map(dep => pick(dep, 'name', 'version'));

        return this.#platformPackages;
    }

    getAllFactories(): NodeFactory[] {
        return ([] as NodeFactory[]).concat(...this.getPackages().map(pack => pack.factories));
    }

    /** @internal */
    getTableByName(name: string): Table {
        const factory = this.getSqlPackageFactories().find(f => {
            return f.table?.name === name;
        });
        if (!factory) {
            throw new Error(`Factory '${name}' could not be found.`);
        }
        return factory.table;
    }

    /** @disabled_internal */
    getFactoryByName(name: string): NodeFactory {
        return this.factoriesManager.getFactoryByName(name);
    }

    /** @disabled_internal */
    tryToGetFactoryByName(name: string): NodeFactory | undefined {
        return this.factoriesManager.tryToGetFactoryByName(name);
    }

    /** @disabled_internal */
    getFactoryByConstructor(nodeConstructor: StaticThis<Node>): NodeFactory {
        return this.factoriesManager.getFactoryByConstructor(nodeConstructor);
    }

    getFactoryByTableName(name: string): NodeFactory {
        return this.factoriesManager.getFactoryByTableName(name);
    }

    tryGetFactoryByTableName(name: string): NodeFactory | undefined {
        try {
            return this.factoriesManager.getFactoryByTableName(name);
        } catch {
            return undefined;
        }
    }

    findFactoriesUsingDatatype(dataType: DataType<AnyValue, unknown>): NodeFactory[] {
        return this.getAllFactories().filter(factory =>
            factory.properties.some(property => property.dataType === dataType),
        );
    }

    /**
     * Checks if a record is used in another node references
     * @param id _id of the record to be checked in other references
     * @param context current context
     * @param className className of the node of _id
     * @returns Promise
     */
    isRecordUsed(id: number, context: Context, className: string): Promise<boolean> {
        const nodeFactory = this.getFactoryByName(className);
        const allReferringNodes = Array.from(
            new Set(nodeFactory.referringProperties.map(refProp => refProp.targetFactory.nodeConstructor)),
        );

        return asyncArray(allReferringNodes).some(async node => {
            const filter: Filter<any> = {
                _or: nodeFactory.referringProperties
                    .filter(
                        refNode => refNode.targetFactory.nodeConstructor === node && refNode.property.isStored === true,
                    )
                    .map(prop => {
                        return { [prop.property.name]: { _id: id } };
                    }),
            };
            return (await context.query(node, { filter, first: 1 }).length) > 0;
        });
    }

    getAllNodes(): (typeof Node)[] {
        return this.getAllFactories().map(factory => factory.nodeConstructor);
    }

    isNodePublished(node: { new (): Node }): boolean {
        const factory = this.getFactoryByConstructor(node);
        return !!factory.isPublished;
    }

    /** @disabled_internal */
    getAllSortedFactories(): NodeFactory[] {
        // TODO: cache
        return sortFactories([...this.getSqlPackageFactories()]);
    }

    /**
     * Create the database schema, used in the cli as well platform tests
     */
    static async dropDbSchema(schemaName: string): Promise<void> {
        await new DatabaseSqlContext().dropSchemaIfExists(schemaName);
    }

    /**
     * Rename a schema
     */
    static async renameSchema(oldName: string, newName: string): Promise<void> {
        await new DatabaseSqlContext().renameSchema(oldName, newName);
    }

    /**
     * Create the database schema, used in the cli as well platform tests
     */
    static async createDbSchema(schemaName: string): Promise<void> {
        ConfigManager.load(__dirname);
        await new DatabaseSqlContext().createSchemaIfNotExists(schemaName);
    }

    async createAdminUser(tenantId: string, data: UserData, options?: CreateAdminUserOptions): Promise<void> {
        // Need to first create the tenant default locale, so that it is available if needed when creating sys user
        await this.asRoot.withCommittedContext(
            tenantId,
            context => Context.localizationManager.createTenantLocale(context, data.locale),
            {
                config: ConfigManager.current,
                locale: data.locale,
                description: () => `Create locale for tenant ${tenantId}`,
            },
        );
        await this.asRoot.withCommittedContext(
            tenantId,
            context => Context.accessRightsManager.createAdminUser(context, data, options),
            { description: () => `Create admin user for tenant ${tenantId}` },
        );
    }

    async getGraphQLSchema(context?: Context): Promise<GraphQLSchema> {
        const key = context ? (await context.getActivePackageNames()).join() : 'all';

        // Double-checked locking is *not* broken in JavaScript
        if (this.graphqlSchemas[key]) return this.graphqlSchemas[key];

        return this.graphqlSchemaFunnel(async () => {
            if (!this.graphqlSchemas[key]) this.graphqlSchemas[key] = await SchemaBuilder.getSchema(this, context);
            return this.graphqlSchemas[key];
        });
    }

    getPackageOfNodeConstructor(node: { new (): Node }): Package {
        const factory = this.getFactoryByConstructor(node);
        if (!factory.package) throw new Error(`${node.name}: application not available`);
        return factory.package;
    }

    /**
     * @internal
     * @param tenantId when set to null, the context can only be used to access to sharedTables or run custom SQL commands
     */
    private async withContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options: ContextOptions,
    ): Promise<T> {
        const opts = { config: ConfigManager.current, ...options };
        if (!opts.isolationLevel) opts.isolationLevel = 'low';
        // For admin tools the default user is root for context that are not initiated by a http request
        // Same for test application
        if (
            !opts.userEmail &&
            !opts.auth?.login &&
            (this.applicationType === 'admin-tool' || this.applicationType === 'test') &&
            opts.response?.constructor?.name !== 'ServerResponse'
        ) {
            opts.userEmail = rootUserEmail;
        }
        const rootContext = await Context.create(this, opts, tenantId);
        if (!ConfigManager.current.storage?.managedExternal) await CollationCache.init(rootContext);
        try {
            if (opts.isReadonly && opts.isolationLevel === 'low') return await body(rootContext);

            return await rootContext.withChildContext(body, opts);
        } finally {
            await rootContext.close();
        }
    }

    /**
     * Create a context bound to a specific tenant. This context will be committed

    * @param tenantId when set to null, the context can only be used to access to sharedTables or run custom SQL commands
     */
    withCommittedContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.withContext(tenantId, body, { ...options });
    }

    /**
     * Create a context bound to a specific tenant. This context will not be committed
     *
     * @param tenantId when set to null, the context can only be used to access to sharedTables or run custom SQL commands
     */
    withUncommittedContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.withContext(tenantId, body, { ...options, noCommit: true });
    }

    /**
     * Create a context bound to a specific tenant. This context will be in read-only mode
     * @param tenantId when set to null, the context can only be used to access to sharedTables or run custom SQL commands
     */
    withReadonlyContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.withContext(tenantId, body, { ...options, isReadonly: true });
    }

    /**
     * Gets a root frame for calling with*Context functions
     */
    get asRoot(): WithContext {
        // We cannot make it internal because we need to call it from system but we add a runtime check to now allow calls
        // outside the back-end and system scope
        // TODO: This check was breaking on a production like environment, we need to do it differently
        // This regex fails if the main package is installed
        // const stack = (new Error().stack?.split(/\s+at\s+/).slice(1) || []).map(l => l?.replace(/\\/, '/'));
        // if (!/\/platform\/(?:back-end|system)\/xtrem-/.test(stack[1])) {
        //     throw new Error("'asRoot' not allowed in this context");
        // }
        return new WithContextFrame(this, { userEmail: rootUserEmail });
    }

    /**
     * Create a context to perform DDL operations
     */
    createContextForDdl<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.withContext(null, body, { ...options, isReadonly: true });
    }

    private initFactoryCacheLogs(): void {
        const logFactoryCacheCounters = (): void => {
            this.getAllFactories()
                .filter(f => f.cache.counters.totalQueries > 0)
                // sort by descending total number of misses
                .sort((f1, f2) => f2.cache.counters.totalMisses - f1.cache.counters.totalMisses)
                // keep the top 100
                .slice(0, 100)
                .forEach(factory => factory.cache.postToNewRelic());
        };

        // log the factory cache counters every minute
        setInterval(logFactoryCacheCounters, 1 * 60 * 1000);
        // log them too on process exit
        process.on('exit', logFactoryCacheCounters);
    }

    clearGlobalCache(): void {
        this.globalCache.clearAll();
    }

    invalidateGlobalCache(context: Context): Promise<void> {
        return this.globalCache.invalidateAllCategories(context);
    }

    /** Used by xtrem-cop to verify that getValue / computeValue are correctly set` */
    async verifySqlConversions(): Promise<integer> {
        let errorCount = 0;
        await asyncArray(this.getAllFactories()).forEach(factory =>
            asyncArray(factory.properties).forEach(async property => {
                errorCount += await property.testSqlConversions();
            }),
        );
        return errorCount;
    }
}

class WithContextFrame implements WithContext {
    constructor(
        private readonly application: Application,
        private readonly frameOptions: ContextOptions,
    ) {}

    withCommittedContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.application.withCommittedContext(tenantId, body, { ...options, ...this.frameOptions });
    }

    withUncommittedContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.application.withUncommittedContext(tenantId, body, { ...options, ...this.frameOptions });
    }

    withReadonlyContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        return this.application.withReadonlyContext(tenantId, body, { ...options, ...this.frameOptions });
    }
}

export function main(body: () => AsyncResponse<void>): void {
    (async () => {
        await body();
        process.exit(0);
    })().catch(err => {
        // eslint-disable-next-line no-console
        console.error(err);
        process.exit(1);
    });
}
