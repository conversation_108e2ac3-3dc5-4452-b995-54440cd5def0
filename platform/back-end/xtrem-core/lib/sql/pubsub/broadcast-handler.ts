/**
 * This class handles broadcasts
 */

import { Dict } from '@sage/xtrem-shared';
import { nanoid } from 'nanoid';
import { promisify } from 'util';
import { Application } from '../../application';
import { ContainerManager } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { ContainerHeartbeatMonitor } from './container-heartbeat-monitor';
import { PubSub, PubSubPayload } from './pubsub';

export type BroadcastPayload = PubSubPayload & {
    $broadcastId: string;
    $fromContainerId: string;
};

type AckPayload = PubSubPayload & {
    $broadcastId: string;
    $containerId: string;
    $error?: string;
};

export abstract class BroadcastHandler {
    private static _makeAckTopic(topic: string): string {
        return `${topic}-ack`;
    }

    /**
     *
     * Broadcast a message and wait_ for an acknowledgement from all the alive containers
     * @param payload
     */
    static async broadcast(
        application: Application,
        topic: string,
        payload: PubSubPayload,
        options?: { timeout?: number },
    ): Promise<void> {
        if (!ContainerHeartbeatMonitor.isActive) throw new Error('Heartbeat monitor is not active for this container');
        // Note: wait_ for 2 periods to make sure that the current container received at least one
        // heartbeat from the other containers (if any)
        await promisify(setTimeout)(ContainerHeartbeatMonitor.heartbeatMillis * 2);

        const broadcastTimeout = options?.timeout || 30000;
        const acknowledgeTopic = BroadcastHandler._makeAckTopic(topic);

        const aliveContainers = ContainerHeartbeatMonitor.aliveContainers;
        if (aliveContainers.length === 0) {
            loggers.pubsub.debug(() => 'Broadcast skipped, no other alive containers');
            return;
        }

        loggers.pubsub.debug(() => `Broadcast payload ${JSON.stringify(payload)}, timeout=${broadcastTimeout} ms`);
        const expectedContainerIds = aliveContainers.reduce((total, id) => {
            total[id] = true;
            return total;
        }, {} as Dict<true>);
        loggers.pubsub.verbose(
            () => `Wait replies from ${aliveContainers.length} containers: ${Object.keys(expectedContainerIds)}`,
        );

        let timeout: NodeJS.Timeout | undefined;
        const broadcastId = nanoid();

        await application.asRoot.withCommittedContext(
            null,
            context =>
                new Promise<void>(
                    // TODO: async promise executor is wrong but we should remove this broadcast / heartbeat implementation
                    // and use a real pubsub middleware instead
                    (resolve, reject) => {
                        const ackListener = (ackPayload: AckPayload): void => {
                            if (ackPayload.$broadcastId !== broadcastId) {
                                // Will happen when 2 broadcast are sent concurrently
                                // This ack is not for this wait_ (it will be processed by another one)
                                return;
                            }
                            if (!expectedContainerIds[ackPayload.$containerId])
                                throw new Error(`Unexpected acknowledge received from ${ackPayload.$containerId}`);
                            if (ackPayload.$error) {
                                reject(
                                    new Error(
                                        `Container ${ackPayload.$containerId} replied with error: ${ackPayload.$error}`,
                                    ),
                                );
                                return;
                            }
                            loggers.pubsub.info(() => `Acknowledge received from ${ackPayload.$containerId}`);
                            delete expectedContainerIds[ackPayload.$containerId];
                            if (Object.keys(expectedContainerIds).length === 0) {
                                // All the live containers have replied
                                resolve();
                            }
                        };

                        timeout = global.setTimeout(() => {
                            // Broadcast timeout
                            if (Object.keys(expectedContainerIds).length === 0) {
                                // Should not happen. If all the containers replied, we should already have exited
                                return;
                            }
                            // Some containers did not acknowledge on time. Are they dead ?
                            const liveContainers = ContainerHeartbeatMonitor.aliveContainers;
                            const missingIds = Object.keys(expectedContainerIds).filter(id => {
                                return liveContainers.includes(id);
                            });
                            // missingIds contains the ids of all the containers that are still alive but did not respond
                            if (missingIds.length) {
                                const msg = `The following containers did not acknowledge in time: \n\t- ${missingIds.join(
                                    '\n\t- ',
                                )}`;
                                reject(new Error(msg));
                            }
                        }, broadcastTimeout);

                        const broadcastPayload: BroadcastPayload = {
                            ...payload,
                            ...{ $broadcastId: broadcastId, $fromContainerId: ContainerManager.containerId },
                        };

                        // Subscribe to the acknowledge topic
                        PubSub.subscribe(acknowledgeTopic, ackListener)
                            .then(() =>
                                PubSub.publish(context, topic, broadcastPayload, {
                                    excludeSelf: true,
                                }),
                            )
                            .catch(err => reject(err instanceof Error ? err : new Error(String(err))));
                    },
                ),
            { description: () => `broadcast(${topic})` },
        );
        if (timeout) clearTimeout(timeout);
        // Unsubscribe from the acknowledge topic
        await PubSub.unsubscribe(acknowledgeTopic);
    }

    /**
     * Send the acknowledgement for a received broadcast
     */
    static async acknowledge(application: Application, topic: string, payload: BroadcastPayload): Promise<void> {
        const acknowledgeTopic = BroadcastHandler._makeAckTopic(topic);
        // Send the ack
        await application.asRoot.withCommittedContext(
            null,
            async context => {
                const ackPayload: AckPayload = {
                    tenantId: context.tenantId!,
                    $broadcastId: payload.$broadcastId,
                    $containerId: ContainerManager.containerId,
                };
                await PubSub.publish(context, acknowledgeTopic, ackPayload, {
                    excludeSelf: true,
                });
            },
            { description: () => `Acknowledge topic ${topic}` },
        );
    }

    /**
     * Send an error as acknowledgement for a received broadcast
     */
    static async acknowledgeWithError(
        application: Application,
        topic: string,
        payload: BroadcastPayload,
        error: string,
    ): Promise<void> {
        const acknowledgeTopic = BroadcastHandler._makeAckTopic(topic);
        loggers.pubsub.warn(`Send error acknowledge to ${topic}, error: ${error}`);
        // Send the ack
        await application.asRoot.withCommittedContext(
            null,
            async context => {
                const ackPayload: AckPayload = {
                    tenantId: context.tenantId!,
                    $broadcastId: payload.$broadcastId,
                    $containerId: ContainerManager.containerId,
                    $error: error,
                };
                await PubSub.publish(context, acknowledgeTopic, ackPayload, {
                    excludeSelf: true,
                });
            },
            { description: () => `Acknowledge with error on topic ${topic}` },
        );
    }
}
