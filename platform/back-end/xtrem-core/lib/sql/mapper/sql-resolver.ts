import { LogicError } from '@sage/xtrem-shared';
import { ConversionResultType } from '@sage/xtrem-ts-to-sql';
import { snakeCase } from 'lodash';
import { Property } from '../../properties';
import { Context, NodeFactory } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { SystemProperties } from '../../runtime/system-properties';
import { makeName63 } from '../statements/naming';
import { ConversionResult, OutputColumn, SqlConverter } from './sql-converter';

const logger = loggers.sql;

export class SqlResolver {
    private static checkDateOrDatetimeType(type: ConversionResultType, selector: string): void {
        if (!type) throw new Error(`Type not defined for '${selector}'`);
        if (type !== 'date' && type !== 'datetime' && type !== 'dateRange' && type !== 'datetimeRange') {
            throw new Error(`Only date/datetime/dateRange properties can be grouped by '${selector}'`);
        }
    }

    private static dateAggregateFormats = {
        day: 'YYYY-MM-DD',
        month: 'YYYY-MM-01',
        year: 'YYYY-01-01',
    };

    private static castIfBoolean(sql: string, type: ConversionResultType): string {
        return type === 'boolean' ? `(CAST(${sql} as INTEGER))` : sql;
    }

    private static aggregateSql(sqlConverter: SqlConverter, result: ConversionResult, name: string): string {
        // TODO: we should propagate a flag to distinguish between group and value
        switch (name) {
            // group by keywords
            case 'min':
            case 'max': {
                if (result.type === 'boolean') {
                    // Postgres cannot apply min/max directly to boolean
                    // So we cast to integer, apply operator and then cast back to boolean
                    return `${name}(CAST(${result.sql} as INTEGER))::BOOLEAN`;
                }
                return `${name.toUpperCase()}(${result.sql})`;
            }
            case 'sum':
            case 'avg': {
                const sql = `${name.toUpperCase()}(${result.sql})`;
                if (result.type === 'short' || result.type === 'integer') {
                    // Postgres returns bigint for sum, and numeric for avg.
                    // But we want the result as a JS number (15 digits is sufficient)
                    // So we cast the result to JS number (aka. float8)
                    return `${sql}::FLOAT8`;
                }
                return sql;
            }

            // value keywords
            case 'value':
                return result.sql;
            case 'year':
            case 'month':
            case 'day':
                SqlResolver.checkDateOrDatetimeType(result.type, name);
                // Let postgres compute the first day of the group as a date.
                // We generate the first day of the period with to_char, and then we cast with ::DATE.
                return `TO_CHAR(${result.sql}, ${sqlConverter.resolveLiteral(
                    SqlResolver.dateAggregateFormats[name],
                )})::DATE`;

            case 'distinctCount':
                return `COUNT(DISTINCT ${SqlResolver.castIfBoolean(result.sql, result.type)})`;
            case 'hasNull':
                return `BOOL_OR(${SqlResolver.castIfBoolean(result.sql, result.type)} IS NULL)`;
            default:
                throw new LogicError(`invalid aggregate operator: ${name}`);
        }
    }

    static castFromJson(sql: string, type: ConversionResultType): string {
        switch (type) {
            case 'short':
            case 'integer':
                return `(${sql}->>0)::INT8`;
            case 'float':
            case 'double':
                return `(${sql}->>0)::FLOAT8`;
            case 'decimal':
                return `(${sql}->>0)::NUMERIC`;
            case 'reference':
                return `(${sql}->>0)::INT8`;
            case 'string':
            case 'enum':
                return `(${sql}->>0)::TEXT`;
            case 'date':
                return `(${sql}->>0)::DATE`;
            default:
                return sql;
        }
    }

    private static getNumericAggregateValueType(type: ConversionResultType, name: 'sum' | 'avg'): ConversionResultType {
        switch (type) {
            case 'short':
            case 'integer':
                return name === 'sum' ? 'integer' : 'double';
            case 'float':
            case 'double':
                return 'double';
            case 'decimal':
                return 'decimal';
            default:
                throw new LogicError(`invalid type for ${name} operator: ${type}`);
        }
    }

    private static getAggregateValueType(type: ConversionResultType, name: string): ConversionResultType {
        switch (name) {
            case 'min':
            case 'max':
                return type;
            case 'sum':
            case 'avg':
                return SqlResolver.getNumericAggregateValueType(type, name);
            case 'distinctCount':
                return 'integer';
            case 'hasNull':
                return 'boolean';
            case 'year':
            case 'month':
            case 'day':
            case 'value':
                return type;

            default:
                throw new LogicError(`invalid aggregate operator: ${name}`);
        }
    }

    static resolveAggregate(sqlConverter: SqlConverter, result: OutputColumn, name: string | undefined): OutputColumn {
        if (!name) throw new LogicError(`${result.path}: missing aggregate name`);
        const type = result.type ?? result.property?.type;
        if (!type) throw new LogicError(`${result.path}: missing type`);
        return {
            ...result,
            aggregationOperator: name,
            type: SqlResolver.getAggregateValueType(type, name),
            sql: SqlResolver.aggregateSql(sqlConverter, result, name),
            columnAlias: SqlResolver.makeColumnAlias(`${result.columnAlias}_${name}`),
        };
    }

    static makeColumnAlias(sql: string): string {
        // Postgres restricts the column alias length to 63 chars, if it is exceeded then the alias will be truncated and the
        // keys of result will not have the alias allocated.
        return makeName63(
            sql
                .replaceAll('__', '._')
                .split('._')
                .map(s => snakeCase(s))
                .join('__'),
        );
    }

    static resolveCustomData(sqlConverter: SqlConverter, result: ConversionResult, name: string): ConversionResult {
        const customFields = sqlConverter.context.customFields[result.factory.fullName];
        const customField = customFields.find(field => field.name === name);
        const type = customField?.dataType as ConversionResultType;
        if (!customField) throw result.factory.logicError(`custom field ${name} not found`);

        // TODO: handle references later
        const sql = this.castFromJson(`${result.sql}->${sqlConverter.resolveLiteral(name)}`, type);

        return {
            ...result,
            type,
            sql,
            columnAlias: this.makeColumnAlias(sql),
        };
    }

    static resolveJson(sqlConverter: SqlConverter, result: ConversionResult, name: string): ConversionResult {
        if (result.property?.name === '_customData' && sqlConverter.context.customFields[result.factory.fullName])
            return this.resolveCustomData(sqlConverter, result, name);

        const sql = `${result.sql}->${sqlConverter.resolveLiteral(name)}`;
        return {
            ...result,
            type: 'json',
            // JSON contains property names, not column names so we don't convert to snake case
            sql,
            columnAlias: this.makeColumnAlias(sql),
        };
    }

    static resolveDate(sqlConverter: SqlConverter, result: ConversionResult, name: string): ConversionResult {
        const resolved = sqlConverter.convertDatePropertyResult(result, name);
        return {
            ...(resolved as ConversionResult),
            columnAlias: this.makeColumnAlias(resolved.sql),
        };
    }

    static resolveRange(result: ConversionResult, name: string, type: ConversionResultType): ConversionResult {
        let sql: string;
        switch (name) {
            case 'start':
                sql = `lower(${result.sql})`;
                break;
            case 'end':
                sql = `upper(${result.sql})`;
                break;
            case 'includedEnd': {
                // By default in postgres the range is exclusive, so we need to subtract 1 from the upper bound
                // [1,3] is stored as [1,4)
                // -> end = 4
                // -> includedEnd = 3
                let interval = '';
                if (type === 'date') {
                    interval = "INTERVAL '1 day'";
                } else if (type === 'integer') {
                    interval = '1';
                } else {
                    throw new LogicError(`${result.path}.${name}: invalid range property for ${type} type`);
                }

                // upper_inc() indicates if the upper value is included or excluded from the range
                // if it is included we don't subtract the interval otherwise we subtract the interval defined above depending on the type
                sql = `CASE WHEN upper_inc(${result.sql}) THEN upper(${result.sql}) ELSE (upper(${result.sql}) - ${interval}) END`;
                break;
            }
            default:
                throw new LogicError(`${result.path}.${name}: invalid range property`);
        }
        return {
            ...result,
            // reset property because type changes
            property: undefined,
            type: type.replace(/Range$/, '') as ConversionResultType,
            sql,
            columnAlias: this.makeColumnAlias(sql),
        };
    }

    static resolveArray(result: ConversionResult, type: ConversionResultType): ConversionResult {
        return {
            ...result,
            type,
        };
    }

    static resolveTextStream(result: ConversionResult): ConversionResult {
        return result;
    }

    static resolve(
        sqlConverter: SqlConverter,
        context: Context,
        parent: ConversionResult,
        name: string,
        rootFactory: NodeFactory,
    ): ConversionResult {
        if (parent.type === 'jsonReference') return SqlResolver.resolveJson(sqlConverter, parent, name);
        if (parent.property?.type === 'json') return SqlResolver.resolveJson(sqlConverter, parent, name);
        if (parent.property?.type === 'date') return SqlResolver.resolveDate(sqlConverter, parent, name);
        if (parent.property?.type === 'dateRange') return SqlResolver.resolveRange(parent, name, 'date');
        if (parent.property?.type === 'datetimeRange') return SqlResolver.resolveRange(parent, name, 'datetime');
        if (parent.property?.type === 'integerRange') return SqlResolver.resolveRange(parent, name, 'integer');
        if (parent.property?.type === 'decimalRange') return SqlResolver.resolveRange(parent, name, 'decimal');

        if (parent.property?.type === 'integerArray') return SqlResolver.resolveArray(parent, 'integer');
        if (parent.property?.type === 'enumArray') return SqlResolver.resolveArray(parent, 'integer');
        if (parent.property?.type === 'stringArray') return SqlResolver.resolveArray(parent, 'string');
        if (parent.property?.type === 'textStream') return SqlResolver.resolveTextStream(parent);

        const parentFactory = parent.factory;

        if (!parentFactory) throw new Error('internal error: no factory');

        let property: Property | undefined;
        if (name === '_constructor') {
            // TODO: this property should be in the factory
            property = SystemProperties.constructorProperty(rootFactory);
            property.isInherited = !!parentFactory.baseFactory;
        } else if (name === '_updateTick') {
            // TODO: clean this up
            property = SystemProperties.updateTickProperty(parentFactory);
        }

        if (!property) property = parentFactory.findProperty(name);

        if (property.delegatesTo) return sqlConverter.convertDelegatesTo(parent, property);

        const path = parent.path ? `${parent.path}.${name}` : name;

        if (context.collectedDependencyPaths) {
            if (!context.collectedDependencyPaths.includes(path)) context.collectedDependencyPaths.push(path);
        }

        let sql = '';
        let columnAlias = '';

        if (!property.isStored) {
            if (property.getValue) {
                const getValue = property.getValue;
                try {
                    sql = sqlConverter.withThisResultScope(parent, () => sqlConverter.convertFunction(getValue)).sql;
                    // The getValue may be complex (e.g. 1+1) and invalid when creating as a column alias
                    // therefore we do use the sql to generate the alias but rather the path so that we create a valid
                    // alias that conforms to SQL standards
                    columnAlias = SqlResolver.makeColumnAlias(path);
                } catch (e) {
                    logger.error(e.message);
                    throw property.systemError(`cannot convert 'getValue' function to SQL: ${e.message}`);
                }
            } else if (property.computeValue) {
                throw property.systemError(
                    "property cannot by converted to SQL because it is defined by a 'computeValue' rule",
                );
            }
        } else {
            // TODO: keep this code around for customization logic
            // const field = bundleId
            //     ? `${parent.alias}._custom_data->${sqlConverter.resolveLiteral(property.name)}`
            //     : `${parent.alias}.${property.columnName}`;

            const field = `${parent.alias}.${property.columnName}`;
            sql = field;

            if (property?.isLocalized) {
                if (context.processLocalizedTextAsJson) {
                    // read json object as a string
                    sql = `${field}#>>${sqlConverter.resolveLiteral('{}')}`;
                } else {
                    // In any case we should find a value for base:
                    const { collation } = context;
                    // If the property is localized we use context.collation
                    // Otherwise we use the column's collation, to get optimal performance in case column
                    // is indexed.
                    // The default column collation is now und-x-icu so we get a reasonable sort order
                    // when strings have case differences and accentuated characters.
                    // TODO: We could do a slightly better job by testing if the column is indexed and
                    // using this.context.collation when not indexed.
                    const collationSql = collation ? ` COLLATE "${collation}"` : '';
                    sql = `coalesce(${[...context.locales, ...['base']]
                        .map(l => `${field}->>${sqlConverter.resolveLiteral(l)}`)
                        .join(', ')})${collationSql}`;
                }
            }
            columnAlias = SqlResolver.makeColumnAlias(field);
        }

        const isNullable =
            property.isNullable || (parent.property?.type === 'reference' && parent.property?.isNullable);

        const resolved: ConversionResult = {
            alias: parent.alias,
            path,
            // KEEP: bundleId ? 'json' : property.type,
            type: property.type,
            isNullable,
            isInherited: property.isInherited && property.isStored, // only inherited properties that are stored need to be resolved to base factory
            property,
            factory: property.isForeignNodeProperty() ? property.targetFactory : parentFactory,
            // KEEP: sql: bundleId ? this.castFromJson(sql, property.type) : sql,
            sql,
            columnAlias,
        };

        if (property.isForeignNodeProperty()) {
            // set target factory and table name for the collection/reference/referenceArray property
            resolved.factory = property.targetFactory;
            if (property.isCollectionProperty() || property.isVital)
                resolved.reverseReferenceName = property.reverseReference;

            if (property.isReferenceProperty() && property.isVital) {
                resolved.parent = parent;
                resolved.path = `${parent.path}.${property.name}`;
                sqlConverter.makeAliasAndJoin(resolved);
                resolved.sql = `${resolved.alias}._id`;
            }
        }
        return resolved;
    }
}
