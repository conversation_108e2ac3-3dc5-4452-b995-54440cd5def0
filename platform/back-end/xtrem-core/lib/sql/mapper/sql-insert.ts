/** @ignore */ /** */
import { AnyR<PERSON>ord, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { ColumnTypeName } from '@sage/xtrem-postgres';
import { Dict, LogicError } from '@sage/xtrem-shared';
import * as lodash from 'lodash';
import { lazyLoadedMarker } from '../../node-state/lazy-loaded-marker';
import { Property, ReferenceProperty } from '../../properties';
import { Context, NodeFactory, rootUserEmail } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { loggers } from '../../runtime/loggers';
import { EnumDataType } from '../../types';
import { FactoryMetadata } from '../../types/sql-file-data';
import { Column } from '../schema/column';
import { Table } from '../schema/table';
import { DataDiagnosis } from '../schema/utils';
import { getSqlCurrvalOfIdSequence } from '../sql-context';
import { SchemaSqlContext } from '../sql-context/schema-sql-context';
import { SqlContext } from '../sql-context/sql-context';
import { makeName63 } from '../statements/naming';
import { getSqlSerialSequence, xtremToPostgreSql } from '../statements/types-conversion';
import { SqlConverter, SqlParameter } from './sql-converter';
import { SqlStatementCacheKeyData } from './sql-statement-cache';
import { SqlUtil } from './sql-util';
import { SqlValueConverter } from './sql-value-converter';

const sqlLogger = loggers.sql;

/**
 * table.insert options
 */
export interface SqlInsertOptions {
    /**
     * Options constructor name - used by subclassing
     */
    constructorName?: string;

    /**
     * Try update if insert fails because of conflict on primary key
     */
    useUpsert?: boolean;

    /**
     * When useUpsert is true: columns that upsert should not update (flagged with isOwnedByCustomer)
     */
    upsertColumnsToIgnore?: string[];

    /**
     * Option to execute insert with ON CONFLICT DO NOTHING
     * This will override useUpsert flag
     */
    onConflictDoNothing?: boolean;

    /**
     * When upsert is true: should errors on upsert (mainly unique index violation) be ignored ?
     */
    ignoreUpsertErrors?: boolean;

    /**
     * Specific metadata to use to insert the value (when not set, it will be extracted from the current table definition)
     */
    metadata?: FactoryMetadata;

    /**
     * insert values in audit columns (_create_user, _create_stamp, _update_user, _update_stamp, _update_tick)
     * instead of letting the database triggers compute them.
     */
    preserveAuditColumns?: boolean;
}

/** Info which is collected from metadata and used to generate the SQL statement */
interface InsertInfo {
    /** The schema name */
    schemaName: string;

    /** The columns to insert */
    columnNames: string[];

    /** The SQL parameters created by the SqlConverter */
    sqlParameters: SqlParameter[];

    /** The parameters names ($1, $2, ...) */
    parameterNames: string[];

    /** The columns to be returned by the insert */
    returningColumns?: string[];

    /** Key columns for upsert */
    upsertKeyColumns?: string[];
}

export interface InsertParameterInfo {
    /** The column (of the table or one of its base tables) */
    column: Column;

    /** The parameter name: p_<column_name> */
    name: string;

    /** The SQL type of the parameter */
    sqlType: string;
}

/**
 * Structure of a cache entry for a generated INSERT statement
 *
 * This structure should be kept reasonably small.
 * It may reference metadata objects (Property instances) because this only costs a pointer
 * but it should not duplicate metadata.
 */
export interface CachedInsert {
    sql: string;
    sqlParameters: SqlParameter[];
    upsertFunctionSql: string;
}

/** @internal */
export class SqlInsert {
    constructor(private readonly table: Table) {}

    get factory(): NodeFactory {
        return this.table.factory;
    }

    static auditColumnNames = ['_create_user', '_create_stamp', '_update_user', '_update_stamp', '_update_tick'];

    private isColumnSkipped(
        column: Column,
        value: AnyValue,
        options: {
            preserveAuditColumns: boolean;
            selfId: number;
        },
    ): boolean {
        if (column.excludeFromInsertIfNull && value === null) return true;

        if (column.property.isRequired && value == null)
            throw new Error(`Cannot insert null value in column ${this.table.name}.${column.columnName}`);

        // Do not save lazy-loaded properties that were not resolved
        if (value === lazyLoadedMarker) return true;

        const isSelfReference =
            column.property instanceof ReferenceProperty &&
            column.property.isSelfReference &&
            (value as number) < 0 &&
            value === options.selfId;

        if (isSelfReference) return true;

        if (!options.preserveAuditColumns && this.table.databaseComputedColumnNames.includes(column.columnName))
            return true;

        if (column.propertyName === '_id' && (value as number) < 0) return true;
        if (column.propertyName === '_sortValue' && !value) return true;

        return false;
    }

    private static fixColumnValue(
        context: Context,
        column: Column,
        val: AnyValue,
        options: { selfId: number; preserveAuditColumns: boolean },
    ): AnyValue {
        // Set localized properties in json format { locale: 'text value' }
        if (column.property.isLocalized) {
            const propertyName = column.property.name;
            return SqlUtil.fixLocalizedValue(context, val, propertyName, 'insert');
        }

        if (Array.isArray(val) && !(column.property.isArrayProperty() || column.property.isJsonProperty()))
            throw column.columnError('unexpected array');

        if (
            column.property.isReferenceProperty() &&
            column.property.isSelfReference &&
            Number.isFinite(val) &&
            Number(val) < 0
        ) {
            return options.selfId;
        }
        return val ?? null;
    }

    private fixAndFilterColumns(
        context: Context,
        row: AnyRecord,
        options: { selfId: number; preserveAuditColumns: boolean },
    ): Column[] {
        return this.table.columns.filter(column => {
            const propertyName = column.property.name;
            const val = row[propertyName];
            row[propertyName] = SqlInsert.fixColumnValue(context, column, val, options);
            return !this.isColumnSkipped(column, val, options);
        });
    }

    private async prepareRow(
        context: Context,
        data: AnyRecord,
        options: SqlInsertOptions = {},
    ): Promise<{ row: AnyRecord; allNewKeys: AnyRecord; isRootUserInsert: boolean; insertedColumns: Column[] }> {
        // Clone the data into the row
        let row = { ...data };
        if (row._id == null || row._id === 0) row._id = context.allocateTransientId();

        let allNewKeys: AnyRecord & { _id?: number } = {};

        // set the _sortValue to have a predictable result when ordered by _sort_value
        // if the _id was supplied then it will he positive and we need to calculate the _sortValue
        // if the _sortValue SQL default value fires with the _id supplied, the currval of the _id sequence is not set
        // and an error is thrown.
        if (
            this.factory.propertiesByName._sortValue &&
            row._sortValue == null &&
            typeof row._id === 'number' &&
            row._id > 0
        ) {
            row._sortValue = row._id * 100;
        }

        let constrName = options.constructorName;
        if (this.factory.isAbstract) {
            // 'insert' function MUST NOT be called directly for abstract classes
            // but should be called just below (recursive call)
            if (!constrName) {
                throw new Error(`'constructor' is missing, cannot insert into '${this.table.name}'`);
            }
            row._constructor = constrName;
        } else {
            // No constructor name should be provided for a concrete factory
            if (constrName) {
                throw new Error(
                    `No 'constructor' should be provided for table '${this.table.name}', constructor was '${constrName}'`,
                );
            }
            constrName = this.factory.name;
        }

        // when dealing with a table with a baseTable, data may contain values for columns from this table
        // and for its baseTable
        const baseTable = this.table.baseTable;
        if (baseTable) {
            const propNamesToKeep = this.table.columns
                .filter(col => {
                    if (col.propertyName === '_id') return true;
                    return !col.property.isInherited;
                })
                .reduce((total, col) => {
                    total[col.propertyName] = true;
                    return total;
                }, {} as Dict<boolean>);
            // only keep data that match the current table and send the other ones to the baseTable (caution : recursive process...)
            const thisRow: AnyRecord = {};
            const baseRow: AnyRecord = {};
            Object.keys(row).forEach(key => {
                if (propNamesToKeep[key]) {
                    thisRow[key] = row[key];
                } else {
                    baseRow[key] = row[key];
                }
            });
            if (row._id && typeof row._id === 'number' && row._id > 0) {
                baseRow._id = row._id;
            }
            // Special case for _sourceId : all the rows from the different tables must have the same _sourceId
            baseRow._sourceId = thisRow._sourceId;
            // Create the record in the baseTable first to get the values for _id
            // (and to not violate foreign keys)
            // TODO: avoid one extra copy below
            allNewKeys = {
                ...allNewKeys,
                ...(await new SqlInsert(baseTable).insert(context, baseRow, {
                    ...options,
                    ...{ constructorName: constrName },
                })),
            };
            row = { ...thisRow, ...allNewKeys };
        }

        const insertedColumns = this.fixAndFilterColumns(context, row, {
            selfId: row._id as number,
            preserveAuditColumns: !!options.preserveAuditColumns,
        });
        const isRootUserInsert =
            row.email === rootUserEmail && this.factory.name === CoreHooks.sysManager.getUserNode().name;

        return { row, allNewKeys, isRootUserInsert, insertedColumns };
    }

    private getReturningColumnNames(context: Context): string[] {
        return this.table.columns
            .filter(col => {
                if (col.isAutoIncrement) return true;
                if (context.processLocalizedTextAsJson && col.property.isLocalized) return true;
                if (col.columnName === '_sort_value') return true;
                if (col.columnName === '_constructor') return true;
                return false;
            })
            .map(col => col.columnName);
    }

    private getInsertInfo(
        sqlConverter: SqlConverter,
        options: SqlInsertOptions & { isRootUserInsert: boolean; insertedColumns: Column[] },
    ): InsertInfo {
        const parameterNames = [] as string[];
        const columnNames = [] as string[];

        const schemaName = sqlConverter.context.schemaName;

        // Columns to return in result of the insert
        // - Auto incremented columns
        // - Localized columns, when context.processLocalizedTextAsJson
        const returningColumns = this.getReturningColumnNames(sqlConverter.context);

        const diagnoses = [] as DataDiagnosis[];

        // extract metadata from the current factory definition

        options.insertedColumns.forEach(column => {
            columnNames.push(SqlContext.escape(column.columnName));
            parameterNames.push(SqlUtil.pushColumnArg(sqlConverter, column));
        });
        if (options.isRootUserInsert) {
            ['_create_user', '_update_user'].forEach(columnName => {
                columnNames.push(columnName);
                parameterNames.push(getSqlCurrvalOfIdSequence(this.table.getFullTableName(sqlConverter.context)));
            });
        }

        const autofixes = diagnoses.filter(d => d.category === 'autofix');
        if (autofixes.length > 0) {
            loggers.sql.warn(`${schemaName}.${this.table.name} has autofixed ${autofixes.length} lines`);
            if (loggers.sql.isActive('debug')) {
                autofixes.forEach(d => loggers.sql.debug(() => d.message));
            }
        }

        let upsertKeyColumns: string[] | undefined;
        if (options.useUpsert) {
            if (!this.factory.naturalKey)
                throw new Error(
                    `Upsert mode can only be used on factories with a natural key (factory was ${this.factory.fullName})`,
                );
            upsertKeyColumns = this.factory.naturalKey.map(
                propertyName => this.factory.propertiesByName[propertyName].requiredColumnName,
            );
            // tables that are not shared but all tenants need to include the _tenant_id in the upsertKeyColumns list
            if (!this.table.isSharedByAllTenants) upsertKeyColumns.unshift('_tenant_id');
        }

        const sqlParameters = sqlConverter.sqlParameters;
        return { schemaName, columnNames, sqlParameters, parameterNames, returningColumns, upsertKeyColumns };
    }

    private getUpsertFunctionSql(schemaName: string, columns: string[], sqlToWrap: string): string {
        return `CREATE OR REPLACE FUNCTION ${schemaName}.upsert(${columns
            .map(columnName => `${schemaName}.${this.table.name}.${columnName}%TYPE`)
            .join(',')}) RETURNS JSON AS
        $$
            DECLARE ret RECORD;
            err_message TEXT;
            err_detail TEXT;
        BEGIN
        ${sqlToWrap} into ret;
        RETURN row_to_json(ret);
        RETURN JSON;
        EXCEPTION
            WHEN unique_violation THEN
                -- duplicate key on another unique index than the one that was used in the ON CONFLICT(...) clause

                -- capture the error message/detail
                GET STACKED DIAGNOSTICS
                    err_message = MESSAGE_TEXT,
                    err_detail = PG_EXCEPTION_DETAIL;
                SELECT column1 as _err_message, TRUE as _upsert_failed, err_detail as _err_detail FROM (VALUES(err_message)) AS T INTO ret;
                RETURN row_to_json(ret);
            WHEN OTHERS THEN RAISE;
        END $$ LANGUAGE plpgsql;`;
    }

    private getInsertSql(insertInfo: InsertInfo, options: SqlInsertOptions): CachedInsert {
        if (!insertInfo.returningColumns) insertInfo.returningColumns = [];
        const upsertKeyColumns = insertInfo.upsertKeyColumns || ['_tenant_id', '_id'];

        const computedColumnNames = this.table.databaseComputedColumnNames;
        const returningColumns = lodash.uniq([...computedColumnNames, ...(insertInfo.returningColumns || [])]);

        const sqlParts = [];
        sqlParts.push(`INSERT INTO ${insertInfo.schemaName}.${this.table.name}\n`);
        sqlParts.push(`(${insertInfo.columnNames.join(',')})\n`);
        sqlParts.push(`VALUES (${insertInfo.parameterNames.join(',')})`);

        if (options.onConflictDoNothing) {
            sqlParts.push('\nON CONFLICT DO NOTHING');
        } else if (options.useUpsert) {
            // On conflict, update the values (except the id/tenantId that will be used as a filter)
            const colParts: string[] = [];
            const paramParts: string[] = [];
            // TODO: _create_user and _update_user are excluded here but they are still sqlParameters of the upsert function.
            // Remove them from the function parameters!
            const columnsToExclude = [
                ...upsertKeyColumns,
                '_create_stamp',
                '_create_user',
                '_update_user',
                ...(options.upsertColumnsToIgnore || []),
            ];
            insertInfo.columnNames.forEach((colName, idx) => {
                if (columnsToExclude.includes(colName)) {
                    // Skip
                } else {
                    colParts.push(colName);
                    paramParts.push(insertInfo.parameterNames[idx]);
                }
            });
            const conflictColumns = upsertKeyColumns
                .map(columnName => {
                    const column = this.table.columnsByColumnName[columnName];
                    if (!column) throw new Error(`Table ${this.table.name}: column ${columnName} does not exist`);
                    if (!column.isNullable) return SqlContext.escape(columnName);
                    // The 'ON CONFLICT(xxx)' part must have the EXACT same definition as the unique index used.
                    // When dealing with a nullable column, the definition of the unique index refers the column
                    // with a COALESCE(x, 0), so we have to use the same syntax here otherwise, we will get the
                    // Postgres error : 'there is no unique or exclusion constraint matching the ON CONFLICT specification'
                    return `COALESCE (${SqlContext.escape(columnName)}, 0)`;
                })
                .join();
            sqlParts.push(`\nON CONFLICT (${conflictColumns}) DO UPDATE SET (${colParts}) = (${paramParts})`);
        }

        if (returningColumns.length) sqlParts.push(`\nRETURNING ${returningColumns.join(',')}`);

        const schemaName = insertInfo.schemaName;
        const columnNames = insertInfo.columnNames;
        let upsertFunctionSql = '';
        let sql = sqlParts.join('');
        if (options.useUpsert) {
            upsertFunctionSql = this.getUpsertFunctionSql(schemaName, columnNames, sql);
            sql = `SELECT ${schemaName}.upsert(${insertInfo.parameterNames.join(',')});`;
        }

        const sqlParameters = insertInfo.sqlParameters;
        return { sql, sqlParameters, upsertFunctionSql };
    }

    /**
     * Collects the SQL insert statements for the table and all its base tables, reusing the methods
     * that we used for the individual inserts.
     */
    collectInsertStatements(
        context: Context,
        options: SqlInsertOptions,
        statements: CachedInsert[] = [],
    ): CachedInsert[] {
        // recurse through baseTables
        if (this.table.baseTable)
            new SqlInsert(this.table.baseTable).collectInsertStatements(context, options, statements);

        const skippedColumns = ['_create_stamp', '_update_stamp', '_update_tick'];

        const sqlConverter = new SqlConverter(context, this.factory);
        const insertInfo = this.getInsertInfo(sqlConverter, {
            ...options,
            isRootUserInsert: false,
            insertedColumns: [...this.table.columns.filter(c => !skippedColumns.includes(c.columnName))],
        });
        statements.push(this.getInsertSql(insertInfo, options));
        return statements;
    }

    static logged = {} as Dict<boolean>;

    /**
     * Build the list of parameters for the insert function.
     * @param schemaName
     * @returns
     */
    getInsertFunctionParameters(schemaName: string): InsertParameterInfo[] {
        const mapPropertyType = (property: Property): string => {
            switch (property.type) {
                case 'string':
                    return property.isLocalized ? 'JSONB' : 'TEXT';
                case 'enum':
                    return `${schemaName}.${(property.dataType as EnumDataType).getEnumType().name}`;
                case 'enumArray':
                    return `${schemaName}.${(property.dataType as EnumDataType).getEnumType().name}[]`;
                case 'jsonReference':
                    return 'JSONB';
                default:
                    if (property.type in xtremToPostgreSql)
                        return xtremToPostgreSql[property.type as ColumnTypeName].type;
                    throw this.factory.logicError(`Unsupported type ${property.type} for property ${property.name}`);
            }
        };

        // Recursively find the column for a property in the factory and its base factories
        const getColumn = (factory: NodeFactory, propertyName: string): Column => {
            const property = factory.findProperty(propertyName);
            if (property.column) return property.column;
            if (!factory.baseFactory) throw this.factory.logicError(`Column not found for property ${property.name}`);
            return getColumn(factory.baseFactory, propertyName);
        };

        return this.factory.properties
            .filter(property => property.isStored)
            .sort((property1, property2) => property1.name.localeCompare(property2.name))
            .map(property => {
                return {
                    column: getColumn(this.factory, property.name),
                    name: `p_${property.columnName}`,
                    sqlType: mapPropertyType(property),
                };
            })
            .filter(p => !SqlInsert.auditColumnNames.includes(p.column.columnName));
    }

    /** Returns the name of the insert function, limited to 63 chars */
    private static getInsertFunctionName(tableName: string): string {
        // leading _ to avoid name collision with insert_table, insert_shared_table, ...
        return makeName63(`_insert_${tableName}`);
    }

    /** Returns the name of the insert function, limited to 63 chars */
    private getInsertFunctionName(): string {
        return SqlInsert.getInsertFunctionName(this.table.name);
    }

    /** Returns the SQL statement that creates the insert function */
    getCreateSqlInsertFunctionSql(context: Context): string {
        const insertStatements = this.collectInsertStatements(context, {});

        // special snakeCase for leading underscore
        const snakeCase = (str: string): string =>
            str[0] === '_' ? `_${snakeCase(str.substring(1))}` : lodash.snakeCase(str);

        // Map the valuePath of a CacheInsert parameter to the corresponding SQL parameter name
        const mapParameterName = (parameter: SqlParameter): string => {
            if (!parameter.valuePath.startsWith('values.'))
                throw this.factory.logicError(`Invalid insert parameter name: ${parameter.valuePath}`);
            const trimmed = parameter.valuePath.substring('values.'.length);
            return trimmed === '_id' ? 'l_id' : `p_${snakeCase(trimmed)}`;
        };

        // The function's parameters
        const parameters = this.getInsertFunctionParameters(context.schemaName);

        // Maps the $1, $2, ... parameters to the SQL parameter names
        const mapStatementParameters = (statement: CachedInsert): string =>
            statement.sql.replace(/\$(\d+)/g, (_all, s) => mapParameterName(statement.sqlParameters[Number(s) - 1]));

        // Maps the statement for an individual insert to a statement suitable for the insert function.
        // Also adds a statement to merge the returned values into the RET variable
        const mapStatement = (statement: CachedInsert, i: number): string => {
            const sql = mapStatementParameters(statement).replace(/\n/g, nl(2));
            const [head, tail] = sql.split(' RETURNING ');
            const returning = tail ?? ' _id';
            const insertSql = `${head} RETURNING ${returning} INTO ret_${i + 1};`;

            return `${insertSql}\nRET := RET || to_jsonb(ret_${i + 1});`;
        };

        // Newline + depth tabs
        const nl = (depth: number): string => `\n${'    '.repeat(depth)}`;

        // The SQL expression to get the next value of the _id sequence
        const idVal = `nextval(${getSqlSerialSequence(this.factory.rootFactory.table.getFullTableName(context))})`;

        // Generates the SQL declaration for the parameters of the insert function.
        // Adds the p__tenant_id and p__constructor parameters if needed.
        const parametersSql = [
            parameters.map(p => `${nl(1)}${p.name} ${p.sqlType}`).join(','),
            this.factory.isSharedByAllTenants ? '' : `,${nl(1)}p__tenant_id TEXT`,
            this.factory.baseFactory ? `,${nl(1)}p__constructor TEXT` : '',
        ].join('');

        // SQL statement to fix the _sortValue if it is not set.
        const fixSortValue =
            this.factory.propertiesByName._sortValue != null
                ? `
        IF p__sort_value = 0 OR p__sort_value is NULL THEN p__sort_value := l_id * 100; END IF;`
                : '';

        // SQL statements to fix the self-references
        const fixSelfReferences = this.factory.properties
            .filter(property => property.isReferenceProperty() && property.isSelfReference)
            .map(
                property => `
    IF p_${property.columnName} IS NULL OR p_${property.columnName} <= 0 THEN p_${property.columnName} := l_id; END IF;`,
            )
            .join('\n');

        // The self reference for the User node is a special case because the property is a system property
        const isUserFactory = this.factory.name === CoreHooks.sysManager.getUserNode().name;
        const selfUserId = isUserFactory ? 'l_id::TEXT' : 'NULL';

        // Return the whole SQL statement to create the insert function
        return `CREATE OR REPLACE FUNCTION ${context.schemaName}.${this.getInsertFunctionName()}(${parametersSql})
RETURNS JSONB AS $$

DECLARE l_id INT8;
DECLARE l_user_id TEXT;
DECLARE p__create_user INT8;
DECLARE p__update_user INT8;
${insertStatements.map((_, i) => `DECLARE ret_${i + 1} RECORD;`).join('\n')}
DECLARE RET JSONB;

BEGIN
    IF p__id IS NULL OR p__id <= 0 THEN
        SELECT ${idVal} into l_id;
    ELSE
        l_id := p__id;
    END IF;${fixSortValue}

    l_user_id := COALESCE(${context.schemaName}.get_config('xtrem.transaction_user_id'), '');
    IF l_user_id = '' THEN l_user_id := ${selfUserId}; END IF;
    p__create_user := l_user_id::INT8;
    p__update_user := l_user_id::INT8;
    ${fixSelfReferences}

    RET := jsonb_build_object('_id', l_id);

    ${insertStatements.map(mapStatement).join('\n\n')}

    RETURN RET;
END;
$$ LANGUAGE plpgsql;`;
    }

    /** Returns the SQL statement to drop the insert function */
    static getDropSqlInsertFunctionSql(context: Context, tableName: string): string {
        return `DROP FUNCTION IF EXISTS ${context.schemaName}.${this.getInsertFunctionName(tableName)};`;
    }

    /** Insert method that inserts in all the nodes tables */
    async insertWithFunction(context: Context, data: AnyRecord): Promise<AnyRecord> {
        if (this.factory.isAbstract) throw this.factory.logicError('Cannot insert with function into abstract factory');

        const parameters = this.getInsertFunctionParameters(context.schemaName);

        /** converts the data record into an array of parameter values */
        const values = await asyncArray(parameters)
            .map(async p =>
                SqlInsert.fixColumnValue(
                    context,
                    p.column,
                    await SqlValueConverter.toSql(context, p.column, data[p.column.property.name]),
                    { selfId: Number(data._id), preserveAuditColumns: false },
                ),
            )
            .toArray();

        // Add the p__tenant_id and p__constructor parameters if needed.
        if (!this.factory.isSharedByAllTenants) values.push(context.tenantId);
        if (this.factory.baseFactory) values.push(this.factory.name);
        const parameterNames = values.map((_p, i) => `$${i + 1}`);

        // The select statement to call the insert function.
        const sql = `SELECT ${context.schemaName}.${this.getInsertFunctionName()}(${parameterNames.join(',')}) as returned`;

        // Execute the SQL statement
        const results = await context.executeSql<AnyRecord[]>(sql, values);
        if (!results || results.length !== 1)
            throw this.factory.logicError(`insert_${this.table.name} returned no result`);
        const result = results[0].returned as AnyRecord;

        // If processLocalizedTextAsJson context flag is set, we have to update data with the localized text that
        // we passed to the insert function. Otherwise don't modify data.
        if (context.processLocalizedTextAsJson) {
            parameters.forEach((parameter, i) => {
                const property = parameter.column.property;
                if (property.isLocalized) data[property.name] = JSON.stringify(values[i]);
            });
        }

        // Keep the test framework happy by setting table.testStatus in the table and its base tables
        this.table.setAsDirty();

        return result;
    }

    /**
     * Drop the upsert function
     */
    static async dropUpsertSqlFunction(context: Context): Promise<void> {
        await new SchemaSqlContext(context.application).executeSqlStatement({
            sql: `DROP FUNCTION IF EXISTS ${context.schemaName}.upsert;`,
        });
        context.sqlFunctionCache.queryWithIgnoredErrors = '';
    }

    /**
     * Create (if needed) the upsert function
     */
    private static async _createUpsertSqlFunction(context: Context, sql: string): Promise<void> {
        if (context.sqlFunctionCache.queryWithIgnoredErrors === sql) return;

        await SqlInsert.dropUpsertSqlFunction(context);
        await new SchemaSqlContext(context.application).executeSqlStatement({
            sql,
        });

        context.sqlFunctionCache.queryWithIgnoredErrors = sql;
    }

    /**
     * Execute an upsert where the conflicts on unique indexes will be ignored
     */
    private async _executeUpsertSqlFunction(
        context: Context,
        cachedInsert: CachedInsert,
        parameterValues: AnyValue[],
    ): Promise<AnyRecord> {
        try {
            await SqlInsert._createUpsertSqlFunction(context, cachedInsert.upsertFunctionSql);

            const results = await CustomMetrics.sql.withMetrics(
                { nodeName: this.factory.name, statementKind: 'upsert' },
                () => context.executeSql<{ upsert: AnyRecord }[]>(cachedInsert.sql, parameterValues),
            );
            const result = results[0];

            return result.upsert;
        } catch (err) {
            sqlLogger.error(`Upsert failed into table ${this.table.name}.
SQL: ${cachedInsert.sql.replace(/\n/g, '\n\t')}
parameters: ${parameterValues}`);
            throw err;
        }
    }

    private upsertConflictErrorMessage(
        context: Context,
        result: AnyRecord,
        cachedInsert: CachedInsert,
        parameterValues: AnyValue[],
    ): string {
        // Upsert had a conflict on a unique index.
        // The node probably has at least 2 unique indexes :
        // - the natural key that was used in the ON CONFLICT(...) clause
        // - at least one other unique index
        // The conflict was on the other unique indexes (not the natural key)
        // There is nothing we can do here, just log an error and skip this record
        // In the future, setup nodes will not be allowed to have more than 1 unique index (the natural key)
        // Some troubleshooting variables were returned by the SQL function
        // - _upsert_failed : true
        // - _err_detail
        // - _err_detail
        return `Could not upsert into ${context.schemaName}.${this.table.name} because of key conflicts
message: ${result._err_message}
details: ${result._err_detail}
sql: ${cachedInsert.upsertFunctionSql}
values: \n\t - ${cachedInsert.sqlParameters
            .map((parameter, i) => `${parameter.valuePath}: ${parameterValues[i]}`)
            .join('\n\t - ')}
                `;
    }

    /**
     * Fetches the _id of the record from its natural key.
     * We need this to return the _id of the existing record when an INSERT ON CONFLICT DO NOTHING statement
     * does nothing because of a conflict.
     *
     * Note: this could be solved differently with an ON CONFLICT(natural_key_columns) DO UPDATE SET _id = EXCLUDED._id
     * instead of an ON CONFLICT DO NOTHING (see https://stackoverflow.com/questions/36886134/postgres-conflict-handling-with-multiple-unique-constraints)
     * but I could not make it work because the natural key index contains COALESCE expressions.
     * We should not invest too much time on this as this is only used by CSV upgrade actions
     * and xtrem-data-management needs a serious refactor.
     */
    private async fetchIdFromNaturalKey(context: Context, data: AnyRecord): Promise<number> {
        const naturalKey = this.factory.isContentAddressable ? ['_valuesHash'] : this.factory.naturalKey;
        if (!naturalKey) throw new LogicError(`${this.factory.name} cannot fetch _id: natural key missing`);

        const sqlConverter = new SqlConverter(context, this.factory);
        const parameterNames = [] as string[];
        const columnNames = [];
        const nullables = [] as boolean[];
        if (!this.factory.isSharedByAllTenants) {
            parameterNames.push(
                sqlConverter.addSqlParameter({
                    // if there is no tenantId in data, we take tenantId from context
                    valuePath: data._tenantId ? 'values._tenantId' : 'context.tenantId',
                    type: 'string',
                }),
            );
            columnNames.push('_tenant_id');
            nullables.push(false);
        }
        naturalKey.forEach(propertyName => {
            const property = this.factory.findProperty(propertyName);
            const factoryName = property.isReferenceProperty() ? property.targetFactory.name : undefined;
            parameterNames.push(
                sqlConverter.addSqlParameter({ valuePath: `values.${propertyName}`, type: property.type, factoryName }),
            );
            columnNames.push(property.columnName);
            nullables.push(property.isNullable);
        });
        const where = columnNames
            .map(
                (columnName, i) =>
                    `(${columnName} = ${parameterNames[i]}${
                        nullables[i] ? ` OR (${columnName} IS NULL AND ${parameterNames[i]} IS NULL)` : ''
                    })`,
            )
            .join(' AND ');
        const sql = `SELECT _id FROM ${this.table.getFullTableName(context)} WHERE ${where}`;
        const parameterValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {
            values: data,
        });
        const records = await CustomMetrics.sql.withMetrics(
            { nodeName: this.factory.name, statementKind: 'select' },
            () => context.executeSql<{ _id: number }[]>(sql, parameterValues),
        );
        if (records.length !== 1)
            throw new LogicError(`${this.factory.name} cannot fetch _id: no match with natural key`);
        return records[0]._id;
    }

    /**
     * Performs an 'insert' and returns the value of all the allocated autoIncrement columns
     */
    private async insertValues(
        /** context */
        context: Context,
        /** The data we are inserting */
        row: AnyRecord,
        /** The options */
        cachedInsert: CachedInsert,
        options: { useUpsert: boolean; ignoreUpsertErrors: boolean },
    ): Promise<AnyRecord> {
        const { sql, sqlParameters } = cachedInsert;

        let result: AnyRecord;
        const parameterValues = await SqlConverter.getParameterValues(context, sqlParameters, {
            values: row,
        });
        if (options.useUpsert) {
            result = await this._executeUpsertSqlFunction(context, cachedInsert, parameterValues);
            if (result._upsert_failed) {
                const msg = this.upsertConflictErrorMessage(context, result, cachedInsert, parameterValues);
                if (options.ignoreUpsertErrors) {
                    // Simply log the error
                    sqlLogger.error(msg);
                    return {};
                }
                throw new Error(msg);
            }

            // SqlValueConverter.toSql expects JS Date object for datetime
            if (result._create_stamp) result._create_stamp = new Date(`${result._create_stamp}Z`);
            if (result._update_stamp) result._update_stamp = new Date(`${result._update_stamp}Z`);
        } else {
            const cnx = context.transaction.connection;
            try {
                result = await CustomMetrics.sql.withMetrics(
                    { nodeName: this.factory.name, statementKind: 'insert' },
                    () => context.sqlPool.execute<AnyRecord>(cnx, sql, parameterValues),
                );
            } catch (err) {
                sqlLogger.error(`Insert failed into table ${this.table.name}.
SQL: ${sql.replace(/\n/g, '\n\t')}
parameters: ${parameterValues}`);
                throw err;
            }

            if (result.updateCount === 0) {
                // ON CONFLICT DO NOTHING did nothing, but did not return the _id of the record so we have to fetch it.
                if (!sql.includes('ON CONFLICT DO NOTHING')) throw new LogicError('unexpected updateCount 0');
                result._id = await this.fetchIdFromNaturalKey(context, row);
            }
        }

        delete result.updateCount;

        if (result && Object.keys(result).length) {
            SqlContext.logger.verbose(() => `Returning values: ${JSON.stringify(result)}`);
        }
        return result;
    }

    getCacheKey(
        context: Context,
        options: SqlInsertOptions & { isRootUserInsert: boolean; insertedColumns: Column[] },
    ): SqlStatementCacheKeyData {
        return {
            kind: options.useUpsert ? 'upsert' : 'insert',
            schemaName: context.schemaName,
            factoryName: this.factory.name,
            options: {
                ...options,
                // Only keep the names of the columns for the key
                insertedColumns: options.insertedColumns.map(column => column.columnName),

                // Context options that influence the INSERT statement
                locale: context.currentLocale,
                locales: context.locales,
                processLocalizedTextAsJson: context.processLocalizedTextAsJson,
            },
        };
    }

    async insert(
        context: Context,
        data: AnyRecord,
        options: SqlInsertOptions = {},
    ): Promise<AnyRecord & { _id?: number }> {
        context.sqlSpy.incrementCounter(this.factory, 'INSERT');
        try {
            // Use the insert function if possible.
            // The insert function does not support the options (yet)
            if (this.factory.hasSqlInsertFunction && Object.keys(options).length === 0)
                return await this.insertWithFunction(context, data);

            const { row, allNewKeys, isRootUserInsert, insertedColumns } = await this.prepareRow(
                context,
                data,
                options,
            );

            const cacheOptions = { ...options, isRootUserInsert, insertedColumns };
            const cachedInsert = this.factory.application.sqlStatementCache.fetch<CachedInsert>({
                getKeyData: () => this.getCacheKey(context, cacheOptions),
                buildStatement: () => {
                    const sqlConverter = new SqlConverter(context, this.factory);
                    const insertInfo = this.getInsertInfo(sqlConverter, cacheOptions);
                    return this.getInsertSql(insertInfo, options);
                },
            });

            const newKeysFromPool = await this.insertValues(context, row, cachedInsert, {
                useUpsert: !!options.useUpsert,
                ignoreUpsertErrors: !!options.ignoreUpsertErrors,
            });

            return { ...allNewKeys, ...newKeysFromPool };
        } finally {
            if (context.testMode) {
                this.table.markAsModifiedForTests();
            }
        }
    }

    private getMetadataInsertInfo(sqlConverter: SqlConverter, metadata: FactoryMetadata, data: AnyRecord): InsertInfo {
        const parameterNames = [] as string[];
        const columnNames = [] as string[];
        // Use specific metadata : this will mainly be used when replaying SQL files
        // where the actual factory definition might differ from the one when the SQL
        // file was recorded.
        const foundPropertyNames = {} as Dict<boolean>;
        metadata.columns.forEach(column => {
            let propertyName = lodash.camelCase(column.name);
            if (column.name.startsWith('_')) {
                // A system property (camelCase removes the leading _)
                propertyName = `_${propertyName}`;
            }

            foundPropertyNames[propertyName] = true;
            data[propertyName] = data[propertyName] ?? null;

            columnNames.push(SqlContext.escape(column.name));
            parameterNames.push(
                sqlConverter.addSqlParameter({
                    // TODO: we are missing target factory name here
                    valuePath: `values.${propertyName}`,
                    type: column.isEncrypted ? 'encryptedString' : column.type,
                }),
            );
        });

        // Temporary hacks for setup CSV reloadings during upgrades.
        // We won't need this with the new insert_table() function because it sets _update_tick to 1.
        // But the SQL files which are replayed by the upgrade replace this function by an older definition which
        // does not set _update_tick, so we need this hack.
        if (!foundPropertyNames._updateTick) {
            columnNames.push(SqlContext.escape('_update_tick'));
            parameterNames.push(sqlConverter.addSqlParameter({ valuePath: 'values._updateTick', type: 'integer' }));
            if (!data._updateTick) data._updateTick = 1;
        }
        // This one should be handled higher, in xtrem-data-management, by adding _tenantId to the metadata.
        if (data._tenantId && !foundPropertyNames._tenantId) {
            columnNames.push(SqlContext.escape('_tenant_id'));
            parameterNames.push(sqlConverter.addSqlParameter({ valuePath: 'values._tenantId', type: 'string' }));
        }

        const returningColumns = this.getReturningColumnNames(sqlConverter.context);

        const schemaName = sqlConverter.context.schemaName;
        const sqlParameters = sqlConverter.sqlParameters;
        const upsertKeyColumns = metadata.naturalKeyColumns;
        return { schemaName, columnNames, sqlParameters, parameterNames, returningColumns, upsertKeyColumns };
    }

    private insertWithMetadata(
        context: Context,
        metadata: FactoryMetadata,
        row: AnyRecord,
        options: SqlInsertOptions & { isRootUserInsert: boolean; insertedColumns: Column[] },
    ): Promise<AnyRecord> {
        const cachedInsert = this.factory.application.sqlStatementCache.fetch<CachedInsert>({
            getKeyData: () => this.getCacheKey(context, options),
            buildStatement: () => {
                const sqlConverter = new SqlConverter(context, this.factory);

                const insertInfo = this.getMetadataInsertInfo(sqlConverter, metadata, row);

                return this.getInsertSql(insertInfo, options);
            },
        });

        return this.insertValues(context, row, cachedInsert, {
            useUpsert: !!options.useUpsert,
            ignoreUpsertErrors: !!options.ignoreUpsertErrors,
        });
    }

    async insertFromSqlFile(
        context: Context,
        data: AnyRecord,
        options: SqlInsertOptions = {},
    ): Promise<AnyRecord & { _id?: number }> {
        const { row, allNewKeys, isRootUserInsert, insertedColumns } = await this.prepareRow(context, data, options);

        if (options.metadata) {
            return {
                ...allNewKeys,
                ...(await this.insertWithMetadata(context, options.metadata, row, {
                    ...options,
                    isRootUserInsert,
                    insertedColumns,
                })),
            };
        }
        return this.insert(context, data, options);
    }
}
