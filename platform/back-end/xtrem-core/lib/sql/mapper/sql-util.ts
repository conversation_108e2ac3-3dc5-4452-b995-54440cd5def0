import { AnyValue, Dict } from '@sage/xtrem-shared';
import { Table } from '..';
import { lazyLoadedMarker } from '../../node-state/lazy-loaded-marker';
import { Context } from '../../runtime';
import { Column } from '../schema/column';
import { SqlContext } from '../sql-context';
import { SqlConverter } from './sql-converter';
import { SqlValueConverter } from './sql-value-converter';

/** Static utility methods shared by SqlInsert and SqlUpdate */
export abstract class SqlUtil {
    static pushColumnArg(sqlConverter: SqlConverter, column: Column): string {
        return sqlConverter.addSqlParameter({
            valuePath: `values.${column.propertyName}`,
            type: column.type,
            column,
            factoryName: (column.property as any).targetFactory?.name,
        });
    }

    /**
     * function to build update/insert sql statements
     *
     * @param context the context
     * @param column the column to process
     * @param val the value (TypeScript value, not a sql value)
     * @param columnNames the name of the columns in the statement
     * @param parameterNames the list of sqlParameters ($1, $2, ...)
     * @param parameterValues the values of the sqlParameters (values of $1, $2, ...)
     */
    static pushValuesForUpdateOrInsert(
        table: Table,
        sqlConverter: SqlConverter,
        column: Column,
        val: AnyValue,
        columnNames: string[],
        parameterNames: string[],
    ): void {
        if (val === lazyLoadedMarker) {
            // Do not save lazy-loaded properties that were not resolved
            return;
        }
        if (column.property.isRequired && val == null)
            throw new Error(`Cannot insert null value in column ${table.name}.${column.columnName}`);
        const v = SqlValueConverter.toSql(sqlConverter.context, column, val);
        if (v === undefined) throw new Error(`Could not insert value in column ${table.name}.${column.columnName}`);
        columnNames.push(SqlContext.escape(column.columnName));
        parameterNames.push(this.pushColumnArg(sqlConverter, column));
    }

    /**
     * Set the localized properties to json format { locale: 'text value', languageCode: 'text value' }
     * The language code will be used as fallback for other locales in the same language
     * For Inserts we also set { defaultLanguage: 'text value' } as fallback value for any other language/locale
     *
     * @param context
     * @param data
     * @param property
     * @param mode
     */
    static fixLocalizedValue(
        context: Context,
        value: AnyValue,
        propertyName: string,
        mode: 'insert' | 'update',
    ): AnyValue {
        // if null or already converted to object, return
        if (typeof value !== 'string') return value;

        let localizedValue: Dict<string> = {
            [context.currentLocale]: value,
        };

        if (context.processLocalizedTextAsJson) {
            if (value.startsWith('{')) {
                // The string is a JSON
                try {
                    localizedValue = JSON.parse(value);
                    if (mode === 'insert') {
                        if (!localizedValue.base) {
                            // If no value was set for the default language then use the first key of the object:
                            // This may happen when importing a csv file containing one single column like description(en). There
                            // is then no way to make the difference between a reference and a localized text:
                            localizedValue.base = localizedValue[Object.keys(localizedValue)[0]];
                        }
                        // When inserting localized texts we have to make sure that one version exist for each main language
                        // present in the object:
                        localizedValue = Object.keys(localizedValue).reduce((r, locale) => {
                            const language = locale.split('-')[0];
                            if (!r[language]) r[language] = r[locale];
                            return r;
                        }, localizedValue);
                    }
                } catch {
                    throw new Error(
                        `Localized property '${propertyName}' has an incorrect format: '${value}'. Expecting '{"key": "value"}'`,
                    );
                }
            } else {
                // The string is not a JSON
                localizedValue = {
                    base: value,
                };
            }
        } else {
            // We insert a value for the default locale language as a fallback value
            if (mode === 'insert') {
                localizedValue.base = value;
                localizedValue[context.defaultLanguage] = value;
                localizedValue[context.currentLocaleLanguage] = value;
            }
            // We only update the current locale language value, if it is the master locale for that language
            if (context.isMasterLocale()) localizedValue[context.currentLocaleLanguage] = value;
        }
        return localizedValue;
    }
}

export const tenantIdColumnName = '_tenant_id';
export const idColumnName = '_id';
