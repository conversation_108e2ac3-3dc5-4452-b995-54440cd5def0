import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>V<PERSON><PERSON>, async<PERSON><PERSON>y, Async<PERSON><PERSON><PERSON><PERSON>eader, AsyncReader } from '@sage/xtrem-async-helper';
import { LogicError } from '@sage/xtrem-shared';
import * as lodash from 'lodash';
import { BaseCollection } from '../../collections/base-collection';
import { CustomMetrics } from '../../metrics';
import { NodeState } from '../../node-state';
import { Property } from '../../properties';
import { Context, isScalar, NodeFactory, PropertyAndValue, tenantCondition } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { Aggregate, AggregateGroup, AggregateGroupResult, Node, NodeQueryFilter, OrderBy } from '../../ts-api';
import { NodeSelector, NodeSelectResult } from '../../ts-api/node-select-types';
import { parseCursor } from '../../types';
import { Column } from '../schema';
import { Table } from '../schema/table';
import { AggregateResult, OutputColumn, SqlConverter, SqlParameter } from './sql-converter';
import { SqlResolver } from './sql-resolver';
import { SqlStatementCacheKeyData } from './sql-statement-cache';
import { SqlValueConverter } from './sql-value-converter';

/** @internal */
export interface OrderByClause {
    path: string[];
    property: Property;
    direction: -1 | 1;
    sql: string;
    columnAlias?: string;
    group?: AggregateGroup;
}

/** @internal */
export interface TableQueryOptions {
    filters?: any[];
    orderByClauses?: OrderByClause[];
    aggregateResult?: AggregateResult;
    first?: number;
    after?: PropertyAndValue[];
    last?: number;
    before?: PropertyAndValue[];
    count?: boolean;
    forUpdate?: boolean;
}

/** @internal */
export interface NodeInternalQueryOptions {
    filters: NodeQueryFilter<Node>[];
    orderBy?: OrderBy<Node>;
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    aggregate?: Aggregate;
    count?: boolean;
    forUpdate?: boolean;
    skipLocked?: boolean;
    locale?: string;
    /**
     * The list of properties to read: only when reading lazy-loaded values
     */
    onlyProperties?: Property[];
    /** set by context.queryWithReader to inhibit caching */
    doNotCache?: boolean;
    /** The query expects a single record in the result */
    singleResultRequest?: boolean;
    /** Selector, for low level context.select(...) queries */
    selector?: NodeSelector<Node>;
    /** Do we map references to natural key instead of _id */
    returnReferencesAsNaturalKey?: boolean;
    collection?: BaseCollection;
}

/**
 * Structure of a cache entry for a generated SELECT statement
 *
 * This structure should be kept reasonably small.
 * It may reference metadata objects (Property instances) because this only costs a pointer
 * but it should not duplicate metadata.
 */
export interface CachedQuery {
    /** SQL statement string */
    sql: string;

    /** Descriptors of the statement parameters */
    sqlParameters: SqlParameter[];

    /** The properties of the paging cursor, if any */
    cursorProperties: Property[];

    /**
     * List of table names that are joined by the query.
     * This information is used to invalidate the cache of query results (NodeFactoryCache)
     */
    allTableNames: string[];

    /**
     * Descriptors of the selected columns (non-aggregate queries).
     * This is used to map column aliases to the property names and to convert the SQL values to property values.
     */
    outputColumns: OutputColumn[] | undefined;

    /** Descriptors of the groups returned by the query (aggregate queries only) */
    groupColumns: OutputColumn[] | undefined;

    /** Descriptors of values returned by the query (aggregate queries only) */
    valueColumns: OutputColumn[] | undefined;
}

/**
 * An output path describes an element which is returned by a query, before it gets processed by the SQL converter.
 * It only contains path information.
 * The SQL converter will transform it into an OutputColumn which contains a SQL expression, an alias, ...
 */
export type OutputPath = {
    /** The path from the root factory of the query or subquery, as an array of property names */
    path: string[];
    /** The output paths of the subquery, if path points to a collection property  */
    subPaths?: OutputPath[];
    /** The function that provides the result (after being converted to SQL) */
    compute?: (this: Node) => any;
};

/**
 * Class which builds a CachedQuery entry.
 * This class is only instantiated if we don't find the entry in the statement cache.
 */
class SqlQueryBuilder {
    /** The SQL converter which builds the query and its parameters */
    private readonly sqlConverter: SqlConverter;

    constructor(
        private readonly factory: NodeFactory,
        private context: Context,
        private readonly options: NodeInternalQueryOptions,
    ) {
        this.sqlConverter = new SqlConverter(context, this.factory);
    }

    /** The Table object */
    private get table(): Table {
        return this.factory.table;
    }

    /** Generates the order by clause for an aggregate query */
    private getGroupsOrderBy(groups: AggregateGroup[]): OrderByClause[] {
        const parsed = this.sqlConverter.convertAggregateGroups(this.sqlConverter, groups);
        return parsed.map(
            (result, i) =>
                ({
                    ...result,
                    path: groups[i].path,
                    direction: 1,
                    property: result.property!,
                    group: groups[i],
                }) as OrderByClause,
        );
    }

    /**
     * Returns the column of a property.
     * Note: it allocates a fake column object if the property is computed.
     */
    private static getColumn(property: any): any {
        if (property.getValue && !property.isStored) {
            // For properties that are not stored and have a getValue (can be converted to sql), we need to return a column instance that will
            // be used specifically in the call to SqlValueConverter.toSql.
            // Note: this column will only be used as an input to SqlValueConverter.toSql
            return new Column(property);
        }
        const column = property.column;
        if (!column) throw new Error(`No column set for the property ${property.name}.`);
        return column;
    }

    /** Generates the where clause for the cursor */
    private cursorWhere(orderByClauses: OrderByClause[], operation: 'after' | 'before'): string {
        if (!this.options[operation]) return '';

        if (!orderByClauses) throw new Error(`${this.table.name}: missing 'orderBy' option`);

        const clausesSqls = orderByClauses.map((clause, i) => {
            const column = SqlQueryBuilder.getColumn(clause.property as any);

            return this.sqlConverter.addSqlParameter({
                valuePath: `${operation}.${i}.value`,
                type: column.type,
                column,
            });
        });

        const parts = orderByClauses.map((orderByClause, i) => {
            const ands = [] as string[];
            for (let j = 0; j < i; j += 1) {
                const clause = orderByClauses[j];
                const column = SqlQueryBuilder.getColumn(clause.property as any);
                let eqClause = `(${clause.sql} = ${clausesSqls[j]})`;
                if (column.isNullable) {
                    eqClause = `(${eqClause} OR (${clause.sql} IS NULL AND ${clausesSqls[j]} IS NULL))`;
                }
                ands.push(eqClause);
            }
            const op =
                operation === 'before'
                    ? orderByClause.direction > 0
                        ? '<'
                        : '>'
                    : orderByClause.direction > 0
                      ? '>'
                      : '<';
            {
                const column = SqlQueryBuilder.getColumn(orderByClause.property as any);
                let clause = `${orderByClause.sql} ${op} ${clausesSqls[i]}`;
                if (column.isNullable) {
                    if (op === '<')
                        clause = `(${clause} OR (${orderByClause.sql} IS NULL AND ${clausesSqls[i]} IS NOT NULL))`;
                    else if (op === '>')
                        clause = `(${clause} OR (${orderByClause.sql} IS NOT NULL AND ${clausesSqls[i]} IS NULL))`;
                }
                ands.push(clause);
            }

            return SqlConverter.and(ands);
        });

        return SqlConverter.or(parts);
    }

    /** Generates the order by clause for a regular and aggregate query */
    private orderBySql(
        orderByClauses: OrderByClause[],
        reverse: boolean,
        groups?: OutputColumn[],
        values?: OutputColumn[],
    ): string {
        if (!orderByClauses) return '';
        if (orderByClauses.length === 0) return '';
        return `ORDER BY ${orderByClauses
            .map(clause => {
                const direction: -1 | 1 = reverse ? (clause.direction === 1 ? -1 : 1) : clause.direction;
                // the aggregatorOperation can be other than "value" (day, month, ...) so we have to find the right one to feed the columAlias
                const aggregationOperator = SqlQueryBuilder.getAggregationOperator(clause, groups, values);
                const columnAlias =
                    groups && !clause.group ? `${clause.columnAlias}${aggregationOperator}` : clause.columnAlias;
                // If there is a collation set on the current context, the localized string property is collated, therefor we
                // need to order by using the collated sql the localized property was resolved to.
                const collated =
                    clause.property.type === 'string' && clause.property.isLocalized && !!this.context.collation;
                return `${collated ? clause.sql : columnAlias} ${
                    direction === -1 ? 'DESC NULLS LAST' : 'ASC NULLS FIRST'
                }`;
            })

            .join(', ')}`;
    }

    /** Get the aggregator operation of the column in the order by clause from group by or values clause */
    private static getAggregationOperator(
        clause: OrderByClause,
        groups?: OutputColumn[],
        values?: OutputColumn[],
    ): string {
        const outputColumns = [...(values || []), ...(groups || [])];
        const existingOutputColumn = outputColumns.find(
            column =>
                // group.columnAlias?.substring = t_0_valued_cost_value
                // group.aggregationOperator = _value
                column.columnAlias?.substring(
                    0,
                    column.columnAlias.lastIndexOf(`_${SqlResolver.makeColumnAlias(column.aggregationOperator || '')}`),
                ) === `${clause.columnAlias}`,
        );
        return existingOutputColumn?.aggregationOperator
            ? `_${SqlResolver.makeColumnAlias(existingOutputColumn.aggregationOperator)}`
            : '';
    }

    /** Generates the where clause for all the query's filters */
    private whereClause(orderByClauses: OrderByClause[]): string {
        // Depending on the context a localized property can be read a stringyfied json or as a selected value of the
        // json but in any case the where cause will select a part of the object:
        const withLocalizedTextAsJson = this.context.processLocalizedTextAsJson;
        try {
            this.context.processLocalizedTextAsJson = false;
            const filters = [
                ...this.sqlConverter.convertFilters(this.options.filters),
                this.cursorWhere(orderByClauses, 'after'),
                this.cursorWhere(orderByClauses, 'before'),
            ];
            return tenantCondition(this.sqlConverter, this.table, 't0', filters);
        } finally {
            // Whatever happens we have to restore the original value of this.context.processLocalizedTextAsJson
            this.context.processLocalizedTextAsJson = withLocalizedTextAsJson;
        }
    }

    /** Generates the group by clauses (aggregate query) */
    private getGroupByClauses(
        aggregateResult: AggregateResult,
        orderByClauses: OrderByClause[],
        hasLast: boolean,
    ): { groupByClause?: string; groupOrderByClause?: string } {
        if (aggregateResult?.groups?.length) {
            // Generate the 'group by' clause
            const groupByClause = `GROUP BY ${aggregateResult?.groups.map(groupBy => groupBy.sql).join(', ')}`;

            const groupOrderByClause = ` ORDER BY ${aggregateResult?.groups
                .map(groupBy => `${groupBy.columnAlias} ${hasLast ? 'DESC NULLS LAST' : 'ASC NULLS FIRST'}`)
                .join(', ')}`;
            return {
                groupByClause,
                groupOrderByClause,
            };
        }
        if (aggregateResult?.values?.length) {
            return {};
        }
        return {
            groupOrderByClause: this.orderBySql(orderByClauses, hasLast),
        };
    }

    /**
     * Converts a selector to an array of paths for the selected columns.
     * For each selected column the returned path the array of properties that lead to this column.
     * For example a selector like `{ name: true, reference: { quantity: true, amount: true } }`
     * is converted to `[['name'], ['reference', 'quantity'], ['reference', 'amount']]`.
     * If the selector is just true, the selected columns will just be true
     */
    private getOutputPaths(
        selector: NodeSelector<Node>,
        factory: NodeFactory | undefined,
        path: string[],
        outputPaths: OutputPath[],
    ): OutputPath[] {
        const selectorToUse = selector === true ? { _id: true } : selector;
        Object.entries(selectorToUse).forEach(([key, value]) => {
            const newPath = [...path, key];
            if (typeof value === 'function') {
                outputPaths.push({
                    path: newPath,
                    compute: value,
                });
                return;
            }
            const property = factory?.findProperty(key);
            if (property?.computeValue) throw property.logicError('computed properties are not allowed in selector');
            const newFactory = property?.isForeignNodeProperty() ? property.targetFactory : undefined;
            if (property?.isCollectionProperty()) {
                const subPaths = value === true ? [] : this.getOutputPaths(value, newFactory, [], []);
                outputPaths.push({
                    path: newPath,
                    subPaths,
                });
            } else if (value === true) {
                outputPaths.push({ path: newPath });
            } else if (value && typeof value === 'object') {
                // If _id is not on selector we push newPath (without _id) to be able to detect null values
                if (!value._id) outputPaths.push({ path: newPath });
                this.getOutputPaths(value, newFactory, newPath, outputPaths);
            }
        });
        return outputPaths;
    }

    /**
     * Returns the paths for all the columns of a table.
     * The result is an array of paths and each path is an array of property names, like the result of getSelectorPaths,
     * but the path arrays contain only one property name in this case.
     */
    private getTableColumnPaths(): OutputPath[] {
        return this.table
            .getColumns({
                inherit: true,
                includeSystemColumns: true,
                skipLazyLoadableColumns: !this.context.noLazyLoading,
            })
            .filter(
                // we filter out _custom_data, except if the requesting factory is SysCustomRecord
                column => {
                    if (column.columnName === '_tenant_id') return false;
                    return true;
                },
            )
            .map(column => ({ path: [column.property.name] }));
    }

    /** Generates the list of output columns (non-aggregate queries) */
    private getOutputColumns(): OutputColumn[] {
        const outputPaths = this.options.selector
            ? this.getOutputPaths(this.options.selector, this.factory, [], [])
            : this.getTableColumnPaths();
        return this.sqlConverter.convertOutputPaths(outputPaths);
    }

    /** Is the query an aggregate query? */
    private static isAggregateQuery(aggregateResult: AggregateResult): boolean {
        return !!aggregateResult?.groups?.length || !!aggregateResult?.values?.length;
    }

    /** Does the aggregate query produce a single result, or a set of groups */
    private static isSingleResult(aggregateResult: AggregateResult): boolean {
        return !!(aggregateResult && aggregateResult.groups && aggregateResult.groups.length === 0);
    }

    /** Builds the query statement. Returns a CacheQuery entry */
    buildQuery(): CachedQuery {
        const options = this.options;
        const aggregateResult = this.sqlConverter.convertAggregate(
            this.sqlConverter,
            options.count && this.options.aggregate?.values
                ? { groups: this.options.aggregate.groups, values: [] }
                : this.options.aggregate,
        );

        const orderByResult =
            this.options.aggregate && !this.options.orderBy
                ? this.getGroupsOrderBy(this.options.aggregate.groups)
                : this.sqlConverter.convertOrderBy(this.options.orderBy ?? this.factory.defaultOrderBy);

        const orderByClauses = this.options.count ? [] : orderByResult;

        const hasLast = !!options.last && !options.count;

        let outputColumns: OutputColumn[];
        if (options.count) {
            outputColumns = [];
        } else if (options.onlyProperties) {
            outputColumns = this.sqlConverter.convertOutputPaths(options.onlyProperties.map(p => ({ path: [p.name] })));
            this.checkAbstractReferences(outputColumns);
        } else if (SqlQueryBuilder.isAggregateQuery(aggregateResult)) {
            const { groups, values } = aggregateResult;

            outputColumns = [...(values || []), ...(groups || [])];
        } else {
            outputColumns = this.getOutputColumns();
            this.checkAbstractReferences(outputColumns);
        }
        const { groupByClause, groupOrderByClause } = this.getGroupByClauses(aggregateResult, orderByClauses, hasLast);

        const missingColumnInGroupBy: string[] = [];
        orderByClauses.forEach(clause => {
            if (
                !outputColumns.find(
                    col =>
                        col.columnAlias === clause.columnAlias ||
                        // this check is for the case order by column is the one in group by too
                        (SqlQueryBuilder.isAggregateQuery(aggregateResult) &&
                            col.columnAlias ===
                                `${clause.columnAlias}_${SqlResolver.makeColumnAlias(col.aggregationOperator || '')}`),
                )
            ) {
                missingColumnInGroupBy.push(clause.path.join('.'));
                outputColumns.push({
                    alias: '',
                    ...clause,
                    path: clause.path.join('.'),
                    factory: this.factory,
                    property: clause.property,
                    type: clause.property.type,
                });
            }
        });
        if (SqlQueryBuilder.isAggregateQuery(aggregateResult) && missingColumnInGroupBy.length > 0)
            throw new Error(
                `${missingColumnInGroupBy.join(
                    ',',
                )} must appear in the GROUP BY clause or be used in an aggregate function`,
            );
        const whereClause = `WHERE ${this.whereClause(orderByClauses)}`;
        const columnsClause = outputColumns.map(column => `${column.sql} AS ${column.columnAlias}`).join(', ');

        // The schema name is included in alias.table
        const aliasesClause = this.sqlConverter.getTableAliases();

        const sqlParts = [
            options.count ? 'SELECT COUNT(*) AS `nrows`' : `SELECT ${columnsClause}`,
            `FROM ${aliasesClause}`,
            whereClause,
        ];
        if (groupByClause != null) sqlParts.push(groupByClause);

        if (!options.count) {
            if (!SqlQueryBuilder.isSingleResult(aggregateResult)) {
                sqlParts.push(
                    this.orderBySql(orderByClauses, hasLast, aggregateResult.groups, aggregateResult.values) ||
                        groupOrderByClause ||
                        '',
                );
            }
            if (hasLast) {
                if (options.last) sqlParts.push(`LIMIT ${options.last}`);
            } else if (options.first) {
                sqlParts.push(`LIMIT ${options.first}`);
            }
            /**
             * Using the 'FOR NO KEY UPDATE' in order to avoid raising 'deadlocks' issue in the db on foreign-keys
             * Postgres has the following behavior, if we consider a parent and child (fk on parent) structure:
             * - in case of a SELECT ... FOR UPDATE on the parent table, Postgres will acquire a 'FOR UPDATE' row-lock on the parent table's index (_id)
             * - if, in another transaction, while the first did not commit yet, we INSERT a child for that parent's _id as fk, Postgres tries to acquire a KEY SHARE lock on the parent
             *   which is not yet possible, hence blocking the 2nd transaction
             * Using FOR NO KEY UPDATE will avoid having idle transactions.
             * I suppose this also reduce the risk of getting in a deadlock situation (tested with artillery)
             * See https://www.migops.com/blog/select-for-update-and-its-behavior-with-foreign-keys-in-postgresql/ for more details
             */
            if (options.forUpdate && this.context.isolationLevel !== 'high') {
                sqlParts.push(`FOR NO KEY UPDATE OF t0 ${options.skipLocked ? 'SKIP LOCKED' : ''}`);
            }
        }

        const sqlParameters = this.sqlConverter.sqlParameters;
        const allTableNames = this.sqlConverter.allTableNames;
        const groupColumns = aggregateResult.groups;
        const valueColumns = aggregateResult.values;
        const cursorProperties = orderByClauses.map(clause => clause.property);
        return {
            sql: sqlParts.join('\n'),
            sqlParameters,
            cursorProperties,
            allTableNames,
            outputColumns,
            groupColumns,
            valueColumns,
        };
    }

    // Check for references to abstract nodes and add their constructor to the query
    private checkAbstractReferences(outputColumns: OutputColumn[]): void {
        const abstractReferences = outputColumns.filter(
            col => col.property?.isReferenceProperty() && col.property.targetFactory?.isAbstract,
        );
        // Add _constructor for abstract node references
        abstractReferences.forEach(ref => {
            outputColumns.push(
                this.sqlConverter.convertOutputPath({
                    path: [`${ref.property?.name}`, '_constructor'],
                }),
            );
        });
    }
}

/** @internal */
export class SqlQuery {
    static internalStatistics = {
        queryCount: 0,
    };

    private constructor(
        private readonly context: Context,
        private readonly factory: NodeFactory,
        private readonly options: NodeInternalQueryOptions,
    ) {
        if (this.factory.storage !== 'sql' && this.factory.storage !== 'external') {
            throw new Error(`${factory.fullName}: cannot query: bad class storage: ${String(this.factory.storage)}`);
        }
        this.fixAggregate();
    }

    private async init(): Promise<SqlQuery> {
        if (this.options.locale) {
            await this.context.setCurrentLocale(this.options.locale);
        }
        return this;
    }

    static create(context: Context, factory: NodeFactory, options: NodeInternalQueryOptions): Promise<SqlQuery> {
        return new SqlQuery(context, factory, options).init();
    }

    private fixAggregate(): void {
        if (!this.options.aggregate) return;
        this.options.aggregate?.groups.forEach(group => {
            if (!group.groupedBy) group.groupedBy = 'value';
        });
    }

    private mapOutputColumnValue(outputColumn: OutputColumn, record: any): Promise<any> {
        if (!outputColumn.columnAlias) throw new LogicError('no column alias');
        const sqlValue = record[outputColumn.columnAlias];
        if (outputColumn.type !== 'collection')
            return SqlValueConverter.fromSql(this.context, outputColumn, record[outputColumn.columnAlias]);

        return Array.isArray(sqlValue)
            ? asyncArray(sqlValue)
                  .map(val => (val && typeof val === 'object' ? this.mapRecordIn(outputColumn.subColumns, val) : val))
                  .toArray()
            : sqlValue;
    }

    private async doMapRecordIn(outputColumns: OutputColumn[] | undefined, record: any): Promise<any> {
        if (this.factory.storage === 'external') {
            return this.factory.externalStorageManager!.mapRecordIn!(record);
        }
        if (!outputColumns) throw new LogicError('missing outputColumns');

        // select call with true as selector - returns the array of selected _ids
        if (this.options.selector === true) {
            const col = outputColumns[0];
            if (col.columnAlias && col.payloadPath?.length === 1 && col.payloadPath[0] === '_id') {
                if (col.property?.name !== '_id') throw new LogicError(`unexpected property: ${col.property?.name}`);
                return record[col.columnAlias];
            }
        }

        const result = {} as any;
        await asyncArray(outputColumns).forEach(async outputColumn => {
            if (!outputColumn.payloadPath) return;

            const value = await this.mapOutputColumnValue(outputColumn, record);

            let current = result;
            let lastKey = outputColumn.payloadPath[outputColumn.payloadPath.length - 1];
            for (let i = 0; i < outputColumn.payloadPath.length - 1 && current != null; i += 1) {
                const key = outputColumn.payloadPath[i];

                // For references to an abstract node, set the key to `_${referencePropertyName}_constructor`
                // This value will be used when instantiating the reference thunk, instead of querying the
                // base table for the specific constructor at that stage
                if (lastKey === '_constructor' && outputColumn.parent?.factory.isAbstract) {
                    lastKey = `_${key}_constructor`;
                    break;
                }

                // Things get a little tricky here because we have to handle nullable references.
                // If current[key] is null we keep the null value
                // Otherwise if current[key] is not already an object we allocate a new object for it.
                if (typeof current[key] !== 'object') current[key] = {};
                // Then, if we are just before an `_id` leaf and value is null, we have to truncate
                // the result here with a `null`, instead of generating an `{ _id: null }` sub object.
                if (value === null && lastKey === '_id' && i === outputColumn.payloadPath.length - 2)
                    current[key] = null;
                current = current[key];
            }
            if (current) current[lastKey] = value;
        });
        if (this.factory.baseFactory && !result._constructor && !this.options.selector) {
            result._constructor = this.factory.name;
        }
        return result;
    }

    private mapRecordIn(outputColumns: OutputColumn[] | undefined, record: any): Promise<any> {
        if (this.options.returnReferencesAsNaturalKey && this.factory.storage === 'sql') {
            return this.context.withReferenceAsNaturalKey(() => this.doMapRecordIn(outputColumns, record));
        }
        return this.doMapRecordIn(outputColumns, record);
    }

    private async mapAggregateRecordIn(
        groupColumns: OutputColumn[] | undefined,
        valueColumns: OutputColumn[] | undefined,
        record: any,
    ): Promise<any> {
        if (this.factory.storage === 'external') {
            return this.factory.externalStorageManager!.mapAggregateRecordIn!(record);
        }
        if (!groupColumns) throw new LogicError('missing groupColumns');
        if (!valueColumns) throw new LogicError('missing valueColumns');

        const assign = (r: any, path: string[], val: any, groupByOperator?: string): void => {
            const key = path.shift()!;
            if (path.length > (groupByOperator ? 1 : 0)) {
                if (!r[key]) r[key] = {};
                assign(r[key], path, val, groupByOperator);
            } else {
                r[key] = val;
            }
        };
        const getAggregatorPath = (col: OutputColumn): string[] => {
            const parts = col.path.split('.').slice(1); // remove first `this` element
            return [...parts.filter(part => part !== '_super'), col.aggregationOperator as string];
        };
        const result = {} as any;
        await asyncArray(groupColumns).forEach(async col => {
            result.group = result.group || {};
            assign(
                result.group,
                getAggregatorPath(col),
                await SqlValueConverter.fromSql(this.context, col, record[col.columnAlias!]),
                col.aggregationOperator,
            );
        });
        await asyncArray(valueColumns).forEach(async col => {
            result.values = result.values || {};
            assign(
                result.values,
                getAggregatorPath(col),
                await SqlValueConverter.fromSql(this.context, col, record[col.columnAlias!]),
            );
        });
        return result;
    }

    /**
     * Converts a filter by its skeleton.
     * Leaves of the filter (scalar values) are handled as parameters so we replace them by 0 to get the key.
     */
    private static stripFilterValues(filter: AnyValue, key: string | number): AnyValue {
        if (filter == null) return filter;
        if (typeof filter === 'function') return filter.toString();
        // The _fn value is not passed as a parameter (it is converted to SQL) so we cannot strip it from the key.
        if (key === '_fn') return filter;
        if (isScalar(filter)) return 0;
        if (filter instanceof Node) return { _id: 0 };
        if (Array.isArray(filter)) return filter.map(SqlQuery.stripFilterValues);
        if (typeof filter === 'object') return lodash.mapValues(filter, SqlQuery.stripFilterValues);
        throw new Error(`invalid filter element: ${filter} of type ${typeof filter}`);
    }

    private getCacheKey(): SqlStatementCacheKeyData {
        return {
            kind: 'select',
            schemaName: this.context.schemaName,
            factoryName: this.factory.name,
            // query options
            options: {
                // Query options that influence the generated SELECT statement
                selector: this.options.selector,
                filters: SqlQuery.stripFilterValues(this.options.filters, ''),
                orderBy: this.options.orderBy,
                first: this.options.first,
                last: this.options.last,
                // Only keep the length of the before/after options
                before: this.options.before?.length,
                after: this.options.after?.length,
                aggregate: this.options.aggregate,
                count: this.options.count,
                forUpdate: this.options.forUpdate,
                skipLocked: this.options.skipLocked,
                locale: this.options.locale || this.context.currentLocale,
                onlyProperties: this.options.onlyProperties?.map(prop => prop.name),
                singleResultRequest: this.options.singleResultRequest,

                context: this.context.getSqlCacheKey(),
            },
        };
    }

    private buildQuery(): CachedQuery {
        return new SqlQueryBuilder(this.factory, this.context, this.options).buildQuery();
    }

    private getCachedQuery(): CachedQuery {
        const cache = this.factory.application.sqlStatementCache;
        return cache.fetch({
            getKeyData: () => this.getCacheKey(),
            buildStatement: () => this.buildQuery(),
        });
    }

    private getRawReader(): {
        reader: AsyncReader<AnyValue>;
        outputColumns?: OutputColumn[];
        groupColumns?: OutputColumn[];
        valueColumns?: OutputColumn[];
    } {
        SqlQuery.internalStatistics.queryCount += 1;
        const { options } = this;

        if (this.factory.storage === 'external') {
            if (!this.factory.externalStorageManager?.query)
                throw new Error(`${this.factory.name} : missing externalStorageManager.query`);
            return {
                reader: this.factory.externalStorageManager.query(this.context, options),
            };
        }

        const { sql, sqlParameters, cursorProperties, allTableNames, outputColumns, groupColumns, valueColumns } =
            this.getCachedQuery();

        const after = this.options.after ? parseCursor(cursorProperties, this.options.after) : undefined;
        const before = this.options.before ? parseCursor(cursorProperties, this.options.before) : undefined;

        // We don't resolve the promises here but we do it when we construct the reader
        // This is because we want to be able to create the reader from a sync API.
        const promisedParameterValues = SqlConverter.getParameterValues(this.context, sqlParameters, {
            filters: options.filters,
            before,
            after,
        });
        if (options.count) {
            // For now we don't cache the count queries but we track them to see if we should cache them.
            this.factory.cache.incrementCountQueryCounter();
            return {
                reader: this.context.createSqlReader(sql, promisedParameterValues).map((r: any) => r.nrows),
            };
        }

        const createReader = (): AsyncReader<AnyValue> => {
            this.context.sqlSpy.incrementCounter(this.factory, 'SELECT');
            return CustomMetrics.sql.wrapReader(
                { nodeName: this.factory.name, statementKind: 'select' },
                this.context.createSqlReader(sql, promisedParameterValues),
            );
        };

        let doNotCache =
            options.doNotCache ||
            // If the forUpdate option is set the query will create locks in the db so we have to execute it.
            options.forUpdate ||
            // Do not cache if factory is not cached
            !this.factory.isCached;

        // Only cache if all the joined tables are cached, because changes are only broadcasted for cached tables.
        if (
            !doNotCache &&
            allTableNames.some(
                tableName => !this.context.application.getFactoryByTableName(tableName.split('.').pop() ?? '').isCached,
            )
        ) {
            const nonCachedTables = allTableNames.filter(
                tableName => !this.context.application.getFactoryByTableName(tableName.split('.').pop() ?? '').isCached,
            );
            loggers.performance.warn(
                `Query cannot be cached because of non cached tables ${nonCachedTables} referenced by one of ${allTableNames}`,
            );
            doNotCache = true;
        }

        if (doNotCache) {
            this.factory.cache.incrementIgnoredCounter();
            return { reader: createReader(), outputColumns, groupColumns, valueColumns };
        }
        const reader = new AsyncArrayReader(async () => {
            const parameterValues = await promisedParameterValues;

            return this.factory.cache.fetch(this.context, {
                getKey: () => JSON.stringify({ sql, args: parameterValues }),
                getValue: async () => {
                    const value = await createReader().readAll();
                    return { value, invalidatingTokens: allTableNames };
                },
                // Global cache cannot be trusted if the context is writable
                isolateInContext: this.context.isWritable,
            });
        });
        return { reader, outputColumns, groupColumns, valueColumns };
    }

    getDataReader(): AsyncReader<AnyRecord> {
        const { reader, outputColumns } = this.getRawReader();
        return reader.map(r => this.mapRecordIn(outputColumns, r));
    }

    async getCount(): Promise<number> {
        const { reader } = this.getRawReader();
        const results = await reader.readAll();
        if (this.options.aggregate) {
            return results.length; // If aggregate is used, we return the number of groups
        }
        if (results.length !== 1 || typeof results[0] !== 'number')
            throw new LogicError('Internal error: getCount does not return a single number');
        return results[0];
    }

    async getNodeData(): Promise<AnyRecord | null> {
        const results = await this.getDataReader().readAll();
        if (results.length > 1) throw new Error('Internal error: getNodeData returns more than 1 record');
        return results.length === 1 ? results[0] : null;
    }

    getNodeReader(): AsyncReader<Node> {
        const forUpdate = !!this.options.forUpdate;
        return this.getDataReader().map(
            record => NodeState.newFromQuery(this.context, this.factory, record, forUpdate).node,
        );
    }

    getSelectReader(): AsyncReader<NodeSelectResult<Node, NodeSelector<Node>>> {
        return this.getDataReader() as AsyncReader<NodeSelectResult<Node, NodeSelector<Node>>>;
    }

    getAggregateReader(): AsyncReader<AggregateGroupResult<Node>> {
        const { reader, groupColumns, valueColumns } = this.getRawReader();
        return reader.map(r => this.mapAggregateRecordIn(groupColumns, valueColumns, r));
    }
}
