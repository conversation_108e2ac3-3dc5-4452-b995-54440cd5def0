import { AnyRecord, AnyValue } from '@sage/xtrem-async-helper';
import { ColumnTypeName } from '@sage/xtrem-shared';

/** @internal */
export type PostgreSqlTypes =
    | 'SERIAL8'
    | 'BOOL'
    | 'INT2'
    | 'INT4'
    | 'INT8'
    | 'INT8RANGE'
    | 'NUMRANGE'
    | 'DATE'
    | 'DATERANGE'
    | 'TSTZRANGE'
    | 'TIME'
    | 'TIMESTAMPTZ(3)'
    | 'FLOAT4'
    | 'NUMERIC'
    | 'FLOAT8'
    | 'BYTEA'
    | 'TEXT'
    | 'VARCHAR'
    | 'UUID'
    | 'JSONB'
    | '_INT8'
    | '_INT2'
    | '_VARCHAR';

/** @internal */
export type XtremToPostgreSqlType = {
    [key in ColumnTypeName]: {
        type: PostgreSqlTypes;
        default: AnyValue;
    };
};

/** @internal */
export type PostgreSqlTypesToXtrem = {
    [key in PostgreSqlTypes]?: {
        name: ColumnTypeName;
        default: AnyValue;
    };
};

/** @internal */
export interface SqlAddColumnsOptions {
    /** Add the column without any constraints */
    skipConstraints?: boolean;
    /**
     * table name to use when adding a column, this is used when a table is to be renamed but the rename
     * action has not happened yet, so we can pass the old table name, so that the add column action does not fail.
     */
    tableNameToUse?: string;
}

/**
 * @internal
 */
export interface PostgreSqlColumnType {
    name: PostgreSqlTypes;
    typeNotation?: string;
    isEnum?: boolean;
}

/**
 * @internal
 */
export interface ParseColumnDefinitionResult {
    columnName: string;
    typeName: string;
    /**
     * The SQL clause to apply (WITHOUT the 'DEFAULT' keyword) to define the SQL default of the column.
     * undefined if the column does not need any SQL default value.
     */
    default?: string;
    /**
     * The SQL clause to apply to the column to set its nullable/Not nullable state.
     */
    nullable: string;
}

export abstract class ColumnInconsistency {
    constructor(private readonly _description: string) {}

    get description(): string {
        return this._description;
    }
}

export class ColumnInconsistencyNullable extends ColumnInconsistency {
    constructor(
        readonly oldNullableFlag: boolean,
        readonly newNullableFlag: boolean,
    ) {
        super(`nullable ${oldNullableFlag} -> ${newNullableFlag}`);
    }
}

/**
 * The SQL default value (int the database) differs to what it should be from the decorators
 */
export class ColumnInconsistencyDefaultValue extends ColumnInconsistency {
    constructor(oldDefaultValue?: string, newDefaultValue?: string) {
        super(
            newDefaultValue === undefined
                ? 'drop default value'
                : `default value ${oldDefaultValue} -> ${newDefaultValue}`,
        );
    }
}

export class ColumnInconsistencyType extends ColumnInconsistency {
    constructor(
        description: string,
        readonly options: {
            // The type of the existing column
            fromType: string;
            // The type of the property (the one that has to be applied to the existing column)
            toType: string;
            // A string the describes the type change 'A->B'
            conversionType: string;
        },
    ) {
        super(description);
    }
}

/** @internal */
export interface DropTableOptions {
    ifExists?: boolean;
    cascade?: boolean;
}

/** @internal */
export interface SqlErrorMessageType {
    violatesForeignKeyConstraint?: {
        table: string;
        foreignKey: string;
        key: AnyRecord;
        fkTable: string;
    };
}
