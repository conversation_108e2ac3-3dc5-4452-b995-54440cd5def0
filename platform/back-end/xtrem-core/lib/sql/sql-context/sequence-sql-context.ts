import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Connection } from '@sage/xtrem-postgres';
import { SchemaSqlContext } from '.';
import { Application } from '../../application';
import { retry } from '../../runtime';
import { idColumnName, tenantIdColumnName } from '../mapper';
import { SqlContext } from './sql-context';
/**
 * Options for fixing a sequence
 */
interface FixAutoIncrementSequenceOptions {
    /**
     * Max value that we have inserted
     */
    newMaxValue?: number;
}

export class SequenceSqlContext extends SchemaSqlContext {
    /** @disabled_internal */
    constructor(
        application: Application,
        readonly tableName: string,
    ) {
        super(application);
    }

    private async getSequenceName(cnx: Connection, column: string): Promise<string | undefined> {
        const resolvedTableName = SqlContext.getFullTableName(this.schemaName, this.tableName);
        try {
            const sql = `SELECT pg_get_serial_sequence('${resolvedTableName}', '${column}') sequence_name;`;
            const result = await this.execute<{ sequence_name: string }[]>(cnx, sql);
            return result && result[0] && result[0].sequence_name;
        } catch {
            // The sequence does not exist
            SqlContext.logger.warn(`Could not find sequence for ${resolvedTableName}.${column}`);
            return undefined;
        }
    }

    private async alterSequence(
        connection: Connection,
        fullTableName: string,
        sequenceName: string,
        columnName: string,
        newMaxVal: number,
    ): Promise<void> {
        SqlContext.logger.verbose(() => `Alter sequence '${sequenceName}`);

        const config = ConfigManager.current;
        const tries = config.storage?.sql?.maxTries ?? 120;
        const delayBeforeRetry = config.storage?.sql?.delayBeforeRetry ?? 1000;

        // Lock table to protect against concurrent inserts while we update the sequence
        // We need to set the sequence to the max of maxVal and the current max in the table.
        // We have to re-read the max of the table in case another transaction inserted into the table
        // between the time newMaxVal was obtained and the time we get our table lock.
        // A shared lock is sufficient.
        // See https://stackoverflow.com/questions/244243/how-to-reset-postgres-primary-key-sequence-when-it-falls-out-of-sync
        const sql = `
            DO
            $$
            BEGIN
                SET LOCAL lock_timeout = '100ms';
                LOCK TABLE ${fullTableName} IN SHARE MODE;
                SELECT setval('${sequenceName}', COALESCE((SELECT GREATEST(MAX(${columnName}), ${newMaxVal}) + 1 FROM ${fullTableName}), 1), false);
                COMMIT;
            EXCEPTION WHEN others THEN
                ROLLBACK;
            END;
            $$
            LANGUAGE PLpgSQL;`;
        await retry(() => this.execute(connection, sql, []), { maxTries: tries, delayBeforeRetry });
    }

    nextSequence(columnName: string): Promise<number> {
        return this.withConnection(cnx => this.getNextSequenceByColumnName(cnx, columnName));
    }

    private async getNextSequence(cnx: Connection, sequenceName: string): Promise<number> {
        const sql = `SELECT CASE WHEN is_called THEN last_value + 1 ELSE last_value END last_value FROM ${sequenceName};`;
        const result = await this.execute<{ last_value: number }[]>(cnx, sql);
        if (result && result[0] && result[0].last_value) {
            return result[0].last_value;
        }
        return 1;
    }

    private async getNextSequenceByColumnName(cnx: Connection, columnName: string): Promise<number> {
        const sequenceName = await this.getSequenceName(cnx, columnName);
        if (sequenceName) {
            return this.getNextSequence(cnx, sequenceName);
        }
        return 1;
    }

    /**
     * Fix the sequence used by an auto-increment column
     * @param column The auto-increment
     * @param options
     */
    async fixAutoIncrementSequence(
        tenantId: string | null,
        column: string,
        options?: FixAutoIncrementSequenceOptions,
    ): Promise<void> {
        await this.fixAutoIncrementSequences(tenantId, [column], options);
    }

    /**
     * Fix the sequences of the bound table
     * @param columns list of auto-increment columns
     * @param options
     */
    async fixAutoIncrementSequences(
        tenantId: string | null,
        columns: string[],
        options: FixAutoIncrementSequenceOptions = {},
    ): Promise<void> {
        const { newMaxValue } = options;
        const factory = this.application.getFactoryByTableName(this.tableName);

        // The strategy is:
        // Given (a) the max _id for the given tenant, (b) the max _id for all tenants and (c) the next sequence value. Then:
        // - If (a <= b and c > b) we are good and we do not need to adjust the sequence (and thus lock the table)
        // - Else (a > b or c <= b) we need to adjust the sequence:
        //   - We add a lock timeout of 100ms before the LOCK TABLE so that we don't deadlock with other transactions.
        //   - We use a retry loop until we manage to lock the table or throw an error after the max tries.
        await this.withConnection(cnx =>
            asyncArray(columns).forEach(async columnName => {
                const fullTableName = SqlContext.getFullTableName(this.schemaName, this.tableName);
                const sequenceName = await this.getSequenceName(cnx, columnName);
                if (sequenceName) {
                    // if an explicit value is provided we do the alter sequence in any case
                    if (
                        this.application.applicationType !== 'test' &&
                        newMaxValue == null &&
                        columnName === idColumnName
                    ) {
                        const sql = factory.isSharedByAllTenants
                            ? `SELECT null as ${tenantIdColumnName}, max(_id) as "max" FROM ${fullTableName}`
                            : `SELECT ${tenantIdColumnName}, max(_id) as "max" FROM ${fullTableName} GROUP BY ${tenantIdColumnName}`;

                        const maxIds = await this.execute<{ [tenantIdColumnName]: string | null; max: number }[]>(
                            cnx,
                            sql,
                            [],
                        );
                        const highestId = Math.max(...maxIds.map(m => m.max));
                        const currentTenantMaxId = factory.isSharedByAllTenants
                            ? highestId
                            : maxIds.filter(ids => ids[tenantIdColumnName] === tenantId)[0]?.max || 0;
                        const nextSequence = await this.getNextSequence(cnx, sequenceName);
                        if (currentTenantMaxId <= highestId && nextSequence > highestId) {
                            return;
                        }
                    }
                    if (!newMaxValue) {
                        const sql = `SELECT max(${columnName}) max_val from ${fullTableName};`;
                        const maxVal = (await this.execute<{ max_val: number }[]>(cnx, sql))[0].max_val;
                        if (maxVal) {
                            await this.alterSequence(cnx, fullTableName, sequenceName, columnName, maxVal);
                        }
                    } else {
                        await this.alterSequence(cnx, fullTableName, sequenceName, columnName, newMaxValue);
                    }
                }
            }),
        );
    }

    private async dropSequence(sequenceName: string): Promise<boolean> {
        try {
            await this.withConnection(async cnx => {
                SqlContext.logger.verbose(() => `Drop sequence '${sequenceName}`);
                await this.execute(cnx, `DROP SEQUENCE ${sequenceName}`);
            });
            return true;
        } catch (err) {
            // The sequence does not exist
            SqlContext.logger.warn(`Could not drop sequence for ${sequenceName} : ${err.stack}`);
            return false;
        }
    }

    /**
     * Drop the sequence used by an auto-increment column
     * @param column The auto-increment
     * @param options
     */
    async dropAutoIncrementSequence(column: string): Promise<void> {
        await this.dropAutoIncrementSequences([column]);
    }

    /**
     * Fix the sequences of the bound table
     * @param columns list of auto-increment columns
     * @param options
     */
    async dropAutoIncrementSequences(columns: string[]): Promise<void> {
        await this.withConnection(cnx =>
            asyncArray(columns).forEach(async column => {
                const sequenceName = await this.getSequenceName(cnx, column);
                if (sequenceName) {
                    await this.dropSequence(sequenceName);
                }
            }),
        );
    }
}
