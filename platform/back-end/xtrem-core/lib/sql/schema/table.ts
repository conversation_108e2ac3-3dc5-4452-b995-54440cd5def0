/** @ignore */ /** */
import { AnyRecord, asyncArray } from '@sage/xtrem-async-helper';
import {
    ColumnDefinition,
    ForeignKeyDefinition,
    ForeignKeyOptions,
    IndexColumn,
    IndexDefinition,
    NotifyEventOptions,
    NotifyOptions,
    SqlCreateTableOptions,
    TableDefinition,
    TableJsonComment,
    TriggerDefinition,
} from '@sage/xtrem-postgres';
import { Dict } from '@sage/xtrem-shared';
import * as lodash from 'lodash';
import { EnumSqlContext, SequenceSqlContext } from '..';
import { Application } from '../../application';
import { ReferenceArrayPropertyDecorator } from '../../decorators/property-decorators/reference-property-decorators';
import { Property, ReferenceArrayProperty, ReferenceProperty } from '../../properties';
import { Context, ContextOptions, NodeFactory, NodeIndex } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { loggers } from '../../runtime/loggers';
import { SystemProperties, databaseComputedColumnNames, orderedSystemColumns } from '../../runtime/system-properties';
import { AnyNode, FlatOrderBy } from '../../ts-api';
import { DecimalDataType, EnumDataType, EnumType, StringDataType } from '../../types';
import { SqlDelete } from '../mapper/sql-delete';
import { SqlInsert, SqlInsertOptions } from '../mapper/sql-insert';
import { RecordUpdateOptions, SqlUpdate } from '../mapper/sql-update';
import { ModifyTableSqlContext } from '../sql-context/modify-table-sql-context';
import { ReadTableSqlContext } from '../sql-context/read-table-sql-context';
import { SchemaSqlContext } from '../sql-context/schema-sql-context';
import { SqlContext } from '../sql-context/sql-context';
import { ColumnInconsistency, DropTableOptions, SqlAddColumnsOptions } from '../sql-context/types';
import {
    CascadeDeleteFromReferenceArraysTriggerBuilder,
    DeleteAttachmentNodeTriggerBuilder,
    RestrictDeleteForReferenceArraysTriggerBuilder,
    TriggerBuilder,
    deleteBaseNodeTriggerBuilder,
    insertSharedTableTriggerBuilder,
    insertTableTriggerBuilder,
    updateSharedTableTriggerBuilder,
    updateTableTriggerBuilder,
} from '../statements/triggers';
import { setSyncTickTriggerBuilder } from '../statements/triggers/set-sync-tick-trigger-builder';
import { Column } from './column';
import { ForeignKey } from './foreign-key';
import { PrimaryKey } from './primary-key';

const sqlLogger = loggers.sql;

/** @internal */
export interface TableDecorators {
    factory: NodeFactory;
    columns: Column[];
    foreignKeys: ForeignKey[];
    cachedTable?: Table;
}

export enum TestStatus {
    loaded,
    modified,
}

/**
 * The result of a call to fixForeignKeys
 */
export type FixForeignKeysResult = {
    /** The number of created FKs */
    createdFks: number;
    /** The number of renamed FKs */
    renamedFks: number;
    /** The number of re-created FKs */
    recreatedFks: number;
};

/** @disabled_internal */
export class Table {
    factory: NodeFactory;

    columns: Column[];

    foreignKeys: ForeignKey[] = [];

    columnsByPropertyName: Dict<Column>;

    columnsByColumnName: Dict<Column>;

    verified: boolean;

    isSharedByAllTenants: boolean;

    testStatus: TestStatus;

    testLayers: string[];

    testNowMock: string;

    testDir: string;

    constructor(decorators: TableDecorators) {
        this.factory = decorators.factory;

        this.columns = [...decorators.columns];
        if (!this.factory.isSharedByAllTenants) this.columns.push(SystemProperties.tenantIdColumn(this.factory));

        this.foreignKeys = decorators.foreignKeys;
        this.columnsByPropertyName = this.columns.reduce((r, col) => {
            r[col.propertyName] = col;
            return r;
        }, {} as Dict<Column>);
        this.columnsByColumnName = this.columns.reduce((r, col) => {
            r[col.columnName] = col;
            return r;
        }, {} as Dict<Column>);

        this.isSharedByAllTenants = !!decorators.factory.isSharedByAllTenants;
        this.testStatus = TestStatus.modified;
    }

    /** The name of the table, provided by its factory */
    get name(): string {
        return this.factory.requiredTableName;
    }

    /** The name of the SQL table */
    get sqlTableName(): string {
        return this.name;
    }

    getFullTableName(context: Context, alias?: string): string {
        return SqlContext.getFullTableName(context.schemaName, this.sqlTableName, alias || '');
    }

    /**
     * Returns the table of the baseClass (if any)
     */
    get baseTable(): Table | undefined {
        return this.factory.baseFactory?.table;
    }

    /**
     * Returns the table of the rootClass
     * The rootClass is the lowest level subNode
     * cat->mammal->animal
     * mammal will be the base table of cat
     * animal will be the root table of cat
     */
    get rootTable(): Table {
        return this.factory.rootFactory.table;
    }

    #primaryKey: PrimaryKey;

    get primaryKey(): PrimaryKey {
        if (this.#primaryKey) return this.#primaryKey;
        // If the sqlTableName is 'sys_custom_record' we have to add factory_name to the primary key
        if (this.sqlTableName === 'sys_custom_record') {
            this.#primaryKey = { columns: ['_tenant_id', 'factory_name', '_id'] };
        } else if (this.isSharedByAllTenants) {
            // shared tables do not have a _tenant_id column
            this.#primaryKey = { columns: ['_id'] };
        } else this.#primaryKey = { columns: ['_tenant_id', '_id'] };

        return this.#primaryKey;
    }

    readonly #uniqueIndexes: PrimaryKey[];

    get uniqueIndexes(): PrimaryKey[] {
        if (this.#uniqueIndexes) return this.#uniqueIndexes;

        return this.factory
            .indexes!.filter(index => index.isUnique && Object.keys(index.orderBy)[0] !== '_id')
            .map(index => ({
                columns: [
                    ...(this.factory.isSharedByAllTenants ? [] : ['_tenant_id']),
                    ...Object.keys(index.orderBy).map(
                        propertyName => this.getSqlColumnByPropertyName(propertyName).columnName,
                    ),
                ],
            }));
    }

    get databaseComputedColumnNames(): readonly string[] {
        const names = this.baseTable ? [] : databaseComputedColumnNames(this.isSharedByAllTenants);
        return this.factory.nodeDecorator.isSynchronizable ? [...names, '_sync_tick'] : names;
    }

    getColumns(
        options: {
            inherit?: boolean;
            includeSystemColumns?: boolean;
            skipLazyLoadableColumns?: boolean;
        } = {},
    ): Column[] {
        return [
            ...this.columns,
            ...(options.inherit && this.baseTable ? this.baseTable.getColumns(options) : []),
            ...(options.inherit && this.factory.isAbstract && !this.baseTable
                ? [SystemProperties.constructorColumn(this.factory)]
                : []),
        ].filter((column, i, cols) => {
            if (!options.includeSystemColumns && column.isSystem) return false;
            // filter out duplicate system columns
            if (options.skipLazyLoadableColumns && column.shouldLazyLoad) {
                // Skip this column, it will be lazy loaded if required
                return false;
            }
            if (column.columnName.startsWith('_') && cols.findIndex(c => c.columnName === column.columnName) !== i) {
                loggers.nodeFactory.debug(
                    () =>
                        `duplicate: ${column.property.factory.tableName}.${column.columnName} isAbstract=${this.factory.isAbstract} factory=${this.factory.name}`,
                );
                return false;
            }
            if (!this.factory.isAbstract && column.columnName === '_constructor') {
                return false;
            }
            return true;
        });
    }

    /**
     * Returns the JSON comment that must be put on the table (in the database)
     */
    getJsonComment(): TableJsonComment {
        const comment = {
            isSharedByAllTenants: this.factory.isSharedByAllTenants,
            baseTable: this.baseTable?.name,
        } as TableJsonComment;

        /**
         * If the current table is a subNode, then only the rootTable should be populated on the table comment
         * The rootTable is the lowest abstract table of the subNode tree
         *  cat->mammal->animal
         * mammal will be the base table of cat
         * animal will be the root table of cat
         * for subNodes this is useful as this is the table that has the SERIAL _id column
         */
        if (this.name !== this.rootTable?.name) {
            comment.rootTable = this.rootTable?.name;
        }

        if (this.factory.hasAttachments) {
            comment.hasAttachments = true;
        }

        if (this.factory.hasTags) {
            comment.hasTags = true;
        }

        /**
         * Only set isSetupNode when the the node is a setup node
         */
        if (this.factory.isSetupNode) {
            comment.isSetupNode = this.factory.isSetupNode;
        }

        /**
         * Only set naturalKey if the node has a natural key
         */
        if (this.factory.naturalKey) {
            comment.naturalKey = this.factory.naturalKey;
        }

        return comment;
    }

    /**
     * Returns the tableDefinition that matches the metadata of this table.
     * WARNING: it may not return the same result as the SQL metadata if the table in the database
     * is not in sync with the definition of the table.
     * @param context
     */
    getTableDefinition(context: Context): TableDefinition {
        const name = this.name;
        const tenantIdColumn = SystemProperties.tenantIdColumn(this.factory);

        const columns = lodash.sortBy(
            this.columns.map(column => this.getColumnDefinition(column)),
            column => {
                const index = orderedSystemColumns.indexOf(column.name);

                // The sort order is controlled by the index of orderedSystemColumns
                // If the column does not exist in the array it will placed in the set of non-system columns inbetween
                // the defined system columns at the index of '' in the arrary.
                return index >= 0 ? index : orderedSystemColumns.indexOf('');
            },
        );

        this.foreignKeys = this.foreignKeys || [];
        const foreignKeys = this.foreignKeys?.map(foreignKey => {
            return Table.getForeignKeyDefinition(foreignKey);
        });

        const indexes = this.factory
            .getSelfIndexes()
            .map(index => this.getIndexDefinition(context.schemaName, index, tenantIdColumn));

        const tableDef: TableDefinition = {
            schemaName: context.schemaName,
            tableName: name,
            columns,
            indexes,
            primaryKey: this.primaryKey,
            foreignKeys,
            notify: this.getNotifyOptions(),
            isSharedByAllTenants: this.isSharedByAllTenants,
            baseDefinition: this.baseTable?.getTableDefinition(context),
            comment: this.getJsonComment(),
        };

        const builders: TriggerBuilder[] = [];
        if (!tableDef.comment?.baseTable) {
            if (this.factory.isSharedByAllTenants) {
                builders.push(insertSharedTableTriggerBuilder);
                builders.push(updateSharedTableTriggerBuilder);
            } else {
                builders.push(insertTableTriggerBuilder);
                builders.push(updateTableTriggerBuilder);
            }
        }

        if (this.factory.hasAttachments && !this.factory.isAbstract) {
            builders.push(
                TriggerBuilder.registerBuilder(
                    new DeleteAttachmentNodeTriggerBuilder(
                        this.factory.name,
                        this.factory.application.getFactoryByConstructor(
                            CoreHooks.getAttachmentManager().getAttachmentNode(),
                        ).tableName || '',
                    ),
                ),
            );
        }

        // If this factory is referenced by some referenceArray properties, a SQL trigger must be
        // defined to maintain the referenceArray column in sync.
        const refArrayProperties = this.factory.referringProperties.filter(
            rp => rp.property.isStored && rp.property.isReferenceArrayProperty(),
        );
        if (refArrayProperties.length > 0) {
            const factoriesWithOnDeleteRemove: Dict<string> = {};
            const factoriesWithOnDeleteRestrict: Dict<string> = {};
            refArrayProperties.forEach(rp => {
                const property = rp.property as ReferenceArrayProperty;
                const decorator = property.decorator as ReferenceArrayPropertyDecorator;
                if (decorator.onDelete === 'remove') {
                    factoriesWithOnDeleteRemove[rp.targetFactory.requiredTableName] = property.requiredColumnName;
                } else {
                    factoriesWithOnDeleteRestrict[rp.targetFactory.requiredTableName] = property.requiredColumnName;
                }
            });
            if (Object.keys(factoriesWithOnDeleteRemove).length > 0) {
                builders.push(
                    TriggerBuilder.registerBuilder(
                        new CascadeDeleteFromReferenceArraysTriggerBuilder(
                            this.factory.name,
                            factoriesWithOnDeleteRemove,
                        ),
                    ),
                );
            }
            if (Object.keys(factoriesWithOnDeleteRestrict).length > 0) {
                builders.push(
                    TriggerBuilder.registerBuilder(
                        new RestrictDeleteForReferenceArraysTriggerBuilder(
                            this.factory.name,
                            factoriesWithOnDeleteRestrict,
                        ),
                    ),
                );
            }
        }

        if (this.factory.nodeDecorator.isSynchronizable) {
            builders.push(setSyncTickTriggerBuilder);
        }

        const notifyEvents = tableDef.notify?.event ?? {};
        if (notifyEvents.created && notifyEvents.updated && notifyEvents.deleted) {
            if (CoreHooks.auditManager.auditTriggerBuilder != null)
                builders.push(CoreHooks.auditManager.auditTriggerBuilder);
        }

        if (tableDef.baseDefinition) {
            builders.push(deleteBaseNodeTriggerBuilder);
        }
        tableDef.triggers = builders.map(b => b.getDefinition(tableDef));
        return tableDef;
    }

    private getNotifyOptions(): NotifyOptions {
        const event = this.factory.notifies.reduce((r, n) => {
            r[n] = true;
            return r;
        }, {} as NotifyEventOptions);
        return {
            event,
            sqlPayloadExpression: this.getSqlExpressionTemplateForPayloadValue(event),
        };
    }

    private getSqlExpressionTemplateForPayloadValue(eventOptions: NotifyEventOptions): string {
        const rootOnlyProperties = this.factory.baseFactory ? [] : ['_updateTick'];
        const properties = ['_id', ...rootOnlyProperties].map(n => this.factory.propertiesByName[n]);
        // We need a string concatenation expression to be used by the trigger in order to insert a record
        // in the sys_notification table with the ad-hoc json payload value.
        // This expression is a templated string, for example for the following list of notification properties set in
        // a node decorator ['id', 'booleanVal', 'stringVal', ], it generates:
        //   "'{' || '"id":' || to_json({{ROW}}.id) || ',' || '"booleanVal":' || to_json({{ROW}}.boolean_val)
        //   || ',' || '"stringVal":' || to_json({{ROW}}.string_val) || '}'"
        //
        // The {{ROW}} placeholder will then be replaced by:
        // - 'NEW' for the created and updated events
        // - 'OLD' for the deleted event
        return Object.keys(eventOptions).length > 0
            ? `'{' || ${properties
                  .map(p => `'"${p.name}":' || to_json({{ROW}}.${p.columnName})`)
                  .join(" || ',' || ")} || '}'`
            : '';
    }

    async dropTable(context: Context, options?: DropTableOptions): Promise<void> {
        const name = this.name;
        const schemaSqlContext = new SchemaSqlContext(context.application);
        const exists = !options?.ifExists || (await schemaSqlContext.tableExists(name));
        if (exists) {
            await schemaSqlContext.dropTable(name, { isCascade: !!options?.cascade, errorsAsWarnings: false });

            if (this.factory.hasSqlInsertFunction) {
                await Table.dropSqlInsertFunction(context, this.factory.table.name);
            }
        }
    }

    async createTable(context: Context, options?: SqlCreateTableOptions): Promise<void> {
        const opts = options || {};
        const baseTable = this.baseTable;

        // we should only try and create a base table if it does not exists.
        // if options.skipDrop is false and the current table is not abstract, we should delete the records from
        // the current table, thereby deleting the base table data specific to this _constructor
        // but preserving data of the other tables were this table is a base table as well.
        if (baseTable) {
            if (!(await baseTable.tableExists(context))) {
                await baseTable.createTable(context, options);
            } else if (!opts.skipDrop && !this.factory.isAbstract && (await this.tableExists(context))) {
                await this.delete(context, {});
            }
        }
        opts.hasNotifyTriggers = this.factory.notifies.length > 0;
        const tableDef = this.getTableDefinition(context);
        const sqlContext = new ModifyTableSqlContext(context.application, tableDef);
        await loggers.sql.doAsync(async () => {
            const enumTypesToCreate: EnumType[] = (tableDef.columns || [])
                .filter(column => column.type === 'enum' || column.type === 'enumArray')
                .map(col => col.enumDataType!);
            await this.createEnumTypes(context, enumTypesToCreate);
            await sqlContext.createTableFromTableDefinition(opts);
            if (!opts.skipGrant) await sqlContext.giveUserRightsToTable(context.sqlPool.user);
        });

        await this.createSqlInsertFunction(context);
    }

    private getModifyTableSqlContext(context: Context): ModifyTableSqlContext {
        return new ModifyTableSqlContext(context.application, this.getTableDefinition(context));
    }

    /**
     * Create a bunch of triggers from their definition
     */
    async createTriggers(context: Context, triggers: TriggerDefinition[]): Promise<void> {
        await loggers.sql.do(() => this.getModifyTableSqlContext(context).createTriggers(triggers));
    }

    /**
     * Drop a bunch of triggers from their definition
     */
    async dropTriggers(context: Context, triggers: TriggerDefinition[]): Promise<void> {
        await loggers.sql.do(() => this.getModifyTableSqlContext(context).dropTriggers(triggers));
    }

    /** Creates the SQL insert function */
    async createSqlInsertFunction(context: Context): Promise<void> {
        if (!this.factory.hasSqlInsertFunction) return;
        await loggers.sql.do(async () => {
            const insertFunctionSql = new SqlInsert(this).getCreateSqlInsertFunctionSql(context);
            const sqlContext = new SchemaSqlContext(context.application);
            await sqlContext.withConnection(cnx => {
                return sqlContext.execute(cnx, insertFunctionSql);
            });
        });
    }

    /** Drops the SQL insert function if it exists */
    static async dropSqlInsertFunction(context: Context, tableName: string): Promise<void> {
        await loggers.sql.do(async () => {
            const dropFunctionSql = SqlInsert.getDropSqlInsertFunctionSql(context, tableName);
            const sqlContext = new SchemaSqlContext(context.application);
            await sqlContext.withConnection(cnx => {
                return sqlContext.execute(cnx, dropFunctionSql);
            });
        });
    }

    async ensureTableExists(context: Context, options?: SqlCreateTableOptions): Promise<void> {
        if (!(await this.tableExists(context))) {
            loggers.runtime.info(`Creating table '${this.name}'`);
            await this.createTable(context, options);
        }
    }

    async ensureAllTableColumnsExists(context: Context): Promise<void> {
        const tableSchema = await new ReadTableSqlContext(context.application).readSchema(this.name, {
            skipForeignKeys: true,
            skipIndexes: true,
            skipSecurity: true,
            skipSequences: true,
        });

        const missingColumns = this.columns.filter(col => !tableSchema.columns?.find(c => c.name === col.columnName));

        if (missingColumns.length > 0) {
            await this.addColumns(
                context,
                missingColumns.map(c => this.getColumnDefinition(c)),
            );
        }
    }

    getColumnDefinition(column: Column): ColumnDefinition {
        const dataType = column.property.dataType;

        const columnType = column.getColumnType();

        const isSelfReference =
            column.property instanceof ReferenceProperty &&
            column.property.isSelfReference &&
            this.factory.naturalKey == null;

        const definition: ColumnDefinition = {
            name: column.columnName,
            maxLength: dataType instanceof StringDataType ? dataType.maxLength : undefined,
            type: columnType,
            isNullable: column.isNullable,
            isAutoIncrement: column.isAutoIncrement,
            isSystem: column.isSystem,
            isEncryptedString: column.isEncryptedString,
            isSelfReference,
            comment: column.getJsonComment(),
            default: column.default,
        };

        if (dataType instanceof DecimalDataType) {
            definition.precision = dataType.precision || 10;
            definition.scale = dataType.scale ? dataType.scale : 0;
        }

        if (dataType instanceof EnumDataType) {
            const columnDataType = dataType as EnumDataType;
            definition.enumDataType = columnDataType.getEnumType();
        }

        return definition;
    }

    /**
     * Maps a ForeignKey to an ForeignKeyDefinition
     */
    static getForeignKeyDefinition(foreignKey: ForeignKey): ForeignKeyDefinition {
        const fkDef: ForeignKeyDefinition = {
            name: foreignKey.name,
            targetTable: lodash.snakeCase(foreignKey.targetTable),
            columnNames: foreignKey.columnNames,
            targetColumnNames: foreignKey.targetColumnNames,
            onDeleteBehaviour: foreignKey.onDeleteBehaviour,
            comment: foreignKey.getJsonComment(),
            isDeferrable: foreignKey.isDeferrable,
        };
        return fkDef;
    }

    /**
     * Maps a NodeIndex to an IndexDefinition
     */
    getIndexDefinition(schemaName: string, index: NodeIndex, tenantIdColumn: Column): IndexDefinition {
        const columns = Object.keys(index.orderBy).map(key => {
            const column = this.columnsByPropertyName[key];
            const definition = {
                name: key === tenantIdColumn.propertyName ? tenantIdColumn.columnName : column.columnName,
                ascending: (index.orderBy as FlatOrderBy<AnyNode>)[key] === 1,
            } as IndexColumn;
            if (index.isUnique && column.property.isNullable) {
                Table.setCoalesceExpressionForNullableInUniqueIndex(schemaName, definition, column.property);
            }
            return definition;
        });
        // If the table is not shared by all tenants then we need to ensure that the tenant id is part of the index
        // to ensure true unique indexes and more efficient indexing of table data on database.
        if (
            !this.factory.isSharedByAllTenants &&
            !columns.map(column => column.name).includes(tenantIdColumn.columnName)
        ) {
            columns.unshift({ name: tenantIdColumn.columnName, ascending: true });
        }
        return {
            name: index.name!,
            isUnique: !!index.isUnique,
            columns,
        } as IndexDefinition;
    }

    private static setCoalesceExpressionForNullableInUniqueIndex(
        schemaName: string,
        definition: IndexColumn,
        property: Property,
    ): void {
        const columnName = definition.name;
        if (property.isEnumProperty()) {
            const enumName = property.dataType.getEnumType().name;
            definition.expression = `${schemaName}.${enumName}_coalesce(${SqlContext.escape(columnName)})`;
            return;
        }
        let coalesceValue;
        if (property.isReferenceProperty()) {
            coalesceValue = '(0)::bigint';
        } else {
            switch (property.type) {
                case 'integer':
                    // set a negative value outside the supported range in javascript
                    // to keep the same order as it was with null first
                    coalesceValue = '(- ((2)::bigint ^ (62)::bigint))::bigint';
                    break;
                case 'date':
                    coalesceValue = "'0001-01-01'::date";
                    break;
                default:
                    throw new Error(`unsupported property type ${property.type} for coalesce expression`);
            }
        }
        if (coalesceValue) {
            definition.expression = `COALESCE(${SqlContext.escape(columnName)}, ${coalesceValue})`;
        }
    }

    /** @internal */
    setAsDirty(): void {
        this.testStatus = TestStatus.modified;
        if (this.baseTable) this.baseTable.setAsDirty();
    }

    /** @internal */
    tableIsDirty(): boolean {
        return this.testStatus === TestStatus.modified;
    }

    /** @disabled_internal */
    async createIndex(context: Context, index: IndexDefinition): Promise<void> {
        await this.getModifyTableSqlContext(context).createIndex(index);
    }

    /** @disabled_internal */
    async dropIndex(context: Context, indexName: string): Promise<void> {
        await this.getModifyTableSqlContext(context).dropIndex(indexName);
    }

    /**
     * @disabled_internal
     *
     * Adds some columns from their column definition
     */
    async addColumns(context: Context, colDefs: ColumnDefinition[], options?: SqlAddColumnsOptions): Promise<void> {
        if (colDefs.length === 0) return;
        await sqlLogger.do(() => this.getModifyTableSqlContext(context).addColumns(colDefs, options));
    }

    /**
     * @disabled_internal
     *
     * Alters some columns from their new column definition
     */
    async alterColumns(
        context: Context,
        columns: { definition: ColumnDefinition; inconsistencies: ColumnInconsistency[] }[],
    ): Promise<void> {
        if (columns.length === 0) return;
        await sqlLogger.do(() => this.getModifyTableSqlContext(context).alterColumns(columns));
    }

    /**
     * @disabled_internal
     */
    async dropColumns(context: Context, columnNames: string[]): Promise<void> {
        await sqlLogger.do(() => this.getModifyTableSqlContext(context).dropColumns(columnNames));
    }

    /**
     * Returns the existing foreignKeyDefinition that matches the fk provided as a parameter.
     * This function will return either the existing fkDef with the same name or the existing fkDef with the same columns.
     *
     * @param currentTableDef The current table schema (the one that matches the current state of the db)
     */
    async getMatchingForeignKeyDefinition(
        context: Context,
        foreignKey: ForeignKey,
        currentTableDef?: TableDefinition,
    ): Promise<ForeignKeyDefinition | undefined> {
        const tableDefToUse =
            currentTableDef ||
            (await new ReadTableSqlContext(context.application).readSchema(this.name, {
                skipColumns: true,
                skipIndexes: true,
                skipSecurity: true,
                skipSequences: true,
            }));
        const fkMatchesColumns = (fk: ForeignKeyDefinition): boolean => {
            return lodash.isEqual(fk.columnNames, foreignKey.columnNames);
        };

        const fromName = tableDefToUse.foreignKeys?.find(fk => fk.name === foreignKey.name);
        if (fromName && fkMatchesColumns(fromName)) return fromName;

        return tableDefToUse.foreignKeys!.find(fk => fkMatchesColumns(fk));
    }

    /** @disabled_internal */
    async dropForeignKey(context: Context, foreignKeyName: string): Promise<void> {
        if (this.baseTable) {
            // We can't really know if the FK is defined on the table itself or on it's base table
            // So, we have to (try to) drop the FK from both tables.
            await this.baseTable.dropForeignKey(context, foreignKeyName);
        }

        await sqlLogger.do(async () => {
            await this.getModifyTableSqlContext(context).dropForeignKey(foreignKeyName);
            const foreignKeyIndex = this.foreignKeys.map(foreignKey => foreignKey.name).indexOf(foreignKeyName);
            if (foreignKeyIndex >= 0) this.foreignKeys.splice(foreignKeyIndex, 1);
        });
    }

    /**
     * Renames (in DB) a foreign key
     */
    async renameForeignKey(context: Context, oldFkName: string, newFkName: string): Promise<void> {
        await sqlLogger.do(() => this.getModifyTableSqlContext(context).renameForeignKey(oldFkName, newFkName));
    }

    /**
     * Make sure all the foreign keys defined in the model exist and that they have the right name
     *
     * @param currentTableDef The current table schema (the one that matches the current state of the db)
     */
    async fixForeignKeys(
        context: Context,
        currentTableDef: TableDefinition,
        options: ForeignKeyOptions,
    ): Promise<FixForeignKeysResult> {
        const result: FixForeignKeysResult = {
            createdFks: 0,
            recreatedFks: 0,
            renamedFks: 0,
        };
        if (this.baseTable && !options?.skipBaseTable) {
            const subTableDef = await new ReadTableSqlContext(context.application).readSchema(this.baseTable.name, {
                skipColumns: true,
                skipIndexes: true,
                skipSecurity: true,
                skipSequences: true,
            });
            const subResult = await this.baseTable.fixForeignKeys(context, subTableDef, options);
            result.createdFks += subResult.createdFks;
            result.recreatedFks += subResult.recreatedFks;
            result.renamedFks += subResult.renamedFks;
        }

        if (!currentTableDef.foreignKeys) {
            sqlLogger.warn(`fixForeignKeys was invoked on table ${this.name} with a current schema with no FKs`);
            return result;
        }
        await asyncArray(this.factory.getForeignKeys()).forEach(async expectedFk => {
            let shouldCreate = !!options.skipExists;
            if (!shouldCreate) {
                const existingFkDef = await this.getMatchingForeignKeyDefinition(context, expectedFk, currentTableDef);
                let mustBeRecreated = false;
                // Warning : getMatchingForeignKeyDefinition will return FKs with either the same name OR the
                // same columns
                if (!existingFkDef) {
                    shouldCreate = !options.dontCreateMissingFks;
                    if (shouldCreate) {
                        sqlLogger.info(`Table ${this.name} - missing FK ${expectedFk.name} will be created`);
                        result.createdFks += 1;
                    }
                } else if (existingFkDef.targetTable !== expectedFk.targetTable) {
                    // The FK needs to be recreated
                    sqlLogger.info(
                        `Table ${this.name} - FK ${existingFkDef.name} will be recreated (wrong target table: ${existingFkDef.targetTable} -> ${expectedFk.targetTable})`,
                    );
                    result.recreatedFks += 1;
                    mustBeRecreated = true;
                } else if (existingFkDef.onDeleteBehaviour !== expectedFk.onDeleteBehaviour) {
                    // The FK needs to be recreated
                    sqlLogger.info(
                        `Table ${this.name} - FK ${existingFkDef.name} will be recreated (wrong delete behaviour: ${existingFkDef.onDeleteBehaviour} -> ${expectedFk.onDeleteBehaviour})`,
                    );
                    result.recreatedFks += 1;
                    mustBeRecreated = true;
                } else if (existingFkDef.isDeferrable !== expectedFk.isDeferrable) {
                    // The FK needs to be recreated
                    sqlLogger.info(
                        `Table ${this.name} - FK ${existingFkDef.name} will be recreated (isDeferrable mismatch: ${existingFkDef.isDeferrable} -> ${expectedFk.isDeferrable})`,
                    );
                    result.recreatedFks += 1;
                    mustBeRecreated = true;
                } else if (existingFkDef.name !== expectedFk.name) {
                    // The FK is Ok but it's name is wrong. Simply rename it
                    await this.renameForeignKey(context, existingFkDef.name, expectedFk.name);
                    result.renamedFks += 1;
                    sqlLogger.info(`Table ${this.name} - FK ${existingFkDef.name} was renamed to ${expectedFk.name})`);
                }
                if (mustBeRecreated) {
                    if (!existingFkDef)
                        throw new Error(`Logic error: the FK to re-create does not exist (${expectedFk.name})`);
                    await this.dropForeignKey(context, existingFkDef.name);
                    shouldCreate = true;
                }
            }
            if (!shouldCreate) return;
            await this.addForeignKey(context, expectedFk);
        });
        return result;
    }

    /**
     * Creates (in DB) a foreign key
     *
     * @param context
     * @param foreignKey
     * @param options
     * @param currentTableSchema The (optional) current table schema (for optimization purpose: reading the schema is very expensive)
     */
    async addForeignKey(
        context: Context,
        foreignKey: ForeignKey,
        currentTableSchema?: TableDefinition,
        options?: ForeignKeyOptions,
    ): Promise<void> {
        if (
            !options?.skipExists &&
            (await this.getMatchingForeignKeyDefinition(context, foreignKey, currentTableSchema))
        )
            return;
        const fkDef = Table.getForeignKeyDefinition(foreignKey);
        await sqlLogger.do(() => this.getModifyTableSqlContext(context).addForeignKey(fkDef, options));

        this.foreignKeys = this.foreignKeys || [];
        if (!this.foreignKeys.some(f => f.name === foreignKey.name && f.targetTable === foreignKey.targetTable)) {
            this.foreignKeys.push(foreignKey);
        }
        if (currentTableSchema) {
            currentTableSchema.foreignKeys = currentTableSchema.foreignKeys || [];
            currentTableSchema.foreignKeys.push(fkDef);
        }
    }

    getForeignKeyByName(name: string): ForeignKey | undefined {
        return this.foreignKeys.find(fk => fk.name === name);
    }

    async dropAllForeignKeys(context: Context): Promise<void> {
        const tableDef = await new ReadTableSqlContext(context.application).readSchema(this.name, {
            skipColumns: true,
            skipIndexes: true,
            skipSecurity: true,
            skipSequences: true,
        });

        await asyncArray(tableDef.foreignKeys || []).forEach(fk => this.dropForeignKey(context, fk.name));
    }

    tableExists(context: Context): Promise<boolean> {
        return new SchemaSqlContext(context.application).tableExists(this.name);
    }

    // eslint-disable-next-line class-methods-use-this
    async createEnumTypes(context: Context, enumTypes: EnumType[]): Promise<void> {
        if (enumTypes.length > 0) await new EnumSqlContext(context.application).createEnumTypes(enumTypes);
    }

    getColumnValues(data: AnyRecord): AnyRecord {
        return lodash.pick(data, Object.keys(this.columnsByPropertyName));
    }

    private getSqlColumnByPropertyName(propertyName: string): Column {
        return this.columnsByPropertyName[propertyName];
    }

    getSqlColumnNameByPropertyName(propertyName: string): string {
        return this.getSqlColumnByPropertyName(propertyName).columnName;
    }

    private getSqlColumnByColumnName(columnName: string): Column {
        return this.columnsByColumnName[columnName];
    }

    getSqlColumnNameByColumnName(columnName: string): string {
        return this.getSqlColumnByColumnName(columnName).columnName;
    }

    insert(
        context: Context,
        initialData: AnyRecord,
        options: SqlInsertOptions = {},
    ): Promise<AnyRecord & { _id?: number }> {
        return new SqlInsert(this).insert(context, initialData, options);
    }

    insertFromSqlFile(
        context: Context,
        initialData: AnyRecord,
        options: SqlInsertOptions = {},
    ): Promise<AnyRecord & { _id?: number }> {
        return new SqlInsert(this).insertFromSqlFile(context, initialData, options);
    }

    static dropUpsertSqlFunction(context: Context): Promise<void> {
        return SqlInsert.dropUpsertSqlFunction(context);
    }

    async update(context: Context, data: AnyRecord, options: RecordUpdateOptions = {}): Promise<AnyRecord[]> {
        const updatedRecord = await new SqlUpdate(this).update(context, data, options);
        updatedRecord.forEach(record => {
            context.prefetcher.afterUpdate(this.factory, Number(data._id) > 0 ? data : { ...data, _id: record._id });
        });
        return updatedRecord;
    }

    delete(context: Context, filter: AnyRecord): Promise<number> {
        return new SqlDelete(this).delete(context, filter);
    }

    findColumnByPropertyName(name: string): Column {
        const col = this.columnsByPropertyName[name];
        if (col) return col;
        if (this.baseTable) return this.baseTable.findColumnByPropertyName(name);
        throw this.tableError(`invalid property name: ${name}`);
    }

    findColumnByColumnName(name: string): Column {
        const col = this.columns.find(c => c.columnName === name);
        if (!col) throw this.tableError(`invalid column name: ${name}`);
        return col;
    }

    tableError(message: string): Error {
        return new Error(`table ${this.name}: ${message}`);
    }

    markAsLoadedForTests(application: Application, contextOptions: ContextOptions): void {
        this.testStatus = TestStatus.loaded;
        this.testLayers = [...contextOptions.testLayers!];
        sqlLogger.debug(() => `markAsLoadedForTests(${this.name}) this.testLayers:${JSON.stringify(this.testLayers)}`);
        this.testNowMock = contextOptions.testNowMock!;
        this.testDir = application.dir;
    }

    markAsModifiedForTests(): void {
        this.testStatus = TestStatus.modified;
    }

    /**
     * Fix the sequences of the table
     */
    async fixAutoIncrementSequences(tenantId: string | null): Promise<void> {
        sqlLogger.verbose(() => `Table ${this.name}: fixing auto-increment sequences`);
        const tableDef = await this.factory.application.createContextForDdl(
            context => this.getTableDefinition(context),
            {
                description: () => `Get table definition ${this.name}`,
            },
        );
        await new SequenceSqlContext(this.factory.application, tableDef.tableName).fixAutoIncrementSequences(
            tenantId,
            tableDef.columns?.filter(column => column.isAutoIncrement).map(column => column.name) || [],
        );
    }
}
