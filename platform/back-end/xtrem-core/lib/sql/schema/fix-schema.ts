import { Dict } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as lodash from 'lodash';
import * as fsp from 'path';
import * as readline from 'readline';
import { orderedSystemColumns } from '../../runtime/system-properties';

export class FixSchema {
    private static writeline(output: fs.WriteStream, line: string): void {
        output.write(`${line}\n`, 'binary');
    }

    /**
     * Sort the buffered column definition lines and print them to the output dump.
     * @param output
     * @param bufferedLines
     */
    private static flushLines(output: fs.WriteStream, bufferedLines: Dict<string>): void {
        const orderedColumns = lodash.sortBy(Object.keys(bufferedLines), columnName => {
            const index = orderedSystemColumns.indexOf(columnName);

            // The sort order is controlled by the index of orderedSystemColumns
            // If the column does not exist in the array it will placed in the set of non-system columns inbetween
            // the defined system columns at the index of '' in the arrary.
            return index >= 0 ? index : orderedSystemColumns.indexOf('');
        });

        orderedColumns.forEach((columnName, i) => {
            const line = bufferedLines[columnName];
            const hasComma = line.endsWith(',');
            const isLast = i === orderedColumns.length - 1;
            this.writeline(
                output,
                isLast ? (hasComma ? line.substring(0, line.length - 1) : line) : hasComma ? line : `${line},`,
            );
        });
    }

    /**
     * Process the read line of the dump
     * @param output
     * @param line
     * @param buffering
     * @param bufferedLines
     * @returns
     */
    private static processLine(
        output: fs.WriteStream,
        line: string,
        bufferedLines: Dict<string>,
        buffering = false,
    ): { buffering: boolean; bufferedLines: Dict<string> } {
        if (/CREATE TABLE /.test(line)) {
            // We have reached a create table statement, we need to start buffering the lines follow
            this.writeline(output, line);
            return { buffering: true, bufferedLines };
        }

        if (buffering) {
            // If the line starts with 4 spaces and a word in double quotes, while we are buffering, it is a column definition
            // and we need to add it our buffer object with the key being column name, which it word in double quotes.
            const m = /^ {4}("?\w+"?)/.exec(line);
            if (m) {
                bufferedLines[m[1]] = line;
                return { buffering: true, bufferedLines };
            }

            // When we reach this point, we are after the column definitions, therefore we need to flush the buffer, printing the
            // sorted columns definitions to the output dump file.
            this.flushLines(output, bufferedLines);
            this.writeline(output, line);
            // Stop buffering
            return { buffering: false, bufferedLines: {} };
        }

        // We are not buffering therefore just write the line to the output dump file
        this.writeline(output, line);
        return { buffering: false, bufferedLines: {} };
    }

    /**
     * Correct the order of columns in the supplied dump file
     * @param directory directory of the dump file
     * @param fileName the name of the dump file to be fixed
     * @param outFilename the name of the output file to spool the corrected dump file
     */
    static async fixColumnOrder(directory: string, fileName: string, outFilename: string): Promise<void> {
        const inputPath = fsp.join(directory, fileName);
        const outputPath = fsp.join(directory, outFilename);

        const input = fs.createReadStream(inputPath, { encoding: 'binary' });
        const output = fs.createWriteStream(outputPath, { encoding: 'binary' });
        let buffering = false;
        let bufferedLines = {} as Dict<string>;
        const rl = readline.createInterface({
            input,
            crlfDelay: Infinity,
            terminal: false,
        });

        // Read the dump file one line at a time
        await new Promise<boolean>((resolve, reject) => {
            rl.on('line', (line: string) => {
                const result = this.processLine(output, line, bufferedLines, buffering);
                buffering = result.buffering;
                bufferedLines = result.bufferedLines;
            });

            rl.on('close', () => {
                resolve(true);
            });
            rl.on('error', (err: any) => reject(err instanceof Error ? err : new Error(String(err))));
        });
    }
}
