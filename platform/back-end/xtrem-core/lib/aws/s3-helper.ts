import { ListObjectsV2CommandInput } from '@aws-sdk/client-s3';
import { Datetime } from '@sage/xtrem-date-time';
import * as fs from 'fs';
import { Readable } from 'stream';
import { fileExists } from '../file-utils';
import { loggers } from '../runtime/loggers';
import { S3Bucket, WithoutBucket } from './s3-bucket';

const logger = loggers.core;

export type S3ObjectInfo = {
    bucketName: string;
    folder: string;
    key: string;
    lastModified?: Datetime;
    eTag?: string;
    size?: number;
    storageClass?: string;
};

export const toMegabytes = (bytes: number): number => bytes / 1024 / 1024;
export const toMegabytesString = (bytes: number): string => `${toMegabytes(bytes).toFixed(2)} MB`;

export abstract class S3Helper {
    /**
     * Ensures that the provided S3Bucket is compatible with S3 infos
     * @param s3Bucket
     * @param s3Infos
     */
    private static _checkS3Bucket(s3Bucket: S3Bucket | undefined, s3Infos: S3ObjectInfo): void {
        if (!s3Bucket) return;
        if (s3Bucket.bucketName !== s3Infos.bucketName)
            throw new Error(`The provided S3 bucket is incompatible with the bucket ${s3Infos.bucketName}`);
    }

    /**
     * Upload a local file to a S3 bucket and returns the S3 URI of the written object
     * @param sourceFilename the local filename of the file to copy
     * @param targetS3Info the bucket/folder/key of the object to write
     * @param s3Bucket the (optional) S3 bucket to use (to optimize chained calls)
     */
    static async upload(sourceFilename: string, targetS3Info: S3ObjectInfo, s3Bucket?: S3Bucket): Promise<string> {
        const s3Uri = S3Helper.buildS3Uri(targetS3Info);
        const bucketToUse = s3Bucket || new S3Bucket(targetS3Info.bucketName);
        S3Helper._checkS3Bucket(s3Bucket, targetS3Info);

        if (!(await fileExists(sourceFilename))) {
            throw new Error(`File ${sourceFilename} does not exist`);
        }
        const readStream = fs.createReadStream(sourceFilename);
        await bucketToUse.putObject({
            Key: `${targetS3Info.folder}/${targetS3Info.key}`,
            ContentType: 'application/string',
            Body: readStream,
        });
        return s3Uri;
    }

    /**
     * Download a file from a S3 bucket
     * @param sourceS3Info the bucket/folder/key of the object to copy
     * @param targetFilename the local file to write
     * @param s3Bucket the (optional) S3 bucket to use (to optimize chained calls)
     */
    static async download(sourceS3Info: S3ObjectInfo, targetFilename: string, s3Bucket?: S3Bucket): Promise<void> {
        const bucketToUse = s3Bucket || new S3Bucket(sourceS3Info.bucketName);
        S3Helper._checkS3Bucket(s3Bucket, sourceS3Info);

        logger.info(`S3 - Downloading s3://${sourceS3Info.bucketName}/${sourceS3Info.folder}/${sourceS3Info.key}`);
        try {
            await bucketToUse.writeObjectToFile(
                {
                    Key: `${sourceS3Info.folder}/${sourceS3Info.key}`,
                },
                targetFilename,
            );
        } catch (err) {
            throw new Error(
                `Could not download s3://${sourceS3Info.bucketName}/${sourceS3Info.folder}/${sourceS3Info.key}, reason was ${err.message}`,
            );
        }
    }

    static downloadStream(sourceS3Info: S3ObjectInfo, s3Bucket?: S3Bucket): Promise<Readable> {
        const bucketToUse = s3Bucket || new S3Bucket(sourceS3Info.bucketName);
        S3Helper._checkS3Bucket(s3Bucket, sourceS3Info);
        return bucketToUse.getObjectStream({
            Key: `${sourceS3Info.folder}/${sourceS3Info.key}`,
        });
    }

    static getS3Bucket(bucketName: string): S3Bucket {
        return new S3Bucket(bucketName);
    }

    /**
     * Parse a S3 URI with the following format : s3://bucketName/xxx/yyy/key
     * @param s3Uri
     */
    static parseS3Uri(s3Uri: string): S3ObjectInfo {
        if (!s3Uri.startsWith('s3://')) throw new Error(`Invalid S3 URI: ${s3Uri}`);
        const parts = s3Uri.substring(5).split('/');
        if (parts.length < 3) throw new Error(`Incomplete S3 URI: ${s3Uri}`);
        return {
            bucketName: parts[0],
            folder: parts.slice(1, parts.length - 1).join('/'),
            key: parts[parts.length - 1],
        };
    }

    /**
     * Builds a valid S3 URI
     * @param s3
     */
    static buildS3Uri(s3: S3ObjectInfo): string {
        return `s3://${s3.bucketName}/${s3.folder}/${s3.key}`;
    }

    /**
     * Returns whether an object exists in a bucket
     * @param s3 the information about the object
     * @param s3Bucket the (optional) S3 bucket to use (to optimize chained calls)
     */
    static async objectExists(s3: S3ObjectInfo): Promise<boolean> {
        const bucketToUse = new S3Bucket(s3.bucketName);
        const params = {
            Prefix: `${s3.folder}/${s3.key}`,
            MaxKeys: 1,
        };

        const result = await bucketToUse.listObjectsV2(params);
        return (result.KeyCount || 0) > 0;
    }

    /**
     * List all objects present in a bucket: this function supports AWS S3 pagination mechanism
     * @param s3 the information about the objects
     * @param s3Bucket the S3 bucket to use
     * @param params the parameters to use
     */
    private static async _listObjectsV2(
        s3: S3ObjectInfo,
        bucketToUse: S3Bucket,
        params: WithoutBucket<ListObjectsV2CommandInput>,
    ): Promise<S3ObjectInfo[]> {
        const listOfObjects = await bucketToUse.listObjectsV2(params);
        if (!listOfObjects.Contents) return [];

        const contents = listOfObjects.Contents.map(
            content =>
                ({
                    key: content.Key,
                    lastModified: content.LastModified,
                    eTag: content.ETag,
                    size: content.Size,
                    storageClass: content.StorageClass,
                }) as S3ObjectInfo,
        );

        return !listOfObjects.IsTruncated
            ? contents
            : contents.concat(
                  await S3Helper._listObjectsV2(s3, bucketToUse, {
                      ...params,
                      ...{ ContinuationToken: listOfObjects.NextContinuationToken },
                  }),
              );
    }

    /**
     * List objects present in a bucket
     * @param s3 the information about the objects
     */
    static listObjects(s3: S3ObjectInfo): Promise<S3ObjectInfo[]> {
        return S3Helper._listObjectsV2(s3, new S3Bucket(s3.bucketName), {
            Prefix: `${s3.folder}/${s3.key}`,
        });
    }
}
