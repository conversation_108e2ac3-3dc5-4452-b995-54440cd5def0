import {
    GetObjectCommand,
    GetObjectCommandInput,
    ListObjectsV2Command,
    ListObjectsV2CommandInput,
    ListObjectsV2CommandOutput,
    PutObjectCommand,
    PutObjectCommandInput,
    PutObjectCommandOutput,
    S3Client,
    S3ClientConfig,
} from '@aws-sdk/client-s3';
import { getDefaultRoleAssumerWithWebIdentity } from '@aws-sdk/client-sts';
import { defaultProvider } from '@aws-sdk/credential-provider-node';
import { ConfigManager } from '@sage/xtrem-config';
import * as fs from 'fs';
import { Readable, Writable } from 'stream';

export type WithoutBucket<T> = Omit<T, 'Bucket'>;

type WithBucket<T> = T & { Bucket: string };

export class S3Bucket {
    /** @internal: Hide internal functions in order not to expose all aws-sdk types
     */
    private readonly _s3Client: S3Client;

    constructor(public readonly bucketName: string) {
        const s3Credendials = ConfigManager.current.s3 && ConfigManager.current.s3[bucketName];
        const awsConfig = { region: S3Bucket.getRegion(bucketName) } as S3ClientConfig;

        if (s3Credendials) {
            // The configuration overrides the environment variables
            if (s3Credendials.accessKey || s3Credendials.secret) {
                awsConfig.credentials = { accessKeyId: s3Credendials.accessKey, secretAccessKey: s3Credendials.secret };
            }
        }
        if (awsConfig.credentials == null) {
            // No credentials from the configuration, use getDefaultRoleAssumerWithWebIdentity to get the credentials
            // https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/modules/_aws_sdk_credential_provider_node.html
            const provider = defaultProvider({
                roleAssumerWithWebIdentity: getDefaultRoleAssumerWithWebIdentity(),
            });
            awsConfig.credentials = provider;
        }
        this._s3Client = new S3Client(awsConfig);
    }

    get s3Client(): S3Client {
        return this._s3Client;
    }

    /**
     * Returns the region to use
     * @param bucketName if set, will return the region specific for this bucket (from configuration)
     * @returns
     */
    static getRegion(bucketName?: string): string {
        let region = process.env.AWS_REGION || 'eu-west-1';
        if (bucketName) {
            const s3Credendials = ConfigManager.current.s3 && ConfigManager.current.s3[bucketName];
            if (s3Credendials?.region) {
                region = s3Credendials?.region;
            }
        }
        return region;
    }

    private _addBucket<T>(request: T): WithBucket<T> {
        return { ...{ Bucket: this.bucketName }, ...request };
    }

    private async _tryWait<T>(promise: Promise<T>): Promise<T> {
        try {
            return await promise;
        } catch (e) {
            switch (e.Code) {
                case 'InvalidAccessKeyId':
                case 'SignatureDoesNotMatch':
                    throw new Error(
                        `${e.Code} [${e.message}] Please set s3.${this.bucketName}.accessKey/s3.${this.bucketName}.secret in xtrem-config.yml or set the environment variables AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY.`,
                    );
                default:
                    break;
            }
            throw new Error(`${e.Code ?? ''} [${e.message}]`);
        }
    }

    /**
     * List the objects from a S3 prefix
     * CAUTION: INTERNAL DO NOT USE DIRECTLY. PLEASE USE S3Helper.ListObjects instead
     * If you still want to use thus function, please note it won't return more than 1000 results
     * @internal
     *
     * @example
     *  const result = await myBucket.listObjectsV2({
     *      Prefix: the_S3_prefix,
     *      MaxKeys: 100,
     *  });
     *
     */
    listObjectsV2(request: WithoutBucket<ListObjectsV2CommandInput>): Promise<ListObjectsV2CommandOutput> {
        return this._tryWait(this._s3Client.send(new ListObjectsV2Command(this._addBucket(request))));
    }

    /**
     * Streams a readableStream to S3
     *
     * @example
     * const res = await myBucket.putObject({
     *              Key: the_key_of_the_S3_object,
     *              Body: readStream,
     *          });
     */
    putObject(request: WithoutBucket<PutObjectCommandInput>): Promise<PutObjectCommandOutput> {
        return this._tryWait(this._s3Client.send(new PutObjectCommand(this._addBucket(request))));
    }

    private async _writeObjectToStream(
        request: WithoutBucket<GetObjectCommandInput>,
        writable: Writable,
    ): Promise<void> {
        const command = new GetObjectCommand(this._addBucket(request));
        const data = await this._tryWait(this._s3Client.send(command));
        if (!data.Body) throw new Error(`Invalid S3 object ${this.bucketName}/${request.Key}`);
        await new Promise((resolve, reject) => {
            const readStream = data.Body;
            if (readStream instanceof Readable) {
                readStream.on('close', () => {
                    resolve(undefined);
                });
                readStream.on('error', err => {
                    reject(err);
                });
                readStream.pipe(writable);
            } else {
                reject(new Error(`Invalid stream type received for object ${this.bucketName}/${request.Key}`));
            }
        });
    }

    /**
     * Copy a file from S3 to a local file
     *
     * @internal
     *
     * @example
     *   await myBucket.writeObjectToFile(
     *       {
     *           Key: the_key_of_the_S3_object,
     *       },
     *       local_filename,
     *   );
     */
    async writeObjectToFile(request: WithoutBucket<GetObjectCommandInput>, filePath: string): Promise<void> {
        const writable = fs.createWriteStream(filePath);
        await this._writeObjectToStream(this._addBucket(request), writable);
    }

    async getObjectStream(request: WithoutBucket<GetObjectCommandInput>): Promise<Readable> {
        const command = new GetObjectCommand(this._addBucket(request));
        const data = await this._tryWait(this._s3Client.send(command));
        if (!data.Body) throw new Error(`Invalid S3 object ${this.bucketName}/${request.Key}`);
        const readStream = data.Body;
        if (readStream instanceof Readable) {
            return readStream;
        }
        throw new Error(`Invalid stream type received for object ${this.bucketName}/${request.Key}`);
    }
}
