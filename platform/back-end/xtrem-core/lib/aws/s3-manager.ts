import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { SqlConfig } from '@sage/xtrem-shared/index';
import { ChildProcess, ChildProcessWithoutNullStreams, SpawnOptions, spawn, spawnSync } from 'child_process';
import * as events from 'events';
import * as fs from 'fs';
import * as os from 'os';
import * as fsp from 'path';
import * as readline from 'readline';
import * as semver from 'semver';
import * as stream from 'stream';
import { Application, ApplicationManager } from '../application';
import { Compress } from '../archive';
import { loggers } from '../runtime/loggers';
import { DatabaseSqlContext } from '../sql/sql-context/database-sql-context';
import { SchemaSqlContext } from '../sql/sql-context/schema-sql-context';
import { checkAndSyncValuesHash, getTenantIdList } from '../utils/check-recompute-values-hash';
import { ProgressTransform } from './progress-transform';
import { RestoreEnvelop } from './restore-envelop';
import { S3Helper, S3ObjectInfo } from './s3-helper';

const logger = loggers.dump;

type S3Configuration = {
    bucket: string;
    folder: string;
    s3Key?: string;
    /**
     * Returns the full folder (S3 prefix) to be used for this configuration
     * When not set, the folder will be used
     */
    getFullFolder?: (app: Application) => string;
};

const s3Configurations = {
    /**
     * The S3 bucket used to store the reference database backups (the backups used for the generation of SQL files)
     */
    forSqlFiles: {
        bucket: 'xtrem-developers-utility',
        folder: 'backupsForSqlFiles',
        getFullFolder(app: Application) {
            // SDMO application: backups will be under backupsForSqlFiles/@sage/xtrem-services-main folder
            // shopfloor: backups will be under backupsForSqlFiles/@sage/shopfloor-main folder
            return `${this.folder}/${app.name}`;
        },
    } as S3Configuration,
    /**
     * The S3 bucket used to store cluster-cu backups
     * Used to restore a backup of cluster-cu, from the begining of the month
     * It's an exact copy of sdmo_cu but we have to keep it for some releases for backward compatibility
     */
    clusterCuBackup: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-cls-cu-backups-result/sdmo',
        s3Key: 'xtrem-cls-cu-sdmo-current.gz',
    } as S3Configuration,

    /**
     * The S3 bucket used to store sdmo-cu backups
     * Used to restore a backup of sdmo-cu, from the begining of the month
     */
    sdmo_cu: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-cls-cu-backups-result/sdmo',
        s3Key: 'xtrem-cls-cu-sdmo-current.gz',
    } as S3Configuration,

    /**
     * The S3 bucket used to store cluster-ci/sdmo backups
     * Used to restore a backup of cluster-ci/sdmo, after the last successful upgrade
     * It's an exact copy of sdmo but we have to keep it for some releases for backward compatibility
     */
    clusterCiBackup: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-ci-v2-backups-result/sdmo',
        s3Key: 'xtrem-ci-v2-sdmo-latestWorking.gz',
    } as S3Configuration,

    /**
     * The S3 bucket used to store sdmo-ci/sdmo backups
     * Used to restore a backup of sdmo-ci/sdmo, after the last successful upgrade
     */
    sdmo: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-ci-v2-backups-result/sdmo',
        s3Key: 'xtrem-ci-v2-sdmo-latestWorking.gz',
    } as S3Configuration,

    /**
     * Cluster release (dev)
     */
    clusterDevRelease: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-cls-release-backups-result/sdmo',
        s3Key: 'xtrem-cls-release-sdmo-latest.gz',
    } as S3Configuration,

    /**
     * Cluster release (qa)
     */
    clusterQaRelease: {
        bucket: 'xtrem-developers-utility',
        folder: 'qa-na-cls-release-backups-result/sdmo',
        s3Key: 'xtrem-cls-release-sdmo-latest.gz',
    } as S3Configuration,

    /**
     * Glossary
     * To regenerate the S3 backup : Use https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=2311
     *    - Name of cluster to dump : glossary
     *    - Env of cluster to dump : dev-eu
     *    - withDate : unchecked
     *    - suffix : latestWorking
     */
    glossary: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-ci-v2-backups-result/glossary',
        s3Key: 'xtrem-ci-v2-glossary-latestWorking.gz',
    } as S3Configuration,

    /**
     * Showcase
     * To regenerate the S3 backup : Use https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=2311
     *    - Name of cluster to dump : showcase
     *    - Env of cluster to dump : dev-eu
     *    - withDate : unchecked
     *    - suffix : latestWorking
     */
    showcase: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-ci-v2-backups-result/showcase',
        s3Key: 'xtrem-ci-v2-showcase-latestWorking.gz',
    } as S3Configuration,

    /**
     * Shopfloor (ci)
     */
    shopfloor: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-ci-v2-backups-result/shopfloor',
        s3Key: 'xtrem-ci-v2-shopfloor-latestWorking.gz',
    } as S3Configuration,

    /**
     * Shopfloor (cu)
     */
    shopfloor_cu: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-cls-cu-backups-result/shopfloor',
        s3Key: 'xtrem-cls-cu-shopfloor-current.gz',
    } as S3Configuration,

    /**
     * X3 Connector
     */
    x3_connector: {
        bucket: 'xtrem-developers-utility',
        folder: 'dev-eu-ci-v2-backups-result/x3-connector',
        s3Key: 'xtrem-ci-v2-x3-connector-latestWorking.gz',
    } as S3Configuration,
};

export type S3ConfigurationType = keyof typeof s3Configurations;

interface TableOfContentProcessResult {
    /**
     * The file where the processed table of content was saved
     */
    filename: string;

    /**
     * List of schemas embedded in the dump file
     */
    schemas: string[];
}

const createRole = [
    'DO $$$$',
    'BEGIN',
    'CREATE ROLE xtrem WITH NOLOGIN;',
    'EXCEPTION WHEN DUPLICATE_OBJECT THEN',
    "RAISE NOTICE 'not creating role xtrem -- it already exists';",
    'END',
    '$$$$;',
    '',
    '',
].join('\n');

export class S3Manager {
    private static _postgreSqlTools = {
        isCi: false,
        version: '',
        image: '',
    };

    readonly tempFolder = os.tmpdir();

    constructor(readonly application: Application) {}

    /**
     * Dump the schema of an application to a S3 bucket (and returns the S3 URI of the written object)
     * @param app the application to dump the schema from
     */
    async dumpSchemaToS3Bucket(): Promise<string> {
        const app = this.application;
        const version = (await app.packageManager.getCurrentVersion(app.mainPackage)) || '0.0.0';
        logger.info(`Upload database schema ${app.name}@${version} to s3://${s3Configurations.forSqlFiles.bucket}`);

        const { tempFolder } = this;
        const s3Key = `${app.shortName}@${version}.zip`;
        const localZipFilename = S3Manager.normalizeApplicationName(s3Key);
        const localZipFullFilename = fsp.join(tempFolder, localZipFilename);
        await S3Manager.dumpSchemaToFile(tempFolder, localZipFilename, app.schemaName);
        try {
            const s3Uri = await this.uploadDumpToS3(tempFolder, localZipFilename, s3Key);
            logger.info(() => `The db dump was uploaded to ${s3Uri}`);
            return s3Uri;
        } finally {
            logger.verbose(() => `Delete temp file ${localZipFullFilename}`);
            if (fs.existsSync(localZipFilename)) fs.unlinkSync(localZipFullFilename);
        }
    }

    /**
     * Returns the full folder (S3 prefix) to be used for a given S3 configuration
     */
    private _getFullS3Folder(s3Configuration: S3Configuration): string {
        return s3Configuration.getFullFolder == null
            ? s3Configuration.folder
            : s3Configuration.getFullFolder(this.application);
    }

    /**
     * Resolve the version of the backup to be used to generate a new SQL file
     * @param app
     * @param version
     * @returns
     */
    private async _resolveVersionToRestoreForSqlFiles(version: string): Promise<string> {
        const s3Configuration = s3Configurations.forSqlFiles;
        if ((version === '' || version === 'latest') && !s3Configuration.s3Key) {
            // Here, we are looking after a backup that can be used to generate a new SQL file but no version was provided
            // We have to resolve 'latest' to the last stored backup
            const versions = await this.getAvailableVersions();
            if (versions.length === 0) await this._raiseErrorVersionNotFoundForSqlFile(version, versions);
            const versionToUse = versions[0];
            logger.info(`'latest' was resolved as ${versionToUse}`);
            return versionToUse;
        }
        return version;
    }

    /**
     * Raise an error when a specific version is not part of the available backups used to generate SQL files
     * @param app
     * @param version
     * @param versions
     */
    private async _raiseErrorVersionNotFoundForSqlFile(version: string, versions?: string[]): Promise<never> {
        const app = this.application;
        const versionsToDisplay = versions || (await this.getAvailableVersions());
        let message = `No version '${version || 'latest'}' could be found for application ${app.name}@${app.version}`;
        const sep = '\n\t- ';
        if (versions?.length) {
            message = `${message}, compatible versions are:${sep}${versionsToDisplay.join(sep)}`;
        }
        throw new Error(message);
    }

    /**
     * Restore a schema version from a S3 bucket.
     * If the version is 'latest', the latest available dump for the application will be restored
     * @param app the application to restore the schema for
     * @param versionOrS3Uri either a version to restore, or a full S3 URI of the backup to restore
     * @param s3ConfigurationType the type of the S3 configuration to restore from
     * @param options.checkSingleSchema if true, will raise an error if there are more than 2 schemas in the database
     * @param options.skipValuesHash if true, the re-compute of _valuesHash will be skipped
     */
    async restoreSchemaFromS3Bucket(
        versionOrS3Uri: string,
        s3ConfigurationType: S3ConfigurationType = 'forSqlFiles',
        options: { checkSingleSchema: boolean; skipValuesHash: boolean } = {
            checkSingleSchema: false,
            skipValuesHash: false,
        },
    ): Promise<void> {
        // First, we need to ensure the database exists
        await new DatabaseSqlContext().createDatabaseIfNotExists();
        const restoreEnvelop = new RestoreEnvelop(this, versionOrS3Uri, s3ConfigurationType);

        await restoreEnvelop.restore((e, f) => S3Manager.runRestoreProcess(e, f));

        let { mayBeAnonymizedData } = restoreEnvelop;
        if (mayBeAnonymizedData && options.skipValuesHash) {
            logger.warn('Recomputation of _valuesHash was skipped');
            mayBeAnonymizedData = false;
        }
        // Once the schema has been restored, we need to update the _valuesHash for all content addressable nodes in the imported tenant from anonymized data
        // First, we need to retrieve all tenantIds from the sysTenant table,
        // in order to pass it to the app.asRoot.withReadonlyContext, while looping on each
        if (mayBeAnonymizedData) {
            const app = this.application;
            const sysTenantIds = await getTenantIdList(app);
            await asyncArray(sysTenantIds).forEach(async tenantId => {
                // Fix the _valuesHash for all content addressable nodes in the restored tenants from anonymized data
                await checkAndSyncValuesHash(app, tenantId, logger);
            });
        }

        await this._listSchemas(options.checkSingleSchema);

        logger.info(restoreEnvelop.successMessage);
    }

    private async _listSchemas(checkSingleSchema: boolean): Promise<void> {
        const app = this.application;
        const names = (
            await app.createContextForDdl(context => {
                return context.executeSql<{ name: string }[]>('select nspname name from pg_catalog.pg_namespace', []);
            })
        )
            .map(schema => schema.name)
            .filter(name => {
                if (name.startsWith('pg_')) return false;
                if (name === 'information_schema') return false;
                if (name === 'public') return false;
                return true;
            });
        logger.info(`Schemas in the database: ${names.join(', ')}`);
        if (checkSingleSchema && names.length !== 1) {
            throw new Error(`Expected a single schema, found ${names.length}`);
        }
    }

    /**
     * Upload a local db dump to a S3 bucket and returns the S3 URL of the written object
     * @param localFolder the local folder that contains the file to copy
     * @param filename the name of the file to copy
     * @param dumpKey the key to use to store the dump into S3
     */
    private uploadDumpToS3(localFolder: string, filename: string, dumpKey: string): Promise<string> {
        return this._uploadLocalFileToS3(s3Configurations.forSqlFiles, fsp.join(localFolder, filename), dumpKey);
    }

    /**
     * Upload a local file to a S3 bucket and returns the S3 URL of the written object
     * @param s3Configuration see s3Configurations
     * @param localFilename the (full) filename of the local file to copy
     * @param objectKey the key to use to store the file into S3
     */
    private _uploadLocalFileToS3(
        s3Configuration: S3Configuration,
        localFilename: string,
        objectKey: string,
    ): Promise<string> {
        const s3Info: S3ObjectInfo = {
            bucketName: s3Configuration.bucket,
            folder: this._getFullS3Folder(s3Configuration),
            key: objectKey,
        };
        logger.verbose(() => `Upload ${localFilename} to ${S3Helper.buildS3Uri(s3Info)}`);
        return S3Helper.upload(localFilename, s3Info);
    }

    /**
     * Returns whether an objectKey matches a db backup that can be used to generate SQL files for a given application
     */
    private _isBackupForSqlFile(objectKey: string): boolean {
        // A valid objectKey will <NAME_EMAIL>, <EMAIL>, ...
        return objectKey.startsWith(`${this.application.shortName}@`);
    }

    /**
     * List the versions hosted in the S3 bucket for a given application.
     * Will only return versions for backups that can be used to generate SQL files.
     * Will only return the versions with the same major version as the application.
     * @param app
     */
    async getAvailableVersions(): Promise<string[]> {
        const app = this.application;
        const majorVersion = semver.major(app.mainPackage.packageJson.version);
        const objs = await this._listS3BackupsForSqlFile(s3Configurations.forSqlFiles.bucket);
        return objs
            .map(key => S3Manager._getVersionFromS3Key(key))
            .filter(version => semver.major(version) === majorVersion)
            .sort(semver.compare)
            .reverse();
    }

    /**
     * Extracts the version from a S3 key.
     * @sage/<EMAIL> -> 6.0.13
     *
     * @param s3Key
     * @returns
     */
    private static _getVersionFromS3Key(s3Key: string): string {
        const atIdx = s3Key.lastIndexOf('@');
        const extIdx = s3Key.lastIndexOf('.');
        return s3Key.substring(atIdx + 1, extIdx);
    }

    /**
     * Returns the list of backups that can be used to generate SQL files for a given application
     * @param app
     * @param bucketName
     * @returns
     */
    private async _listS3BackupsForSqlFile(bucketName: string): Promise<string[]> {
        const app = this.application;
        const s3Configuration = s3Configurations.forSqlFiles;
        const fullFolder = this._getFullS3Folder(s3Configuration);
        logger.verbose(
            () =>
                `Looking for versions compatible with ${app.name}@${app.version} from s3://${bucketName}/${fullFolder}`,
        );
        const result = await S3Helper.listObjects({
            bucketName,
            folder: fullFolder,
            key: '',
        });

        if (!result) return [];
        return result
            .map(objInfo => {
                // Note objInfo.Key looks like 'backupsForSqlFiles/@sage/xtrem-services-main/<EMAIL>'
                if (objInfo.key == null) return null;
                if (!objInfo.key.startsWith(`${fullFolder}/`)) return null;
                const path = objInfo.key.substring(fullFolder.length + 1);
                // Note: path <NAME_EMAIL>
                if (!this._isBackupForSqlFile(path)) return null;
                return path;
            })
            .filter(o => o) as string[];
    }

    async getS3Info(s3ConfigurationType: S3ConfigurationType, versionOrS3Uri: string): Promise<S3ObjectInfo> {
        const app = this.application;
        const s3Configuration = s3Configurations[s3ConfigurationType];
        if (!s3Configuration) throw new Error(`Invalid configuration: ${s3ConfigurationType}`);

        let versionToUse: string;
        if (s3ConfigurationType === 'forSqlFiles')
            versionToUse = await this._resolveVersionToRestoreForSqlFiles(versionOrS3Uri);
        else versionToUse = versionOrS3Uri;

        logger.info(
            `[${app.schemaName}] restore ${app.name}@${versionToUse} from s3://${s3Configuration.bucket}/${s3Configuration.folder}`,
        );

        const s3Key = s3Configuration.s3Key || `${app.shortName}@${versionToUse}.zip`;

        if (
            !(await S3Helper.objectExists({
                bucketName: s3Configuration.bucket,
                folder: this._getFullS3Folder(s3Configuration),
                key: s3Key,
            }))
        ) {
            const versions = await this.getAvailableVersions();
            await this._raiseErrorVersionNotFoundForSqlFile(versionToUse, versions);
        }
        return {
            bucketName: s3Configuration.bucket,
            folder: this._getFullS3Folder(s3Configuration),
            key: s3Key,
        };
    }

    private static _tailLogLine(msg: string): string {
        if (msg.endsWith('\n')) return msg.substring(0, msg.length - 1);
        return msg;
    }

    /**
     * Pipes a readable stream to a writable stream.
     * Transformations are applied according to the provided mappings.
     */
    private static async _pipeStreamWithTransformations(options: {
        /**
         * The readable stream to read from
         */
        inData: stream.Readable;
        /**
         * The readable stream to read the errors from
         */
        inErr?: stream.Readable;
        /**
         * The process to observe to get the 'close' event
         */
        inMain: events.EventEmitter;
        /**
         * The writable stream to write to.
         */
        outStream: stream.Writable;
        /**
         * The mappings
         */
        mappings: {
            /**
             * The regex to match
             */
            reg: RegExp;
            /**
             * The replacement value
             */
            val: string;
        }[];
        /**
         * The expected code to get from the 'close' event
         */
        expectedExitCode: number | undefined;
    }): Promise<void> {
        const formatSize = (bytes: number): string => {
            if (bytes >= 1024 * 1024) {
                return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
            }
            if (bytes >= 1024) {
                return `${(bytes / 1024).toFixed(2)} KB`;
            }
            return `${bytes} bytes`;
        };

        await new Promise<void>((resolve, reject) => {
            const rl = readline.createInterface({
                input: options.inData,
                crlfDelay: Infinity,
            });

            let canWrite = true;
            let maxLineLength = 0; // Track max line length found in the data
            let linesProcessed = 0;
            let currentLine = '';
            let isErrorHandled = false;

            // On error, destroy output stream and reject the promise
            const handleError = (err: Error): void => {
                logger.error(`Transformation error: ${err.message}`);
                logger.info(`Processed ${linesProcessed} lines.\nLast line read: ${currentLine}`);
                isErrorHandled = true;
                options.outStream.destroy();
                reject(err);
            };

            rl.on('error', handleError);
            options.outStream.on('error', handleError);

            if (options.inErr) {
                options.inErr.on('data', data => {
                    const txt: string = data.toString();
                    logger.error(`ERR ${S3Manager._tailLogLine(txt)}`);
                });

                options.inErr.on('error', handleError);
            }

            rl.on('line', line => {
                currentLine = line;
                options.mappings.forEach(({ reg, val }) => {
                    currentLine = currentLine.replace(reg, val);
                });
                canWrite = options.outStream.write(`${currentLine}\n`);
                if (!canWrite) rl.pause(); // Pause reading if the writable stream needs to drain

                linesProcessed++;
                maxLineLength = Math.max(maxLineLength, currentLine.length);

                logger.debug(() => `\t${S3Manager._tailLogLine(line)}`);
            });

            options.outStream.on('drain', () => {
                rl.resume(); // Resume reading when the writable stream is ready to accept more data
            });

            rl.on('close', () => {
                options.outStream.end();
                logger.info(
                    `Transformation finished processing ${linesProcessed} lines.\nMax line length encountered: ${formatSize(maxLineLength)}`,
                );
            });

            options.outStream.on('finish', () => {
                if (!isErrorHandled) resolve();
            });

            options.inMain.on('close', code => {
                if (![0, options.expectedExitCode].includes(code)) {
                    handleError(new Error(`Exited with code ${code}`));
                }
            });
        });
    }

    private static _pgClientToolCommand(
        command: string,
        args: string[],
        options?: SpawnOptions,
    ): ChildProcessWithoutNullStreams {
        const config = ConfigManager.current;
        const pgTool = {
            isCi: !!config.env?.isCI,
            version: '16.1',
            image: 'postgres:16.1-alpine',
        };
        let cmd: string[] = [];
        if (!this._postgreSqlTools.version) {
            const pgVersionRegex = /^psql\s+\(\w+\)\s+(\d+\.\d+)/;
            const expectedVersion = '>=16.1';
            const installMessage = `Please, install a version ${expectedVersion}.
on Ubuntu systems you can do it with:
sudo apt update
sudo apt install postgresql-client`;
            cmd = ['psql', '--version'];
            if (pgTool.isCi) {
                const versionResult = spawnSync('docker', ['exec', '-i', 'xtrem_postgres', 'psql', '--version'], {
                    encoding: 'utf-8',
                });
                if (versionResult.error != null) {
                    throw new Error(`Cannot get image version of xtrem_postgres. ${installMessage}`);
                }
                pgTool.version = pgVersionRegex.exec(versionResult.stdout)?.[1] ?? '';
                pgTool.image = `postgres:${pgTool.version}-alpine`;
                logger.info(`Use PostgreSQL client tools version ${pgTool.version} from image ${pgTool.image}`);
                cmd = ['docker', 'run', '-i', '--rm', pgTool.image, ...cmd];
            }
            const result = spawnSync(cmd[0], cmd.slice(1), {
                encoding: 'utf-8',
            });
            if (result.error != null) {
                throw new Error(`Cannot get PostgreSQL client tools version. ${installMessage}`);
            }
            // version is something like:
            // psql (PostgreSQL) 16.1 (Ubuntu 16.1-1.pgdg20.04+1)
            pgTool.version = pgVersionRegex.exec(result.stdout)?.[1] ?? '';
            const version = semver.coerce(pgTool.version);
            if (version == null) {
                throw new Error(
                    `Cannot get PostgreSQL client tools version. The version ${result.stdout} does not have the expected format. ${installMessage}`,
                );
            }
            if (!semver.satisfies(version, expectedVersion)) {
                throw new Error(
                    `PostgreSQL client tools version ${version} does not satisfies the expected version ${expectedVersion}. ${installMessage}`,
                );
            }
            this._postgreSqlTools = pgTool;
        }
        const tmpdir = os.tmpdir();
        cmd = [command, ...args];
        if (pgTool.isCi) {
            cmd = [
                'docker',
                'run',
                '-i',
                '--rm',
                // '--network=host',
                // '--add-host=host.docker.internal:host-gateway',
                '-v',
                `${tmpdir}:/tmp`,
                '-e',
                `PGPASSWORD=${options?.env?.PGPASSWORD}`,
                pgTool.image,
                command,
                ...args.map(a =>
                    a.replace(tmpdir, '/tmp').replace('localhost', '**********').replace('127.0.0.1', '**********'),
                ),
            ];
        }
        logger.info(() => `Run command: ${cmd.join(' ').replace(/PGPASSWORD=[^ ]+/g, 'PGPASSWORD=****')}`);
        return spawn(cmd[0], cmd.slice(1), { ...options, stdio: ['pipe', 'pipe', 'pipe'] });
    }

    /**
     * Dumps the current schema (described by the current configuration) to a local dump file.
     * Note: if the filename is a zip file, the dump will be a valid zip archive.
     *
     * Note: the created file will be anonymized: it will contain no schema/user name, they will be replaced with tags:
     * - schema -> [SCHEMA_NAME]
     * - user -> [USER_NAME]
     *
     * @param folder the folder that contains the dump file
     * @param filename the name of the dump file
     */
    static async dumpSchemaToFile(folder: string, filename: string, schemaName: string): Promise<void> {
        // Auto-detect if we have to zip the dump
        const zipIt = filename.endsWith('.zip');
        // Use a temp filename when zipping
        const dumpFilename = zipIt ? 'sql.dump' : filename;
        const dumpFullFilename = fsp.join(folder, dumpFilename);
        const sqlCfg = ConfigManager.current.storage?.sql;
        if (sqlCfg == null) {
            throw new Error('SQL configuration is missing');
        }

        logger.verbose(() => `Dump ${sqlCfg.database}.${schemaName} to ${dumpFullFilename}`);
        const args = [
            '--format=p', // Format in plain text to be able to transform the generated script
            `--host=${sqlCfg.hostname}`,
            `--dbname=${sqlCfg.database}`,
            `--schema=${schemaName}`,
            `--username=${sqlCfg.user}`,
            '--no-password', // Will use the PGPASSWORD env variable
        ];
        const proc = this._pgClientToolCommand('pg_dump', args, {
            env: { ...process.env, PGPASSWORD: sqlCfg.password },
            cwd: folder,
        });
        // Run the pg_dump command, transform its output with mappings and write it to dumpFullFilename
        await S3Manager._pipeStreamWithTransformations({
            inData: proc.stdout,
            inErr: proc.stderr,
            inMain: proc,
            outStream: fs.createWriteStream(dumpFullFilename),
            mappings: [
                {
                    reg: new RegExp(` SCHEMA ${schemaName}`, 'g'),
                    val: ' SCHEMA [SCHEMA_NAME]',
                },
                { reg: new RegExp(`${schemaName}\\.`, 'g'), val: '[SCHEMA_NAME].' },
                { reg: new RegExp(` TO ${sqlCfg.user};`, 'g'), val: ' TO [USER_NAME];' },
                {
                    reg: /ALTER DEFAULT PRIVILEGES FOR ROLE xtrem/,
                    val: `${createRole}ALTER DEFAULT PRIVILEGES FOR ROLE xtrem`,
                },
            ],
            expectedExitCode: 0,
        });

        const outFilename = fsp.join(folder, filename);
        if (zipIt) {
            logger.verbose(() => `Zip ${dumpFullFilename} to ${outFilename}`);

            // Zip the generated file
            await Compress.zipFile(dumpFullFilename, dumpFilename, outFilename, { zlib: { level: 9 } });

            // Delete the temp file
            logger.debug(() => `Deleting temp file ${dumpFullFilename}`);
            if (fs.existsSync(dumpFullFilename)) fs.unlinkSync(dumpFullFilename);
        }
    }

    /**
     * Returns the mappings that should be applied to a sql file before it get restored
     * @param schemaName
     * @param dumpFullFilename
     * @param sqlCfg
     */
    private static _getMappingsForDumpFiles(
        schemaName: string,
        dumpFullFilename: string,
        sqlCfg: SqlConfig,
    ): { reg: RegExp; val: string }[] {
        // NOTE: if we created the dump file, it contains [SCHEMA_NAME] / [USER_NAME] tags that must be mapped
        // to the actual schema/user (see dumpSchemaToFile)
        const mappings = [
            { reg: /\[SCHEMA_NAME\]/g, val: schemaName },
            { reg: /\[USER_NAME\]/g, val: sqlCfg.user },
        ];

        // There is a line at the beginning of the sql file that looks like:
        // -- Name: xtrem; Type: SCHEMA; Schema: -; Owner: bart
        // It tells us what were the schema and user name of the database that was backed up
        // here, the user was 'bart'. The SQL file will contain some lines like 'ALTER TABLE xxxx OWNER TO bart'
        // We need to replace this with 'ALTER TABLE xxxx OWNER TO yyyy' where yyyy is the current SQL user
        // otherwise, the current user will not be allowed to use the database.
        const handle = fs.openSync(dumpFullFilename, 'r');
        try {
            const chunkSize = 1000;
            const buffer = Buffer.alloc(chunkSize);
            const count = fs.readSync(handle, buffer);
            if (count !== chunkSize) {
                // Something went wrong ... we could not get all of the xxxx first chars of the sql file
                logger.warn(`\t- could only get ${count} out of ${chunkSize} first chars of the SQL file`);
                return mappings;
            }
            const beginningOfFile = buffer.toString();
            const userMatch = beginningOfFile.match(/^-- Name: \S+; Type: SCHEMA; Schema: \S+; Owner: (\S+)$/m);
            if (userMatch && sqlCfg.user !== userMatch[1]) {
                logger.info(`\t- will remap user '${userMatch[1]}' to '${sqlCfg.user}'`);
                mappings.push({
                    reg: new RegExp(` OWNER TO ${userMatch[1]};$`, 'gm'),
                    val: ` OWNER TO ${sqlCfg.user};`,
                });
            }
        } catch (err) {
            logger.warn(`\t- encountered error ${err.message} while building mappings to apply to the SQL file`);
        } finally {
            fs.closeSync(handle);
        }

        return mappings;
    }

    static runRestoreProcess(
        restoreEnvelop: RestoreEnvelop,
        filenameOrStream: string | stream.Readable,
    ): Promise<void> {
        if (restoreEnvelop.isZip) {
            return S3Manager.runRestoreFromMappedFile(restoreEnvelop, filenameOrStream);
        }
        if (filenameOrStream instanceof stream.Readable) {
            const { s3ConfigurationType, localFullFilename } = restoreEnvelop;
            return restoreEnvelop.s3Manager.restoreSchemaFromBackupStream(
                localFullFilename,
                filenameOrStream,
                s3ConfigurationType === 'clusterCiBackup' ||
                    s3ConfigurationType === 'sdmo' ||
                    // Happens when we are restoring from a local file (copied from S3 with 'aws s3 cp')
                    (s3Configurations.sdmo.s3Key != null && localFullFilename.includes(s3Configurations.sdmo.s3Key)),
            );
        }
        throw new Error('No filename or stream provided to restore the schema');
    }

    /**
     * Run the restore process for a schema from a mapped file (i.e. a dump file where the mappings have been applied)
     * @param filenameOrStream the FULL filename of the '.mapped' file
     * @param schemaName
     */
    static runRestoreFromMappedFile(
        restoreEnvelop: RestoreEnvelop,
        filenameOrStream: string | stream.Readable,
    ): Promise<void> {
        if (filenameOrStream instanceof stream.Readable) {
            // To investigate we've got a memory issue when trying to stream to the process stdin
            throw new Error('Readable stream are not supported yet');
        }
        return this._restoreFromMappedFile(filenameOrStream, restoreEnvelop.s3Manager.application.schemaName);
    }

    /**
     * Restore a schema from a mapped file (i.e. a dump file where the mappings have been applied)
     * @param dumpFullFilename the FULL filename of the 'sql.mapped' file
     * @param schemaName
     */
    private static async _restoreFromMappedFile(mappedDumpFullFilename: string, schemaName: string): Promise<void> {
        // SQL config
        const sqlCfg = ConfigManager.current.storage?.sql;
        if (sqlCfg == null) throw new Error('SQL configuration is missing');

        logger.verbose(() => `Restore ${sqlCfg.database}.${schemaName} from mapped file ${mappedDumpFullFilename}`);

        logger.info(() => `[${schemaName}] restore schema from ${mappedDumpFullFilename}`);
        // The script was generated by pg_dump but we can't use pg_restore because the script was generated
        // in plain text and pg_restore does not manage plain text scripts. We are using psql instead
        const args = [
            `--host=${sqlCfg.hostname}`,
            `--dbname=${sqlCfg.database}`,
            `--username=${sqlCfg.sysUser}`,
            `--file=${mappedDumpFullFilename}`,
            '--single-transaction',
        ];
        if (sqlCfg.port !== undefined) {
            args.push('--port');
            args.push(`${sqlCfg.port}`);
        }

        const proc = this._pgClientToolCommand('psql', args, {
            env: { ...process.env, PGPASSWORD: sqlCfg.sysPassword },
            cwd: fsp.dirname(mappedDumpFullFilename),
            // TODO: Stream support to investigate
            // cwd: typeof filenameOrStream === 'string' ? fsp.dirname(filenameOrStream) : undefined,
        });

        await S3Manager._childProcessHandler(proc, {
            hackForSdmo: false,
            rejectOnStderr: true, // we don't wan to continue if the restore process fails
            // TODO: Stream support to investigate
            // streams: typeof filenameOrStream === 'string' ? undefined : [filenameOrStream],
        });
    }

    /**
     * Restore a schema from a dump file
     * @param tempFolder the 'temp' folder where to create the 'sql.mapped.dump' file
     * @param dumpFullFilename the FULL filename of the 'sql.dump' file
     * @param schemaName
     */
    static async restoreFromDumpFile(folder: string, dumpFullFilename: string, schemaName: string): Promise<void> {
        // SQL config
        const sqlCfg = ConfigManager.current.storage?.sql;
        if (sqlCfg == null) {
            throw new Error('SQL configuration is missing');
        }

        logger.verbose(() => `Restore ${sqlCfg.database}.${schemaName} from ${dumpFullFilename}`);

        const mappedDumpFullFilename = fsp.join(folder, 'sql.mapped.dump');
        logger.info(() => `[${schemaName}] transform ${dumpFullFilename} to ${mappedDumpFullFilename}`);

        const mappings = S3Manager._getMappingsForDumpFiles(schemaName, dumpFullFilename, sqlCfg);
        const inStream = fs.createReadStream(dumpFullFilename);
        await S3Manager._pipeStreamWithTransformations({
            inData: inStream,
            inMain: inStream,
            outStream: fs.createWriteStream(mappedDumpFullFilename),
            mappings,
            expectedExitCode: undefined,
        });

        // Delete the original dump file after transformation
        logger.info(() => `Delete original dump file ${dumpFullFilename} after transformation`);
        if (fs.existsSync(dumpFullFilename)) fs.unlinkSync(dumpFullFilename);

        await S3Manager._restoreFromMappedFile(mappedDumpFullFilename, schemaName);

        // Delete mapped dump file after restore process completes
        if (fs.existsSync(mappedDumpFullFilename)) fs.unlinkSync(mappedDumpFullFilename);
    }

    /**
     * Restore from a gz dump stream
     * @param dumpFullFilename the FULL filename of the dump to restore
     * @param sourceStream
     * @param hackForSdmo
     */
    async restoreSchemaFromBackupStream(
        dumpFullFilename: string,
        sourceStream: stream.Readable,
        hackForSdmo = false,
    ): Promise<void> {
        const { tempFolder } = this;

        const sqlCfg = ConfigManager.current.storage?.sql;
        if (sqlCfg == null) {
            throw new Error('SQL configuration is missing');
        }

        // first, save the sourceStream to the dumpFullFilename
        const writeStream = fs.createWriteStream(dumpFullFilename);
        await new Promise((resolve, reject) => {
            stream.pipeline(sourceStream, new ProgressTransform(), writeStream, err => {
                if (err) {
                    reject(err);
                } else {
                    resolve(dumpFullFilename);
                }
            });
        });

        const { application } = this;

        const tableOfContents = await this._processTableOfContentsForRestoration(dumpFullFilename);

        logger.info(() => `The dump concerns the following schemas : ${tableOfContents.schemas}`);
        if (!tableOfContents.schemas.includes(application.schemaName)) {
            logger.warn(
                () =>
                    `Your configuration is using the schema ${application.schemaName} which will not be restored by this backup`,
            );
        }

        if (application.schemaName !== ApplicationManager.getDefaultServiceSchemaName()) {
            // The user must have used the XTREM_SCHEMA_NAME environment variable to try to restore into a
            // specific schema
            switch (tableOfContents.schemas.length) {
                case 0:
                    logger.warn(() => 'The dump does not contain any data/metadata for any schema');
                    break;
                case 1:
                    if (tableOfContents.schemas[0] !== application.schemaName) {
                        logger.warn(
                            () =>
                                `The dump will be restored to the schema '${tableOfContents.schemas[0]}' (forced schema '${application.schemaName}' will be ignored)`,
                        );
                    }
                    break;
                default:
                    if (!tableOfContents.schemas.includes(application.schemaName)) {
                        logger.warn(
                            () =>
                                `The dump concerns many schemas but none of them matches the schema ${application.schemaName}`,
                        );
                    }
            }
        }

        await this._dropSchemasBeforeRestoration(tableOfContents.schemas);

        const args = [
            '-L',
            tableOfContents.filename,
            '--host',
            sqlCfg.hostname,
            '--username',
            sqlCfg.sysUser ?? '',
            '--dbname',
            sqlCfg.database ?? '',
            dumpFullFilename,
            '--no-owner', // Do not restore ownership
            '--no-privileges', // Do not restore privileges
        ];

        if (sqlCfg.port !== undefined) {
            args.push('--port');
            args.push(`${sqlCfg.port}`);
        }

        const proc = S3Manager._pgClientToolCommand('pg_restore', args, {
            env: { ...process.env, PGPASSWORD: sqlCfg.sysPassword },
            cwd: tempFolder,
        });

        await S3Manager._childProcessHandler(proc, { hackForSdmo });

        if (sqlCfg.user !== sqlCfg.sysUser) {
            await asyncArray(tableOfContents.schemas).forEach(async schemaName => {
                // set the users default privileges on the schema
                await new DatabaseSqlContext().setUserDefaultPrivileges(schemaName);

                // set the users privileges restored schemas objects created
                await new DatabaseSqlContext().setUserPrivileges(schemaName);
            });
        }
    }

    /**
     * Process the table of content of a postgres dump.
     * Mainly : some entries related to extensions will be filtered and the content will be parsed to retrieve the name of schemas
     */
    private async _processTableOfContentsForRestoration(
        dumpFullFilename: string,
    ): Promise<TableOfContentProcessResult> {
        const generateTempFilename = async (): Promise<string> => {
            const dirName = await fs.promises.mkdtemp(`${this.tempFolder}${fsp.sep}`);
            return fsp.join(dirName, 'pg_restore_without_extenstion_comments.list');
        };

        const result: TableOfContentProcessResult = {
            filename: await generateTempFilename(),
            schemas: [],
        };

        await S3Manager._childProcessHandler(S3Manager._pgClientToolCommand('pg_restore', ['--version']), {
            hackForSdmo: false,
            stdoutCallback: data => {
                logger.info(`pg_restore.version=${data}`);
            },
        });

        // Re-enable for debug purpose
        // await S3Manager._childProcessHandler(this._pgClientToolCommand('dpkg', ['-l']), {
        //     hackForSdmo: false,
        //     stdoutCallback: data => {
        //         logger.info(`pg_restore.version=${data}`);
        //     },
        // });

        // Re-enable for debug purpose
        // await S3Manager._childProcessHandler(this._pgClientToolCommand('which', ['pg_restore']), {
        //     hackForSdmo: false,
        //     stdoutCallback: data => {
        //         logger.info(`which pg_restore=${data}`);
        //     },
        // });

        // Retrieve the table of content from the dump file
        let tableOfContent = '';
        await S3Manager._childProcessHandler(S3Manager._pgClientToolCommand('pg_restore', ['-l', dumpFullFilename]), {
            hackForSdmo: false,
            stdoutCallback: data => {
                tableOfContent = `${tableOfContent}${data}`;
            },
        });

        // Remove all the 'COMMENT - EXTENSION ...' so that not superUser RDS Amazon can restore the database.
        // these lines will not really be removed but commented (with a starting ';');
        // 14496; 0 0 COMMENT - EXTENSION pgcrypto -> ;14496; 0 0 COMMENT - EXTENSION pgcrypto ->
        // eslint-disable-next-line @sage/redos/no-vulnerable
        tableOfContent = tableOfContent.replace(/(\d+; \d+ \d+ COMMENT - EXTENSION)/g, ';$1');

        // Retrieve the names of the schema(s) embedded in the dump
        // eslint-disable-next-line @sage/redos/no-vulnerable
        const matches = [...tableOfContent.matchAll(/\d+; \d+ \d+ SCHEMA - (\w+)/g)];
        if (matches) {
            result.schemas = matches.map(match => match[1]).filter(s => s !== 'public');
        }

        fs.writeFileSync(result.filename, tableOfContent);
        return result;
    }

    dropApplicationSchema(): Promise<void> {
        return this._dropSchemasBeforeRestoration([this.application.schemaName]);
    }

    /**
     * Drops a bunch of schemas (if they exist) before restoring a dump
     */
    private _dropSchemasBeforeRestoration(schemaNames: string[]): Promise<void> {
        const { application } = this;
        return application.createContextForDdl(
            context =>
                asyncArray(schemaNames).forEach(schemaName => {
                    logger.info(() => `[${schemaName}] Drop schema (if exists)`);
                    return new SchemaSqlContext(context.application).dropSchema(schemaName);
                }),
            {
                description: () => `Drop schemas ${schemaNames.join()}`,
            },
        );
    }

    /** Generic asynchronous handler for child process stdout, stderr and close event listeners */
    private static async _childProcessHandler(
        childProc: ChildProcess,
        options: {
            hackForSdmo: boolean;
            rejectOnStderr?: boolean; // If true, will exit the process on error
            stdoutCallback?: (data: string) => void;
            streams?: (stream.Readable | stream.Writable)[];
        },
    ): Promise<void> {
        let errorsCount = 0;
        await new Promise<void>((resolve, reject) => {
            childProc.stdout!.on('data', data => {
                if (options.stdoutCallback) {
                    options.stdoutCallback((data as Buffer).toString());
                } else {
                    logger.debug(() => (data as Buffer).toString());
                }
            });
            childProc.stderr!.on('data', data => {
                const err = (data as Buffer).toString();
                if (options.hackForSdmo) {
                    // Backups of sdmo-ci report some errors while being restored, but the restoration is successful
                    const knownErrors = [
                        'plv8',
                        'errors ignored on restore: 1',
                        'relation "lock_monitor" already exists',
                    ];
                    if (knownErrors.some(knownError => err.includes(knownError))) return;
                }
                if (err.includes('ERROR:')) errorsCount += 1;
                logger.error(err);
                if (err && options.rejectOnStderr) {
                    logger.error(`Got error from child process: ${childProc.pid}`);
                    sourceStream?.destroy();
                    reject(new Error(err));
                }
            });
            childProc.on('close', code => {
                // Note: in some cases, some errors can be raised but the final exitCode is still 0 ...
                if (code === 0 && errorsCount === 0) {
                    resolve();
                    return;
                }
                if (options.hackForSdmo && code === 1) {
                    // Hack to be able to restore backups from sdmo-ci, even on database where the plv8 extension
                    // is not installed
                    resolve();
                    return;
                }

                const message = `process ${childProc.pid} exited with code ${code}, ${errorsCount} errors`;
                logger.error(message);
                sourceStream?.destroy();
                reject(new Error(message));
            });

            const sourceStream = options.streams?.[0] as stream.Readable;

            // Handle child process error event
            childProc.on('error', err => {
                console.error('Error child:', err);
                sourceStream?.destroy();
                reject(err);
            });

            if (sourceStream) {
                const stdin = childProc.stdin;
                if (!stdin) {
                    sourceStream?.destroy();
                    reject(new Error('Child process stdin is not available'));
                    return;
                }
                sourceStream
                    .pipe(new ProgressTransform('Processed'))
                    .pipe(stdin)
                    .on('end', () => {
                        stdin.end();
                        resolve();
                    })
                    .on('error', err => {
                        if ((err as any).code === 'EPIPE') {
                            // eslint-disable-next-line no-console
                            console.error('EPIPE error:', err);
                            sourceStream.destroy();
                            stdin.end();
                            resolve();
                        } else {
                            logger.error(err.message);
                            if (err) process.exit(1);
                            sourceStream?.destroy();
                            reject(err);
                        }
                    });
            }
        });
    }

    static normalizeApplicationName(appName: string): string {
        return appName.replace(/@/g, '').replace(/[/,\\]/g, '-');
    }
}
