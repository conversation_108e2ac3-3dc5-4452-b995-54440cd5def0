import { AnyValue, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import {
    BusinessRuleError,
    ColumnTypeName,
    Dict,
    integer,
    LogicError,
    nameToSqlName,
    SystemError,
} from '@sage/xtrem-shared';
import { isNumber, snakeCase, uniq } from 'lodash';
import { BinaryStreamProperty, TextStreamProperty } from '.';
import { Package, ServiceOption } from '../application';
import {
    DelegatesTo,
    PropertyFilterTag,
    PropertySqlAttributes,
    ReferenceArrayPropertyDecorator,
    TypedPropertyDecorator,
} from '../decorators';
import { chainArrays, chainEvents, chainIsFrozens, TypeName } from '../decorators/decorator-utils';
import {
    DefaultValueRule,
    DuplicatedValueRule,
    PropertyPaths,
    PropertyRuleName,
    SetValueData,
    UpdatedValueRule,
} from '../decorators/property-decorators/base-property-events';
import { NodeState } from '../node-state';
import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { FactoryDecorators, NodeFactory, NodeFunction } from '../runtime/node-factory';
import { SqlConverter } from '../sql';
import { Column } from '../sql/schema';
import { AnyNode, Node, ValidationContext, Validator } from '../ts-api';
import { DataType, Enum, InternalPropertyJoin, TextStream } from '../types';
import { arrayTypes } from '../types/arrays';
import { typeDefaultValue } from '../types/util';
import { BooleanProperty } from './boolean-property';
import {
    DateProperty,
    DateRangeProperty,
    DatetimeProperty,
    DatetimeRangeProperty,
    TimeProperty,
} from './date-time-property';
import { EnumArrayProperty, EnumProperty } from './enum-property';
import {
    CollectionProperty,
    ForeignNodeProperty,
    ReferenceArrayProperty,
    ReferenceProperty,
} from './foreign-node-property';
import { JsonProperty } from './json-property';
import { DecimalProperty, DoubleProperty, FloatProperty, IntegerProperty, NumberProperty } from './numeric-property';
import { StringArrayProperty, StringProperty } from './string-property';

/** @internal */
export const foreignNodePropertyTypes = ['collection', 'reference', 'jsonReference', 'instance', 'referenceArray'];
const propertyLogger = loggers.property;

/** @internal */
export type PropagatePropertyPath = Property[];

/** @internal */
export type InternalDefaultValueRule =
    | DefaultValueRule<Node, AnyValue>
    | ((context: Context) => AsyncResponse<AnyValue>);

/** @internal */
export type InternalDuplicatedValueRule =
    | DuplicatedValueRule<Node, AnyValue>
    | ((context: Context) => AsyncResponse<AnyValue>);

/** @internal */
export type InternalUpdatedValueRule =
    | UpdatedValueRule<Node, AnyValue>
    | ((context: Context) => AsyncResponse<AnyValue>);

/** @internal */
export type InternalBooleanOrUndefined = boolean | ((this: Node) => AsyncResponse<boolean>) | undefined;

/** @internal */
export const overridableAttributes = [
    'name',
    'type',
    'columnName',
    'dependencyIndex',
    'extends',
    'isOverride',
    'defaultValue',
    'deferredDefaultValue',
    'duplicatedValue',
    'updatedValue',
    'getValue',
    'adaptValue',
    'prepare',
    'control',
    'setValue',
    'dependsOn',
    'upgrade',
    'computeValue',
    'node',
    'isFrozen',
    'isStored',
    'isVitalParent',
    'isPublished',
];

export abstract class Property {
    private _dependencyIndex?: number;

    private _propagatesTo?: PropagatePropertyPath[];

    private _column?: Column;

    protected _columnName?: string;

    protected _factory: NodeFactory;

    private _columnType?: ColumnTypeName;

    private _isInherited?: boolean;

    private _isAutoIncrement?: boolean;

    private _isSystemProperty?: boolean;

    private _isFrozen?: InternalBooleanOrUndefined;

    // Properties defined in system-properties.ts have their defaultValue method taking a context as first parameter.
    // Other properties take the node as first parameter.
    private _defaultValue?: InternalDefaultValueRule;

    private _deferredDefaultValue?: InternalDefaultValueRule;

    private _duplicatedValue?: InternalDuplicatedValueRule;

    private _updatedValue?: InternalUpdatedValueRule;

    private _adaptValue?: (this: Node, val: AnyValue) => AsyncResponse<AnyValue>;

    private _getValue?: (this: Node) => AsyncResponse<AnyValue>;

    private _computeValue?: (this: Node) => AsyncResponse<AnyValue>;

    private _setValue?: (this: Node, val: SetValueData<Node>) => AsyncResponse<void>;

    private _control?: Validator<Node, AnyValue>;

    private _prepare?: (this: Node, cx: ValidationContext, val: AnyValue) => AsyncResponse<void>;

    private _dependsOn?: PropertyPaths<Node>;

    #serviceOptions: ServiceOption[];

    constructor(
        factory: NodeFactory,
        protected _decorator: TypedPropertyDecorator,
        readonly definingPackage: Package,
    ) {
        this._factory = factory;
        if (!this._decorator.name) throw new Error("Can't create property without name.");
        if (!this._decorator.type) throw new Error("Can't create property without type.");
        this._factory = factory.application.getFactoryByName(this._factoryDecorators.node.name);
        this._factory.addPropertyAccessors(this);
    }

    get factory(): NodeFactory {
        return this._factory;
    }

    get _factoryDecorators(): FactoryDecorators {
        return this._factory.decorators;
    }

    /**
     * Returns whether this property should be lazy loaded
     */
    // eslint-disable-next-line class-methods-use-this
    get shouldLazyLoad(): boolean {
        return false;
    }

    /**
     * Returns the name of attributes that differ between 2 properties
     */
    static getDecoratorsDifferences(
        prop1: Property | TypedPropertyDecorator,
        prop2: Property | TypedPropertyDecorator,
    ): string[] {
        const attributesToCheck = ['isStored', 'isPublished', 'isNullable', 'type', 'isOverride', 'dataType'];
        return attributesToCheck.filter(attrName => {
            let attr1 = (prop1 as any)[attrName];
            let attr2 = (prop2 as any)[attrName];
            if (typeof attr1 === 'boolean' || typeof attr2 === 'boolean') {
                // Allow to compare isNullable=false with isNullable not set
                attr1 = !!attr1;
                attr2 = !!attr2;
            }
            return attr1 !== attr2;
        });
    }

    getLocalizedTitleKey(): string {
        const property = this.rootProperty;

        if (property.isSystemProperty || property.name === '_sortValue') {
            return `@sage/xtrem-core/system_property_${snakeCase(property.name)}`;
        }

        return !property.isExtendedProperty
            ? `${property.factory.package.name}/nodes__${snakeCase(property.factory.name)}__property__${property.name}`
            : `${property.definingPackage.name}/node-extensions__${snakeCase(
                  property.factory.name,
              )}_extension__property__${property.name}`;
    }

    getLocalizedTitle(context: Context): string {
        // Examples:
        // Node - @sage/xtrem-foo/nodes__node_class_name__property__property_name
        // Node extension - @sage/xtrem-foo/node-extensions__node_name_extension__property__extended_property_name
        const property = this.rootProperty;
        const key = this.getLocalizedTitleKey();
        try {
            return context.localize(key, property.name);
        } catch {
            propertyLogger.warn(
                `Could not resolve localized title for property name: ${this.factory.name}.${this.name}, key ${key}`,
            );
            return this.name;
        }
    }

    // Override for subclasses
    verifyDelegatedToProperty(toProperty: Property): void {
        if (toProperty.type !== this.type) {
            throw this.logicError(
                `Invalid delegatesTo attribute: type mismatch, expected ${this.type}, got ${toProperty.type}`,
            );
        }
        if (this.dataType && this.dataType !== toProperty.dataType) {
            throw this.logicError('Invalid delegatesTo attribute: dataType mismatch');
        }
    }

    private verifyDelegatesTo(): void {
        const delegatesTo = this.delegatesTo;
        if (!delegatesTo) return;

        if (typeof delegatesTo !== 'object') throw this.logicError('delegatesTo is not an object');
        const keys = Object.keys(delegatesTo);
        if (keys.length !== 1)
            throw this.logicError(
                `Invalid delegatesTo attribute: object must have exactly one key, found ${keys.length}.`,
            );
        const referenceProperty = this.factory.findProperty(keys[0]);
        if (!referenceProperty.isReferenceProperty())
            throw this.logicError(`Invalid delegatesTo attribute: property ${this.fullName} is not a reference.`);
        if (referenceProperty.isNullable && !this.isNullable)
            throw this.logicError(
                `Property must be nullable because its dependsOn reference ${referenceProperty.fullName} is nullable`,
            );
        if (!referenceProperty.isMutable && !referenceProperty.isVital)
            throw referenceProperty.logicError(
                'Property must be marked as isVital or isMutable because it is used in delegatesTo attributes',
            );

        const childPropertyName = delegatesTo[keys[0]];
        if (typeof childPropertyName !== 'string')
            throw this.logicError('delegatesTo child property name is not a string');
        const childProperty = referenceProperty.targetFactory.propertiesByName[childPropertyName];
        if (!childProperty) throw this.logicError('Invalid delegatesTo attribute: child property not found');
        if (childProperty.isNullable && !this.isNullable)
            throw this.logicError(
                `Property must be nullable because its dependsOn child property ${childProperty.fullName} is nullable`,
            );
    }

    private verifyDuplicatedValue(): void {
        if (this.decorator.duplicatedValue === undefined) return;

        if (!this.factory.canBeDuplicated) {
            throw this.systemError('The duplicatedValue attribute can only be set if the node can be duplicated.');
        }
    }

    private verifyClearByReset(): void {
        if (!this.isClearedByReset) return;
        if (this.factory.isClearedByReset)
            throw this.systemError('isClearedByReset tag is already declared in the node decorator');

        if (!this.isStored && this.delegatesTo == null)
            throw this.systemError('isClearedByReset tag can only be set to stored properties or delegated properties');

        if (typeof this.defaultValue === 'function')
            throw this.systemError('isClearedByReset tag cannot be set if defaultValue is a function');

        if (this.factory.indexes?.some(index => Object.keys(index.orderBy)[0] === this.name && index.isUnique))
            throw this.systemError('isClearedByReset tag cannot be set on a unique index');
    }

    private verifyDefaultValue(): void {
        // TODO: remove this exclusion after implementing SQL converter for Datetime
        if (this.fullName === 'UpgradeAddColumns.newMandatoryDatetimeWithDefault') return;

        const value = this.defaultValue;
        if (value == null) return;
        // Accept non objects (boolean, number, string, function)
        if (typeof value !== 'object') return;
        // Accept plain objects
        if (value.constructor === Object) return;
        // Accept empty text streams
        if (value === TextStream.empty) return;
        // Accept empty array
        if (Array.isArray(value) && value.length === 0) return;

        throw this.systemError('defaultValue must be a boolean, a number, a string, a function or a plain object');
    }

    verify(): void {
        const factory = this._factory;

        if (this.definingPackage !== this.factory.package && this.isStored) {
            if (this.definingPackage.isService) {
                this.systemError('Cannot define stored property in node extension inside a service package');
            }
        }

        if ((this._decorator as any).isNotEmpty !== undefined && this.type !== 'string')
            throw this.systemError('isNotEmpty attribute can only be used for string properties');

        if ((this._decorator as any).node && !foreignNodePropertyTypes.includes(this.type))
            throw this.systemError('Invalid property definition (has a node() function but is not a reference).');

        if (this.type === 'collection' && !this.isMutable && this.isRequired)
            throw this.systemError('Non vital collection cannot be required.');

        this.verifyDuplicatedValue();
        this.verifyClearByReset();

        // Check if the tagged factory by isClearedByReset can be cleared.
        if (
            this.isReferenceProperty() &&
            this.isStored &&
            !this.isInherited &&
            this.column &&
            this.targetFactory.isClearedByReset &&
            this.isHardDependency
        ) {
            if (!factory.isClearedByReset) {
                const targetFactoryName = this.targetFactory.name;
                throw this.systemError(
                    `target factory ${targetFactoryName} is flagged by isClearedByReset. The current factory ${factory.name} should be also be flagged by isClearedByReset`,
                );
            }
        }

        if (
            factory.table &&
            this.type !== 'collection' &&
            !this.isMutable &&
            !this.isStored &&
            !this.isTransientInput &&
            this.delegatesTo == null &&
            this._decorator.getValue == null &&
            this._decorator.computeValue == null &&
            this.join == null &&
            this.isPublished
        )
            throw this.systemError(
                "A published property needs either 'isStored', 'isVital', 'isMutable', 'isTransientInput', 'computeValue', 'getValue' or 'join' decorator member to be set.",
            );

        if (this.isStored && this.isTransientInput)
            throw this.systemError(
                "Property attributes 'isStored' and 'isTransientInput' cannot be both true on a single property.",
            );

        if (this.isStored && this._decorator.getValue != null)
            throw this.systemError(
                "Property attribute 'isStored' or 'isStoredOutput' cannot be true when 'getValue' is supplied on a single property.",
            );

        if (this.decorator.lookupAccess && this.isTransientInput)
            throw this.systemError(
                "Property attributes 'lookupAccess' and 'isTransientInput' cannot be both true on a single property.",
            );

        if (!this.isPublished && this._decorator.lookupAccess && !['_updateStamp', '_createStamp'].includes(this.name))
            throw this.systemError(
                "Property attribute 'lookupAccess' cannot be true when property attribute 'isPublished' is not on a single property.",
            );

        if (!this.updatedValue && this.isStoredOutput)
            throw this.systemError("If 'isStoredOutput' is set to true an 'updatedValue' must be supplied.");

        if (this.isStored && factory.storage !== 'sql' && factory.storage !== 'external') {
            throw this.systemError(
                `'isStored' decorator member is invalid on non-persistent class, got ${factory.storage}`,
            );
        }

        if (factory.storage !== 'external') {
            if (this._decorator.columnName)
                throw this.systemError(
                    "'columnName' decorator member is only allowed in class with 'external' storage",
                );

            if (this._decorator.columnType)
                throw this.systemError(
                    "'columnType' decorator member is only allowed in class with 'external' storage",
                );
        }

        if (
            !this.isStored &&
            !this.isMutable &&
            !this.isTransientInput &&
            this.defaultValue &&
            this.type !== 'collection' &&
            (factory.storage === 'sql' || factory.storage === 'external')
        )
            throw this.systemError('defaultValue is not allowed on computed properties');

        this.verifyDefaultValue();

        if (this.isNullable && this.isRequired) throw this.systemError('required property cannot be nullable');

        if (this.type === undefined) throw this.systemError('type is undefined');

        if (this.isVitalParent && !factory.isVitalChild) {
            throw this.systemError(
                "The 'isVitalParent' attribute can only be used in nodes flagged with 'isVitalReferenceChild' or 'isVitalCollectionChild'",
            );
        }

        if (this.isVitalParent && this.isTransientInput)
            throw this.systemError('A vital parent property cannot be a transient input');
        if (this.isVitalParent && this.isMutable)
            throw this.systemError('A vital parent property cannot be a mutable property');
        if (this.isVitalParent && !this.isStored)
            throw this.systemError("A vital parent property must be flagged with 'isStored'");

        if (this.isReferenceArrayProperty()) {
            const onDelete = (this.decorator as ReferenceArrayPropertyDecorator).onDelete;
            if (this.isStored && onDelete == null) {
                throw this.systemError('onDelete attribute is required for reference array properties');
            } else if (!this.isStored && onDelete != null) {
                throw this.systemError('onDelete attribute is only allowed for stored reference array properties');
            }
        }

        if (this.isMutable && !foreignNodePropertyTypes.includes(this.type))
            throw this.systemError('A mutable property must be either a collection or a reference');

        if ((this.decorator as any).node) {
            if (!foreignNodePropertyTypes.includes(this.type))
                throw this.systemError(
                    `Invalid property type: expected ${foreignNodePropertyTypes.join(' | ')}, got '${this.type}'`,
                );
        }

        if (
            this.factory.storage !== 'external' &&
            this.serviceOptions.length > 0 &&
            this.isReferenceProperty() &&
            !this.isNullable &&
            this.serviceOptions.some(serviceOption => !this.factory.serviceOptions.includes(serviceOption))
        ) {
            throw this.systemError('A reference property depending on a service option must be nullable');
        }

        // Cannot override isFrozen on a property that is already frozen in a base class
        if (this._isFrozen === false) {
            if (this.isFrozen) {
                throw this.systemError('Cannot override isFrozen if already frozen in base class');
            }
        }

        // Validate there are no duplicatedValue attributes set for
        // - non vital nodes with canCreate = false
        // - vital nodes without at least one parent with canCreate = true
        if (this.duplicatedValue && !this.factory.canCreate) {
            if (this.factory.isVitalChild) {
                // In the case of duplicatedValue, if the current node has canCreate = false but is a vital child
                // We need to check if any of his parents has canCreate = true
                let currentVitalParentFactory = this.factory.vitalParentFactory;
                let canParentCreate = currentVitalParentFactory.canCreate;

                // while we didn't found a parent with canCreate = true and we still have parents to check
                while (!canParentCreate && currentVitalParentFactory) {
                    canParentCreate = currentVitalParentFactory.canCreate;

                    // In case we found a parent with canCreate = true we stop looking for another ones
                    if (canParentCreate) break;

                    // In case we didn't found a parent with canCreate = true yet, we move to the above parent in the tree
                    currentVitalParentFactory = currentVitalParentFactory.vitalParentFactory;
                }

                // Throw an error if the node is a vital node and none of its parents have canCreate = true
                if (!canParentCreate) {
                    throw this.systemError(
                        'The duplicatedValue attribute is only allowed for a vital node if at least one of its parents has canCreate set to true.',
                    );
                }
            } else {
                // Throw an error if the node is not a vital node and has canCreate = false
                throw this.systemError(
                    'The duplicatedValue attribute is only allowed for non vital node if it has canCreate set to true.',
                );
            }
        }

        if (
            this.isReferenceProperty() &&
            !this.isNullable &&
            this.factory.naturalKey &&
            !this.targetFactory.naturalKey &&
            !this.targetFactory.isContentAddressable &&
            this.defaultValue === undefined
        ) {
            if (
                [
                    'AccountsReceivableInvoiceLineStaging',
                    'JournalEntryLineStaging',
                    'AccountsPayableInvoiceLineStaging',
                    'JournalEntryLineStaging',
                ].includes(this._factory.name)
            ) {
                propertyLogger.warn(() => `${this._factory.name}.${this.name}: must be natural key`);
            } else {
                // Throw an error if the node is not a vital node and has canCreate = false
                throw this.systemError(
                    'Reference properties on nodes with a natural key must either; be linked to a target node with a natural key, be nullable, or have defaultValue.',
                );
            }
        }

        this.verifyAnonymizeMethod();

        // display warning if dataType is anonymous
        if (this.dataType) {
            if (ConfigManager.current.deploymentMode === 'development') {
                // This rule can only be checked in dev mode (we won't have the source in binary build)
                const extractDataTypeName = (expression?: string): string | undefined => {
                    if (!expression || expression.includes('new')) return undefined;
                    // eslint-disable-next-line @sage/redos/no-vulnerable
                    const match = /(?:[^.]+\.)*(\w+)\.?/.exec(expression);
                    return match ? match[1] : undefined;
                };
                const dataTypeName = extractDataTypeName(this?.decorator?.dataType?.toString());
                if (!dataTypeName)
                    propertyLogger.warn(`${factory.name}.${this.name}: anonymous DataTypes are not allowed`);
            }
        }

        this.verifyDelegatesTo();

        if (this.cacheComputedValue && !this.getValue && !this.computeValue)
            throw this.systemError('cacheComputedValue is only allowed if getValue or computeValue is set');
    }

    private verifyAnonymizeMethod(): void {
        if (this.decorator.anonymizeMethod) {
            if (this.decorator.dataSensitivityLevel === undefined) {
                // Throw an error where no dataSensitivityLevel is specified
                throw this.systemError('dataSensitivityLevel is required when specifying an anonymize method.');
            }

            if (
                ['fixed', 'custom'].includes(this.decorator.anonymizeMethod) &&
                this.decorator.anonymizeValue === undefined
            ) {
                // Throw an error where no anonymizeValue is specified for 'fixed' or 'custom'
                throw this.systemError("anonymizeValue is required for 'fixed' and 'custom' anonymize methods.");
            }

            if (['hash', 'hashLimit'].includes(this.decorator.anonymizeMethod) && this.type !== 'string') {
                // Throw an error where hash is set on a property other than string
                throw this.systemError("anonymizeMethod 'hash' is only applicable to properties of type string.");
            }

            if (
                this.decorator.anonymizeMethod === 'random' &&
                !['short', 'integer', 'float', 'decimal', 'double'].includes(this.type)
            ) {
                // Throw an error where hash is set on a property other than string
                throw this.systemError("anonymizeMethod 'random' is only applicable to properties with a number type.");
            }

            if (
                this.decorator.anonymizeMethod === 'hashLimit' &&
                (this.decorator.anonymizeValue === undefined || !isNumber(this.decorator.anonymizeValue))
            ) {
                // Throw an error where hash is set on a property other than string
                throw this.systemError("A number is required in anonymizeValue for 'hashLimit' anonymize method.");
            }

            if (
                this.decorator.anonymizeMethod === 'binary' &&
                typeof this.decorator.anonymizeValue === 'string' &&
                !['pdf', 'image'].includes(this.decorator.anonymizeValue)
            ) {
                // Throw an error where hash is set on a property other than string
                throw this.systemError(
                    "A valid AnonymizeBinary value is required in anonymizeValue for 'binary' anonymize method.",
                );
            }
        }
    }

    addExtension(extension: TypedPropertyDecorator): void {
        this.verifyExtensionKeys(Object.keys(extension), overridableAttributes);
        this.overrideAttributes(extension);
    }

    protected verifyExtensionKeys(extensionKeys: string[], authorizedKeys: string[]): void {
        extensionKeys
            .filter(key => !authorizedKeys.includes(key))
            .forEach(key => {
                throw this.systemError(`${key} can't be redefined by an extension.`);
            });
    }

    protected overrideAttributes(extension: TypedPropertyDecorator): void {
        const extensionKeys = Object.keys(extension);
        extensionKeys.forEach(key => {
            switch (key) {
                case 'name':
                case 'type':
                    if (this.decorator[key] !== extension[key]) throw this.systemError(`Extension mismatch on ${key}`);
                    break;
                case 'columnName':
                case 'dependencyIndex':
                case 'extends':
                case 'isOverride':
                    break;
                case 'isFrozen':
                    this[key] = chainIsFrozens(this, extension);
                    break;
                case 'getValue':
                    if (extension.getValue !== undefined) {
                        this._getValue = extension.getValue;
                        this._computeValue = undefined;
                    }
                    break;
                case 'computeValue':
                    if (extension.computeValue !== undefined) {
                        this._computeValue = extension.computeValue;
                        this._getValue = undefined;
                    }
                    break;

                case 'defaultValue':
                case 'deferredDefaultValue':
                    if (extension[key] !== undefined) this[key] = extension[key];
                    break;
                case 'duplicatedValue':
                    if (extension.duplicatedValue !== undefined) this.duplicatedValue = extension.duplicatedValue;
                    break;
                case 'updatedValue':
                    if (extension.updatedValue !== undefined) this.updatedValue = extension.updatedValue;
                    break;
                case 'adaptValue':
                    if (extension.adaptValue !== undefined) this.adaptValue = extension.adaptValue;
                    break;
                case 'prepare':
                case 'control':
                case 'setValue':
                    try {
                        this[key] = chainEvents(this[key], extension[key], key);
                    } catch (e) {
                        throw this.systemError(e.message);
                    }
                    break;
                case 'dependsOn':
                    this[key] = chainArrays(this[key], extension[key]);
                    break;
                default:
                    break;
            }
        });
    }

    getTypeDefaultValue(): AnyValue {
        return typeDefaultValue(this.type, this.isNullable, this.isLocalized, this.factory.storage);
    }

    get decorator(): TypedPropertyDecorator {
        return this._decorator;
    }

    get name(): string {
        return this._decorator.name!;
    }

    get fullName(): string {
        return `${this.factory.name}.${this.name}`;
    }

    get type(): TypeName {
        return this._decorator.type!;
    }

    get serviceOptions(): ServiceOption[] {
        if (!this.#serviceOptions) {
            this.#serviceOptions = uniq([
                ...(this._decorator.serviceOptions?.() || []),
                ...((this.dataType && this.dataType.serviceOptions) || []),
                ...((this.isForeignNodeProperty() && this.targetFactory.serviceOptions) || []),
            ]);
        }
        return this.#serviceOptions;
    }

    get propagatesTo(): PropagatePropertyPath[] | undefined {
        return this._propagatesTo;
    }

    set propagatesTo(paths: PropagatePropertyPath[] | undefined) {
        this._propagatesTo = paths;
    }

    get dependsOn(): any[] | undefined {
        return this._dependsOn || this._decorator.dependsOn;
    }

    set dependsOn(dependsOn: any[] | undefined) {
        this._dependsOn = dependsOn;
    }

    get dependencyIndex(): number | undefined {
        return this._dependencyIndex === undefined ? this._decorator.dependencyIndex : this._dependencyIndex;
    }

    set dependencyIndex(index: number | undefined) {
        this._dependencyIndex = index;
    }

    get isOverride(): boolean {
        return !!this._decorator.isOverride;
    }

    get isInherited(): boolean {
        return this._isInherited === undefined ? !!this._decorator.isInherited : this._isInherited;
    }

    set isInherited(isInherited: boolean) {
        this._isInherited = isInherited;
    }

    get isClearedByReset(): InternalBooleanOrUndefined {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isClearedByReset;
        return this._decorator.isClearedByReset;
    }

    get duplicateRequiresPrompt(): InternalBooleanOrUndefined {
        return this._decorator.duplicateRequiresPrompt;
    }

    set isSystemProperty(isSystemProperty: boolean) {
        this._isSystemProperty = isSystemProperty;
    }

    get isSystemProperty(): boolean {
        return !!this._isSystemProperty;
    }

    get rootProperty(): this {
        if (!this.factory.baseFactory) return this;

        const baseProperty = this.factory.baseFactory.propertiesByName[this.name];
        return baseProperty ? (baseProperty.rootProperty as this) : this;
    }

    get delegatesTo(): DelegatesTo<AnyNode> | undefined {
        return this._decorator.delegatesTo as DelegatesTo<AnyNode> | undefined;
    }

    getDelegatingInfo(): { reference: ReferenceProperty; childProperty: Property } {
        const delegatesTo = this.delegatesTo;
        if (!delegatesTo) throw this.logicError('missing delegatesTo attribute');
        const keys = Object.keys(delegatesTo);
        if (keys.length !== 1) throw this.logicError(`expected 1 key in delegatesTo, got ${keys.length}`);
        const reference = this.factory.findProperty(keys[0]);
        if (!reference.isReferenceProperty())
            throw this.logicError(`delegating property ${keys[0]} is not a reference`);
        const childPropertyName = (delegatesTo as Dict<AnyValue>)[reference.name];
        if (typeof childPropertyName !== 'string')
            throw this.logicError('delegatesTo child property name is not a string');
        const childProperty = reference.targetFactory.findProperty(childPropertyName);
        return { reference, childProperty };
    }

    /** Returns an empty string if the property can be used in an index, otherwise the reason why
     * the property cannot be used in an index */
    get invalidForIndexReason(): string {
        switch (this.type) {
            case 'string':
                if (this.isLocalized) return `${this.type} - localized`;
                if (this.isStringProperty() && this.isStoredEncrypted) return `${this.type} - encrypted`;
                return '';
            case 'reference':
                if (this.isVital) return `${this.type} - vital`;
                return '';
            case 'boolean':
            case 'enum':
            case 'short':
            case 'integer':
            case 'float':
            case 'double':
            case 'decimal':
            case 'date':
            case 'datetime':
            case 'time':
            case 'uuid':
            case 'dateRange':
                return '';
            default:
                return this.type;
        }
    }

    get accessCode(): string | undefined {
        return this._decorator.accessCode;
    }

    get isStored(): boolean {
        return !!this.decorator.isStored || !!this.decorator.isStoredOutput;
    }

    get isTransientInput(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isTransientInput;
        return !!this._decorator.isTransientInput;
    }

    get isStoredOutput(): boolean {
        return !!this._decorator.isStoredOutput;
    }

    get columnType(): ColumnTypeName | undefined {
        return this._columnType || this._decorator.columnType;
    }

    set columnType(columnType: ColumnTypeName | undefined) {
        this._columnType = columnType;
    }

    get columnName(): string | undefined {
        if (this._columnName) return this._columnName;
        if ((!this.isStored && this.getValue) || (this.isStored && this._factory.storage !== 'external'))
            return nameToSqlName(this.name);

        return this._decorator.columnName;
    }

    set columnName(name: string | undefined) {
        this._columnName = name;
    }

    get requiredColumnName(): string {
        const columnName = this.columnName;
        if (!columnName) throw this.logicError('missing columnName');
        return columnName;
    }

    get column(): Column | undefined {
        if (this._column) return this._column;
        const factory = this._factory;
        const column = factory.table && factory.table.columns.find(col => col.propertyName === this.name);
        // We get this error because of the way some name conflicts are resolved.
        // For now, just warn and continue instead of throwing - will revert to throw later
        // throw this.propertyError(prop, `no column for property`);
        if (!column) {
            if (factory.storage === 'sql' && this.isStored && !this.isInherited)
                propertyLogger.warn(`${factory.name}.${this.name}: no column ${this.columnName}`);

            return undefined;
        }
        return column;
    }

    set column(column: Column | undefined) {
        this._column = column;
    }

    get isAutoIncrement(): boolean {
        return this._isAutoIncrement === undefined ? !!this._decorator.isAutoIncrement : this._isAutoIncrement;
    }

    set isAutoIncrement(isAutoIncrement: boolean) {
        this._isAutoIncrement = isAutoIncrement;
    }

    get provides(): PropertyFilterTag[] | undefined {
        return this._decorator.provides;
    }

    get isOwnedByCustomer(): boolean | undefined {
        return this._decorator.isOwnedByCustomer;
    }

    get canSearch(): boolean {
        return !!this._decorator.canSearch;
    }

    get searchCategory(): string | undefined {
        return this._decorator.searchCategory;
    }

    get lookupAccess(): boolean {
        return (
            !!this._decorator.lookupAccess ||
            (this.isVital && this.isReferenceProperty()) ||
            this.isVitalParent ||
            this.isAssociationParent
        );
    }

    get excludedFromPayload(): boolean {
        return !!this._decorator.excludedFromPayload;
    }

    get isPublished(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isPublished;
        return !!this._decorator.isPublished;
    }

    get isRequired(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isRequired;
        return !!this._decorator.isRequired;
    }

    get isFrozen(): InternalBooleanOrUndefined {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isFrozen;
        // Vital parent properties are frozen by default
        if (this.isVitalParent || this.isAssociationParent) return true;

        return this._isFrozen === undefined ? this._decorator.isFrozen : this._isFrozen;
    }

    set isFrozen(isFrozen: InternalBooleanOrUndefined) {
        this._isFrozen = isFrozen;
    }

    get dataType(): DataType<AnyValue, Node> | undefined {
        return this._decorator.dataType ? this._decorator.dataType() : undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    get isVitalParent(): boolean {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    get isAssociationParent(): boolean {
        return false;
    }

    get isExtendedProperty(): boolean {
        return this.definingPackage !== this.factory.package;
    }

    get max(): integer | undefined {
        return this._decorator.max;
    }

    get defaultValue(): InternalDefaultValueRule {
        return this._defaultValue === undefined ? this._decorator.defaultValue : this._defaultValue;
    }

    set defaultValue(defaultValue: InternalDefaultValueRule) {
        this._defaultValue = defaultValue;
    }

    get deferredDefaultValue(): InternalDefaultValueRule {
        return this._deferredDefaultValue === undefined
            ? this._decorator.deferredDefaultValue
            : this._deferredDefaultValue;
    }

    set deferredDefaultValue(deferredDefaultValue: InternalDefaultValueRule) {
        this._deferredDefaultValue = deferredDefaultValue;
    }

    get duplicatedValue(): InternalDuplicatedValueRule {
        return this._duplicatedValue === undefined ? this._decorator.duplicatedValue : this._duplicatedValue;
    }

    set duplicatedValue(duplicatedValue: InternalDuplicatedValueRule) {
        this._duplicatedValue = duplicatedValue;
    }

    get updatedValue(): InternalUpdatedValueRule {
        return this._updatedValue === undefined ? this._decorator.updatedValue : this._updatedValue;
    }

    set updatedValue(updatedValue: InternalUpdatedValueRule) {
        this._updatedValue = updatedValue;
    }

    get adaptValue(): ((this: Node, val: AnyValue) => AsyncResponse<AnyValue>) | undefined {
        return this._adaptValue === undefined ? this._decorator.adaptValue : this._adaptValue;
    }

    set adaptValue(adaptValue: ((this: Node, val: AnyValue) => AsyncResponse<AnyValue>) | undefined) {
        this._adaptValue = adaptValue;
    }

    get setValue(): ((this: Node, val: SetValueData<AnyValue>) => AsyncResponse<void>) | undefined {
        return this._setValue === undefined ? this._decorator.setValue : this._setValue;
    }

    set setValue(setValue: ((this: Node, val: SetValueData<AnyValue>) => AsyncResponse<void>) | undefined) {
        this._setValue = setValue;
    }

    get control(): Validator<Node, AnyValue> | undefined {
        return this._control === undefined ? this._decorator.control : this._control;
    }

    set control(control: Validator<Node, AnyValue> | undefined) {
        this._control = control;
    }

    get prepare(): ((this: Node, cx: ValidationContext, val: AnyValue) => AsyncResponse<void>) | undefined {
        return this._prepare === undefined ? this._decorator.prepare : this._prepare;
    }

    set prepare(prepare: ((this: Node, cx: ValidationContext, val: AnyValue) => AsyncResponse<void>) | undefined) {
        this._prepare = prepare;
    }

    get getValue(): ((this: Node) => AsyncResponse<AnyValue>) | undefined {
        return '_getValue' in this ? this._getValue : this._decorator.getValue;
    }

    set getValue(getValue: ((this: Node) => AsyncResponse<AnyValue>) | undefined) {
        this._getValue = getValue;
    }

    get computeValue(): ((this: Node) => AsyncResponse<AnyValue>) | undefined {
        return '_computeValue' in this ? this._computeValue : this._decorator.computeValue;
    }

    set computeValue(computeValue: ((this: Node) => AsyncResponse<AnyValue>) | undefined) {
        this._computeValue = computeValue;
    }

    get cacheComputedValue(): boolean {
        return !!this._decorator.cacheComputedValue;
    }

    get isNullable(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isNullable;
        return !!(this._decorator as any).isNullable;
    }

    get allowedInUniqueIndex(): boolean {
        return !!(this._decorator as any).allowedInUniqueIndex;
    }

    // eslint-disable-next-line class-methods-use-this
    get isVital(): boolean {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    get isMutable(): boolean {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    get isNotEmpty(): boolean {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    get isLocalized(): boolean {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    get isNotZero(): boolean {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    get enum(): Enum | undefined {
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    get maxLength(): number | undefined {
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    get countColumnName(): Enum | undefined {
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    get reverseReference(): string | undefined {
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    get join(): InternalPropertyJoin<Node> | undefined {
        return undefined;
    }

    get isOutputOnly(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isOutputOnly;
        return isPropertyOutputOnly(this);
    }

    get isOnOutputType(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isOnOutputType;
        return (this.isOutputOnly || this.isPublished || !this.excludedFromPayload) && !this.isTransientInput;
    }

    get isInputOnly(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isInputOnly;
        return isPropertyInputOnly(this);
    }

    get isOnInputType(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isOnInputType;
        return (
            this.isPublished &&
            !this.isStoredOutput &&
            (!!this.setValue || this.isInputOnly || this.isMutable || this.isStored || this.isTransientInput)
        );
    }

    get canTranslateToSql(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.canTranslateToSql;
        return this.isStored || !!this.getValue;
    }

    get canSort(): boolean {
        return this.canTranslateToSql;
    }

    get canFilter(): boolean {
        return this.canTranslateToSql;
    }

    /**
     * Internal sql attributes used when we generate the column SQL definition.
     * default - used on the column DEFAULT
     */
    get sqlAttributes(): PropertySqlAttributes | undefined {
        return this.decorator.sqlAttributes;
    }

    async isEnabledByServiceOptions(context: Context): Promise<boolean> {
        return (
            (await asyncArray(this.serviceOptions).every(serviceOption =>
                context.isServiceOptionEnabled(serviceOption),
            )) && context.isAccessCodeAvailable(this.accessCode)
        );
    }

    async isAuthorized(context: Context): Promise<boolean> {
        if (this.factory.authorizedBy) {
            const authorizedByStatus = (await this.factory.authorizedBy(context, this.name, {}))?.status;
            if (authorizedByStatus !== undefined)
                return authorizedByStatus === 'authorized' || authorizedByStatus === 'readonly';
        }
        const status = (await Context.accessRightsManager.getUserAccessFor(context, this.factory.name, this.name))
            .status;
        return status === 'authorized' || status === 'readonly';
    }

    isBooleanProperty(): this is BooleanProperty {
        return this.type === 'boolean';
    }

    isIntegerProperty(): this is IntegerProperty {
        return this.type === 'short' || this.type === 'integer';
    }

    isFloatingPointProperty(): this is FloatProperty | DoubleProperty {
        return this.type === 'float' || this.type === 'double';
    }

    isDecimalProperty(): this is DecimalProperty {
        return this.type === 'decimal';
    }

    isNumberProperty(): this is NumberProperty {
        return this.isIntegerProperty() || this.isDecimalProperty() || this.isForeignNodeProperty();
    }

    isStringProperty(): this is StringProperty {
        return this.type === 'string';
    }

    isStringArrayProperty(): this is StringArrayProperty {
        return this.type === 'stringArray';
    }

    isDateProperty(): this is DateProperty {
        return this.type === 'date';
    }

    isDatetimeProperty(): this is DatetimeProperty {
        return this.type === 'datetime';
    }

    isDatetimeRangeProperty(): this is DatetimeRangeProperty {
        return this.type === 'datetimeRange';
    }

    isDateRangeProperty(): this is DateRangeProperty {
        return this.type === 'dateRange';
    }

    isRangeProperty(): boolean {
        return ['integerRange', 'decimalRange', 'dateRange', 'datetimeRange'].includes(this.type);
    }

    isTimeProperty(): this is TimeProperty {
        return this.type === 'time';
    }

    isForeignNodeProperty(): this is ForeignNodeProperty {
        return this.isReferenceProperty() || this.isReferenceArrayProperty() || this.isCollectionProperty();
    }

    isReferenceProperty(): this is ReferenceProperty {
        return this.type === 'reference';
    }

    isReferenceArrayProperty(): this is ReferenceArrayProperty {
        return this.type === 'referenceArray';
    }

    isCollectionProperty(): this is CollectionProperty {
        return this.type === 'collection';
    }

    isArrayProperty(): boolean {
        return arrayTypes.includes(this.type);
    }

    isEnumProperty(): this is EnumProperty {
        return this.type === 'enum';
    }

    isEnumArrayProperty(): this is EnumArrayProperty {
        return this.type === 'enumArray';
    }

    isJsonProperty(): this is JsonProperty {
        return this.type === 'json';
    }

    isTextStreamProperty(): this is TextStreamProperty {
        return this.type === 'textStream';
    }

    isBinaryStreamProperty(): this is BinaryStreamProperty {
        return this.type === 'binaryStream';
    }

    // This method checks the type of input data. It is dedicated to `state.setPropertyValue`.
    // It performs a basic sanity check on the JavaScript data type, not a thorough check because `adaptValue` has not been called yet.
    // Null values are checked separately so it is never called with null/undefined value.
    abstract isValueTypeValid(value: AnyValue): boolean;

    // This method checks the type of input data. It is dedicated to `state.setPropertyValue`.
    // It checks that value can be mapped to the expected type
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    needsTypeConversion(_value: AnyValue): boolean {
        return false;
    }

    // This method makes it possible to map the type of input data. It is dedicated to `state.setPropertyValue`.
    // It is intended to be used, for instance, to map the input string 'true' into the boolean true
    // eslint-disable-next-line class-methods-use-this
    mapInputValue(value: AnyValue): AnyValue {
        return value;
    }

    executeRule<ResultT extends AnyValue>(
        state: NodeState,
        ruleName: PropertyRuleName,
        ...args: AnyValue[]
    ): Promise<ResultT> {
        const method = (this as any)[ruleName] as NodeFunction<ResultT>;
        return state.context.sqlSpy.withRuleMetrics(
            { nodeName: this.factory.name, propertyName: this.name, ruleName },
            () => method.apply(state.node, args),
        );
    }

    testSqlConversion(fn: () => any): Promise<string | null> {
        return this.factory.application.asRoot.withReadonlyContext(
            '7'.repeat(21),
            context => {
                try {
                    new SqlConverter(context, this.factory, { quiet: true }).convertFunction(fn);
                    return null;
                } catch (e) {
                    return e.message;
                }
            },
            { withoutSqlConnection: true },
        );
    }

    /** Test if getValue and computeValue are correctly configured - used by xtrem-cop  */
    async testSqlConversions(): Promise<integer> {
        let errorCount = 0;
        const getValue = this.getValue;
        const computeValue = this.computeValue;
        if (getValue && computeValue) {
            propertyLogger.error(`${this.fullName}: getValue and computeValue cannot be set at the same time`);
            errorCount += 1;
        }
        if (getValue) {
            const error = await this.testSqlConversion(getValue);
            if (error) {
                propertyLogger.error(
                    `${this.fullName}: getValue cannot be converted to SQL, replace it by computeValue: \n[${error}]`,
                );
                errorCount += 1;
            }
        } else if (computeValue && !(await this.testSqlConversion(computeValue))) {
            propertyLogger.error(`${this.fullName}: computeValue can be converted to SQL, replace it by getValue`);
            errorCount += 1;
        }
        return errorCount;
    }

    inputError(message: string): SystemError {
        return new BusinessRuleError(`${this._factory.name}.${this.name}: ${message}`);
    }

    systemError(message: string): SystemError {
        return new SystemError(`${this._factory.name}.${this.name}: ${message}`);
    }

    logicError(message: string): SystemError {
        return new LogicError(`${this._factory.name}.${this.name}: ${message}`);
    }
}

export function isPropertyOutputOnly(property: Property): boolean {
    if (property.delegatesTo) {
        const { childProperty } = property.getDelegatingInfo();
        return isPropertyOutputOnly(childProperty);
    }
    if (property.isStoredOutput) return true;
    if ((property.getValue || property.computeValue) && !property.setValue) return true;
    if (property.isReferenceProperty() && property.decorator.join && !property.isMutable) return true;
    if (property.isCollectionProperty()) {
        if (!(property.isTransientInput || property.isMutable)) return true;
        if (property.getFilter) return true;
    }
    return false;
}

export function isPropertyInputOnly(property: Property): boolean {
    if (property.isTransientInput || (!property.getValue && !property.computeValue && property.setValue)) return true;
    return false;
}
