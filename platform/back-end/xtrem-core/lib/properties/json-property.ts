import { AnyValue } from '@sage/xtrem-shared';
import * as JSON5 from 'json5';
import { Package } from '../application';
import { JsonPropertyDecorator } from '../decorators';
import { NodeFactory } from '../runtime/node-factory';
import { JsonDataType } from '../types';
import { Property } from './property';

export class JsonProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: JsonPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    override get dataType(): JsonDataType {
        return super.dataType as JsonDataType;
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'object';
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        const isJson5String = (str: string): boolean => {
            try {
                JSON5.parse(str);
            } catch {
                return false;
            }
            return true;
        };
        return typeof value === 'string' && isJson5String(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        const stringValue = value as string;
        return JSON5.parse(stringValue);
    }
}
