import { AnyValue } from '@sage/xtrem-async-helper';
import { DateRange, Datetime, DatetimeRange, DateValue, Time } from '@sage/xtrem-date-time';
import { Package } from '../application';
import {
    DatePropertyDecorator,
    DateRangePropertyDecorator,
    DatetimePropertyDecorator,
    DatetimeRangePropertyDecorator,
    TimePropertyDecorator,
} from '../decorators';
import { NodeFactory } from '../runtime';
import { Property } from './property';

export class DateProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DatePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return DateValue.isDate(value);
    }

    // Date have to formated like YYYY-MM-DD
    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return typeof value === 'string' && /^([12]\d{3}[-/](0[1-9]|1[0-2])[-/](0[1-9]|[12]\d|3[01]))$/.test(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        const stringValue = value as string;
        return DateValue.parse(stringValue, 'base', 'YYYY-MM-DD');
    }
}

export class DateRangeProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DateRangePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return DateRange.isDateRange(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        if (typeof value === 'string') {
            try {
                DateRange.parse(value);
                return true;
                // eslint-disable-next-line no-empty
            } catch {}
        }
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return DateRange.parse(value as string);
    }
}

const isValueConvertibleToDatetime = (value: AnyValue): boolean =>
    typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/.test(value);

export class DatetimeProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DatetimePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // Datetime have to be formatted like YYYY-MM-DD[T]HH:mm:ssZ
    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return isValueConvertibleToDatetime(value);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return Datetime.isDatetime(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        const stringValue = value as string;
        return Datetime.parse(stringValue);
    }
}

export class DatetimeRangeProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DatetimeRangePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return DatetimeRange.isDatetimeRange(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        if (typeof value === 'string') {
            try {
                DatetimeRange.parse(value);
                return true;
                // eslint-disable-next-line no-empty
            } catch {}
        }
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return DatetimeRange.parse(value as string);
    }
}

export class TimeProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: TimePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return Time.isTime(value);
    }
}
