import { getLocaleFromHeader } from '@sage/xtrem-i18n';
import { Config } from '@sage/xtrem-shared';
import { Express, Request, Response } from 'express';
import { Application } from '../application';
import { runMiddleware } from '../concurrency-utils';
import { getTenantId } from '../graphql';
import { loggers } from '../runtime/loggers';
import { FileStorageManager } from './file-storage-manager';

const logger = loggers.fileStorage;

/**
 * The base path for downloading files.
 */
export const downloadBasePath = '/download';

/**
 * Adds a download route to the Express app.
 * This route handles the GET request for downloading a file.
 *
 * @param app - The Express app.
 * @param application - The application instance.
 */
export function addDownloadRoute(app: Express, application: Application): void {
    app.get(downloadBasePath, async (req: Request, res: Response): Promise<void> => {
        // use query parameter to avoid caching and ensure that the parameter is not logged
        const encodedTarget = req.query.t;
        if (typeof encodedTarget !== 'string') {
            logger.error(`Failed to get download URL. Bad target type ${typeof encodedTarget}`);
            res.status(404).end();
            return;
        }
        const config = res.locals.config as Config;
        const locale = getLocaleFromHeader(req.headers);

        await application.withReadonlyContext(
            getTenantId(application, config),
            context =>
                runMiddleware(
                    context,
                    async () => {
                        const location = await FileStorageManager.getTargetDownloadUrl(context, encodedTarget);
                        // use 302 to avoid caching and because this is a GET request
                        res.redirect(location);
                    },
                    () => {
                        res.status(404).end();
                    },
                ),
            {
                config,
                locale,
                request: req,
                response: res,
                source: 'rest',
            },
        );
    });
}
