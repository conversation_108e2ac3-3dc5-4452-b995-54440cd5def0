import { AnyValue, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { AuthorizationError, Dict, ErrorWithDiagnoses, TimeoutError, withRethrow } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { Package } from '../../application/package';
import { runResolver } from '../../concurrency-utils';
import { PlainOperationDecorator } from '../../decorators';
import { OperationError } from '../../errors/operation-error';
import {
    Context,
    NodeFactory,
    StandardOperation,
    standardOperations,
    stringifyLogReplacer,
    UserAccess,
} from '../../runtime';
import { loggers } from '../../runtime/loggers';

const logger = loggers.authorization;
const loggerGraphQl = loggers.graphQl;

export interface AccessRightsOptions {
    factory: {
        name: string;
    };
    args?: Dict<any>;
    operation?: StandardOperation | string;
    /** Is this a mutation */
    isMutation?: boolean;
    /** Is this a property access operation */
    isPropertyAccess?: boolean;
    operationAction?: string;
}

interface CheckOptions {
    nodeName: string;
    operationName: StandardOperation | string;
    args?: Dict<any>;
    isPropertyAccess?: boolean;
    isMutation?: boolean;
    operationAction?: string;
}

interface OperationAccessOptions {
    nodeName: string;
    operationName: string;
    args?: Dict<any>;
    operationAction?: string;
    bypassAuthorizedBy?: boolean;
    parentNodeName?: string;
}

/**
 * @disabled_internal
 *
 * Static class which provides methods to enforce access rights
 */
export class AccessRights {
    static runSecure<T extends AnyValue>(
        context: Context,
        operation: StandardOperation | string,
        options: AccessRightsOptions,
        body: () => AsyncResponse<T>,
    ): Promise<T> {
        // TODO: operation may be the name of a property or operation, we need to improve the parameter name in the future
        logger.debug(() => `runSecure -> operation: ${operation}`);
        return CustomMetrics.graphql.withMetrics(
            {
                nodeName: options.factory.name,
                operationName: operation,
                operationKind: options.isMutation ? 'mutation' : 'query',
            },
            () =>
                runResolver(context, async () => {
                    await withRethrow(
                        async () => {
                            if (options.isMutation) AccessRights.checkNotReadonly();
                            await AccessRights.checkOperationAccessRight(context, {
                                nodeName: options.factory.name,
                                // give priority to options.operation for access rights
                                operationName: options.operation || operation,
                                args: options.args ?? {},
                                isPropertyAccess: !!options.isPropertyAccess,
                                isMutation: !!options.isMutation,
                                operationAction: options.operationAction,
                            });
                        },
                        err =>
                            new OperationError(
                                context,
                                context.application.getFactoryByName(options.factory.name),
                                // give priority to operation for error message
                                operation ?? options.operation,
                                err instanceof ErrorWithDiagnoses
                                    ? (err.extensions.diagnoses ?? [])
                                    : [
                                          {
                                              severity: 4,
                                              message: err.message,
                                              path: [],
                                          },
                                      ],
                                err,
                            ),
                    );

                    // We have the possibility to limit the life span of a graphQL operation
                    // therefore we need to check at every runSecure cycle if the time limit has been exceeded
                    if (context.timeLimitAsTimestamp <= performance.now()) {
                        throw new TimeoutError('Request Timeout');
                    }
                    try {
                        return await body();
                    } catch (err) {
                        AccessRights.logInputData(context, options);
                        throw err;
                    }
                }),
        );
    }

    static isPackageAvailable(pack: Package, activePackages: string[]): boolean {
        return (
            ConfigManager.current.storage?.managedExternal ||
            activePackages[0] === 'all' ||
            activePackages.includes(pack.name)
        );
    }

    private static checkNotReadonly(): void {
        if (ConfigManager.current.graphql!.isReadonly) {
            // TODO: XT-859 we don't have the context to localize the error.
            throw new AuthorizationError('Mutations are not allowed. Endpoint is readonly');
        }
    }

    static async isOperationAvailable(context: Context, options: CheckOptions): Promise<boolean> {
        const { operationName, nodeName } = options;
        const isServiceOptionEnabled = context.isServiceOptionEnabled.bind(context);

        const factory = context.application.getFactoryByName(nodeName);
        if (factory.serviceOptions && !(await asyncArray(factory.serviceOptions).every(isServiceOptionEnabled))) {
            return false;
        }

        if (options.isMutation) {
            const mutation = factory.mutations.find(decorator => decorator.name === operationName);
            return asyncArray(mutation?.serviceOptions?.() || []).every(isServiceOptionEnabled);
        }

        const query = factory.queries.find(decorator => decorator.name === operationName);
        return asyncArray(query?.serviceOptions?.() || []).every(isServiceOptionEnabled);
    }

    /**
     * Get authorizedBy access for a given operation
     * First check on operation level, then on factory level
     * @param context
     * @param factory
     * @param operation
     * @param operationName
     * @param args
     * @param operationAction
     * @returns
     */
    static getAuthorizedByAccess(
        context: Context,
        factory: NodeFactory,
        operation: PlainOperationDecorator | undefined,
        operationName: string,
        args?: Dict<any>,
        operationAction?: string,
    ): AsyncResponse<UserAccess | undefined> {
        // First check if the operation has an authorizedBy function
        if (operation?.authorizedBy) {
            const authorizedBy = (factory.nodeConstructor as any)[operation.authorizedBy];
            if (typeof authorizedBy !== 'function') {
                throw factory.logicError(`Authorization query ${operationName} is not a function`);
            }
            // TODO: we need to improve this code to handle the bulk mutations case

            // We need to fill args with the parameters of the operation and fill the optional ones with undefined
            // before applying the authorizedBy function
            const fillArgs = operation.parameters.map(param => (args ? args[param.name] : undefined));

            return authorizedBy.apply(factory.nodeConstructor, [
                { action: operationAction, trackingId: args?.trackingId },
                context,
                ...fillArgs,
            ]);
        }

        // If no authorizedBy function for operation, we check if the factory has a default one
        if (factory.authorizedBy) {
            return factory.authorizedBy(context, operationName, args);
        }

        return undefined;
    }

    private static async getUserAccessOnOperation(
        context: Context,
        { nodeName, operationName, args, operationAction, bypassAuthorizedBy }: OperationAccessOptions,
    ): Promise<UserAccess> {
        const factory = context.application.getFactoryByName(nodeName);
        const isStandardOperation = standardOperations.some(standardOperation => standardOperation === operationName);
        const operation = isStandardOperation ? undefined : factory.getOperationByName(operationName, operationAction);

        const authorizedByAccess = bypassAuthorizedBy
            ? undefined
            : await AccessRights.getAuthorizedByAccess(
                  context,
                  factory,
                  operation,
                  operationName,
                  args ?? {},
                  operationAction,
              );
        if (authorizedByAccess) return authorizedByAccess;

        let userAccess = await Context.accessRightsManager.getUserAccessFor(context, nodeName, operationName);
        if (!userAccess || userAccess.status !== 'authorized') {
            // Non standard operation, check custom queries for isGrantedByLookup
            if (!isStandardOperation) {
                const query = operation?.operationKind === 'query' ? operation : undefined;
                if (query && query.isGrantedByLookup) {
                    userAccess = await Context.accessRightsManager.getUserAccessFor(context, nodeName, 'lookup');
                }
            }

            // Resolve read as lookup if lookupAccess properties present on node and read is not denied by denyReadOnLookupOnlyAccess
            if (operationName === 'read' && !factory.denyReadOnLookupOnlyAccess && factory.hasLookupAccess) {
                userAccess = await Context.accessRightsManager.getUserAccessFor(context, nodeName, 'lookup');
            }
        }
        return userAccess;
    }

    static mergeUserAccess(userAccess1: UserAccess, userAccess2: UserAccess): UserAccess {
        if (userAccess1.status === 'unavailable') return userAccess1;
        if (userAccess2.status === 'unavailable') return userAccess2;

        if (userAccess1.status === 'inactive') return userAccess1;
        if (userAccess2.status === 'inactive') return userAccess2;

        if (userAccess1.status === 'unauthorized') return userAccess1;
        if (userAccess2.status === 'unauthorized') return userAccess2;

        // We are left with 'authorized' or 'readonly'
        const status =
            userAccess1.status === 'authorized' && userAccess2.status === 'authorized' ? 'authorized' : 'readonly';
        const intersect = (a: string[] | null, b: string[] | null): string[] | null => {
            if (a == null) return b;
            if (b == null) return a;
            return _.intersection(a, b);
        };
        const sites = intersect(userAccess1.sites, userAccess2.sites);
        const accessCodes = intersect(userAccess1.accessCodes, userAccess2.accessCodes);
        return { status, sites, accessCodes };
    }

    static async getUserAccessOnOperations(
        context: Context,
        operations: OperationAccessOptions[],
    ): Promise<UserAccess> {
        const result = await asyncArray(operations).reduce(
            async (acc, operation) => {
                const userAccess = await AccessRights.getUserAccessOnOperation(context, operation);
                return acc == null ? userAccess : this.mergeUserAccess(acc, userAccess);
            },
            null as UserAccess | null,
        );

        if (result == null) {
            return { status: 'unauthorized', sites: null, accessCodes: null };
        }

        return result;
    }

    static async checkOperationAccessRight(context: Context, options: CheckOptions): Promise<void> {
        if (options.isPropertyAccess) return;

        const { nodeName, operationName, args, operationAction } = options;

        if (
            !(await AccessRights.isOperationAvailable(context, {
                nodeName,
                operationName,
                args,
                isMutation: !!options.isMutation,
                isPropertyAccess: options.isPropertyAccess,
            }))
        ) {
            await logger.infoAsync(
                () =>
                    `Operation ${nodeName}.${operationName}' is not enabled by the current configuration of the application`,
            );
            // TODO: this is not an "authorization" error as it applies to all users.
            throw new AuthorizationError(
                context.localize(
                    '@sage/xtrem-core/operation-not-enabled',
                    'Operation {{nodeName}}.{{operationName}} is not enabled by the configuration of the application',
                    {
                        nodeName,
                        operationName,
                    },
                ),
            );
        }

        const userAccess = await AccessRights.getUserAccessOnOperation(context, {
            nodeName,
            operationName,
            args,
            operationAction,
        });

        const user = await context.user;
        if (user && (!userAccess || userAccess.status !== 'authorized')) {
            throw new AuthorizationError(
                context.localize(
                    '@sage/xtrem-core/user-not-allowed-operation',
                    'You cannot perform this operation {{nodeName}}.{{operation}}',
                    {
                        nodeName,
                        operation: operationName,
                    },
                ),
            );
        }
        context.setAllowedAccessCodes(userAccess);
    }

    private static logInputData(context: Context, options: AccessRightsOptions): void {
        if (options.args && context.request) {
            loggerGraphQl.error(`input data: ${JSON.stringify(options.args, stringifyLogReplacer)}`);
        }
    }
}
