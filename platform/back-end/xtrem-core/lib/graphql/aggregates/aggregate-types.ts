import { asyncArray } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import {
    ArgumentNode,
    FieldNode,
    GraphQLBoolean,
    GraphQLFloat,
    GraphQLInt,
    GraphQLObjectType,
    GraphQLResolveInfo,
    GraphQLString,
    GraphQLType,
    Kind,
    SelectionNode,
} from 'graphql';
import { Context } from '../../runtime';
import { NodeFactory } from '../../runtime/node-factory';
import { Aggregate, AggregateGroup, AggregateValue, GroupBySelectorDataType, ValuesOperator } from '../../ts-api';
import {
    dateRangeType,
    dateType,
    datetimeRangeType,
    datetimeType,
    decimalRangeType,
    decimalType,
    integerArrayType,
    integerRangeType,
    makeCustomEnumOutputType,
    stringArrayType,
    timeType,
} from '../types/basic-types';
import { NodeType } from '../types/node-type';
import { GraphQlProperty, PropertyType } from '../types/property-type';
import { TypeCache } from '../utils/type-cache';

export const groupByVerbsEnumType = makeCustomEnumOutputType('Group_Verbs_Enum', GroupBySelectorDataType);

/**
 * @internal
 *
 * Static class which provides GraphQL types used by readAggregate and queryAggregate operations.
 */
export class AggregateTypes {
    // Creates GraphQL type for aggregate on non numeric types
    // Returned type always includes `distinctCount`
    // It also includes `min` and `max` if the type is comparable (if `comparableType` parameter is passed)
    private static makeNonNumericAggregateType(typeName: string, comparableType?: GraphQLType): GraphQLObjectType {
        const typeData: any = {
            description: `Aggregate type for ${typeName}`,
            name: `AggregateTypeFor${typeName}Property`,
            fields: {
                distinctCount: { type: GraphQLInt },
            },
        };

        if (comparableType) {
            typeData.fields.min = { type: comparableType };
            typeData.fields.max = { type: comparableType };
            typeData.fields.hasNull = { type: GraphQLBoolean };
        }

        return new GraphQLObjectType(typeData);
    }

    // GraphQL types for all the types that we accept in aggregate queries
    private static aggregateTypes = {
        int: new GraphQLObjectType({
            description: 'Aggregate type for int',
            name: 'AggregateTypeForIntProperty',
            fields: {
                min: { type: GraphQLInt },
                max: { type: GraphQLInt },
                avg: { type: GraphQLFloat },
                sum: { type: GraphQLInt },
                distinctCount: { type: GraphQLInt },
                hasNull: { type: GraphQLBoolean },
            },
        }),
        integerRange: AggregateTypes.makeNonNumericAggregateType('IntegerRange', integerRangeType),
        decimalRange: AggregateTypes.makeNonNumericAggregateType('DecimalRange', decimalRangeType),
        integerArray: AggregateTypes.makeNonNumericAggregateType('IntegerArray', integerArrayType),
        decimal: new GraphQLObjectType({
            description: 'Aggregate type for decimals',
            name: 'AggregateTypeForDecimalProperty',
            fields: {
                min: { type: decimalType },
                max: { type: decimalType },
                avg: { type: decimalType },
                sum: { type: decimalType },
                distinctCount: { type: GraphQLInt },
                hasNull: { type: GraphQLBoolean },
            },
        }),
        date: AggregateTypes.makeNonNumericAggregateType('Date', dateType),
        dateRange: AggregateTypes.makeNonNumericAggregateType('DateRange', dateRangeType),
        datetimeRange: AggregateTypes.makeNonNumericAggregateType('DatetimeRange', datetimeRangeType),
        time: AggregateTypes.makeNonNumericAggregateType('Time', timeType),
        datetime: AggregateTypes.makeNonNumericAggregateType('Datetime', datetimeType),
        boolean: AggregateTypes.makeNonNumericAggregateType('Boolean', GraphQLBoolean),
        string: AggregateTypes.makeNonNumericAggregateType('String', GraphQLString),
        stringArray: AggregateTypes.makeNonNumericAggregateType('StringArray', stringArrayType),
        uuid: AggregateTypes.makeNonNumericAggregateType('Uuid'), // No comparableType: will only have distinctCount operator
        enum: (typeCache: TypeCache, prop: GraphQlProperty) => {
            const enumType = PropertyType.makePropertyGraphQLEnumType(typeCache, undefined, prop, 'nodeInput');
            const typeName = `Enum_${(enumType as any).name}`;
            return typeCache.internType(`AggregateTypeFor${typeName}Property`, () => {
                return AggregateTypes.makeNonNumericAggregateType(typeName, enumType);
            });
        },
        enumArray: (typeCache: TypeCache, prop: GraphQlProperty) => {
            const enumArrayType = PropertyType.makePropertyGraphQLEnumType(typeCache, undefined, prop, 'nodeInput');
            const typeName = `EnumArray_${(enumArrayType as any).name}`;
            return typeCache.internType(`AggregateTypeFor${typeName}Property`, () => {
                return AggregateTypes.makeNonNumericAggregateType(typeName);
            });
        },
        referenceArray: AggregateTypes.makeNonNumericAggregateType('ReferenceArray'),
        json: new GraphQLObjectType({
            description: 'Aggregate type for json',
            name: 'AggregateTypeForJsonProperty',
            fields: {
                min: { type: decimalType },
                max: { type: decimalType },
                avg: { type: decimalType },
                sum: { type: decimalType },
                distinctCount: { type: GraphQLInt },
                hasNull: { type: GraphQLBoolean },
            },
        }),
    };

    private static parseAstArgs(argNodes: readonly ArgumentNode[]): Dict<any> {
        return argNodes.reduce((result, arg) => {
            const argName = arg.name.value;
            const argValue = (arg.value as any).value;
            result[argName] = argValue;
            return result;
        }, {} as Dict<any>);
    }

    private static parseAstNodes(astNodes: readonly SelectionNode[]): {
        values: Dict<any>;
        containsAggregation: boolean;
    } {
        let containsAggregation = false;
        // This function maps the AST to a javascript object (easier to parse)
        const vals = astNodes.reduce((total, astNode) => {
            if (astNode.kind === Kind.FIELD) {
                const fieldNode = astNode as any as FieldNode;
                // If a field is referenced more than once in the selector, each instance will have an alias.
                // We can use this when scanning the ast and constructung the tree.
                // e.g. the JSON property _customData could have to fields in the request
                // _customData(selector:"foo"){ distincCount } and _customData(selector:"foo"){ distinctCount}
                // Graphql will not allow these fields in the request unless we alias the fields.
                // We then use the alias as part of the key we use to store the value in the result of the scan
                // capturing both instances of _customData.
                const fieldKey = fieldNode.alias
                    ? `${fieldNode.name.value}|${fieldNode.alias.value}`
                    : fieldNode.name.value;
                const fieldName = fieldNode.name.value;
                if (fieldNode.selectionSet) {
                    const subResult = this.parseAstNodes(fieldNode.selectionSet.selections);
                    if (subResult.containsAggregation) {
                        const mapped = Object.keys(subResult.values).reduce(
                            (t, operator) => {
                                t.$aggregations[operator] = true;
                                return t;
                            },
                            {
                                $aggregations: {} as Dict<any>,
                            },
                        );
                        subResult.values = mapped;
                    }
                    total[fieldKey] = subResult.values;
                    if (fieldName === '_customData' && fieldNode.arguments)
                        total[fieldKey].__args = this.parseAstArgs(fieldNode.arguments);
                } else if (['min', 'max', 'avg', 'sum', 'distinctCount', 'hasNull'].includes(fieldName)) {
                    // The node is a leaf (no children : fieldNode.selectionSet === undefined)
                    // and its name matches an aggregation operator
                    total[fieldKey] = true;
                    containsAggregation = true;
                } else if (fieldNode.arguments) {
                    total[fieldKey] = { __args: this.parseAstArgs(fieldNode.arguments) };
                }
            }
            return total;
        }, {} as Dict<any>);
        return { values: vals, containsAggregation };
    }

    /**
     * Extract groups and values of aggregate nodes
     * @param context
     * @param factory
     * @param rootNode
     * @param rootPath
     * @param contextVerb
     * @returns
     */
    private static async parseAggregateNode(
        context: Context,
        factory: NodeFactory,
        rootNode: Dict<any>,
        rootPath: string[],
        contextVerb: 'nodeValues' | 'edgesValues' | 'group',
    ): Promise<{ groups: AggregateGroup[]; values: AggregateValue[] }> {
        const groups: AggregateGroup[] = [];
        const values: AggregateValue[] = [];
        await asyncArray(Object.keys(rootNode)).forEach(async p => {
            if (p === '__args') return;
            // The key may be a concatenation of a propertyName and alias.
            // split the key by the | seperator to get the property name as the first element.
            const propName = p.split('|')[0];

            const node = rootNode[p];
            const path = rootPath ? [...rootPath, propName] : [propName];
            const property = factory.findProperty(propName);
            const forbiddenProperty =
                !(await property.isEnabledByServiceOptions(context)) || !(await property.isAuthorized(context));
            if (forbiddenProperty) {
                context.addRestrictedPropertyDiagnose(path);
                return;
            }

            if (property.type === 'json') {
                if (typeof node.__args?.selector !== 'string')
                    throw property.logicError('selector arg missing on json property');

                if (property.name === '_customData' && !context.customFields[factory.fullName]) {
                    await NodeType.resolveCustomFields(context, factory, property);
                }
                path.push(node.__args.selector);
            }

            if (node.$aggregations && (contextVerb === 'nodeValues' || contextVerb === 'edgesValues')) {
                Object.keys(node.$aggregations).forEach(operator => {
                    values.push({
                        path,
                        operator: operator as ValuesOperator,
                    });
                });
            } else if (node.__args && contextVerb === 'group') {
                // A leaf with arguments
                groups.push({
                    path,
                    groupedBy: node.__args.by || 'value',
                });
            } else if (property.isReferenceProperty()) {
                const referenceResult = await this.parseAggregateNode(
                    context,
                    property.targetFactory,
                    node,
                    path,
                    contextVerb,
                );
                groups.push(...referenceResult.groups);
                values.push(...referenceResult.values);
            } else throw new Error(`Multi-level aggregates can only rely on references ${rootPath}`);
        });

        return { groups, values };
    }

    /**
     * Parses the AST of the GraphQL request to extract the groups and values.
     */
    static async parseAggregate(
        context: Context,
        ast: GraphQLResolveInfo,
        rootFactory: NodeFactory,
    ): Promise<Aggregate> {
        const groups: AggregateGroup[] = [];
        const values: AggregateValue[] = [];
        const fillResult = async (
            rootNode: Dict<any>,
            rootPath: string[],
            contextVerb: 'nodeValues' | 'edgesValues' | 'group',
        ): Promise<boolean> => {
            const result = await this.parseAggregateNode(context, rootFactory, rootNode, rootPath, contextVerb);
            groups.push(...result.groups);
            values.push(...result.values);
            // dummy return to please sonar cloud
            return true;
        };

        const astAsObj = this.parseAstNodes(ast.fieldNodes).values;
        // the root of astNode is the name of the node to query (sth like datatypes, referring, ...)
        const aggregateNode = Object.keys(astAsObj).length && astAsObj[Object.keys(astAsObj)[0]];
        // note : total[fieldName] is already a javascript object, not an AST node
        if (astAsObj.queryAggregate) {
            if (!aggregateNode.edges) {
                throw new Error("'edges' is missing under 'queryAggregate'");
            }
            if (!aggregateNode.edges.node) {
                throw new Error("'node' is missing under 'queryAggregate/edges'");
            }
            if (!aggregateNode.edges.node.group)
                throw new Error("'group' is missing under 'queryAggregate/edges/node'");

            await fillResult(aggregateNode.edges.node.group, [], 'group');
            if (aggregateNode.edges.node.values) {
                await fillResult(aggregateNode.edges.node.values, [], 'edgesValues');
            }
        } else if (astAsObj.readAggregate) {
            await fillResult(aggregateNode, [], 'nodeValues');
        } else {
            throw new Error(`unsupported ast node name '${ast.fieldName}'`);
        }

        if (groups.length === 0 && values.length === 0) {
            throw new Error('Internal error: AST parse did not find groups or values');
        }
        return { groups, values };
    }

    /**
     * Get the GraphQL value type given a property decorator.
     *
     * Returns the GraphQL type or undefined if the property cannot be aggregated.
     */
    static getAggregateType(typeCache: TypeCache, prop: GraphQlProperty): GraphQLType | undefined {
        switch (prop.type) {
            case 'boolean':
                return this.aggregateTypes.boolean;
            case 'decimal':
            case 'float':
            case 'double':
                return this.aggregateTypes.decimal;
            case 'byte':
            case 'integer':
            case 'short':
                return this.aggregateTypes.int;
            case 'string':
            case 'date':
            case 'dateRange':
            case 'datetimeRange':
            case 'integerRange':
            case 'decimalRange':
            case 'time':
            case 'datetime':
            case 'uuid':
            case 'integerArray':
            case 'referenceArray':
            case 'stringArray':
            case 'json':
                return this.aggregateTypes[prop.type];
            case 'enumArray':
                return this.aggregateTypes.enumArray(typeCache, prop);
            case 'enum':
                return this.aggregateTypes.enum(typeCache, prop);
            default:
                // Other types won't have any (min, max, ...) aggregators
                return undefined;
        }
    }
}
