import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Value, asyncArray } from '@sage/xtrem-async-helper';
import { cloneDeep, omit } from 'lodash';
import { Context, NodeFactory } from '../../runtime';
import { friendlyJsonParse } from '../../runtime/node-factory-utils';
import { AnyOrderBy, Node, OrderBy } from '../../ts-api';
import { NodeType } from '../types/node-type';

/**
 * @internal
 *
 * Static class which provides the parser for query orderBy arguments
 */
export class PagingOrderBy {
    // Gets a list of all restricted properties belonging to the given node and all its children.
    // This is a recursive function which optionally takes an object as a third argument to limit
    // the recursive process by checking only the set of properties that map to existing object keys.
    private static async getAllForbiddenPropertiesFromRootNode(
        context: Context,
        factory: NodeFactory,
        obj: any = null,
        parentPath: string[] = [],
    ): Promise<Array<string[]>> {
        let forbiddenProperties: Array<string[]> = [];
        const filteredProperties = obj
            ? factory.properties.filter(property => Object.keys(obj).includes(property.name))
            : factory.properties;
        await asyncArray(filteredProperties).forEach(async property => {
            if (!(await property.isEnabledByServiceOptions(context)) || !(await property.isAuthorized(context))) {
                forbiddenProperties.push([...parentPath, property.name]);
                return;
            }
            if (property.isForeignNodeProperty()) {
                const nestedPropertyFactory = property.targetFactory;
                forbiddenProperties = [
                    ...forbiddenProperties,
                    ...(await PagingOrderBy.getAllForbiddenPropertiesFromRootNode(
                        context,
                        nestedPropertyFactory,
                        obj ? obj[property.name] : null,
                        [...parentPath, property.name],
                    )),
                ];
            }
        });
        return forbiddenProperties;
    }

    // Extract the properties that the API client is not allowed to see.
    private static async extractAllowedOrderByProperties(
        context: Context,
        factory: NodeFactory,
        orderBy: AnyOrderBy,
    ): Promise<AnyOrderBy> {
        const restrictedProperties = await PagingOrderBy.getAllForbiddenPropertiesFromRootNode(
            context,
            factory,
            orderBy,
        );
        let allowedOrderBy = cloneDeep(orderBy);
        restrictedProperties.forEach(path => {
            context.addRestrictedOrderByDiagnose(path);
            allowedOrderBy = omit(orderBy, path);
        });
        return allowedOrderBy;
    }

    static async resolveOrderByCustomFields(context: Context, factory: NodeFactory, orderBy: AnyValue): Promise<void> {
        if (!orderBy || typeof orderBy !== 'object') return;
        await asyncArray(Object.keys(orderBy)).forEach(async key => {
            const property = factory.findProperty(key);
            if (property.isReferenceProperty()) {
                await this.resolveOrderByCustomFields(context, property.targetFactory, (orderBy as AnyRecord)[key]);
            } else if (key === '_customData') await NodeType.resolveCustomFields(context, factory, property);
        });
    }

    /**
     * Parses the 'orderBy' clauses provided by the client.
     * @param context
     * @param factory
     * @param jsonOrderBy 'orderBy' clause from the client
     * @param defaultOrderBy the 'orderBy' to use if no 'jsonOrderBy' is provided
     */
    static async parseOrderBy(
        context: Context,
        factory: NodeFactory,
        jsonOrderBy: string | undefined,
        defaultOrderBy: AnyOrderBy | undefined,
    ): Promise<OrderBy<Node> | undefined> {
        const orderBy = jsonOrderBy ? friendlyJsonParse(jsonOrderBy) : defaultOrderBy;
        if (orderBy === undefined) return undefined;

        if (jsonOrderBy?.includes('_customData')) await this.resolveOrderByCustomFields(context, factory, orderBy);

        const allowedOrderBy = await PagingOrderBy.extractAllowedOrderByProperties(
            context,
            factory,
            orderBy as AnyOrderBy,
        );

        if (!('_id' in allowedOrderBy) && defaultOrderBy) {
            // append the primary key (or the default orderBy) properties to ensure uniqueness
            // TODO: optimize by checking if there is a match with a unique index other than primary.
            const clauses = Object.keys(defaultOrderBy).map(propertyName => {
                const property = factory.findProperty(propertyName);
                return {
                    property,
                    directionOrSubOrder: defaultOrderBy[propertyName]!,
                };
            });
            clauses
                .filter(clause => !(clause.property.name in allowedOrderBy))
                .forEach(clause => {
                    allowedOrderBy[clause.property.name] = clause.directionOrSubOrder as any;
                });
        }

        return allowedOrderBy;
    }
}
