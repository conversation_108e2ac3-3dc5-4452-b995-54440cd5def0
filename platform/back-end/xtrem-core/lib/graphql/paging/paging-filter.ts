import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Value, asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse, DataInputError, Dict } from '@sage/xtrem-shared';
import { Converter, GenericConversionResult } from '@sage/xtrem-ts-to-sql';
import * as _ from 'lodash';
import { intersection, omit } from 'lodash';
import { Property } from '../../properties';
import { Context, NodeFactory, isCompound, isScalar } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { friendlyJsonParse } from '../../runtime/node-factory-utils';
import { AnyFilterObject, Node, NodeQueryFilter } from '../../ts-api';
import { PropertyType } from '../types/property-type';

/**
 * @internal
 *
 * Static class which provides the parser for query filter arguments
 */
export class PagingFilter {
    // Names of properties that are not allowed or not available.
    private static getRestrictedPropertyNames(
        context: Context,
        factory: NodeFactory,
        propertyList: string[],
    ): Promise<string[]> {
        return asyncArray(factory.properties.filter(property => propertyList.includes(property.name)))
            .filter(
                async property =>
                    !(await property.isEnabledByServiceOptions(context)) || !(await property.isAuthorized(context)),
            )
            .map(property => property.name)
            .toArray();
    }

    // Names of properties that the API client is allowed to see.
    private static async extractAllowedFilterProperties(
        context: Context,
        factory: NodeFactory,
        filter: AnyRecord,
        parentPath: string[] = [],
    ): Promise<AnyRecord> {
        const restrictedProperties = await PagingFilter.getRestrictedPropertyNames(
            context,
            factory,
            Object.keys(filter),
        );
        const restrictedFilters = intersection(Object.keys(filter), restrictedProperties).map(restrictedProperty => [
            ...parentPath,
            restrictedProperty,
        ]);
        restrictedFilters.forEach(restrictedFiltersWithPaths =>
            context.addRestrictedFilterDiagnose(restrictedFiltersWithPaths),
        );
        return omit(filter, restrictedProperties);
    }

    private static verifyFunctionValue(context: Context, factory: NodeFactory, value: string): AsyncResponse<boolean> {
        const paths: string[] = [];
        const converter = new Converter(
            context,
            factory,
            {
                resolveColumnName(
                    _cx: Context,
                    parent: GenericConversionResult<NodeFactory, Property>,
                    propertyName: string,
                ): GenericConversionResult<NodeFactory, Property> {
                    const property = parent.factory.propertiesByName[propertyName];
                    return {
                        path: parent.path ? `${parent.path}.${propertyName}` : propertyName,
                        alias: parent.alias,
                        factory: parent.factory,
                        type: property?.type || 'unknown',
                        property,
                        sql: propertyName,
                    };
                },
                resolveTableName(f: NodeFactory): string {
                    return f.table?.sqlTableName || f.tableName || f.name;
                },
                resolveLiteral(v: AnyValue): string {
                    return typeof v === 'string' ? v : `${v}`;
                },
            },
            {
                dialect: 'postgres',
                beforeWalkCallback: p => {
                    paths.push(p);
                },
            },
        );

        // Using a simple SQL converter and a callback before the walk to get the paths of properties used in the _fn filter
        converter.convertFunctionBody(converter.convertThisExpression(), value);

        if (paths.length > 0) {
            const object = paths.reduce((r, k) => {
                if (_.get(r, k) != null) return r;
                _.set(r, k, {});
                return r;
            }, {} as Dict<any>);

            const walker = (obj: Dict<any>, currentFactory: NodeFactory): AsyncResponse<boolean> => {
                if (_.isEmpty(obj)) return true;

                return asyncArray(Object.keys(obj)).every(async key => {
                    const prop = currentFactory.propertiesByName[key];
                    // If the current element is not a property, we can skip it, it will be validated later
                    // when the real converter tries to convert this function to SQL
                    if (!prop) {
                        // We may be using a collection, or something complex like that
                        // TODO: See if we can improve this later.
                        if (typeof obj[key] === 'object') {
                            return walker(obj[key], currentFactory);
                        }
                        return true;
                    }
                    if ((await prop.isEnabledByServiceOptions(context)) && (await prop.isAuthorized(context))) {
                        // If the property is a foreign node property, and properties of it target factory are used
                        // we need to validate the target factory properties as well.
                        if (typeof obj[key] === 'object' && prop.isForeignNodeProperty()) {
                            return walker(obj[key], prop.targetFactory);
                        }
                        return true;
                    }

                    // Property used in the function is restricted by service option or authorization
                    return false;
                });
            };

            return walker(object, factory);
        }

        return false;
    }

    // Trims the filter to keep only the properties that the API client is allowed to see.
    private static applyAllowedFilters(
        context: Context,
        factory: NodeFactory,
        allowedFilters: AnyRecord,
        parentPath: string[],
        property?: Property,
    ): Promise<AnyFilterObject> {
        return asyncArray(Object.keys(allowedFilters)).reduce(async (r, k) => {
            const val = allowedFilters[k] as AnyRecord;
            const prop = factory.propertiesByName[k];

            let propertyFilter;
            if (!prop) {
                if (k[0] !== '_') throw new DataInputError(`${factory.name}.${k}: Property not found`);
                if (k === '_regex' || k === '_options') {
                    if (!isScalar(val)) throw new DataInputError(`Invalid value for ${k} on ${parentPath.join('.')}`);

                    propertyFilter = String(val);
                } else if (k === '_fn') {
                    if (await this.verifyFunctionValue(context, factory, allowedFilters[k] as string)) {
                        propertyFilter = String(val);
                    } else {
                        context.addRestrictedFilterDiagnose([...parentPath, k]);
                    }
                } else {
                    propertyFilter = await PagingFilter.convertFilter(
                        context,
                        factory,
                        val,
                        [...parentPath, k],
                        property,
                    );
                }
            } else if (
                (prop.isReferenceProperty() && val && isCompound(val)) ||
                prop.isCollectionProperty() ||
                prop.isReferenceArrayProperty()
            ) {
                const targetFactory = prop.targetFactory;
                if (prop.isCollectionProperty() && targetFactory.storage === 'sql' && !prop.reverseReference)
                    throw prop.inputError(
                        'Nested filter on collection property without reverse reference is not supported',
                    );
                propertyFilter = await PagingFilter.convertFilter(context, targetFactory, val, [...parentPath, k]);
            } else {
                propertyFilter = await PagingFilter.convertFilter(context, factory, val, [...parentPath, k], prop);
            }

            if (propertyFilter !== undefined) r[k] = propertyFilter;
            return r;
        }, {} as AnyFilterObject);
    }

    // Recursive function to convert a filter item
    private static async convertFilter(
        context: Context,
        factory: NodeFactory,
        filter: AnyValue,
        parentPath: string[] = [],
        property?: Property,
    ): Promise<AnyValue> {
        if (!filter) return filter;
        if (Array.isArray(filter)) {
            if (filter.length === 0) return [];
            const arrayOfResult = (
                await asyncArray(filter)
                    .map((val, i) =>
                        PagingFilter.convertFilter(context, factory, val, [...parentPath, String(i)], property),
                    )
                    .toArray()
            ).filter(element => element !== undefined);
            return arrayOfResult.length === 0 ? undefined : arrayOfResult;
        }
        if (Object.keys(filter).find(key => key === '_customData')) {
            const customFields = await CoreHooks.customizationManager?.getCustomFields(context, [factory.fullName]);
            if (customFields) Object.assign(context.customFields, customFields);
        }
        if (isScalar(filter) || (property?.isArrayProperty() && Array.isArray(filter))) {
            if (property) return PropertyType.getInputValue(context, filter, factory, property);
            return filter;
        }
        if (
            property?.type &&
            ['json', 'date', 'dateRange', 'datetimeRange', 'integerRange', 'decimalRange'].includes(property.type)
        ) {
            return filter;
        }
        const allowedFilters = await PagingFilter.extractAllowedFilterProperties(
            context,
            factory,
            filter as AnyRecord,
            parentPath,
        );
        const result = await PagingFilter.applyAllowedFilters(context, factory, allowedFilters, parentPath, property);
        if (parentPath.length !== 0 && Object.keys(result).length === 0) return undefined;
        return result;
    }

    /**
     * Converts a filter argument that was passed through the `filter` parameter of a GraphQL query
     *
     * This method maps the filter keys to properties of the node factory.
     * It converts the filter values to TS types (Decimal, DateValue, Datetime, ...), based on the property types.
     * It also filters out filter elements that the API client is not authorized to see.
     */
    static async parseFilter(
        context: Context,
        factory: NodeFactory,
        filter: string | undefined,
    ): Promise<NodeQueryFilter<Node, Node>> {
        const parsedFilter = filter && friendlyJsonParse(filter);
        // Add additional check, to throw error if filter is wrongly defined with ""filterQuery""
        // Parsed filter should return as an object
        if (typeof parsedFilter === 'string') {
            const message = 'Invalid filter (parsed value is a string, expected an object): "{{filter}}"';
            const localized = context
                .localize('@sage/xtrem-core/invalid-filter', message, {
                    filter: `"${parsedFilter}"`,
                })
                .replace(/&quot;/g, '"')
                .replace(/&#x27;/g, "'");
            throw new DataInputError(localized);
        }

        return (await PagingFilter.convertFilter(context, factory, parsedFilter)) as NodeQueryFilter<Node, Node>;
    }
}
