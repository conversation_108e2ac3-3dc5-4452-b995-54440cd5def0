import { FieldNode, GraphQLFieldConfig, GraphQLInterfaceType, GraphQLObjectType, Kind } from 'graphql';
import { BaseCollection } from '../../collections';
import { Context, NodeFactory } from '../../runtime';
import { Paging, QueryArgs } from '../paging/paging';
import { AccessRights } from '../security/access-rights';
import { TypeCache } from '../utils/type-cache';
import { NodeQuery } from './node-query';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for collection queries on a node:
 *
 * ``` graphql
 *  query { xtremFoo { Bar { query { edges { node { lines { query(...) { ... } } } } } } } }`
 *  ```
 */
export class CollectionQuery {
    /** Resolver for `query { xtremFoo { Bar { query { edges { node { lines { query(...) { ... } } } } } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLFieldConfig<BaseCollection, Context, QueryArgs> {
        return {
            type: NodeQuery.makeType(typeCache, factory, nodeType),
            args: Paging.getPagingArgumentTypes(),
            resolve(collection, args, _context, info) {
                const context = collection.sourceNode.$.context;
                return AccessRights.runSecure(context, 'read', { factory }, async () => {
                    const defaultOrderBy = args.orderBy
                        ? collection.factory.defaultOrderBy
                        : {
                              ...collection.orderBy,
                              ...collection.factory.defaultOrderBy,
                          };

                    const pagingOpts = await Paging.parsePagingOptions(
                        context,
                        collection.factory,
                        args,
                        info,
                        defaultOrderBy,
                    );
                    const selections =
                        info.fieldNodes[0].selectionSet?.selections
                            .filter(field => field.kind === Kind.FIELD)
                            .map((field: FieldNode) => field.name.value) || [];

                    const options = {
                        ...pagingOpts,
                        first: pagingOpts.first && pagingOpts.first + 1,
                        last: pagingOpts.last && pagingOpts.last + 1,
                        selections,
                    };
                    if (!options.first && !options.last) throw new Error('page size missing in collection.readPage');

                    const page = await collection.readPage(options);
                    return Paging.buildOutputPage(page, pagingOpts);
                });
            },
        };
    }
}
