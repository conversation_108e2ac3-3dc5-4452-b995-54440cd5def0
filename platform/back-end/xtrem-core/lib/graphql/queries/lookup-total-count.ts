import { GraphQLFieldConfig, GraphQLInt } from 'graphql';
import { Context } from '../../runtime';
import { QueryArgs } from '../paging/paging';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { ReferenceLookupsArgs } from './lookup-query';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `totalCount` of a property lookup query
 */
export class LookupTotalCount {
    /** Resolver for `totalCount` of a lookup query */
    static makeResolver(): GraphQLFieldConfig<ReferenceLookupsArgs, Context, QueryArgs> {
        return {
            type: GraphQLInt,
            resolve(parent, _args, rootContext) {
                return AccessRights.runSecure(
                    rootContext,
                    'lookup',
                    { factory: parent.parentArgs.factory },
                    async () => {
                        const context = parent.context;
                        const args = parent.parentArgs;
                        if (!args.lookupArgs._id && !args.lookupArgs.data)
                            throw new Error(
                                `${args.parentFactory.name}.${args.propertyName}: lookups missing id or data parameter`,
                            );

                        if (args.lookupArgs.data) {
                            args.lookupArgs.data = await NodeType.nodeValuesIn(
                                context,
                                args.lookupArgs.data,
                                args.parentFactory,
                            );
                        }

                        const count = await context.lookupCount(
                            args.parentFactory.nodeConstructor,
                            args.propertyName,
                            args.lookupArgs,
                        );

                        return count;
                    },
                );
            },
        };
    }
}
