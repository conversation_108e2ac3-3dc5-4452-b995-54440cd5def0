import { asyncArray } from '@sage/xtrem-async-helper';
import {
    artifactReferenceToTitleStringKey,
    getLiteral,
    getLocaleFromHeader,
    menuItemIdToStringKey,
} from '@sage/xtrem-i18n';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { LocalizeLocale, SitemapEntry } from '@sage/xtrem-shared';
import { GraphQLBoolean, GraphQLInt, GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { ClientService } from '../../system/pages/client-service';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const logger = loggers.application;

const sitemapEntryType: GraphQLObjectType = new GraphQLObjectType({
    name: '_SitemapEntry',
    fields: () => ({
        id: { type: GraphQLString },
        title: { type: GraphQLString },
        icon: { type: GraphQLString },
        isPage: { type: GraphQLBoolean },
        priority: { type: GraphQLInt },
        children: {
            type: sitemapEntryListType,
        },
    }),
});

const sitemapEntryListType = new GraphQLList(sitemapEntryType);

const resolveSitemapElements = async (
    context: Context,
    elements: SitemapEntry[],
    activePackages: string[],
    locale: LocalizeLocale,
): Promise<SitemapEntry[]> => {
    if (elements.length === 0) {
        return elements;
    }

    let filteredElements = asyncArray(elements)
        .filter(element => {
            const keep =
                activePackages.length === 0 ||
                (element?.packageName != null && activePackages.includes(element?.packageName));
            if (!keep)
                logger.debug(
                    () =>
                        `resolveSitemapElements don't keep element title:${element.title} packageName:${element.packageName}`,
                );
            return keep;
        }) // Filter only active packages
        .filter(async (element: SitemapEntry) => {
            // Filter if the user has access to the page/menu item
            if (element.isPage) {
                const keep = await ClientService.userHasAccessToPage(context, element.id!);
                if (!keep)
                    logger.debug(() => `resolveSitemapElements don't keep element ${element.title || element.id!}`);
                return keep;
            }

            // TODO: we have to filter out empty sub-menus
            return true;
        });

    if ((await filteredElements.length) > 0) {
        filteredElements = filteredElements.map(async (element: SitemapEntry) => {
            const copyElement = { ...element };
            copyElement.children = await resolveSitemapElements(
                context,
                copyElement.children || [],
                activePackages,
                locale,
            );
            const literalKey = copyElement.isPage
                ? artifactReferenceToTitleStringKey(copyElement.id!, 'pages')
                : menuItemIdToStringKey(copyElement.id!);
            copyElement.title = getLiteral(literalKey, locale).content;
            return copyElement;
        });
        return filteredElements
            .filter((element: SitemapEntry) => {
                const keep = element.isPage || (element.children && element.children.length > 0);
                if (!keep)
                    logger.debug(
                        () =>
                            `resolveSitemapElements don't keep element ${element.title} isPage:${
                                element.isPage
                            } children:${element.children ? element.children.length : 0}`,
                    );
                return !!keep;
            })
            .toArray();
    }
    return [];
};

const resolveSitemap = (source: unknown, args: unknown, context: Context): Promise<SitemapEntry[] | null> =>
    CustomMetrics.metadata.withMetrics(
        {
            nodeName: 'sitemap',
            operationName: 'read',
            operationKind: 'query',
        },
        () =>
            runResolver(context, async () => {
                const locale = getLocaleFromHeader(context.request.headers);

                const activePackages = await context.getActivePackageNames();
                logger.debug(() => `resolveSitemap(activePackages:${activePackages})`);
                return resolveSitemapElements(
                    context,
                    await ClientService.getSitemapTree(context),
                    activePackages,
                    locale,
                );
            }),
    );

export const getSitemapSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const sitemap = {
        type: sitemapEntryListType,
        resolve: resolveSitemap,
    };

    return {
        queries: {
            sitemap,
        },
    };
};
