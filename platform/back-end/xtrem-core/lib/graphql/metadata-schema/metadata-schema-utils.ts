import { asyncArray } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-client';
import { getLocaleFromHeader } from '@sage/xtrem-i18n';
import { CustomMetrics } from '@sage/xtrem-metrics';
import {
    GraphQLBoolean,
    GraphQLFieldConfig,
    GraphQLFieldConfigArgumentMap,
    GraphQLFieldConfigMap,
    GraphQLInputObjectType,
    GraphQLList,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import { isNil } from 'lodash';
import { Application } from '../../application/application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { ArtifactFilter, ArtifactManager } from '../../system/artifact-manager';
import { ApplicationArtifact, ClientArtifactType, ClientService } from '../../system/pages/client-service';

export interface MetadataSchemaFragment {
    queries?: GraphQLFieldConfigMap<any, any>;
    mutations?: GraphQLFieldConfigMap<any, any>;
}

const extensionsType = new GraphQLObjectType({
    name: '_PageExtension',
    fields: {
        packageName: { type: GraphQLString },
        content: { type: GraphQLString },
    },
});

export const extensionsListType = new GraphQLList(extensionsType);

export const fragmentsType = new GraphQLList(
    new GraphQLObjectType({
        name: '_PageFragment',
        fields: {
            name: { type: GraphQLString },
            packageName: { type: GraphQLString },
            content: { type: GraphQLString },
        },
    }),
);

export const pluginsType = new GraphQLList(GraphQLString);

const accessStatusType = new GraphQLObjectType({
    name: '_AccessStatus',
    fields: {
        name: { type: GraphQLString },
        status: { type: GraphQLString },
    },
});

const bindingsType = new GraphQLList(accessStatusType);

const accessType = new GraphQLObjectType({
    name: '_Access',
    fields: {
        node: { type: GraphQLString },
        bindings: { type: bindingsType },
    },
});

export const accessListType = new GraphQLList(accessType);

const localizationType = new GraphQLObjectType({
    name: '_Strings',
    fields: {
        key: { type: GraphQLString },
        content: { type: GraphQLString },
    },
});

export const localizationsType = new GraphQLList(localizationType);

export const getArtifactStringsConfig = (
    artifactType: ClientArtifactType,
): GraphQLFieldConfig<ApplicationArtifact, Context> => ({
    type: localizationsType,
    resolve: (artifact: ApplicationArtifact, _args: any, context: Context) => {
        return runResolver(context, () => ArtifactManager.resolveArtifactStrings(context, artifactType, artifact));
    },
});

export const getArtifactTitleConfig = (
    artifactType: ClientArtifactType,
): GraphQLFieldConfig<ApplicationArtifact, Context> => ({
    type: GraphQLString,
    resolve: (artifact: ApplicationArtifact, _args: any, context: Context) => {
        return runResolver(context, () => ArtifactManager.getArtifactTitle(context, artifactType, artifact));
    },
});

export const getArtifactDescriptionConfig = (
    artifactType: ClientArtifactType,
): GraphQLFieldConfig<ApplicationArtifact, Context> => ({
    type: GraphQLString,
    resolve: (artifact: ApplicationArtifact, _args: any, context: Context) => {
        return runResolver(context, () => ArtifactManager.getArtifactDescription(context, artifactType, artifact));
    },
});

export const artifactArgsWithFilter: GraphQLFieldConfigArgumentMap = {
    filter: {
        type: new GraphQLInputObjectType({
            name: '_ArtifactFilter',
            fields: {
                packageOrPage: { type: GraphQLString },
                exactMatch: { type: GraphQLBoolean, defaultValue: false },
            },
        }),
    },
};

export const pagesArgsWithFilter: GraphQLFieldConfigArgumentMap = {
    filter: {
        type: new GraphQLInputObjectType({
            name: '_PagesFilter',
            fields: {
                packageOrPage: { type: GraphQLString },
                exactMatch: { type: GraphQLBoolean, defaultValue: false },
                pageNode: { type: GraphQLString, defaultValue: '' },
            },
        }),
    },
};

export const resolveArtifact =
    (application: Application, artifactType: ClientArtifactType) =>
    (_obj: unknown, args: { filter: ArtifactFilter }, context: Context) =>
        CustomMetrics.metadata.withMetrics(
            {
                nodeName: artifactType,
                operationName: 'read',
                operationKind: 'query',
            },
            () =>
                runResolver(context, async () => {
                    const locale = getLocaleFromHeader(context.request.headers);
                    let artifacts = [];
                    if (args && args.filter) {
                        // On exact match request we send the full artifact including its source code and extensions
                        if (args.filter.exactMatch) {
                            const artifact = await ArtifactManager.readClientArtifact(
                                context,
                                artifactType,
                                args.filter,
                            );
                            artifacts = [artifact];
                        } else {
                            artifacts = await ClientService.searchClientArtifactsFromStorage(
                                context,
                                artifactType,
                                args.filter,
                                locale,
                            );
                        }
                    } else {
                        artifacts = await ArtifactManager.listAllClientArtifactsFromStorage(context, artifactType);
                    }

                    const activePackages = await context.getActivePackageNames();
                    const result = await asyncArray(
                        artifacts.filter(
                            artifact =>
                                activePackages.length === 0 ||
                                (artifact?.packageName && activePackages.includes(artifact?.packageName)),
                        ),
                    ) // Filter active packages
                        .filter(async artifact => {
                            // Filter if user has access to the page/artifact
                            if (
                                artifactType !== ClientArtifactType.pages &&
                                artifactType !== ClientArtifactType.stickers
                            )
                                return true;
                            return (
                                (await ClientService.getPageAccess(context, artifact?.pageAccess, {
                                    authorizationCode: artifact?.authorizationCode,
                                })) === 'authorized'
                            );
                        })
                        .map(async artifact => {
                            const mappedRecord = {
                                ...artifact,
                            };

                            if (
                                artifactType === ClientArtifactType.widgets &&
                                mappedRecord.key &&
                                mappedRecord.category
                            ) {
                                const category = await CoreHooks.createDashboardManager(application).getWidgetCategory(
                                    context,
                                    { _id: mappedRecord.key, id: mappedRecord.category },
                                );
                                if (category) {
                                    mappedRecord.category = category.key;
                                    mappedRecord.categoryLabel = category.title;
                                }
                            }

                            return mappedRecord;
                        })
                        .toArray();
                    return result;
                }),
        );

export type SchemaFragmentFunction = (application: Application) => MetadataSchemaFragment;

export const expandNodeNames = (application: Application, nodeNames: string[], depth: number | null): string[] => {
    const dict: Dict<number> = {};
    nodeNames.forEach(nodeName => expandNodeName(application, nodeName, depth || 5, dict));
    return Object.keys(dict);
};

const expandNodeName = (application: Application, nodeName: string, depth: number, dict: Dict<number>): void => {
    if (!isNil(dict[nodeName]) && dict[nodeName] >= depth) return;
    dict[nodeName] = depth;
    const factory = application.getFactoryByName(nodeName);
    factory.properties.forEach(prop => {
        if (prop.isForeignNodeProperty()) {
            if (depth > 1) expandNodeName(application, prop.targetFactory.name, depth - 1, dict);
        }
    });
};
