import { snakeCase } from 'lodash';
import { PlainOperationDecorator, PlainParameter } from '../../decorators';
import { Context, NodeFactory } from '../../runtime';
import { loggers } from '../../runtime/loggers';

const operationLogger = loggers.operation;

export class NodeOperations {
    static getLocalizedTitle(context: Context, factory: NodeFactory, operation: PlainOperationDecorator): string {
        // Examples:
        // Query - @sage/xtrem-foo/nodes__node_class_name__query__query_name
        // Query from extension - @sage/xtrem-foo/node-extensions__node_class_name__query__query_name
        // Mutation - @sage/xtrem-foo/nodes__node_class_name__mutation__mutation_name
        // AsyncMutation - @sage/xtrem-foo/nodes__node_class_name__asyncMutation__mutation_name
        // Extension Mutation - @sage/xtrem-foo/node-extensions__node_class_name__asyncMutation__mutation_name
        // Extension AsyncMutation - @sage/xtrem-foo/node-extensions__node_class_name__asyncMutation__mutation_name

        const isFromExtension = operation.definingPackage?.name !== factory.package.name;
        const packageName = isFromExtension ? operation.definingPackage?.name : factory.package.name;
        const keyPrefix = isFromExtension ? 'node-extensions' : 'nodes';
        const className = snakeCase(isFromExtension ? operation.className : factory.name);
        let key = '';
        switch (operation.operationKind) {
            case 'mutation':
                key = `${packageName}/${keyPrefix}__${snakeCase(
                    isFromExtension ? operation.className : factory.name,
                )}__mutation__${operation.name}`;
                break;
            case 'asyncMutation':
                key = `${packageName}/${keyPrefix}__${className}__asyncMutation__${operation.name}`;
                break;
            case 'query':
                key = `${packageName}/${keyPrefix}__${className}__query__${operation.name}`;
                break;
            default:
                throw Error(`${operation.operationKind} operation is not localized`);
        }

        try {
            return context.localize(key, operation.name);
        } catch {
            operationLogger.warn(
                `Could not resolve localized title for property name: ${factory.name}.${operation.name}, key: ${key}`,
            );
            return operation.name;
        }
    }

    static getLocalizedParameter(
        context: Context,
        factory: NodeFactory,
        operation: PlainOperationDecorator,
        parameter: PlainParameter,
    ): string {
        // Examples:
        // Query - @sage/xtrem-foo/nodes__node_class_name__query__query_name__parameter__parameter_name
        // Query from extension - @sage/xtrem-foo/node-extensions__node_class_name__query__query_name__parameter__parameter_name
        // Mutation - @sage/xtrem-foo/nodes__node_class_name__mutation__mutation_name__parameter__parameter_name
        // AsyncMutation - @sage/xtrem-foo/nodes__node_class_name__asyncMutation__mutation_name__parameter__parameter_name
        // Extension Mutation - @sage/xtrem-foo/node-extensions__node_class_name__asyncMutation__mutation_name__parameter__parameter_name
        // Extension AsyncMutation - @sage/xtrem-foo/node-extensions__node_class_name__asyncMutation__mutation_name__parameter__parameter_name

        const isFromExtension = operation.definingPackage?.name !== factory.package.name;
        const packageName = isFromExtension ? operation.definingPackage?.name : factory.package.name;
        const keyPrefix = isFromExtension ? 'node-extensions' : 'nodes';
        const className = snakeCase(isFromExtension ? operation.className : factory.name);

        let key = '';
        switch (operation.operationKind) {
            case 'mutation':
                key = `${packageName}/${keyPrefix}__${className}__mutation__${operation.name}__parameter__${parameter.name}`;
                break;
            case 'asyncMutation':
                key = `${packageName}/${keyPrefix}__${className}__asyncMutation__${operation.name}__parameter__${parameter.name}`;
                break;
            case 'query':
                key = `${packageName}/${keyPrefix}__${className}__query__${operation.name}__parameter__${parameter.name}`;
                break;
            default:
                throw Error(`${operation.operationKind} operation is not localized`);
        }

        try {
            return context.localize(key, parameter.name);
        } catch {
            operationLogger.warn(
                `Could not resolve localized parameter name: ${factory.name}.${operation.name}.${parameter.name}, key ${key}`,
            );
            return parameter.name;
        }
    }
}
