import { asyncArray } from '@sage/xtrem-async-helper';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLBoolean, GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { ServiceOption } from '../../application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const serviceOptionType = new GraphQLObjectType({
    name: '_ServiceOption',
    fields: {
        name: { type: GraphQLString },
        package: { type: GraphQLString },
        description: { type: GraphQLString },
        status: { type: GraphQLString },
        isActive: { type: GraphQLBoolean },
    },
});

const serviceOptionsType = new GraphQLList(serviceOptionType);

export const getServiceOptionsSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const serviceOptions = {
        type: serviceOptionsType,
        resolve: (_: unknown, __: unknown, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'serviceOptions',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        const appServiceOptions = context.application.serviceOptionsByName;
                        const values = asyncArray(Object.values(appServiceOptions)).map(
                            async (serviceOption: ServiceOption) => ({
                                name: serviceOption.name,
                                package: serviceOption.packageName,
                                description: serviceOption.description,
                                status: serviceOption.status,
                                isActive: await context.isServiceOptionEnabled(serviceOption),
                            }),
                        );
                        return values.toArray();
                    }),
            ),
    };

    return {
        queries: {
            serviceOptions,
        },
    };
};
