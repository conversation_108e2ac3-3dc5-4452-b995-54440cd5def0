import {
    GraphQLBoolean,
    GraphQLFieldConfig,
    GraphQLList,
    GraphQLNonNull,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { jsonType } from '../types/basic-types';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const userSetting = new GraphQLObjectType({
    name: '_MetaClientUserSetting',
    fields: {
        _id: { type: GraphQLString },
        content: { type: jsonType },
        elementId: { type: GraphQLString },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
    },
});

const userSettingList = new GraphQLList(userSetting);

const clientUserSettingsRootType = new GraphQLObjectType({
    name: '_ClientUserSettingsRoot',
    fields: {
        variantsForElement: {
            type: userSettingList,
            args: {
                screenId: { type: new GraphQLNonNull(GraphQLString) },
                elementId: { type: new GraphQLNonNull(GraphQLString) },
            },
            resolve: (_: unknown, args: any, context: Context) =>
                runResolver(context, () =>
                    context.application.requestFunnel(() =>
                        context.withChildContext(
                            ctx =>
                                ctx.application.clientSettingsManager.getVariantsForElement(
                                    ctx,
                                    args.screenId,
                                    args.elementId,
                                ),
                            { isolationLevel: 'low' },
                        ),
                    ),
                ),
        },
        activeUserSettings: {
            type: userSettingList,
            args: {
                screenId: { type: new GraphQLNonNull(GraphQLString) },
            },
            resolve: (_: unknown, args: any, context: Context) =>
                runResolver(context, () =>
                    context.application.requestFunnel(() =>
                        context.withChildContext(
                            ctx =>
                                ctx.application.clientSettingsManager.getActiveClientSettingsForArtifact(
                                    ctx,
                                    args.screenId,
                                ),
                            { isolationLevel: 'low' },
                        ),
                    ),
                ),
        },
        listPage: {
            type: GraphQLString,
            resolve: (_: unknown, _args: any, context: Context) => {
                try {
                    return context.application.clientSettingsManager.getCustomizationListPage(context);
                } catch {
                    return null;
                }
            },
        },
        editPage: {
            type: GraphQLString,
            resolve: (_: unknown, _args: any, context: Context) => {
                try {
                    return context.application.clientSettingsManager.getCustomizationEditPage(context);
                } catch {
                    return null;
                }
            },
        },
    },
});

const updateUserClientSetting: GraphQLFieldConfig<unknown, any, Context> = {
    type: userSetting,
    args: {
        _id: { type: new GraphQLNonNull(GraphQLString) },
        content: { type: jsonType },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx =>
                        ctx.application.clientSettingsManager.updateClientSetting(
                            ctx,
                            args._id,
                            args.content,
                            args.title || '',
                            args.description || '',
                        ),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const createUserClientSetting: GraphQLFieldConfig<unknown, any, Context> = {
    type: userSetting,
    args: {
        screenId: { type: new GraphQLNonNull(GraphQLString) },
        elementId: { type: new GraphQLNonNull(GraphQLString) },
        content: { type: jsonType },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx =>
                        ctx.application.clientSettingsManager.createClientSetting(
                            ctx,
                            args.screenId,
                            args.elementId,
                            args.content,
                            args.title || '',
                            args.description || '',
                        ),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const setCurrentUserClientSetting: GraphQLFieldConfig<unknown, any, Context> = {
    type: userSetting,
    args: {
        _id: { type: new GraphQLNonNull(GraphQLString) },
        screenId: { type: new GraphQLNonNull(GraphQLString) },
        elementId: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx =>
                        ctx.application.clientSettingsManager.selectClientSetting(
                            ctx,
                            args.screenId,
                            args.elementId,
                            args._id,
                        ),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const unselectClientUserSettings: GraphQLFieldConfig<unknown, any, Context> = {
    type: userSetting,
    args: {
        screenId: { type: new GraphQLNonNull(GraphQLString) },
        elementId: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx =>
                        ctx.application.clientSettingsManager.unselectedClientSetting(
                            ctx,
                            args.screenId,
                            args.elementId,
                        ),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const resetClientSettings: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    resolve: (_: unknown, _args: any, context: Context) =>
        runResolver(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(ctx => ctx.application.clientSettingsManager.resetClientSettings(ctx), {
                    isolationLevel: 'low',
                }),
            ),
        ),
};

const deleteUserClientSetting: GraphQLFieldConfig<unknown, any, Context> = {
    type: userSetting,
    args: {
        _id: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx => ctx.application.clientSettingsManager.deleteClientSetting(ctx, args._id),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const clientUserSettingsMutationType = new GraphQLObjectType({
    name: '_ClientUserSettingsRootMutation',
    fields: {
        createUserClientSetting,
        deleteUserClientSetting,
        resetClientSettings,
        setCurrentUserClientSetting,
        unselectClientUserSettings,
        updateUserClientSetting,
    },
});

export const getClientUserSettingsSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const clientUserSettings = {
        type: clientUserSettingsRootType,
        resolve: () => ({}),
    };
    const clientUserSettingsMutation = {
        type: clientUserSettingsMutationType,
        resolve: () => ({}),
    };

    return {
        queries: {
            clientUserSettings,
        },
        mutations: {
            clientUserSettings: clientUserSettingsMutation,
        },
    };
};
