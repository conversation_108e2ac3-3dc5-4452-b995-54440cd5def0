/* eslint-disable no-console */
import { ConfigManager } from '@sage/xtrem-config';
import { getLocaleFromHeader } from '@sage/xtrem-i18n';
import { setWebTransactionName } from '@sage/xtrem-metrics';
import { Config } from '@sage/xtrem-shared';
import * as express from 'express';
import { Express, Request, Response } from 'express';
import { graphqlHTTP } from 'express-graphql';
import { GraphQLSchema } from 'graphql';
import { ValidationSeverity } from '..';
import { Application } from '../application';
import { Context } from '../runtime';
import { logger } from '../security';
import { Test } from '../test';
import { Diagnose } from '../ts-api';
import { createMetadataSchema } from './metadata-schema';
import { setGraphQlHint } from './utils/request';

export interface GraphQlAppOptions {
    graphqlTimeLimitInSeconds?: number;
}

interface ExtendedResponse extends Response {
    diagnoses?: Diagnose[];
}

/**
 * We have the possibility to limit the life span of a graphQL operation, but when no specific value is provided
 * within the graphQL config this default value will be used instead
 */
const defaultGraphQlTimeLimitInSeconds = (options?: { managedExternal: boolean }): number =>
    options?.managedExternal ? 120 : 27.5;

export const graphQlTimeLimitInSeconds = (): number =>
    ConfigManager.current.graphql?.timeLimitInSeconds ||
    defaultGraphQlTimeLimitInSeconds({ managedExternal: !!ConfigManager.current.storage?.managedExternal });

function createExpressApp(): Express {
    return express().disable('x-powered-by');
}

export function setGraphQlTransactionName(req: Request): void {
    setWebTransactionName(() => setGraphQlHint(req));
}

async function checkTenantId(context: Context, res: ExtendedResponse): Promise<boolean> {
    if (ConfigManager.current.storage?.managedExternal) {
        return true;
    }

    const tenants = await Context.tenantManager.listTenantsIds(context);
    if (!tenants.includes(context.tenantId ?? '')) {
        logger.error(`${ConfigManager.current.app}~${context.tenantId}: Tenant does not exist`);
        res.status(200).send({
            data: {},
            errors: [
                {
                    extensions: {
                        diagnoses: [
                            {
                                severity: ValidationSeverity.error,
                                message: `${ConfigManager.current.app}: Tenant does not exist`,
                            },
                        ],
                    },
                },
            ],
        });
        return false;
    }
    return true;
}

export function graphQlApp(application: Application, options?: GraphQlAppOptions): Express {
    const app = createExpressApp();

    app.all('{/*path}', (req: Request, res: ExtendedResponse) => {
        setGraphQlTransactionName(req);
        const config = res.locals.config as Config;
        const locale = getLocaleFromHeader(req.headers);
        const timeLimitInSeconds =
            options?.graphqlTimeLimitInSeconds ??
            (config.graphql?.timeLimitInSeconds ||
                defaultGraphQlTimeLimitInSeconds({ managedExternal: !!config.storage?.managedExternal }));

        const timeLimitAsTimestamp = performance.now() + 1000 * timeLimitInSeconds;
        const executeRequest = async (context: Context): Promise<void> => {
            if (!(await checkTenantId(context, res))) {
                return;
            }
            const schema = await application.getGraphQLSchema(context);

            await graphqlHTTP({
                schema,
                graphiql: false,
                context,
                extensions: () => {
                    return {
                        diagnoses: context.diagnoses,
                    };
                },
            })(req, res);
        };
        return application.requestFunnel(() =>
            application.withReadonlyContext(
                getTenantId(application, config),
                async context => {
                    const isolationLevel = req.body?.isolationLevel;
                    if (isolationLevel && isolationLevel !== 'low') {
                        await context.withChildContext(executeRequest, {
                            // force readonly - isolationLevel is only valid with queries
                            isReadonly: true,
                            isolationLevel,
                            // force deferrable in serializable mode
                            isDeferrable: isolationLevel === 'high',
                        });
                    } else {
                        await executeRequest(context);
                    }
                },
                {
                    config,
                    locale,
                    request: req,
                    response: res,
                    source: 'graphql',
                    timeLimitAsTimestamp,
                },
            ),
        );
    });
    return app;
}

let metadataSchema: GraphQLSchema;

export function getTenantId(application: Application, config: Config): string | null {
    if (config.tenantId) return config.tenantId;
    if (application.applicationType === 'test') return Test.defaultTenantId;
    if (ConfigManager.current.storage!.managedExternal) return null;
    throw new Error('TenantId is not set - cannot execute graphQl Request');
}

export function graphQlMetadata(application: Application): Express {
    const app = createExpressApp();
    app.all('{/*path}', (req: Request, res: ExtendedResponse) => {
        setGraphQlTransactionName(req);
        const config = res.locals.config as Config;
        const locale = getLocaleFromHeader(req.headers);
        return application.requestFunnel(() =>
            application.withReadonlyContext(
                getTenantId(application, config),
                async context => {
                    if (!(await checkTenantId(context, res))) {
                        return;
                    }
                    if (!metadataSchema) {
                        metadataSchema = createMetadataSchema(application);
                    }

                    await graphqlHTTP({
                        schema: metadataSchema,
                        graphiql: true,
                        context,
                        customFormatErrorFn(error: Error) {
                            logger.error(error.stack || error.message);
                            return {
                                message: error.message,
                            };
                        },
                    })(req, res);
                },
                { config, locale, request: req, response: res },
            ),
        );
    });

    return app;
}
