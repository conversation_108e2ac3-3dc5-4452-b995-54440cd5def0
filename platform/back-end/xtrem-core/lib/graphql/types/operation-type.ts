import { Any<PERSON><PERSON><PERSON>, AnyValue } from '@sage/xtrem-async-helper';
import { Dict, LogicError, with<PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import {
    GraphQLFieldConfig,
    GraphQLFieldConfigArgumentMap,
    GraphQLFieldConfigMap,
    GraphQLInputType,
    GraphQLObjectType,
    GraphQLOutputType,
} from 'graphql';
import { camelCase } from 'lodash';
import { Application } from '../../application';
import { ObjectParameter, Parameter, PlainOperationDecorator, ScalarParameter, TypeName } from '../../decorators';
import { OperationError } from '../../errors/operation-error';
import { Context, NodeFactory, NodeFunction } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { AccessRights } from '../security/access-rights';
import { TypeCache } from '../utils/type-cache';
import { GraphQlTypingMode } from './node-type';
import { ObjectType } from './object-type';
import { GraphQlFactory, GraphQlProperty, PropertyType } from './property-type';
/**
 * @internal
 *
 * Static class to generate GraphQL type custom queries and mutations
 */
export class OperationType {
    static makeParameter(parameterOrTypeName: Parameter | TypeName): Parameter {
        if (typeof parameterOrTypeName === 'string') return { type: parameterOrTypeName } as Parameter;
        return parameterOrTypeName;
    }

    static makeParameterProperty(
        typeCache: TypeCache,
        baseName: string,
        parameterOrTypeName: Parameter | TypeName,
        name: string,
        typingMode: GraphQlTypingMode,
    ): GraphQlProperty {
        const isInputOnly = typingMode === 'parameter';
        const isOutputOnly = typingMode === 'return';
        const isPublished = true;
        const parameter = OperationType.makeParameter(parameterOrTypeName);
        const result = { ...parameter, name, isPublished, isInputOnly, isOutputOnly } as GraphQlProperty;
        if (parameter.type === 'reference' || parameter.type === 'instance')
            result.targetFactory = typeCache.application.getFactoryByConstructor(parameter.node());
        if (parameter.type === 'object')
            result.targetFactory = OperationType.makeGraphQlFactory(typeCache, baseName, result, typingMode);
        if (parameter.type === 'array')
            result.item = OperationType.makeParameterProperty(
                typeCache,
                baseName,
                parameter.item,
                result.name,
                'parameter',
            );

        const dataType = (parameter as ScalarParameter).dataType;
        if (dataType) {
            result.dataType = typeof dataType === 'function' ? dataType() : dataType;
        }
        return result;
    }

    static makeGraphQlFactory(
        typeCache: TypeCache,
        baseName: string,
        property: GraphQlProperty,
        typingMode: GraphQlTypingMode,
    ): GraphQlFactory {
        if (property.type !== 'object') throw new Error(`${property.name}: invalid property type: ${property.type}`);
        const propertiesDict = (property as any).properties as Dict<Parameter>;
        const properties = Object.keys(propertiesDict).map(name => {
            return OperationType.makeParameterProperty(typeCache, baseName, propertiesDict[name], name, typingMode);
        });
        const name = `${baseName}_${property.name}`;
        return {
            name,
            fullName: name,
            publishedSystemProperties: {},
            publishedInputSystemProperties: [],
            properties,
            impactingServiceOptions: [],
            subFactories: [],
            naturalKey: [],
            getNodeSchemaDescription() {
                return `${property.name}`;
            },
        };
    }

    // Convert the operations array of paramaters to an 'object' parameter
    static combineOperationParameters(op: PlainOperationDecorator): ObjectParameter {
        return {
            type: 'object',
            name: op.name,
            properties: op.parameters.reduce((r: any, param: any) => {
                r[param.name] = param;
                return r;
            }, {}),
        };
    }

    /**
     * Starts an async mutation by sending a notification, and returns its tracking id.
     */
    private static async executeAsyncMutationStart(
        context: Context,
        factory: NodeFactory,
        name: string,
        args: AnyRecord,
    ): Promise<{ trackingId: string }> {
        const trackingId = await CoreHooks.communicationManager.startAsyncMutation(context, factory.name, name, args);
        return { trackingId };
    }

    /**
     * Stops an async mutation.
     */
    private static async executeAsyncMutationStop(
        context: Context,
        args: { trackingId: string; reason: string },
    ): Promise<boolean> {
        const { trackingId, reason } = args;
        await CoreHooks.communicationManager.requestStop(context, trackingId, reason);
        return true;
    }

    /**
     * Stops an async mutation.
     */
    private static async executeAsyncMutationRequestUserNotification(
        context: Context,
        args: { trackingId: string; reason: string },
    ): Promise<boolean> {
        const { trackingId } = args;
        await CoreHooks.communicationManager.requestUserNotification(context, trackingId);
        return true;
    }

    /**
     * Dispatch async mutation actions.
     */
    private static executeAsyncMutation(
        context: Context,
        factory: NodeFactory,
        operation: PlainOperationDecorator,
        args: AnyRecord,
    ): Promise<AnyValue> {
        switch (operation.action) {
            case 'start':
                return this.executeAsyncMutationStart(context, factory, operation.name, args);
            case 'stop':
                return this.executeAsyncMutationStop(context, args as { trackingId: string; reason: string });
            case 'requestUserNotification':
                return this.executeAsyncMutationRequestUserNotification(
                    context,
                    args as { trackingId: string; reason: string },
                );
            default:
                throw new LogicError(`invalid operation action: ${operation.action}`);
        }
    }

    /**
     * Reads the operation's status from the notification history table.
     */
    private static executeAsyncTrackerQuery(
        context: Context,
        factory: NodeFactory,
        trackingId: string,
        op: PlainOperationDecorator,
    ): Promise<AnyRecord> {
        return CoreHooks.communicationManager.trackAsyncMutation(context, factory, trackingId, op);
    }

    /**
     * Returns the static method that executes the query or mutation
     */
    private static getStaticMethod(factory: NodeFactory, op: PlainOperationDecorator): NodeFunction<AnyValue> {
        const method = (factory.nodeConstructor as unknown as Dict<NodeFunction<AnyValue>>)[op.name];
        if (!method) throw new LogicError(`${factory.fullName}.${op.name}: operation not found`);
        if (typeof method !== 'function')
            throw new LogicError(`${factory.fullName}.${op.name}: operation is not a function`);
        return method;
    }

    private static async execute(
        rootContext: Context,
        factory: NodeFactory,
        op: PlainOperationDecorator,
        args: Dict<any>,
        isMutation: boolean,
        operationFactory: GraphQlFactory,
        operationProperty: GraphQlProperty,
    ): Promise<unknown> {
        const body = async (context: Context): Promise<AnyValue> => {
            switch (op.operationKind) {
                case 'query':
                case 'mutation': {
                    const method = OperationType.getStaticMethod(factory, op);
                    const argsObject = (await ObjectType.getInputValue(
                        context,
                        args,
                        operationFactory,
                        operationProperty,
                    )) as AnyRecord;

                    const methodArgs = op.parameters.map(p => argsObject[p.name]);
                    return method.apply(factory.nodeConstructor, [context, ...methodArgs]);
                }
                case 'asyncTrackerQuery':
                    return OperationType.executeAsyncTrackerQuery(context, factory, args.trackingId, op);
                case 'bulkMutation':
                case 'asyncMutation':
                    return OperationType.executeAsyncMutation(context, factory, op, args);

                default:
                    throw new LogicError(`${factory.fullName}.${op.name}: invalid operation kind: ${op.operationKind}`);
            }
        };

        const isolationLevel = op.isolationLevel || 'low';
        if (!isMutation && isolationLevel === 'low') return body(rootContext);

        // We are in a mutation, so test if we need to start with a readonly context
        if (isMutation && op.startsReadOnly && !['bulkMutation', 'asyncMutation'].includes(op.operationKind)) {
            // the startsReadOnly decorator attribute is set so we need to resolve it.
            const startAsReadonly =
                typeof op.startsReadOnly === 'function'
                    ? op.startsReadOnly.call(factory.nodeConstructor, rootContext)
                    : !!op.startsReadOnly;

            // startAsReadonly resolves to true so we need to execute the static method with the rootContext
            if (startAsReadonly) {
                const currentRootContextSource = rootContext.source;
                // set the source of the rootContext to customMutation so that the developers can use runInWritableContext
                rootContext.source = 'customMutation';
                try {
                    return await body(rootContext);
                } finally {
                    // set the source back to the previous value in case we reuse this context in the future for something else;
                    rootContext.source = currentRootContextSource;

                    Application.emitter.emit('customOperationExecuted', {});
                }
            }
        }

        const isolationOptions = {
            isolationLevel,
            isReadonly: !isMutation,
            isDeferrable: op.isDeferrable,
        };
        return rootContext.withChildContext(body, isolationOptions);
    }

    /**
     * Resolver for
     *      `query { xtremFoo { Bar { queries { zoo(...) { ... } } } } }`
     *  or
     *      `mutation { xtremFoo { Bar { mutations { zoo(...) { ... } } } } }`
     */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        op: PlainOperationDecorator,
        isMutation: boolean,
    ): GraphQLFieldConfig<unknown, Context, Dict<any>> {
        const graphqlOperationName = op.action ? `${op.name}_${op.action}` : op.name;
        const operationProperty = OperationType.makeParameterProperty(
            typeCache,
            factory.name,
            OperationType.combineOperationParameters(op),
            graphqlOperationName,
            'parameter',
        );
        const operationFactory = OperationType.makeGraphQlFactory(
            typeCache,
            factory.name,
            operationProperty,
            'parameter',
        );
        return {
            description: isMutation ? `Mutation ${op.name}` : `Query ${op.name}`,
            type: PropertyType.makePropertyType(
                typeCache,
                operationFactory,
                OperationType.makeParameterProperty(typeCache, operationFactory.name, op.return, 'return', 'return'),
                'return',
            ) as GraphQLOutputType,
            args: op.parameters.reduce((r, param) => {
                r[param.name] = {
                    type: PropertyType.makePropertyType(
                        typeCache,
                        operationFactory,
                        OperationType.makeParameterProperty(
                            typeCache,
                            operationFactory.name,
                            param,
                            param.name,
                            'parameter',
                        ),
                        'parameter',
                    ) as GraphQLInputType,
                };
                return r;
            }, {} as GraphQLFieldConfigArgumentMap),
            resolve(source, args, rootContext) {
                return AccessRights.runSecure(
                    rootContext,
                    op.name,
                    {
                        factory: {
                            name: factory.name,
                        },
                        isMutation,
                        args,
                        operationAction: op.action,
                    },
                    () =>
                        withRethrow(
                            () =>
                                OperationType.execute(
                                    rootContext,
                                    factory,
                                    op,
                                    args,
                                    isMutation,
                                    operationFactory,
                                    operationProperty,
                                ),
                            OperationError.errorMapper(rootContext, factory, op.name),
                        ),
                );
            },
        };
    }

    static addOperationFields(
        typeCache: TypeCache,
        factory: NodeFactory,
        parentTypeName: string,
        operations: PlainOperationDecorator[],
        isMutation: boolean,
        fields: GraphQLFieldConfigMap<any, any>,
    ): void {
        const actionFieldConfigMaps = {} as Dict<GraphQLFieldConfigMap<any, any>>;
        const globalOps: string[] = [];
        operations
            .filter(op => op.isPublished)
            .forEach(op => {
                const opName = camelCase(op.name);
                if (opName.startsWith('_') || fields[opName]) {
                    throw new Error(`${factory.fullName}.${opName}: operation name conflicts with system operation`);
                }
                const resolver = OperationType.makeResolver(typeCache, factory, op, isMutation);
                if (op.action) {
                    if (!actionFieldConfigMaps[opName]) actionFieldConfigMaps[opName] = {};
                    actionFieldConfigMaps[opName][op.action] = resolver;
                    if (op.isGlobal) globalOps.push(opName);
                } else {
                    fields[opName] = resolver;
                    if (op.isGlobal) {
                        typeCache.addGlobalNamespaceEntry(opName, resolver);
                    }
                }
            });
        Object.keys(actionFieldConfigMaps).forEach(opName => {
            const actionTypeName = `${parentTypeName}_${opName}`;
            const type = typeCache.internType(
                actionTypeName,
                () => new GraphQLObjectType({ name: actionTypeName, fields: actionFieldConfigMaps[opName] }),
            );
            fields[opName] = {
                type,
                resolve() {
                    return {};
                },
            };
            if (globalOps.includes(opName)) {
                typeCache.addGlobalNamespaceEntry(opName, {
                    type,
                    resolve() {
                        return {};
                    },
                });
            }
        });
    }
}
