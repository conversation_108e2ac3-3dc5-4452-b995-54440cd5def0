import { DateRang<PERSON>, Datetime, DatetimeRange, DateValue, DecimalRange, IntegerRange, Time } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { AnyValue, integer, LogicError } from '@sage/xtrem-shared';
import {
    GraphQLEnumType,
    GraphQLEnumValueConfigMap,
    GraphQLInputObjectType,
    GraphQLObjectType,
    GraphQLScalarType,
    GraphQLString,
    Kind,
    ValueNode,
} from 'graphql';
import * as json5 from 'json5';
import { EnumDataType, isInt32 } from '../../types';

// Numeric types
function makeNumberType(
    name: string,
    description: string,
    options?: { isReference?: boolean; isExternal?: boolean; isGraphQLInt?: boolean },
): GraphQLScalarType {
    const resolveValue = (value: AnyValue): AnyValue => {
        if (options?.isReference) {
            // For internal nodes empty strings must be resolved as null and not as 0.
            // 0 will be resolved as null which is good as well
            if (!value && !options?.isExternal) return null;

            // For external nodes, falsy values like '' and 0 are allowed.
            if (value == null && options?.isExternal) return null;

            // if the type of the value is string
            // * The value is passed as "_id:XXX"
            // * or this is a reference to an external factory and the value can be a string
            if (typeof value === 'string') {
                if (options?.isExternal) return value;
                if (value.startsWith('_id:')) return value;
                if (value.startsWith('{')) return json5.parse(value);
                if (value.startsWith('#')) return value;
            }
        }
        const numberValue = Number(value);
        if (options?.isGraphQLInt && !isInt32(numberValue)) {
            throw new LogicError(`Int cannot represent non 32-bit signed integer value: ${value}`);
        }
        return numberValue;
    };

    return new GraphQLScalarType({
        name,
        description,
        serialize(value: any): number | null {
            return value == null ? value : Number(value);
        },
        parseValue(value: any): AnyValue {
            return value == null ? value : resolveValue(value);
        },
        parseLiteral(ast: ValueNode): AnyValue {
            switch (ast.kind) {
                case Kind.STRING:
                case Kind.INT:
                case Kind.FLOAT:
                    return resolveValue(ast.value);
                case Kind.NULL:
                    return null;
                default:
                    throw new LogicError(`String or number expected, got ${ast.kind}`);
            }
        },
    });
}

/** @internal integer input type - accepts int or string */
export const integerInputType = makeNumberType(
    'IntOrString',
    '# Integer or string\n\nString will be converted to integer',
    { isGraphQLInt: true },
);

/** @internal integer range type */
export const integerRangeType = makeScalarType<IntegerRange>(
    'IntegerRange',
    IntegerRange.parse,
    '# IntegerRange --- format: [|d+,d+]|)',
    false,
);

/** @internal integer array type */
export const integerArrayType = makeScalarType<integer[]>(
    'IntegerArray',
    Array.from,
    '# IntegerArrayType --- format: [d+,d+,...])',
    false,
);

/** @internal string array type */
export const stringArrayType = makeScalarType<string[]>(
    'StringArray',
    Array.from,
    "# StringArrayType --- format: ['s','s',...])",
    false,
);

/** @internal decimal range type */
export const decimalRangeType = makeScalarType<DecimalRange>(
    'DecimalRange',
    DecimalRange.parse,
    '# DecimalRange --- format: [|d+,d+]|)',
    false,
);

/** @internal float input type - accepts int or string */
export const floatInputType = makeNumberType(
    'FloatOrString',
    '# Float or string\n\nString will be converted to number',
);

/** @internal reference input type - accepts int or string */
export const referenceInputType = makeNumberType(
    'IntReference',
    "# Number or string\n\nString will be converted to number except if it starts with '_id:'",
    { isReference: true },
);

export const externalReferenceInputType = makeNumberType('ExternalReference', "# Number or string'", {
    isReference: true,
    isExternal: true,
});

/** @internal decimal type - accepts number or string as input, outputs string */
export const decimalType = new GraphQLScalarType({
    name: 'Decimal',
    description:
        '# Decimal\n\nDecimal value may be input as number or string and will be returned as string to get full precision',
    serialize(value: any): any {
        return value == null ? value : `${value}`;
    },
    parseValue(value: any): Decimal {
        return value == null ? value : Decimal.make(value);
    },
    parseLiteral(ast: ValueNode): Decimal | null {
        switch (ast.kind) {
            case Kind.STRING:
            case Kind.INT:
            case Kind.FLOAT:
                return ast.value == null || ast.value === '' ? null : Decimal.make(ast.value);
            case Kind.NULL:
                return null;
            default:
                throw new LogicError(`String or Int expected, got ${ast.kind}`);
        }
    },
});

export const booleanInputType = new GraphQLScalarType({
    name: 'BooleanOrString',
    description: '# Boolean\n\nBoolean value may be input as boolean or string and will be returned as boolean',
    serialize(value: any): any {
        return value.toString();
    },
    parseValue(value: any): boolean {
        if (typeof value === 'boolean') {
            return value;
        }
        if (typeof value === 'string') {
            switch (value) {
                case 'true':
                    return true;
                case 'false':
                    return false;
                default:
                    throw new LogicError(`${value}: invalid boolean value`);
            }
        }
        throw new LogicError(`${value}: invalid ${typeof value} value`);
    },
    parseLiteral(ast: ValueNode): boolean {
        switch (ast.kind) {
            case Kind.BOOLEAN:
                return ast.value;
            case Kind.STRING:
                switch (ast.value) {
                    case 'true':
                        return true;
                    case 'false':
                        return false;
                    default:
                        throw new LogicError(`${ast.value}: invalid boolean value`);
                }
            default:
                throw new LogicError(`String or Boolean expected, got ${ast.kind}`);
        }
    },
});

export type ScalarObjectTypes =
    | DateValue
    | DateRange
    | DatetimeRange
    | IntegerRange
    | Datetime
    | DecimalRange
    | Time
    | integer[]
    | string[]
    | string;

// helper to create date, dateRange, datetime and time types
function makeScalarType<T extends ScalarObjectTypes>(
    name: string,
    parse: (value: any) => T,
    description: string,
    isReference: boolean,
): GraphQLScalarType {
    const resolveValue = (value: any): T | string | number => {
        if (isReference && typeof value === 'string') {
            if (value.startsWith('_id:')) return value;
            if (value.startsWith('#')) return value;
        }
        return parse(value);
    };

    return new GraphQLScalarType({
        name,
        description,
        serialize(value: any): string | null {
            return value ? value.toString() : null;
        },
        parseValue(value: any): T | string | number | null {
            return value ? resolveValue(value) : null;
        },
        parseLiteral(ast: ValueNode): T | string | number | null {
            switch (ast.kind) {
                case Kind.STRING:
                    return resolveValue(ast.value);
                case Kind.NULL:
                    return null;
                default:
                    throw new LogicError(`String expected, got ${ast.kind}`);
            }
        },
    });
}

/** @internal id type */
export const idInputType = makeScalarType<string>('Id', (value: string) => value, '# ID', true);

/** @internal date type */
export const dateType = makeScalarType<DateValue>(
    'Date',
    (value: string) => DateValue.parse(value),
    '# Date\n\nISO 8601 format: YYYY-MM-DD',
    false,
);

/** @internal date type */
export const dateRangeType = makeScalarType<DateRange>(
    'DateRange',
    DateRange.parse,
    '# DateRange\n\nISO --- format: ([|()YYYY-MM-DD,YYYY-MM-DD(]|))',
    false,
);

/** @internal date type */
export const datetimeRangeType = makeScalarType<DatetimeRange>(
    'DatetimeRange',
    DatetimeRange.parse,
    '# DateRange\n\nISO --- format: ([|()YYYY-MM-DD,YYYY-MM-DD(]|))',
    false,
);

/** @internal time type */
export const timeType = makeScalarType<Time>(
    'Time',
    (value: string) => Time.parse(value),
    '# Time\n\nISO 8601 format: HH:mm:ss',
    false,
);

/** @internal datetime type */
export const datetimeType = makeScalarType<Datetime>(
    'Datetime',
    (value: string) => Datetime.parse(value),
    '# Datetime\n\nISO 8601 format: YYYY-MM-DD[T]HH:mm:ssZ',
    false,
);

/** @internal stream output type */
export const textStreamOutputType = new GraphQLObjectType({
    name: '_OutputTextStream',
    fields: {
        value: {
            type: GraphQLString,
        },
    },
});

/** @internal stream output type */
export const binaryStreamOutputType = new GraphQLObjectType({
    name: '_OutputBinaryStream',
    fields: {
        value: {
            type: GraphQLString,
            resolve(obj) {
                return obj ? obj.toString() : null;
            },
        },
    },
});

/** @internal stream input type */
export const streamInputType = new GraphQLInputObjectType({
    name: '_InputStream',
    fields: {
        value: {
            type: GraphQLString,
        },
    },
});

/** @internal JSON type */
export const jsonType = new GraphQLScalarType({
    name: 'Json',
    description: '# JSON\n\nValue is input and returned as a string (stringified JSON)',
    serialize(value: any): any {
        return JSON.stringify(value != null ? value : null);
    },
    parseValue(value: any): AnyValue {
        return value != null ? JSON.parse(value) : null;
    },
    parseLiteral(ast: ValueNode): AnyValue | null {
        switch (ast.kind) {
            case Kind.STRING:
                return JSON.parse(ast.value);
            case Kind.NULL:
                return null;
            default:
                throw new LogicError(`String expected, got ${ast.kind}`);
        }
    },
});

/** @internal create enum output type */
export function makeCustomEnumOutputType(name: string, enumDataType: EnumDataType): GraphQLEnumType {
    return new GraphQLEnumType({
        name,
        values: enumDataType.values.reduce((values, key) => {
            values[key] = { value: key };
            return values;
        }, {} as GraphQLEnumValueConfigMap),
    });
}

/** @internal create enum input type */
export function makeCustomEnumInputType(name: string, enumDataType: EnumDataType): GraphQLScalarType {
    const valuesDescription = enumDataType.values.map(key => `\n- ${key}`).join('');
    const description = `# Enum\n\nEnum value may be input as string and will be returned as string.\n\nValues:${valuesDescription}`;

    const parseValue = (value: any): string => {
        enumDataType.validateMemberValue(value);
        return value;
    };

    return new GraphQLScalarType({
        name,
        description,
        serialize(value: any): string {
            if (value === '') return value;
            if (!Number.isFinite(Number(value))) {
                enumDataType.validateMemberValue(value);
                return value;
            }
            const enumValue = enumDataType.stringValue(value);
            if (!enumValue) throw new LogicError(`${name}: invalid value: ${value}`);
            return enumValue;
        },
        parseValue,
        parseLiteral(ast: ValueNode): string {
            switch (ast.kind) {
                case Kind.ENUM:
                case Kind.STRING: {
                    return parseValue(ast.value);
                }
                default:
                    throw new LogicError(`${name}: bad kind: ${ast.kind}`);
            }
        },
    });
}

export interface TenantContext {
    tenantPackages: string[];
}
