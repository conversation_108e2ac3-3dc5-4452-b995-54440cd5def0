/** @ignore */ /** */
import { Any<PERSON><PERSON><PERSON>, AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { DateRange, DateValue, Datetime, DatetimeRange, DecimalRange, IntegerRange } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { Maybe, SystemError } from '@sage/xtrem-shared';
import { topoSort } from '@sage/xtrem-toposort';
import * as _ from 'lodash';
import { Property } from '../properties';
import { SqlConverter, Table } from '../sql';
import { Uuid } from '../types/index';
import { loggers } from './loggers';
import { NodeFactory } from './node-factory';
import { friendlyJsonParse } from './node-factory-utils';

export interface RetryOptions {
    maxTries: number;
    delayBeforeRetry: number;
    message?: string;
    onError?: (error: Error) => void;
}

/**
 * Returns whether a value is a scalar value (decimal, datetime, date, dateRange, uuid, rawValue).
 * (Objects are rejected).
 * @internal
 */
export function isScalar(val: AnyValue): boolean {
    if (!val) return true; // Treat null and undefined as scalar
    if (Decimal.isDecimal(val)) return true;
    if (Datetime.isDatetime(val)) return true;
    if (DateValue.isDate(val)) return true;
    if (DateRange.isDateRange(val)) return true;
    if (DatetimeRange.isDatetimeRange(val)) return true;
    if (IntegerRange.isIntegerRange(val)) return true;
    if (DecimalRange.isDecimalRange(val)) return true;
    if (val instanceof Uuid) return true;
    return typeof val !== 'object';
}

export function isCompound(val: AnyValue): val is AnyRecord {
    return !isScalar(val);
}

export function tenantCondition(
    sqlConverter: SqlConverter,
    table: Table,
    alias: string,
    clauses: (string | undefined)[],
): string {
    const { context } = sqlConverter;
    if (!table.isSharedByAllTenants && context.tenantId == null && !context.unsafeApplyToAllTenants) {
        throw new Error('Invalid null tenantId');
    }
    const aliasDot = alias ? `${alias}.` : '';
    const allClauses = [...clauses];

    if (!table.isSharedByAllTenants && context.tenantId != null) {
        allClauses.push(
            `${aliasDot}_tenant_id = ${sqlConverter.addSqlParameter({
                valuePath: 'context.tenantId',
                type: 'string',
            })}`,
        );
    }

    // If all allClauses === []  then the condition returned by SqlConverter.and will be TRUE
    return SqlConverter.and(allClauses);
}

/**
 * Returns the dependencies (as factory names) of a factory
 * @param factory the factory to analyze
 * @param allAvailableFactories the full list of available factories. This function will only return factories that belong to this list
 */
export function getFactoryDependsOn(factory: NodeFactory, allAvailableFactories: NodeFactory[]): string[] {
    const dependsOn: string[] = [];

    // Handle polymorphic references by pushing (recursively)
    // all subclasses of the referenced class into dependsOn.
    // We need this for the loading of CSV files.
    // If A references an abstract class B with subclasses C, D, E
    // we must load C, D and E before loading A.
    const addPolymorphicDependencies = (factoryToProcess: NodeFactory): void => {
        if (!dependsOn.includes(factoryToProcess.name)) dependsOn.push(factoryToProcess.name);
        if (factoryToProcess.isAbstract) {
            allAvailableFactories
                .filter(f => f.baseFactory === factoryToProcess)
                .forEach(f => addPolymorphicDependencies(f));
        }
    };

    factory.properties
        .filter(property => property.isStored)
        .forEach(property => {
            if (property.isReferenceProperty() && property.isToposortDependency) {
                // Add a dependency for all the not-nullable references or nullable references allowed in a unique index
                addPolymorphicDependencies(property.targetFactory);
            }
            if (property.dependsOn) {
                // Add a dependency for all the dependencies that point to a reference
                property.dependsOn.forEach(dep => {
                    // dep can be:
                    // - a property name (sth like dependsOn: ['addresses'],)
                    // - an object (sth like dependsOn: [{ components: ['startValue', 'endValue'] }])
                    const propsToCheck = typeof dep === 'string' ? [dep] : Object.keys(dep);
                    propsToCheck.forEach(propToCheck => {
                        const depProp = factory.findProperty(propToCheck);
                        if (depProp.isReferenceProperty() && depProp.isToposortDependency && !depProp.isVital) {
                            addPolymorphicDependencies(depProp.targetFactory);
                        }
                    });
                });
            }
        });
    const baseFactory = factory.baseFactory;
    if (baseFactory) {
        dependsOn.push(baseFactory.name);
    }

    return dependsOn;
}

/**
 * Use topoSort to order passed in factories based on their dependencies
 *
 * @param factories
 */
export function sortFactories(factories: NodeFactory[]): NodeFactory[] {
    const factoryMap: Map<string, NodeFactory> = new Map();

    // Add missing base factories to the factories array
    factories
        .filter(factory => factory.baseFactory)
        .forEach(factory => {
            let baseFactory = factory.baseFactory;
            while (baseFactory && !factories.includes(baseFactory)) {
                factories.push(baseFactory);
                baseFactory = baseFactory.baseFactory;
            }
        });

    const factoriesToSort = factories.map(factory => {
        const dependsOn = getFactoryDependsOn(factory, factories);
        factoryMap.set(factory.name, factory);
        return {
            name: factory.name,
            dependsOn,
        };
    });
    let preSortedFactories = topoSort(factoriesToSort).map(item => factoryMap.get(item.name)!);
    // All the non-sharedByAllTenant tables have a FK to SysTenant (defined in the factory but the _tenantId property is still a string[21]
    // on not a reference, so all the computed 'dependsOn' do not take this FK into account)
    // If SysTenant is part of the factories, then it must be the first one, otherwise many processes will fail
    // - loading of CSV files (a CSV file can only be imported after the sys-tenant.csv file because of the FK)
    // - even for non-sharedByAllTenant tables, the sys_tenant must be initialized first to be able to write the sys_csv_checksum record
    //   at the end of the loading (sys_csv_checksum also has a FK to sys_tenant)
    // That's why sys_tenant is always set first, even before all the non-sharedByAllTenant tables.
    // And SysCustomer must be placed before SysTenant (the sys_tenant table has a FK to sys_customer).
    let runUniq = false;
    ['SysTenant', 'SysCustomer'].forEach(sysFactoryName => {
        const sysFactory = preSortedFactories.find(f => f.name === sysFactoryName);
        if (sysFactory) {
            // Place the factory first, _.uniq will then clean up duplicates
            preSortedFactories.unshift(sysFactory);
            runUniq = true;
        }
    });
    if (runUniq) preSortedFactories = _.uniq(preSortedFactories);
    return preSortedFactories;
}

function isDefaultableReferenceProperty(property: Property, propertiesToUpdate?: Maybe<string[]>): boolean {
    return (
        property.isStored &&
        property.isReferenceProperty() &&
        (property.isNullable || property.isSelfReference) &&
        (!propertiesToUpdate || propertiesToUpdate.includes(property.name))
    );
}

/**
 * Returns the list of defaultable reference properties.
 * Reference properties are defaultable if they are either nullable or self-referencing.
 */
export function getDefaultableReferenceProperties(
    factory: NodeFactory,
    propertiesToUpdate?: Maybe<string[]>,
): Property[] {
    return factory.properties.filter(property => isDefaultableReferenceProperty(property, propertiesToUpdate));
}

/**
 * Converts the data key to either snakeCase or camelCase
 * This function is used on the application when data is read from CSV files
 * and need to be loaded into postgres or if data is written into CSV
 *
 * @param data
 * @param conversion
 */
export function convertDataKey(data: AnyRecord[], conversion: 'snakeCase' | 'camelCase'): AnyRecord[] {
    let conversionFunction = (string?: string): string | undefined => string;
    switch (conversion) {
        case 'camelCase':
            conversionFunction = _.camelCase;
            break;
        case 'snakeCase':
            conversionFunction = _.snakeCase;
            break;
        default:
            break;
    }
    return data.map(dataRow => {
        const propertyDataRow = {} as AnyRecord;
        Object.keys(dataRow).forEach(key => {
            const propertyKey = key.startsWith('_')
                ? `_${conversionFunction(key)}`
                : key === '$layer'
                  ? key
                  : conversionFunction(key);
            propertyDataRow[propertyKey!] = dataRow[key];
        });
        return propertyDataRow;
    });
}

/**
 * Returns the language code based on the locale supplied
 * @param locale
 */
export function getLanguageFromLocale(locale: string): string {
    if (locale === 'base') return 'en';
    const language = locale.split('-')[0];
    if (!language || language.length > 2) throw Error(`Locale ${locale} not supported`);
    return language;
}

export const defaultAlphabet =
    ' !"#$%&()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[]^_abcdefghijklmnopqrstuvwxyz{|}~';

export function randomString(maxLength = 100, alphabet = defaultAlphabet): string {
    return [...Array(Math.floor(maxLength * Math.random()) + 1)]
        .map(() => alphabet[Math.floor(Math.random() * alphabet.length)])
        .join('');
}

/**
 * Re-tries the given function until it succeeds or a maximum number of tries are reached.
 * @param action - The action to be retried.
 * @param maxTries - The maximum number of tries to make. This includes the first try. Values less than 1 will try 1 time.
 * @param delayBeforeRetry - The delay in milliseconds between each successive retry.
 */
export async function retry<T extends AnyValue>(
    action: () => AsyncResponse<T>,
    options: RetryOptions = { maxTries: 3, delayBeforeRetry: 1000 },
): Promise<T> {
    const { maxTries, delayBeforeRetry } = options;
    const maximumTries = Math.max(maxTries, 1);
    const allErrors: string[] = [];

    for (let currentTry = 1; currentTry <= maximumTries; currentTry += 1) {
        try {
            return await action();
        } catch (error) {
            loggers.application.error(
                () =>
                    `Retry ${currentTry}/${maximumTries} [${options.message ?? ''}]: ${currentTry === 1 ? error.stack : error.message}`,
            );
            let message = error?.response?.data?.error || error.message || error;
            if (typeof message !== 'string') {
                message = JSON.stringify(message);
            }
            if (!allErrors.includes(message)) {
                allErrors.push(message);
            }
            if (options.onError) {
                options.onError(error);
            }
            await new Promise(resolve => {
                setTimeout(resolve, delayBeforeRetry);
            });
        }
    }
    const maxErrorTypes = 10;
    if (allErrors.length > maxErrorTypes) {
        loggers.application.error(
            () => `Retry as more than ${maxErrorTypes} errors types. The combined error has been truncated.`,
        );
    }
    // todo XT-859: what error type should be thrown here ?
    throw new SystemError(allErrors.slice(0, maxErrorTypes).join('\n\n'));
}

export function getServerUrl(path: string, params?: URLSearchParams): string {
    const config = ConfigManager.current;
    const appUrl = config.apps?.[config.app || '']?.appUrl || `http://localhost:${config.server?.port || '8240'}`;
    const host = config.security?.redirectUrl || appUrl;
    const url = new URL(host);
    url.pathname = path;
    if (params) {
        url.search = params.toString();
    }
    return url.href;
}

/**
 * Replaces values in an object for logging purposes.
 * If the value is a string that starts and ends with '{' and '}', it will be parsed as JSON.
 * Non-object values will be replaced with '***'.
 * @param key - The key of the value being replaced.
 * @param value - The value being replaced.
 * @returns The replaced value.
 */
export function stringifyLogReplacer(key: any, value: any): any {
    if (key === 'filter' && typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
        return friendlyJsonParse(value);
    }
    if (!_.isObject(value)) {
        return '***';
    }
    return value;
}
