/** @ignore */ /** */
import { AnyR<PERSON>ord, AnyValue, AsyncArray, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { Datetime, DateValue } from '@sage/xtrem-date-time';
import { Decimal, compare as decimalCompare } from '@sage/xtrem-decimal';
import { Dict, LogicError } from '@sage/xtrem-shared';
import { omit } from 'lodash';
import { BaseCollection } from '../collections';
import { Property } from '../properties';
import { OrderByClause } from '../sql/mapper';
import {
    AnyFilterObject,
    AnyFilterValue,
    AnyOrderBy,
    FilterOp,
    Node,
    NodeQueryFilter,
    NodeQueryOptions,
    OrderBy,
} from '../ts-api';
import { parseCursor } from '../types';
import { Stream } from '../types/stream';
import { Context } from './context';
import { NodeFactory } from './node-factory';
import { isCompound, isScalar } from './utils';

type Predicate = (val: AnyValue) => AsyncResponse<boolean>;
type Op = (val: AnyValue, parent?: AnyValue) => Predicate;

type Comparator = (val1: AnyValue, val2: AnyValue) => AsyncResponse<number>;

/** @internal */
export async function applyPagingOptions(
    factory: NodeFactory,
    context: Context,
    clnOrItems: BaseCollection | Node[],
    options?: NodeQueryOptions,
): Promise<{
    items: Node[];
    totalCount: number;
}> {
    let items = Array.isArray(clnOrItems) ? clnOrItems : await clnOrItems.toArray();
    let totalCount = items.length;
    if (!options) return { items, totalCount };
    if (options.last) {
        if (options.first) throw new Error('first cannot be supplied with last.');

        if (options.after) throw new Error('after cannot be supplied with last.');
    } else if (options.before) {
        throw new Error('before cannot be supplied without last.');
    }
    if (options.filter) {
        items = await applyFilter(items, options.filter, context.locales).toArray();
        totalCount = items.length;
    }

    let orderBy = [] as OrderByClause[];

    if (factory.storage !== 'external') {
        orderBy = factory.parseOrderBy(context, options.orderBy);
    } else {
        if (!factory.externalStorageManager) {
            throw factory.logicError('External storage manager is not defined');
        }
        // we set the order by to the key properties
        orderBy = factory.externalStorageManager.parseOrderBy(context, options.orderBy);
    }
    // The full collection has been fetched from the database: orderBy clause has to be computed in memory
    if (orderBy.length !== 0 && options.orderBy) {
        items = await applyOrderBy(items, options.orderBy);
    }

    const getCursorValue = (val: string): PropertyAndValue[] => {
        if (factory.storage === 'external') {
            if (!factory.externalStorageManager) {
                throw factory.logicError('External storage manager is not defined');
            }
            return factory.externalStorageManager.parseCursor(orderBy, val);
        }

        return parseCursor(
            orderBy.map(clause => clause.property),
            val,
        );
    };

    if (options.after != null) {
        // apply after
        items = await applyPagingOperation(items, orderBy, getCursorValue(options.after), 'after').toArray();
        // Note : totalCount must not be updated to items.length
    }
    if (options.before != null) {
        // apply before
        items = await applyPagingOperation(items, orderBy, getCursorValue(options.before), 'before').toArray();
        // Note : totalCount must not be updated to items.length
    }
    if (options.first != null && options.first !== 0) {
        items = items.slice(0, options.first);
        // Note : totalCount must not be updated to items.length
    }
    if (options.last != null && options.last !== 0) {
        items = items.slice(-options.last);
        // Note : totalCount must not be updated to items.length
    }

    return { items, totalCount };
}

function composeComparators(f: Comparator, g: Comparator): Comparator {
    return async (val1: AnyValue, val2: AnyValue) => {
        const fResult = await f(val1, val2);
        if (fResult !== 0) return fResult;
        return g(val1, val2);
    };
}

export function dateFromString(date: string): DateValue {
    return date.includes('T') ? DateValue.parse(date, undefined, 'YYYY-MM-DDThh:mm:ss.SSSZ') : DateValue.parse(date);
}

/** @internal */
export function valueComparator(val1: AnyValue, val2: AnyValue, locales?: string[]): number {
    if (val1 == null) {
        return val2 == null ? 0 : -1;
    }
    if (val2 == null) return +1;
    if (Decimal.isDecimal(val1) || Decimal.isDecimal(val2)) return decimalCompare(val1, val2) || 0;
    if (DateValue.isDate(val1)) {
        if (DateValue.isDate(val2)) return val1.compare(val2);
        if (typeof val2 === 'string' && val2 !== '') return val1.compare(dateFromString(val2));
        if (typeof val2 === 'number') return val1.compare(new DateValue(val2));
        return -1;
    }
    if (Datetime.isDatetime(val1)) {
        if (Datetime.isDatetime(val2)) return val1.compare(val2);
        if (typeof val2 === 'string') return val1.compare(Datetime.parse(val2));
        return -1;
    }
    if (Stream.isStream(val1)) return val1.compareTo(val2 as Stream);
    if (Array.isArray(val1))
        return JSON.stringify(val1) === JSON.stringify(val2)
            ? 0
            : JSON.stringify(val1) < JSON.stringify(val2)
              ? -1
              : +1;

    if (typeof val1 === 'string' && typeof val2 === 'string')
        return val1.localeCompare(
            val2,
            locales?.map(locale => (locale === 'base' ? 'en' : locale)),
        );

    if (val1 instanceof Node && val2 instanceof Node) {
        // if the nodes are from different factories, we cannot compare them and they will be not equal
        if (val1.$.factory.rootFactory !== val2.$.factory.rootFactory)
            throw new LogicError('Cannot compare nodes with different base nodes');
        return valueComparator(val1._id, val2._id, locales);
    }

    if (val1 instanceof Node) {
        return valueComparator(val1._id, val2, locales);
    }

    if (val2 instanceof Node) {
        return valueComparator(val1, val2._id, locales);
    }

    // use == to get valueOf conversion
    // eslint-disable-next-line eqeqeq
    if (val1 == val2) return 0;
    return val1 < val2 ? -1 : +1;
}

function compileOrderBy(orderBy: AnyOrderBy): Comparator {
    let out: Comparator = () => 0;
    Object.keys(orderBy).forEach((propertyName, idx) => {
        const dir = orderBy[propertyName];
        let newComparator: Comparator;
        if (dir === -1 || dir === 1) {
            newComparator = async (parent1: Node, parent2: Node): Promise<number> => {
                async function getPropertyValue(node: Node): Promise<AnyValue> {
                    // ids of transient objects are negative.
                    // So we move them into the positive space to get proper sorting by default
                    // TODO: consider switching to a huge integer offset (like 2**50) instead of negating.
                    if (propertyName === '_id' && node.$.factory.storage !== 'external') {
                        return Math.abs(node._id);
                    }
                    const property = node.$.factory.findProperty(propertyName, {
                        includeSystemProperties: true,
                    });
                    let propertyValue = await node.$.state.getPropertyValue(property);
                    if (property.isEnumProperty() && typeof propertyValue === 'string') {
                        propertyValue = property.dataType.numberValue(propertyValue);
                    }
                    return propertyValue;
                }

                if (parent1 === parent2) {
                    return 0;
                }
                if (parent1 == null) return parent2 == null ? 0 : -dir;
                if (parent2 == null) return dir;
                const val1 = await getPropertyValue(parent1);
                const val2 = await getPropertyValue(parent2);

                const result = valueComparator(val1, val2, parent1.$.context.locales) * dir;
                return result;
            };
        } else {
            const p = compileOrderBy(orderBy[propertyName] as AnyOrderBy);
            newComparator = async (val1: Dict<Promise<AnyValue>>, val2: Dict<Promise<AnyValue>>) => {
                if (val1 == null && val2 == null) return 0;
                if (val2 == null) return p(await val1[propertyName], null);
                if (val1 == null) return p(null, await val2[propertyName]);
                return p(await val1[propertyName], await val2[propertyName]);
            };
        }
        if (idx === 0) out = newComparator;
        else out = composeComparators(out, newComparator);
    });
    return out;
}

/** @internal */
export function applyOrderBy<T extends Node = Node>(items: T[], orderBy: OrderBy<T>): Promise<T[]> {
    return asyncArray(items)
        .sort((elt1, elt2) => compileOrderBy(orderBy)(elt1, elt2))
        .toArray();
}

/** @internal */
export function matchesFilter(
    filter: NodeQueryFilter<Node>,
    values: AnyRecord | Node,
    locales?: string[],
): AsyncResponse<boolean> {
    const predicate = converter(locales)(filter);
    return predicate(values);
}

export function applyFilter<T extends Node = Node>(
    items: T[],
    filter: AnyFilterValue,
    locales?: string[],
): AsyncArray<T> {
    const predicate = converter(locales)(filter);
    return asyncArray(items).filter(item => predicate(item));
}

function filterPartToString(propertyName: string, singleObj: AnyValue): string {
    if (singleObj == null) return `${propertyName} is NULL`;
    if (isScalar(singleObj)) return singleObj.toString();
    const singleKey = Object.keys(singleObj)[0] as FilterOp;
    const singleVal = Object.values(singleObj)[0];
    const arrayToStr = (arr: AnyValue[]): string[] => {
        return arr.map(f => filterPartToString(propertyName, f));
    };
    switch (singleKey) {
        case '_eq':
            return `${propertyName} = ${singleVal}`;
        case '_ne':
            return `${propertyName} != ${singleVal}`;
        case '_gt':
            return `${propertyName} > ${singleVal}`;
        case '_gte':
            return `${propertyName} >= ${singleVal}`;
        case '_lt':
            return `${propertyName} < ${singleVal}`;
        case '_lte':
            return `${propertyName} <= ${singleVal}`;
        case '_contains':
        case '_containsRange':
            return `${propertyName} @> ${singleVal}`;
        case '_containedBy':
            return `${propertyName} <@ ${singleVal}`;
        case '_in':
            return `${propertyName} in [${arrayToStr(singleVal).join(', ')}]`;
        case '_nin':
            return `${propertyName} nin [${arrayToStr(singleVal).join(', ')}]`;
        case '_and':
            return `(${arrayToStr(singleVal).join(' && ')})`;
        case '_or':
            return `(${arrayToStr(singleVal).join(' || ')})`;
        case '_not':
            return `!(${filterPartToString(propertyName, singleVal)})`;
        default:
            return JSON.stringify(singleObj);
    }
}

/** @internal */
export function filterToString(filter: AnyFilterObject): string {
    return Object.keys(filter)
        .map(k => filterPartToString(k, filter[k]))
        .join(' and ');
}

/** @internal */
export interface PropertyAndValue {
    value: AnyValue;
    property: Property;
}

/** @internal */
export function applyPagingOperation(
    items: Node[],
    orderByClauses: OrderByClause[],
    values: PropertyAndValue[],
    op: 'after' | 'before',
): AsyncArray<Node> {
    const parts = orderByClauses.map((orderByClause, i) => {
        const ands = [] as AnyFilterValue[];
        for (let j = 0; j < i; j += 1) {
            // We pass in the order by clause path joined by `.` as the converter will resolve each part to the correct value
            // if the path down a reference property tree
            ands.push({ [orderByClauses[j].path.join('.')]: { _eq: values[j].value } });
        }
        const operator =
            orderByClause.direction > 0 ? (op === 'before' ? '_lt' : '_gt') : op === 'before' ? '_gt' : '_lt';

        // We pass in the order by clause path joined by `.` as the converter will resolve each part to the correct value
        // if the path down a reference property tree
        ands.push({ [orderByClauses[i].path.join('.')]: { [operator]: values[i].value } });
        return ands.length === 1 ? ands[0] : { _and: ands };
    });

    const filters = parts.length === 1 ? parts[0] : { _or: parts };
    return applyFilter(items, filters);
}

// Largely inspired from https://github.com/Sage/f-streams/blob/master/lib/predicate.ts
function converter(locales?: string[]): (val: AnyValue) => Predicate {
    const pfalse: Predicate = () => false;
    const ptrue: Predicate = () => true;

    // function to check is value passed is an array, if not then return the value converted to a single value array
    const valueToArray = (value: AnyValue): AnyValue[] => {
        if (Array.isArray(value)) return value;

        return [value];
    };

    const ops: { [name: string]: Op } = {
        _eq: val => v => valueComparator(v, val, locales) === 0,
        _ne: val => v => valueComparator(v, val, locales) !== 0,
        _gt: val => v => valueComparator(v, val, locales) > 0,
        _gte: val => v => valueComparator(v, val, locales) >= 0,
        _lt: val => v => valueComparator(v, val, locales) < 0,
        _lte: val => v => valueComparator(v, val, locales) <= 0,
        _in: val => v => valueToArray(val).some(elt => valueComparator(elt, v, locales) === 0),
        _nin: val => v => valueToArray(val).every(elt => valueComparator(elt, v, locales) !== 0),
        _and: val => and(valueToArray(val).map(cvt)),
        _or: val => or(valueToArray(val).map(cvt)),
        _nor: val => not(or(valueToArray(val).map(cvt))),
        _not: val => not(cvt(val)),
        _exists: val => v =>
            (typeof val === 'string' || typeof val === 'number') && v != null && typeof v === 'object' && val in v,
        _type: val => v => typeof v === val,
        _mod: val => v =>
            Array.isArray(val) && typeof val[0] === 'number' && typeof v === 'number' && v % val[0] === val[1],
        _regex: (val, parent) => {
            if (typeof val !== 'string') throw new LogicError(`invalid regex type:${typeof val}`);
            const re = new RegExp(val, ((parent as AnyRecord)._options as string) || '');
            return v => typeof v === 'string' && re.test(v);
        },
    };

    const reTest = (re: RegExp) => (val: AnyValue) => typeof val === 'string' && re.test(val);
    const not =
        (predicate: Predicate): ((arg1: AnyValue) => AsyncResponse<boolean>) =>
        async (obj: AnyValue) =>
            !(await predicate(obj));

    const or = (predicates: Predicate[]): Predicate => {
        if (predicates.length === 0) return pfalse;
        if (predicates.length === 1) return predicates[0];
        return (obj: AnyValue) => asyncArray(predicates).some(predicate => predicate(obj));
    };

    const and = (predicates: Predicate[]): Predicate => {
        if (predicates.length === 0) return ptrue;
        if (predicates.length === 1) return predicates[0];
        return (obj: AnyValue) => asyncArray(predicates).every(predicate => predicate(obj));
    };

    const compose =
        (f: Predicate, g: Predicate): ((arg1: AnyValue) => AsyncResponse<boolean>) =>
        async (obj: AnyValue) =>
            f(await g(obj));

    const deref = (key: string) => (obj: AnyRecord) => {
        if (obj == null) return undefined;
        // TODO: investigate next line. It feels very strange (obj.state instead of obj.$.state ???). Maybe it is obsolete
        if (key === '_index' && obj.$) return (obj.state as AnyRecord).index;
        const v = obj[key];
        return typeof v === 'function' ? v() : v;
    };

    const walk: (p: string) => Predicate = p => {
        const i = p.indexOf('.');
        if (i >= 0) {
            return compose(walk(p.substring(i + 1)), walk(p.substring(0, i)));
        }
        return deref(p);
    };

    const isQuantifiedBy = (val: object, quantifier: string): boolean => quantifier in val;

    const quantifiedFilter = (
        val: AnyFilterObject,
        quantifier: string,
        fn: (count: number, filterObj: AnyFilterObject) => AsyncResponse<boolean>,
    ): ((v: AnyRecord) => AsyncResponse<boolean>) => {
        const condition = cvt(omit(val, quantifier));
        return async v => {
            let count = 0;
            // If the filter is on the parent level but filters a collection
            // { children: { _every: true, intVal: { _eq: 1 } } }
            // The item passed is the collection
            if (v instanceof BaseCollection) {
                count = await v.filter(condition).length;
            }

            // If the filter is on the parent (which is a collection) level but filters
            // { _every: true, intVal: { _eq: 1 } }
            // The item passed is the node of the collection being filtered
            if (v instanceof Node && v.$.state.collection) {
                count = await v.$.state.collection.filter(condition).length;
            }
            return fn(count, val);
        };
    };

    const isFilterObject = (val: AnyFilterValue): val is AnyFilterObject => {
        return val != null && typeof val === 'object';
    };

    const cvt: (val: AnyValue) => Predicate = val => {
        if (val instanceof RegExp) {
            return reTest(val);
        }
        if (isFilterObject(val) && isQuantifiedBy(val, '_atLeast')) {
            return quantifiedFilter(val, '_atLeast', (count, filterObj) => count >= (filterObj._atLeast as number));
        }
        if (isFilterObject(val) && isQuantifiedBy(val, '_atMost')) {
            return quantifiedFilter(val, '_atMost', (count, filterObj) => count <= (filterObj._atMost as number));
        }
        if (isFilterObject(val) && isQuantifiedBy(val, '_none')) {
            return quantifiedFilter(val, '_none', (count, filterObj) => (filterObj._none ? count === 0 : count !== 0));
        }
        if (isFilterObject(val) && isQuantifiedBy(val, '_every')) {
            // every x satisfies y <==> none x satisfies not y
            return cvt({
                _not: { ...omit(val, '_every') },
                _none: val && val._every,
            });
        }

        if (val && isCompound(val)) {
            if (val instanceof Node) return ops._eq(val);

            return and(
                Object.keys(val).map(k => {
                    const v = (val as any)[k];
                    if (k === '_options') {
                        // Skip '_options', it will only be used by the _regex
                        // Just return ptrue as it will have no effect on the 'and'
                        return ptrue;
                    }
                    if (k[0] === '_' && k !== '_id' && k !== '_sortValue') {
                        if (!ops[k]) throw new LogicError(`bad operator: ${k}`);
                        return ops[k](v, val);
                    }
                    return compose(cvt(v), walk(k));
                }),
            );
        }
        return ops._eq(val);
    };
    return cvt;
}
