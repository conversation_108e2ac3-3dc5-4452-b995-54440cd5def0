/** @ignore */ /** */
import { Datetime } from '@sage/xtrem-date-time';
import { AnyValue, AsyncResponse, Dict } from '@sage/xtrem-shared';
import { createHash } from 'crypto';
import { Application } from '../application';
import {
    CollectionPropertyDecorator,
    ReferenceArrayPropertyDecorator,
    ReferencePropertyDecorator,
    StaticThis,
} from '../decorators';
import { Property } from '../properties';
import { createProperty } from '../properties/create-property';
import { Column } from '../sql/schema';
import { getSqlCurrvalOfIdSequence } from '../sql/statements/types-conversion';
import { JoinLiteralValue, Node } from '../ts-api';
import { Context } from './context';
import { CoreHooks } from './core-hooks';
import { loggers } from './loggers';
import { NodeFactory } from './node-factory';
import { PropertyDecorator } from './property';
import {
    _constructorDataType,
    _etagDataType,
    _jsonDataType,
    _sourceIdDataType,
    _syncTickDataType,
    _updateActionDataType,
    _valuesHashDataType,
    nanoIdDataType,
} from './system-data-types';

export const defaultCodeLength = 32;

/** @disabled_internal */
export class SystemProperties {
    private static readonly publishedSystemProperties: Dict<Property[]> = {};

    private static readonly systemProperties: Dict<Property[]> = {};

    private static readonly idProperties: Dict<Property> = {};

    private static readonly idColumns: Dict<Column> = {};

    private static readonly updateTickColumns: Dict<Column> = {};

    private static readonly syncTickColumns: Dict<Column> = {};

    private static readonly syncInfoColumns: Dict<Column> = {};

    private static readonly customDataProperties: Dict<Property> = {};

    private static readonly customDataColumns: Dict<Column> = {};

    private static readonly tenantIdProperties: Dict<Property> = {};

    private static readonly tenantIdColumns: Dict<Column> = {};

    private static readonly sourceIdProperties: Dict<Property> = {};

    private static readonly sourceIdColumns: Dict<Column> = {};

    private static readonly sortValueProperties: Dict<Property> = {};

    private static readonly sortValueColumns: Dict<Column> = {};

    private static readonly updateActionProperties: Dict<Property> = {};

    private static readonly updateActionColumns: Dict<Column> = {};

    private static readonly constructorProperties: Dict<Property> = {};

    private static readonly constructorColumns: Dict<Column> = {};

    private static readonly createUserProperties: Dict<Property> = {};

    private static readonly updateUserProperties: Dict<Property> = {};

    private static readonly vendorProperties: Dict<Property> = {};

    private static readonly updateTickProperties: Dict<Property> = {};

    private static readonly syncTickProperties: Dict<Property> = {};

    private static readonly syncInfoProperties: Dict<Property> = {};

    private static readonly etagProperties: Dict<Property> = {};

    private static readonly valuesHashProperties: Dict<Property> = {};

    private static readonly valuesHashColumns: Dict<Column> = {};

    private static readonly attachmentsProperties: Dict<Property> = {};

    private static readonly tagsProperties: Dict<Property> = {};

    private static readonly notesProperties: Dict<Property> = {};

    private static readonly factoryProperties: Dict<Property> = {};

    /** @internal */
    static getPublishedSystemProperties(factory: NodeFactory): Property[] {
        const cached = SystemProperties.publishedSystemProperties;
        const name = factory.name;
        if (!cached[name]) {
            cached[name] = [this.etagProperty(factory)];
            if (['sql', 'external'].includes(factory.storage || '')) {
                const createStampProperty = createProperty(factory, createStamp, factory.rootFactory.package);
                const updateStampProperty = createProperty(factory, updateStamp, factory.rootFactory.package);
                if (name !== factory.rootFactory.name) {
                    createStampProperty.isInherited = true;
                    updateStampProperty.isInherited = true;
                }
                cached[name].push(createStampProperty);
                cached[name].push(updateStampProperty);
            }
            if (factory.isSynchronizable) {
                const syncTickProperty = createProperty(factory, syncTick, factory.rootFactory.package);
                if (!factory.nodeDecorator.isSynchronizable) {
                    syncTickProperty.isInherited = true;
                }
                cached[name].push(syncTickProperty);
            }
            if (factory.isSynchronized) {
                const syncInfoProperty = createProperty(factory, syncInfo, factory.rootFactory.package);
                if (!factory.nodeDecorator.isSynchronized) {
                    syncInfoProperty.isInherited = true;
                }
                cached[name].push(syncInfoProperty);
            }

            cached[name].forEach(p => {
                p.isSystemProperty = true;
            });
        }

        return cached[name];
    }

    /** @disabled_internal */
    static getSystemProperties(factory: NodeFactory): Property[] {
        const cached = SystemProperties.systemProperties;
        const name = factory.name;
        if (!cached[name]) {
            const createUserDecorator = factory.isSharedByAllTenants
                ? createUserManagedProperty.integer
                : createUserManagedProperty.reference;

            const updateUserDecorator = factory.isSharedByAllTenants
                ? updateUserManagedProperty.integer
                : updateUserManagedProperty.reference;

            if (factory.storage === 'external') {
                createUserDecorator.isNullable = true;
                updateUserDecorator.isNullable = true;
            }
            cached[name] = [
                createProperty(factory, updateTick, factory.rootFactory.package),
                createProperty(factory, createUserDecorator, factory.rootFactory.package),
                createProperty(factory, updateUserDecorator, factory.rootFactory.package),
                ...SystemProperties.getPublishedSystemProperties(factory),
            ];
            if (name !== factory.rootFactory.name) {
                cached[name].forEach(p => {
                    if (p.name !== '_etag' && p.name !== '_syncTick' && p.name !== '_syncInfo') p.isInherited = true;
                });
            }
            if (factory.storage === 'sql') {
                if (factory.hasVendorProperty)
                    cached[name].push(createProperty(factory, vendorManagedProperty.reference, factory.package));
            }
            cached[name].forEach(p => {
                p.isSystemProperty = true;
            });
        }

        return cached[name];
    }

    static getSystemProperty(factory: NodeFactory, name: string): Property | undefined {
        const systemProperties = SystemProperties.getSystemProperties(factory);
        return systemProperties.find(property => property.name === name);
    }

    /** @disabled_internal */
    static isSystemOrTechnicalColumn(factory: NodeFactory, columnName: string): boolean {
        switch (columnName) {
            case '_id':
            case '_tenant_id':
            case '_source_id':
            case '_sort_value':
            case '_custom_data':
            case '_layer':
            case '_vendor':
            case '_constructor': {
                return true;
            }
            default: {
                if (
                    factory.table.columns
                        .filter(column => column.isInternalSystemProperty)
                        .some(column => column.columnName === columnName)
                )
                    return true;
                return false;
            }
        }
    }

    /** @internal */
    static isSystemOrTechnicalProperty(propertyName: string): boolean {
        switch (propertyName) {
            case '_id':
            case '_sourceId':
            case '_sortValue':
            case '_customData':
            case '_layer':
            case '_vendor':
            case '_etag':
            case '_constructor':
            case '_valuesHash':
            case '_updateTick':
            case '_syncTick':
            case '_syncInfo':
            case '_attachments':
            case '_notes':
            case '_tags':
            case '_factory':
            case '_createStamp':
            case '_updateStamp':
            case '_createUser':
            case '_updateUser': {
                return true;
            }
            default: {
                return false;
            }
        }
    }

    /** @internal */
    static getPublishedInputSystemProperties(): string[] {
        return [etagTransient.name];
    }

    private static getProperty(
        factory: NodeFactory,
        cached: Dict<Property>,
        persistent: PropertyDecorator,
        transient: PropertyDecorator,
        ownerFactory = factory,
    ): Property {
        const name = factory.name;
        if (!cached[name]) {
            const isStored = factory.storage === 'sql';
            cached[name] = isStored
                ? createProperty(factory, persistent, ownerFactory.package)
                : createProperty(factory, transient, ownerFactory.package);
            cached[name].isSystemProperty = true;
            cached[name].isInherited = ownerFactory.name !== factory.name;
        }
        return cached[name];
    }

    private static getColumn(factory: NodeFactory, cached: Dict<Column>, property: Property): Column {
        const name = factory.name;
        const isStored = factory.storage === 'sql';
        if (!isStored) throw new Error(`${name} not stored: no column can be created.`);
        if (!cached[name]) cached[name] = new Column(property);
        return cached[name];
    }

    /** @disabled_internal */
    static idProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.idProperties;
        return SystemProperties.getProperty(factory, cached, persistentId, transientId);
    }

    /** @disabled_internal */
    static idColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.idColumns;
        const property = SystemProperties.idProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static updateTickProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.updateTickProperties;
        const name = factory.name;
        if (cached[name]) return cached[name];
        cached[name] = createProperty(factory, updateTick, factory.rootFactory.package);
        if (factory.baseFactory) cached[name].isInherited = true;
        cached[name].isSystemProperty = true;
        return cached[name];
    }

    /** @internal */
    static updateTickColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.updateTickColumns;
        const property = SystemProperties.updateTickProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static syncTickProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.syncTickProperties;
        const name = factory.name;
        if (cached[name]) return cached[name];
        cached[name] = createProperty(factory, syncTick, factory.rootFactory.package);
        if (factory.baseFactory) cached[name].isInherited = true;
        return cached[name];
    }

    /** @internal */
    static syncTickColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.syncTickColumns;
        const property = SystemProperties.syncTickProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static syncInfoProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.syncInfoProperties;
        const name = factory.name;
        if (cached[name]) return cached[name];
        cached[name] = createProperty(factory, syncInfo, factory.rootFactory.package);
        if (factory.baseFactory) cached[name].isInherited = true;
        return cached[name];
    }

    /** @internal */
    static syncInfoColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.syncInfoColumns;
        const property = SystemProperties.syncInfoProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static customDataProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.customDataProperties;
        const decorator = factory.hasVendorProperty ? persistentCustomDataWithVendor : persistentCustomData;
        return SystemProperties.getProperty(factory, cached, decorator, decorator);
    }

    /** @internal */
    static customDataColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.customDataColumns;
        const property = SystemProperties.customDataProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @disabled_internal */
    static sourceIdProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.sourceIdProperties;
        return SystemProperties.getProperty(
            factory,
            cached,
            persistentSourceId,
            transientSourceId,
            factory.rootFactory,
        );
    }

    /** @disabled_internal */
    static sourceIdColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.sourceIdColumns;
        const property = SystemProperties.sourceIdProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static sortValueProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.sortValueProperties;
        const ownerFactory = factory.rootCollectionFactory ?? factory;
        const prop = SystemProperties.getProperty(
            factory,
            cached,
            persistentSortValue(factory),
            transientSortValue,
            ownerFactory,
        );
        prop.isSystemProperty = false;
        return prop;
    }

    /** @internal */
    static sortValueColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.sortValueColumns;

        const property = SystemProperties.sortValueProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static tenantIdProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.tenantIdProperties;
        const name = factory.name;
        if (!cached[name]) {
            const property = createProperty(factory, tenantId, factory.package);
            property.isSystemProperty = true;
            cached[name] = property;
        }
        return cached[name];
    }

    /** @disabled_internal */
    static tenantIdColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.tenantIdColumns;
        const name = factory.name;
        if (!cached[name]) {
            cached[name] = new Column(SystemProperties.tenantIdProperty(factory));
        }
        return cached[name];
    }

    /** @internal */
    static updateActionProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.updateActionProperties;
        const name = factory.name;
        if (!cached[name]) {
            cached[name] = createProperty(factory, updateAction, factory.package);
            cached[name].isSystemProperty = true;
        }
        return cached[name];
    }

    /** @internal */
    static updateActionColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.updateActionColumns;
        const name = factory.name;
        if (!cached[name]) cached[name] = new Column(SystemProperties.updateActionProperty(factory));
        return cached[name];
    }

    /** @internal */
    static constructorProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.constructorProperties;
        const name = factory.name;
        if (!cached[name]) {
            cached[name] = createProperty(factory, constructorProperty, factory.package);
            cached[name].isSystemProperty = true;
        }
        return cached[name];
    }

    /** @internal */
    static constructorColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.constructorColumns;
        const name = factory.name;
        if (!cached[name]) cached[name] = new Column(SystemProperties.constructorProperty(factory));
        return cached[name];
    }

    /** @internal */
    static valuesHashProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.valuesHashProperties;
        const decorator = valuesHash;
        return SystemProperties.getProperty(factory, cached, decorator, decorator);
    }

    /** @internal */
    static valuesHashColumn(factory: NodeFactory): Column {
        const cached = SystemProperties.valuesHashColumns;
        const property = SystemProperties.valuesHashProperty(factory);
        return SystemProperties.getColumn(factory, cached, property);
    }

    /** @internal */
    static attachmentsProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.attachmentsProperties;
        const name = factory.name;
        if (cached[name]) return cached[name];
        const attachmentManager = CoreHooks.getAttachmentManager();
        const attachments: CollectionPropertyDecorator = {
            name: '_attachments',
            type: 'collection',
            isMutable: true,
            isPublished: true,
            dependsOn: ['_id'],
            node: () => attachmentManager.getAttachmentNode() as { new (): Node },
            join: {
                sourceNodeName: new JoinLiteralValue(factory.name),
                sourceNodeId() {
                    return this._id;
                },
            } as any,
            async saveBegin() {
                await this._attachments.forEach(async attachment => {
                    await attachment.$.set({ sourceNodeId: this._id, sourceNodeName: factory.name });
                });
            },
        };
        cached[name] = createProperty(
            factory,
            attachments,
            factory.systemPropertyDefiningPackage._attachments ?? factory.rootFactory.package,
        );
        cached[name].isSystemProperty = true;
        if (factory.baseFactory) cached[name].isInherited = true;
        return cached[name];
    }

    /**
     * The _tags system property (a reference array)
     * @internal
     */
    static tagsProperty(factory: NodeFactory): Property {
        const cache = SystemProperties.tagsProperties;
        const name = factory.name;
        if (cache[name]) return cache[name];
        const tagsManager = CoreHooks.getTagManager();

        const tagsDecorator: ReferenceArrayPropertyDecorator = {
            name: '_tags',
            type: 'referenceArray',
            isPublished: true,
            onDelete: 'remove',
            node: () => tagsManager.getTagNode() as { new (): Node },
            isStored: true,
            defaultValue: [],
            serviceOptions: () => [tagsManager.getServiceOption()],
        };
        cache[name] = createProperty(
            factory,
            tagsDecorator,
            factory.systemPropertyDefiningPackage._tags ?? factory.rootFactory.package,
        );
        cache[name].isSystemProperty = true;
        if (factory.baseFactory) cache[name].isInherited = true;
        return cache[name];
    }

    /**
     * The _notes system property (a reference array)
     * @internal
     */
    static notesProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.notesProperties;
        const name = factory.name;
        if (cached[name]) return cached[name];
        const noteManager = CoreHooks.getNoteManager();
        const notes: CollectionPropertyDecorator = {
            name: '_notes',
            type: 'collection',
            isMutable: true,
            isPublished: true,
            dependsOn: ['_id'],
            node: () => noteManager.getNoteNode() as { new (): Node },
            join: {
                sourceNodeName: new JoinLiteralValue(factory.name),
                sourceNodeId() {
                    return this._id;
                },
            } as any,
            async saveBegin() {
                await this._notes.forEach(async note => {
                    await note.$.set({ sourceNodeId: this._id, sourceNodeName: factory.name });
                });
            },
        };
        cached[name] = createProperty(
            factory,
            notes,
            factory.systemPropertyDefiningPackage._notes ?? factory.rootFactory.package,
        );
        cached[name].isSystemProperty = true;
        if (factory.baseFactory) cached[name].isInherited = true;
        return cached[name];
    }

    /** @internal */
    static factoryProperty(factory: NodeFactory): Property | null {
        const metaNodeFactoryConstructor = CoreHooks.metadataManager.getMetaNodeFactoryConstructor();
        if (!metaNodeFactoryConstructor) return null;
        if (factory.storage !== 'sql') return null;
        const cached = SystemProperties.factoryProperties;
        const name = factory.name;
        if (cached[name]) return cached[name];

        const hasConstructorProperty = factory.isAbstract || !!factory.baseFactory;
        const join = hasConstructorProperty
            ? {
                  name(this: Node & { _constructor: string }) {
                      return this._constructor;
                  },
              }
            : {
                  name: new JoinLiteralValue(factory.name),
              };

        const decorator: ReferencePropertyDecorator = {
            name: '_factory',
            type: 'reference',
            isPublished: true,
            canLookup: false,
            lookupAccess: true,
            node: () => metaNodeFactoryConstructor,
            join,
        };

        cached[name] = createProperty(
            factory,
            decorator,
            factory.systemPropertyDefiningPackage._factory ?? factory.rootFactory.package,
        );
        cached[name].isSystemProperty = true;
        if (factory.baseFactory) cached[name].isInherited = true;
        return cached[name];
    }

    static getReferencedFactory(application: Application, referenceDecorator: PropertyDecorator): NodeFactory | null {
        try {
            const nodeConstructor = referenceDecorator.node?.();
            return nodeConstructor ? application.getFactoryByConstructor(nodeConstructor) : null;
        } catch (e) {
            loggers.runtime.error(`failed to get referenced factory ${referenceDecorator.name}: ${e.message}`);
            return null;
        }
    }

    static isReferencedFactory(
        factory: NodeFactory,
        referenceFactoryName: string,
        referenceDecorator: PropertyDecorator,
    ): boolean {
        try {
            return factory.name === referenceDecorator.node?.().name;
        } catch {
            return [referenceFactoryName, `Test${referenceFactoryName}`].includes(factory.name);
        }
    }

    /** @internal */
    private static makeManagedReferenceProperty(
        factory: NodeFactory,
        managedProperty: ManagedProperty,
        cached: Dict<Property>,
        ownerFactory = factory,
    ): Property {
        const name = factory.name;
        let isReference = managedProperty.isReference(factory);
        if (isReference) {
            // Handle special cases where the managed factory is not yet added to the application
            // For instance, Company.updateUser will be created as an integerProperty (instead of a referenceProperty)
            // Also disable foreign key for User table, because of the self reference.
            // TODO: Revisit later
            if (SystemProperties.getReferencedFactory(factory.application, managedProperty.reference) == null) {
                isReference = false;
            }
        }
        if (!cached[name] || cached[name].isReferenceProperty() !== isReference) {
            cached[name] = createProperty(
                factory,
                isReference ? managedProperty.reference : managedProperty.integer,
                ownerFactory.package,
            );
            cached[name].isSystemProperty = true;

            cached[name].isInherited = ownerFactory.name !== factory.name;
        }
        return cached[name];
    }

    /** @internal */
    static createUserProperty(factory: NodeFactory): Property {
        return this.makeManagedReferenceProperty(
            factory,
            createUserManagedProperty,
            SystemProperties.createUserProperties,
            factory.rootFactory,
        );
    }

    /** @internal */
    static isUserManagementProperty(property: Property): boolean {
        return [createUserManagedProperty.reference.name, updateUserManagedProperty.reference.name].includes(
            property.name,
        );
    }

    /** @internal */
    static isAttachmentsProperty(property: Property): boolean {
        return property.name === '_attachments';
    }

    /** @internal */
    static isNotesProperty(property: Property): boolean {
        return property.name === '_notes';
    }

    /** @internal */
    static updateUserProperty(factory: NodeFactory): Property {
        return this.makeManagedReferenceProperty(
            factory,
            updateUserManagedProperty,
            SystemProperties.updateUserProperties,
            factory.rootFactory,
        );
    }

    /** @internal */
    static vendorProperty(factory: NodeFactory): Property {
        const ownerFactory = factory.rootVendorFactory ?? factory;

        return this.makeManagedReferenceProperty(
            factory,
            vendorManagedProperty,
            SystemProperties.vendorProperties,
            ownerFactory,
        );
    }

    /** @internal */
    static etagProperty(factory: NodeFactory): Property {
        const cached = SystemProperties.etagProperties;
        const name = factory.name;
        if (!cached[name]) cached[name] = createProperty(factory, etagTransient, factory.package);
        return cached[name];
    }

    static getInternalSystemProperties(factory: NodeFactory): Property[] {
        const systemProperties: Property[] = [];

        // _id property
        const idPropToAdd = SystemProperties.idProperty(factory);

        // For subclasses, _id can't be auto-incremented. We must be able to set _id's value when creating a new node.
        if (factory.decorators.superDecorators) idPropToAdd.isAutoIncrement = false;

        systemProperties.push(idPropToAdd);

        // _sourceId
        systemProperties.push(SystemProperties.sourceIdProperty(factory));

        // Non platform nodes can be have custom data.
        if (!factory.isPlatformNode && factory.storage === 'sql')
            systemProperties.push(SystemProperties.customDataProperty(factory));

        // Add _sortValue system property for vital child collections.
        if (factory.isVitalCollectionChild && !factory.isAssociationChild) {
            const sortValueProp = SystemProperties.sortValueProperty(factory);
            systemProperties.push(sortValueProp);
        }

        // abstract class : add the constructor property.
        if (factory.isAbstract) systemProperties.push(SystemProperties.constructorProperty(factory));
        if (!factory.isSharedByAllTenants && ['sql', 'external'].includes(factory.storage || '')) {
            systemProperties.push(SystemProperties.createUserProperty(factory));
            systemProperties.push(SystemProperties.updateUserProperty(factory));
        }
        // _updateTick
        if (['sql'].includes(factory.storage || '')) {
            systemProperties.push(SystemProperties.updateTickProperty(factory));
        }

        if (factory.hasVendorProperty) systemProperties.push(SystemProperties.vendorProperty(factory));
        if (factory.isContentAddressable) systemProperties.push(SystemProperties.valuesHashProperty(factory));

        if (factory.hasAttachments && !factory.isAbstract)
            systemProperties.push(SystemProperties.attachmentsProperty(factory));

        if (factory.hasTags && !factory.isAbstract) systemProperties.push(SystemProperties.tagsProperty(factory));

        if (factory.hasNotes && !factory.isAbstract) systemProperties.push(SystemProperties.notesProperty(factory));

        const metaNodeFactoryProperty = SystemProperties.factoryProperty(factory);
        if (metaNodeFactoryProperty) systemProperties.push(metaNodeFactoryProperty);

        return systemProperties;
    }
}

/** entityTag */
const etagTransient = {
    name: '_etag',
    type: 'string',
    isPublished: true,
    excludedFromPayload: true,
    dataType: () => _etagDataType,
    async computeValue() {
        return createHash('sha256')
            .update((await this.$.updateStamp).toString())
            .digest('base64');
    },
} as PropertyDecorator;

const createStamp = {
    name: '_createStamp',
    type: 'datetime',
    isStored: true,
    excludedFromPayload: true,
    lookupAccess: true,
    defaultValue(): AsyncResponse<Datetime> {
        return Datetime.now();
    },
    sqlAttributes: {
        default: 'now()', // lower case 'now()' as postgres converts it to lowercase and it will be a column consistency during the upgrade if we use NOW()
    },
} as PropertyDecorator;

const updateStamp = {
    name: '_updateStamp',
    type: 'datetime',
    isStored: true,
    excludedFromPayload: true,
    lookupAccess: true,
    defaultValue(): Datetime {
        return Datetime.now();
    },
    sqlAttributes: {
        default: 'now()', // lower case 'now()'  as postgres converts it to lowercase and it will be a column consistency during the upgrade if we use NOW()
    },
} as PropertyDecorator;

const updateTick = {
    name: '_updateTick',
    type: 'integer',
    isStored: true,
    isPublished: true,
    excludedFromPayload: true,
    defaultValue(): number {
        return 1;
    },
} as PropertyDecorator;

const syncTick = {
    name: '_syncTick',
    type: 'decimal',
    dataType: () => _syncTickDataType,
    isStored: true,
    isPublished: true,
    excludedFromPayload: true,
    defaultValue(): number {
        return 0;
    },
    exportValue: 0,
} as PropertyDecorator;

const syncInfo = {
    name: '_syncInfo',
    type: 'json',
    isStored: true,
    isPublished: true,
    excludedFromPayload: true,
    defaultValue: {},
    exportValue: {},
} as PropertyDecorator;

/** A property that is under the control of a manager and thus requires special processing */
interface ManagedProperty {
    factoryName: string;
    reference: PropertyDecorator;
    integer: PropertyDecorator;
    isNullable?: boolean;
    defaultValue?: AnyValue | (() => AsyncResponse<AnyValue>);
    isReference: (factory: NodeFactory) => boolean;
}

/** Options to make properties that are under the control of a manager*/
interface MakeManagedPropertyOptions {
    factoryName: string;
    propertyName: string;
    node: () => { new (): any };
    isNullable?: boolean;
    defaultValue?: AnyValue | (() => AsyncResponse<AnyValue>);
    isReference: (factory: NodeFactory) => boolean;
    ignoreIsActive?: boolean;
    isPublished?: boolean;
    lookupAccess?: boolean;
    canLookup?: false;
}

function makeManagedProperty(options: MakeManagedPropertyOptions): ManagedProperty {
    const {
        factoryName,
        propertyName,
        node,
        isNullable,
        defaultValue,
        isReference,
        ignoreIsActive,
        isPublished,
        lookupAccess,
        canLookup,
    } = options;
    return {
        factoryName,
        reference: {
            name: propertyName,
            type: 'reference',
            isStored: true,
            isNullable,
            node,
            defaultValue: defaultValue == null ? null : defaultValue,
            ignoreIsActive,
            isPublished,
            lookupAccess,
            canLookup,
        } as PropertyDecorator,
        integer: {
            name: propertyName,
            type: 'integer',
            isStored: true,
            isNullable,
            defaultValue: defaultValue == null ? null : defaultValue,
        } as PropertyDecorator,
        isReference,
    };
}

function getUserNode(): StaticThis<Node> {
    return CoreHooks.sysManager.getUserNode();
}

const createUserManagedProperty = makeManagedProperty({
    factoryName: 'User',
    propertyName: '_createUser',
    node: () => getUserNode(),
    isReference: (factory: NodeFactory) => !factory.isSharedByAllTenants,
    ignoreIsActive: true,
    isPublished: true,
    lookupAccess: true,
    canLookup: false,
});

const updateUserManagedProperty = makeManagedProperty({
    factoryName: 'User',
    propertyName: '_updateUser',
    node: () => getUserNode(),
    isReference: (factory: NodeFactory) => !factory.isSharedByAllTenants,
    ignoreIsActive: true,
    isPublished: true,
    lookupAccess: true,
    canLookup: false,
});

const vendorManagedProperty = makeManagedProperty({
    factoryName: 'SysVendor',
    propertyName: '_vendor',
    node: () => Context.dataSettingsManager.getSysVendorNode(),
    isPublished: true,
    isNullable: true,
    isReference: (factory: NodeFactory) => !(factory.isSharedByAllTenants || factory.isPlatformNode),
});

const transientId = {
    name: '_id',
    type: 'string',
    isPublished: true,
} as PropertyDecorator;

const persistentId = {
    ...transientId,
    type: 'integer',
    isStored: true,
    isAutoIncrement: true,
    isTransientInput: false,
    lookupAccess: true,
} as PropertyDecorator;

const persistentCustomData = {
    name: '_customData',
    dataType: () => _jsonDataType,
    type: 'json',
    isNullable: true,
    isTransientInput: false,
    defaultValue: {},
    isStored: true,
    isPublished: true,
    lookupAccess: true,
} as PropertyDecorator;

const persistentCustomDataWithVendor = {
    ...persistentCustomData,
    isOwnedByCustomer: true,
} as PropertyDecorator;

const tenantId = {
    name: '_tenantId',
    isStored: true,
    type: 'string',
    dataType: () => nanoIdDataType,
} as PropertyDecorator;

const transientSourceId = {
    name: '_sourceId',
    type: 'string',
    dataType: () => _sourceIdDataType,
    isPublished: true,
    isTransientInput: true,
    dependencyIndex: 0,
} as PropertyDecorator;

const persistentSourceId = {
    ...transientSourceId,
    isStored: true,
    isTransientInput: false,
} as PropertyDecorator;

const transientSortValue = {
    name: '_sortValue',
    type: 'integer',
    isPublished: true,
} as PropertyDecorator;

const valuesHash = {
    name: '_valuesHash',
    type: 'string',
    dataType: () => _valuesHashDataType,
    isStored: true,
    excludedFromPayload: true,
} as PropertyDecorator;

const persistentSortValue = (factory: NodeFactory): PropertyDecorator => {
    const rootFactory = factory.rootFactory;
    return {
        ...transientSortValue,
        isStored: true,
        allowedInUniqueIndex: true,
        lookupAccess: true,
        /**
         * The SQL default of the sortValue is the currval of the _id sequence * 100
         * When defaulting on insert the currval will be the same as the _id.
         */
        sqlAttributes: {
            default: rootFactory.fullTableName
                ? `(${getSqlCurrvalOfIdSequence(rootFactory.fullTableName)} * 100)`
                : undefined,
            excludeFromInsertIfNull: true,
        },
    } as PropertyDecorator;
};

const updateAction = {
    name: '_action',
    type: 'enum',
    dataType: () => _updateActionDataType,
    isNullable: true,
    excludedFromPayload: true,
    isPublished: true,
    isTransientInput: true,
} as PropertyDecorator;

const constructorProperty = {
    name: '_constructor',
    isStored: true,
    type: 'string',
    dataType: () => _constructorDataType,
    isPublished: true,
    lookupAccess: true,
} as PropertyDecorator;

// TODO: _update_tick should be in this list but we don't have any triggers for it in shared tables
export const sharedDatabaseComputedColumnNames = Object.freeze(['_create_stamp', '_update_stamp']);
export const nonSharedDatabaseComputedColumnNames = Object.freeze([
    '_create_user',
    '_update_user',
    '_create_stamp',
    '_update_stamp',
    '_update_tick',
]);

export const databaseComputedColumnNames = (isSharedByAllTenants: boolean): readonly string[] => {
    return isSharedByAllTenants ? sharedDatabaseComputedColumnNames : nonSharedDatabaseComputedColumnNames;
};

// Explicit ordering of the system columns on a table.
export const orderedSystemColumns = Object.freeze([
    '_tenant_id',
    '_id',
    '_constructor',
    '_sort_value',
    '', // This entry is important as it indicates the split of systems columns in a table. Column before will be created at the start of the table and after will be created at the end
    '_vendor',
    '_create_user',
    '_update_user',
    '_create_stamp',
    '_update_stamp',
    '_update_tick',
    '_source_id',
    '_custom_data',
]);
