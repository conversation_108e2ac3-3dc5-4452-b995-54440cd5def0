/** @ignore */ /** */
import { AnyValue, asyncArray, As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Async<PERSON>eader, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Datetime } from '@sage/xtrem-date-time';
import { Connection, ConnectionPool, SqlExecuteOptions } from '@sage/xtrem-postgres';
import { Dict, LogicError } from '@sage/xtrem-shared';
import { NodeFactory } from '.';
import { NodeState, StateStatus } from '../node-state';
import { IsolationOptions } from '../ts-api/isolation-options';
import { Context } from './context';
import { CoreHooks } from './core-hooks';
import { Debug } from './debug';
import { loggers } from './loggers';

/** @internal */
export type DeferredAction = () => Promise<void>;

/** @internal */
export class Transaction {
    // The postgres SQL Client
    private _connection?: Connection;

    private _id = -1;

    protected debugId: number;

    skipUnsavedCheck = false;

    constructor(
        public readonly context: Context,
        private readonly _options = {} as TransactionOptions,
    ) {
        this.debugId = Debug.newId();
    }

    transientNodeStates = {} as Dict<NodeState>;

    readonlyNodeStates = {} as Dict<NodeState>;

    writableNodeStates = {} as Dict<NodeState>;

    readonly #deferredActionQueue = [] as DeferredAction[];

    /**
     * #factoryTicks tracks changes in factory tables.
     *
     * They are updated every time a record is inserted, updated or deleted in one of the factory's tables.
     * Note: there may be several tables for a factory because of subclassing. We track the ticks on the root table.
     *
     * These ticks are used to detect when cached collections are invalid and must be refetched from the database.
     */
    #factoryTicks = new Map<NodeFactory, number>();

    get id(): Promise<number | undefined> {
        return (async () => {
            if (this._id >= 0) return this._id;
            const res = (await this.executeSql<{ tid: number }[]>('select txid_current() as tid', []))[0];
            this._id = res?.tid;
            return this._id;
        })();
    }

    get isWritable(): boolean {
        return !this._options.isReadonly;
    }

    get connection(): Connection {
        if (!this._connection) throw new Error('transaction is not isolated');
        return this._connection;
    }

    private get sqlPool(): ConnectionPool {
        return this.context.sqlPool;
    }

    queueDeferredAction(action: DeferredAction): void {
        this.#deferredActionQueue.push(action);
    }

    async flushDeferredActions(): Promise<void> {
        if (this.#deferredActionQueue.length === 0) return;
        await asyncArray(this.#deferredActionQueue).forEach(action => action());
        this.#deferredActionQueue.length = 0;
    }

    private checkUnsaved(): void {
        if (this.skipUnsavedCheck) return;

        // ignore updatable nodes that haven't been modified
        const isStatusUnsaved = (status: StateStatus): boolean =>
            status !== StateStatus.updatable && status !== StateStatus.constructed && status !== StateStatus.stale;
        const keys = Object.keys(this.writableNodeStates).filter(
            k =>
                !this.writableNodeStates[k].isTransient &&
                !this.writableNodeStates[k].isOnlyForDefaultValues &&
                !this.writableNodeStates[k].isOnlyForDuplicate &&
                !this.writableNodeStates[k].isOnlyForLookup &&
                !this.writableNodeStates[k].factory.isContentAddressable &&
                isStatusUnsaved(this.writableNodeStates[k].status),
        );
        if (keys.length > 0) {
            throw new Error(`transaction error: modified nodes have not been saved: ${keys.slice(0, 10).join(', ')}`);
        }
    }

    /**
     * Fetch the replica lag and returns the number of milliseconds the replica is behind the master
     * @returns
     */
    private async getReplicaLag(): Promise<number> {
        let lagSql: string;
        if (ConfigManager.current.deploymentMode === 'production') {
            // pg_last_xact_replay_timestamp is not available in Aurora, so we use the aurora_replica_status function
            lagSql = 'select  MAX(replica_lag_in_msec) lag_ms from aurora_replica_status()'; // Aurora
        } else {
            lagSql =
                'SELECT FLOOR(extract(epoch FROM clock_timestamp()) - extract(epoch FROM pg_last_xact_replay_timestamp())) lag_ms';
        }

        const lagInfoResult = await this._executeSql<{ lag_ms: number }[]>(this.context.replicaPool, lagSql, []);

        if (lagInfoResult.length > 0) {
            const lagInfo = lagInfoResult[0];

            const lag = Number(lagInfo.lag_ms);
            if (Number.isFinite(lag)) {
                return lag;
            }
        }

        // Lag query failed, we assume the replica is behind, we should never reach this point
        throw new Error('Failed to get replica lag');
    }

    /**
     * Promise that caches the replica lag
     * This promise is recomputed, if necessary, by replicaHasLag()
     */
    #cachedReplicaLag: Promise<number> | undefined;

    /** Returns true if the replica has a lag that matters to this transaction/context */
    private async replicaCanBeUsed(): Promise<boolean> {
        if (this.context.lastCommitTimestamp == null) {
            // If we don't have a last commit timestamp, we can use the replica pool
            this.invalidateReplicaLag();
            return true;
        }

        const lastCommitMillis = this.context.lastCommitTimestamp.value;
        let replicaLagMillis = await this.#cachedReplicaLag;
        const nowMillis = Datetime.now(true).value;

        if (
            // If we don't know the lag we have to query it
            replicaLagMillis === undefined ||
            // If we know the lag and now is after it, we have to query it again
            // because replication may have slowed down and the lag may have increased since we last queried it
            nowMillis >= lastCommitMillis + replicaLagMillis
        ) {
            // Query the lag and cache its promise.
            this.#cachedReplicaLag = this.getReplicaLag();
            // Update replicaLagMillis
            replicaLagMillis = await this.#cachedReplicaLag;
        }
        // We have a lag and we can trust it because we have refreshed it if necessary
        if (nowMillis < lastCommitMillis + replicaLagMillis) {
            // Now is within the lag, we cannot use the replica pool.
            return false;
        }
        // Now is after the lag. We reset lastCommitStamp, we can use the replica pool
        this.context.lastCommitTimestamp = null;
        return true;
    }

    /**
     * Invalidates the replica lag promise so that it is recomputed
     */
    invalidateReplicaLag(): void {
        this.#cachedReplicaLag = undefined;
    }

    private async getReaderPool(): Promise<ConnectionPool> {
        // If the replica is not behind, we can use the replica pool
        if (await this.replicaCanBeUsed()) {
            return this.context.replicaPool;
        }
        loggers.sql.verbose(() => 'Replica is behind of the master, using the replica pool');

        return this.context.masterPool;
    }

    // Execute connection management

    // Executes a single SQL statement
    async executeSql<T extends AnyValue>(sql: string, args: AnyValue[], opts?: SqlExecuteOptions): Promise<T> {
        const sqlPool = this.context.prefersReaderPool ? await this.getReaderPool() : this.sqlPool;
        return this._executeSql(sqlPool, sql, args, opts);
    }

    // Executes a single SQL statement
    private async _executeSql<T extends AnyValue>(
        sqlPool: ConnectionPool,
        sql: string,
        args: AnyValue[],
        opts?: SqlExecuteOptions,
    ): Promise<T> {
        const cnx = this._connection ?? (await sqlPool.allocConnection(this.context.originId));
        try {
            // Pools are in charge of profiling
            return await sqlPool.execute<T>(cnx, sql, args, opts);
        } finally {
            if (!this._connection) sqlPool.releaseConnection(cnx);
        }
    }

    // Write connection management

    private sqlIsolation(): string {
        switch (this._options.isolationLevel) {
            case 'high':
                return 'SERIALIZABLE';
            case 'medium':
                return 'REPEATABLE READ';
            case 'low':
                return 'READ COMMITTED';
            default:
                throw new LogicError(`invalid isolation level: ${this._options.isolationLevel}`);
        }
    }

    private sqlReadonlyOptions(): string {
        if (!this._options.isReadonly) return '';
        return `READ ONLY ${this._options.isDeferrable ? 'DEFERRABLE' : ''}`;
    }

    /**
     * Returns the SQL that must executed to set the user of a transaction
     * @returns
     */
    async getSqlToSetSessionUser(): Promise<string> {
        const user =
            this.context.tenantId == null || this.context.withoutTransactionUser ? null : await this.context.user;
        const transactionUser = await this.context.transactionUser;
        const login = (await this.context.loginUser) || user;
        const isAuditEnabled = await CoreHooks.auditManager.isAuditEnabled(this.context);

        return `SELECT set_config('xtrem.transaction_user_id', '${transactionUser?._id || ''}', true);
        SELECT set_config('xtrem.user_email', '${user?.email}', true);
        SELECT set_config('xtrem.login_email', '${login?.email}', true);
        SELECT set_config('xtrem.is_audit_enabled', '${isAuditEnabled ? 'true' : 'false'}', true);
        SELECT set_config('xtrem.locale', '${this.context.currentLocale}', true);
        `;
    }

    async resetSessionUser(): Promise<void> {
        await this.executeSql(await this.getSqlToSetSessionUser(), []);
    }

    // Handles connection allocation/release and commit/rollback logic for writable transaction
    async withCommit<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        if (this.context.managedExternal) return body();
        if (this._connection) throw new Error('nested _withCommit');

        loggers.sql.debug(() => `BEGIN TRANSACTION: debugId=${this.debugId}, isWritable=${!this._options.isReadonly}`);

        this._connection = await this.sqlPool.allocConnection(this.context.originId);
        // Note: don't remove the '-- ** CONFIGURATION **' comment, it will be used to filter the sql command
        // when recording the statements executed by the upgrade (same thing for the 'end' statement)
        const begin = this.context.isAutoCommit
            ? ''
            : `BEGIN TRANSACTION ISOLATION LEVEL ${this.sqlIsolation()} ${this.sqlReadonlyOptions()};
        `;
        await this.sqlPool.execute(
            this._connection,
            `-- ** CONFIGURATION **
${begin}
SELECT set_config('xtrem.origin_id', '${this.context.originId}', true);
${await this.getSqlToSetSessionUser()}
${this.context.disableAllCrudNotifications ? "SELECT set_config('xtrem.notification.disable.ALL', 'true', true);" : ''}
${
    this.context.disableTenantCrudNotifications
        ? `SELECT set_config('xtrem.notification.disable.t_${this.context.tenantId}', 'true', true);`
        : ''
}
`,
            [],
            { logLevel: 'debug' },
        );

        try {
            const result = await body();
            await this.context.flushDeferredSaves();
            if (this.context.isAutoCommit) {
                // nothing
            } else if (this.context.mayCommit) {
                this.checkUnsaved();
                loggers.sql.debug(() => `COMMIT TRANSACTION: debugId=${this.debugId}`);
                await this.flushDeferredActions();
                await this.commit();
            } else if (this.isWritable) {
                loggers.sql.debug(() => `ROLLBACK TRANSACTION: debugId=${this.debugId}`);
                await this.rollback();
            }
            return result;
        } catch (ex) {
            loggers.sql.debug(() => `TRANSACTION FAILED debugId=${this.debugId}, stack=${ex.stack}`);
            // TODO: try/catch because rollback fails on tedious - investigate later...
            try {
                await this.rollback();
            } catch (ex2) {
                loggers.sql.error(`ROLLBACK FAILED${ex2.stack}`);
            }
            throw ex;
        }
    }

    close(): void {
        if (!this._connection) return;
        this.sqlPool.releaseConnection(this._connection);
        this._connection = undefined;
        this._id = -1;
    }

    // Low-level commit
    private async commit(): Promise<void> {
        if (!this._connection) throw new Error('invalid commit: no connection');
        await this.context.notifyModifiedCachedCategories();
        await this._connection.query('COMMIT');

        this.context.lastCommitTimestamp = Datetime.now(true);
        if (this.context.isHttp()) {
            this.context.setLastCommitTimestampCookie();
        }
        await this.context.commitModifiedCachedCategories();
        this.commitCache();

        // Send successful CRUD operations to be broadcasted
        this.context.sendDeferredMessagesForBroadcast();
    }

    // Low-level rollback
    private async rollback(): Promise<void> {
        if (!this._connection) throw new Error('invalid commit: no connection');
        await this._connection.query('ROLLBACK');
        this.context.rollbackModifiedCachedCategories();
        this.rollbackCache();
    }

    // Reader connection management

    // Low-level call to create a SQL reader
    // Ensures that the connection will be properly released when the reader completes.
    createSqlReader<T extends AnyValue>(
        sql: string,
        args: AsyncResponse<AnyValue[]>,
        opts?: SqlExecuteOptions,
    ): AsyncReader<T> {
        let sqlPool: ConnectionPool;
        let cnx: Connection | undefined;
        const connection = this._connection;
        let reader: AsyncReader<T>;
        const read = async (): Promise<T | undefined> => {
            if (!cnx) {
                sqlPool = this.context.prefersReaderPool ? await this.getReaderPool() : this.sqlPool;
                cnx = connection ?? (await sqlPool.allocConnection(this.context.originId));
                reader = sqlPool.createReader<T>(cnx, sql, await args, opts);
            }
            const val = await reader.read();
            if (val === undefined) await stop();
            return val;
        };
        const stop = async (): Promise<void> => {
            if (cnx) {
                if (connection !== this._connection) throw new Error('connection mismatch in stop');
                if (!sqlPool) sqlPool = this.context.prefersReaderPool ? await this.getReaderPool() : this.sqlPool;
                if (!connection) {
                    sqlPool.releaseConnection(cnx);
                }
                cnx = undefined;
            }
            if (reader) {
                // Note : when the connection failed (no TNS listener for instance), the reader is not set
                await reader.stop();
            }
        };
        return new AsyncGenericReader<T>({ read, stop });
    }

    // Low-level object cache management during commit/rollback operations

    private static markStatesAsStale(states: Dict<NodeState>): void {
        Object.values(states).forEach(state => {
            state.status = StateStatus.stale;
        });
    }

    private static revertStatesToThunk(states: Dict<NodeState>): void {
        Object.values(states).forEach(state => {
            state.isThunk = true;
            state.references.clear();
            state.referenceArrays.clear();
            state.collections.clear();
        });
    }

    /** @see #factoryTicks */
    getFactoryTick(factory: NodeFactory): number {
        return this.#factoryTicks.get(factory.rootFactory) ?? 0;
    }

    /** @see #factoryTicks */
    incrementFactoryTick(factory: NodeFactory): void {
        this.#factoryTicks.set(factory.rootFactory, this.getFactoryTick(factory) + 1);
        // Postgres will cascade-delete the vital references but we need to increment the ticks of these factories
        // so that any collection pointing to these factories will be invalidated and re-computed.
        factory.vitalProperties.forEach(prop => {
            this.incrementFactoryTick(prop.targetFactory);
        });
    }

    /**
     * @internal
     */
    rollbackCache(): void {
        // must mark all nodes as stale, as saved nodes are already in readonly cache
        Transaction.markStatesAsStale(this.readonlyNodeStates);
        Transaction.markStatesAsStale(this.writableNodeStates);
        this.readonlyNodeStates = {};
        this.writableNodeStates = {};
    }

    private commitCache(): void {
        // Update the states which are cached in the pooled transaction
        const pooledTransaction = this.context.pooledTransaction;
        if (pooledTransaction === this) throw new LogicError('cannot call commitCache on pooled transaction');
        if (pooledTransaction) {
            // Revert all states to thunk as database update may have modified their state
            // This avoids a bug with cross references (negative ids that are allocated when we save).
            // TODO: see if we can improve this and fix the state so that we can keep it.
            Transaction.revertStatesToThunk(this.readonlyNodeStates);
            Transaction.revertStatesToThunk(this.writableNodeStates);
            this.readonlyNodeStates = {};
            this.writableNodeStates = {};
        }
    }

    async withConnection<BodyReturnType extends AnyValue>(
        body: (cnx: Connection) => AsyncResponse<BodyReturnType>,
    ): Promise<BodyReturnType> {
        const sqlPool = this.context.prefersReaderPool ? await this.getReaderPool() : this.sqlPool;
        const cnx = this._connection ?? (await sqlPool.allocConnection(this.context.originId));
        try {
            // Pools are in charge of profiling
            return await body(cnx);
        } finally {
            if (!this._connection) sqlPool.releaseConnection(cnx);
        }
    }
}

export interface TransactionOptions extends IsolationOptions {
    noCommit?: boolean;

    /** is the transaction in autocommit mode - overrides other transaction options  */
    isAutoCommit?: boolean;
}
