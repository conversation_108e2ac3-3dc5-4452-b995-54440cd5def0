import { AnyR<PERSON>ord, funnel, Funnel } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import type { AnyValue, Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { zipObject } from 'lodash';
import { isScalar, LogicError, Node, OrderBy } from '..';
import { CollectionProperty } from '../properties';
import type { NodeFactory, ReferenceProperty } from '../system-exports';
import type { Context } from './context';

/**
 * This file implements prefetching for the framework.
 *
 * Each context has a context.prefetcher field that references a ContextPrefetcher instance.
 * This instance tracks the keys of records that may be fetched later, and of the records that have already been fetched.
 *
 * Prefetching proceeds in two passes:
 *
 * 1. A visit pass.
 *    In this pass, the visit method is called for each record that was just fetched from the database,
 *    or that is being inserted/updated. It scans the references and collections of the record and it collects their keys
 *    in the prefetcher buckets.
 * 2. A fetch pass.
 *    When the frameork needs to read or query records, it calls the prefetcher tryRead/tryQuery method, instead of
 *    fetching directly from the database.
 *    These methods return the record(s) if they can be prefetched, undefined otherwise.
 *    If the prefetcher return undefined, the framework fetches the records.
 *
 * The prefetcher uses buckets to track the keys that have been collected during the visit pass, and the records
 * that have been fetched.
 * There are several buckets for each factory because we have to track references and collections separately
 * (references have complete keys whereas collection have partial keys), and because records that have been read with
 * forUpdate false cannot be used when we read with forUpdate true.
 *
 * When tryRead is called, the prefetcher checks if the requested key is already in the bucket map. If so, it returns it.
 * Otherwise, it fetches all the keys that have been collected, including the requested key.
 * All the records read are put in the bucket's fetched records map, indexed by their key.
 * The prefetcher only returns the record that was requested. The other records are stored in the bucket map.
 * When a subsequent tryRead is called in the same transaction, the prefetcher is likely to find the record in the map.
 *
 * This reduces significantly the number of database calls.
 *
 * Let us see how this works when we read a sales order, its lines and the items of the lines.
 * - First, we read the sales order header (1 SQL query)
 * - Then we read the lines (1 SQL query)
 * - The line records are visited. All their item keys are collected in the 'Item' bucket.
 * - Then the application code processes the first line and reads its item.
 * - The prefetcher checks if the item is already in the bucket. It won't be because it was not fetched yet.
 * - So it fetches the item key and all the other keys that have collected in the 'Item' bucket (1 SQL query).
 * - The prefetcher returns the item that was requested (or null if not found) and puts the fetched records in the bucket's map.
 * - When the application code processes the following lines and reads their items, the prefetcher finds the items in the
 *   bucket map, without having to fetch them from the database.
 *
 * So for a sales order with 50 lines and 50 items, we have only 3 SQL queries instead of 52: 1 to fetch the header,
 * 1 to fetch the lines and 50 to fetch the items individually.
 *
 * Note that we cannot know in advance if the transaction will enventually read the items or not.
 * Some transactions might only need other fields from the lines, like amount, quantity, etc.
 * This is why the visit pass collects the item keys, but does not actually fetch the items.
 * The items are only fetched when the applicative code tries to fetch the first item.
 * The prefetcher assumes that, if we need to fetch an item on one line, it is likely that we will also need to fetch items
 * on the other lines.
 *
 * In the visit pass the prefetcher will usually collect some keys that will never be used by the transaction.
 * This is not a problem because the visit pass is very fast and uses little memory to store the keys.
 *
 * In the fetch pass, the prefetcher may read records that will not be used by the transaction.
 * This is a little more problematic but the bet is that, most of the time, the transaction will execute similar logic
 * on similar records and will thus end up fetching all the records or most of them.
 * Also, reading more records than needed is much less expensive than reading them one by one.
 *
 * The prefetcher also has methods that get called after an insert or an update. These methods adjust the buckets to
 * take into account the changes, so that subsequent reads and queries will return correct results.
 * We do not try to do a perfect job here, we just ensure that the results will always been correct, event if this
 * means invalidating maps that could have been patched with more sophisticated logic.
 *
 * Limitations: prefetching is limited to nodes with 'sql' storage. External storage may be supported later.
 */

/** Type for the records that are fetched from the database and stored in the bucket maps */
export type PrefetchedRecord = AnyRecord;

/** Data type for record or collection key values (including _id) */
export type Key = string;

/** Data type for _id values - string so that we can handle external storage later */
export type SysId = string;

/** Bucket for keys and records that are visited as references with a key other than _id (a reverseReference, or a join) */
export interface ReferenceBucket {
    /** The factory bucket */
    readonly factoryBucket: FactoryBucket;

    /** The bucketId - names of the key properties concatenated with '|'  */
    readonly bucketId: string;

    /** The key values that have collected during the visit pass but not fetched yet */
    readonly pendingRecordKeys: Set<Key>;

    /**
     * The map from key values to _id values (or null if record was not found).
     * The records are not stored in this bucket but in factoryBucket.fetchedRecords.
     */
    readonly fetchedSysIds: Map<Key, SysId | null>; // null on composite unique keys that are not found
}

/** Bucket for keys and records that are visited as collections */
export interface CollectionBucket {
    /** The factory bucket */
    readonly factoryBucket: FactoryBucket;

    /** The bucketId - names of the collection join properties concatenated with '|'  */
    readonly bucketId: string;

    /** The key values that have collected during the visit pass but not fetched yet */
    readonly pendingRecordKeys: Set<Key>;

    /**
     * The map from key values to _id values (or null if record was not found).
     * The records are not stored in this bucket but in factoryBucket.fetchedRecords.
     */
    readonly fetchSysIdArrays: Map<Key, SysId[]>;
}

/**
 * Factory-level bucket
 * A factory may have 2 such buckets, for update true and update false.
 */
export interface FactoryBucket {
    /** The factory of the records that will be fetched */
    readonly factory: NodeFactory;

    /** Are the records fetched _for update_ or not? */
    readonly forUpdate: boolean;

    /** The _id values that have collected during the visit pass but not fetched yet */
    readonly pendingSysIds: Set<SysId>;

    /** The map from _id values to fetched records */
    readonly fetchedRecords: Map<SysId, PrefetchedRecord>;

    /** The reference buckets for keys other than _id */
    readonly referenceBuckets: Map<string, ReferenceBucket>;

    /** The collection buckets */
    readonly collectionBuckets: Map<string, CollectionBucket>;

    /** The funnel to prevent concurrent fetching of the same key */
    readonly funnel: Funnel;
}

/** Camel case that preserves leading _ */
export function camelCase(str: string): string {
    const camel = _.camelCase(str);
    return str[0] === '_' ? `_${camel}` : camel;
}

/**
 * @internal
 * The prefetcher class, used to prefetch records in the context.
 */
export class ContextPrefetcher {
    private factoryBuckets = {} as Dict<FactoryBucket>;

    constructor(private readonly context: Context) {}

    // eslint-disable-next-line class-methods-use-this
    spy(factory: NodeFactory, ...args: any[]): void {
        const spiedNodeNames = ConfigManager.current.storage?.prefetch?.spiedNodeNames;
        if (!spiedNodeNames?.length) return;

        if (
            spiedNodeNames.some(spied =>
                spied[0] === '~' ? new RegExp(spied.substring(1)).test(factory.name) : spied === factory.name,
            )
        )
            // eslint-disable-next-line no-console
            console.log(factory.name, ...args);
    }

    /**
     * Does prefetching skip this factory?
     *
     * The factory will skipped if:
     * - The prefetching is disabled in the configuration
     * - The factory has an external storage manager
     * - The factory records are cached globally (across transactions)
     *
     * This method is called from the public methods of this class.
     */
    private skipFactory(factory: NodeFactory): boolean {
        if (ConfigManager.current.storage?.prefetch?.isDisabled) return true;
        if (this.context.managedExternal || factory.externalStorageManager) return true;
        if (factory.isVitallyCached) return true;

        return false;
    }

    /**
     * Returns a factory bucket, creating it if necessary
     *
     * @param factory The factory of records that will be fetched
     * @param forUpdate Should the records be fetched _for update_?
     */
    private getFactoryBucket(factory: NodeFactory, forUpdate: boolean): FactoryBucket {
        const bucketKey = `${factory.name}.${forUpdate}`;
        let bucket = this.factoryBuckets[bucketKey];
        if (!bucket) {
            bucket = {
                factory,
                forUpdate,
                pendingSysIds: new Set(),
                fetchedRecords: new Map(),
                referenceBuckets: new Map(),
                collectionBuckets: new Map(),
                funnel: funnel(1),
            };
            this.factoryBuckets[bucketKey] = bucket;
        }
        return bucket;
    }

    /**
     * Returns a bucket for a reference property, creating it if necessary
     *
     * @param factory The factory of records that will be fetched
     * @param bucketId The names of properties of the key, concatenated with '|'
     * @param forUpdate Should the records be fetched _for update_?
     */
    private getReferenceBucket(factory: NodeFactory, bucketId: string, forUpdate: boolean): ReferenceBucket {
        if (bucketId === '_id') throw new LogicError('Cannot create reference bucket for _id');

        const bucket = this.getFactoryBucket(factory, forUpdate);
        let referenceBucket = bucket.referenceBuckets.get(bucketId);
        if (referenceBucket) return referenceBucket;

        referenceBucket = {
            factoryBucket: bucket,
            bucketId,
            pendingRecordKeys: new Set(),
            fetchedSysIds: new Map(),
        };
        bucket.referenceBuckets.set(bucketId, referenceBucket);
        return referenceBucket;
    }

    /**
     * Returns a bucket for a collection property, creating it if necessary
     *
     * @param factory The factory of records that will be fetched
     * @param bucketId The names of properties of the key (it will be a partial key), concatenated with '|'
     * @param forUpdate Should the records be fetched _for update_?
     */
    private getCollectionBucket(factory: NodeFactory, bucketId: string, forUpdate: boolean): CollectionBucket {
        const bucket = this.getFactoryBucket(factory, forUpdate);
        let collectionBucket = bucket.collectionBuckets.get(bucketId);
        if (collectionBucket) return collectionBucket;

        collectionBucket = {
            factoryBucket: bucket,
            bucketId,
            pendingRecordKeys: new Set(),
            fetchSysIdArrays: new Map(),
        };
        bucket.collectionBuckets.set(bucketId, collectionBucket);
        return collectionBucket;
    }

    /** Returns a fetched record from factoryBucket.fetchedRecords, given its _id */
    // eslint-disable-next-line class-methods-use-this
    private getFetchedRecord(factoryBucket: FactoryBucket, sysId: SysId): PrefetchedRecord {
        const record = factoryBucket.fetchedRecords.get(sysId);
        if (!record) throw factoryBucket.factory.logicError(`record not found: {"_id":${sysId}}`);
        return record;
    }

    /**
     * Returns the fetched records for a collection, undefined if the collection is not prefetched
     */
    private getFetchedCollectionRecords(
        factory: NodeFactory,
        bucketId: string,
        forUpdate: boolean,
        recordKey: Key,
    ): PrefetchedRecord[] | undefined {
        const bucket = this.getFactoryBucket(factory, forUpdate);
        const sysIds = bucket.collectionBuckets.get(bucketId)?.fetchSysIdArrays.get(recordKey);
        return sysIds ? sysIds.map(sysId => this.getFetchedRecord(bucket, sysId)) : undefined;
    }

    //
    // Low level utilities
    //

    /**
     * Is a sysId value?
     * TODO: review for external storage
     */
    // eslint-disable-next-line class-methods-use-this
    isSysIdValid(sysId: AnyValue): boolean {
        return Number(sysId) > 0;
    }

    /** Extracts the _id value from an unknown value */
    private getSysId(value: AnyValue): string {
        if (typeof value === 'number') return String(value);
        if (typeof value === 'string') return value;
        if (value && typeof value === 'object') return this.getSysId((value as PrefetchedRecord)._id);
        return '';
    }

    /** Formats an array of key values as a string */
    // eslint-disable-next-line class-methods-use-this
    private formatRecordKey(values: AnyValue[]): Key {
        if (values.length === 1 && typeof values[0] === 'number') return String(values[0]);
        if (values.some(v => v && typeof v === 'object')) throw new LogicError('Cannot format record id with objects');
        return JSON.stringify(values.map(v => (v === null ? null : String(v))));
    }

    /** Parses a key string into an array of key values */
    // eslint-disable-next-line class-methods-use-this
    private parseRecordKey(str: Key): AnyValue[] {
        if (str[0] !== '[') return [str];
        return JSON.parse(str);
    }

    /** Formats a bucket id from an array of property names */
    // eslint-disable-next-line class-methods-use-this
    private formatBucketId(propertyNames: string[]): string {
        return propertyNames.join('|');
    }

    /** Parses a bucket id into an array of property names */
    // eslint-disable-next-line class-methods-use-this
    private parseBucketId(bucketId: string): string[] {
        return bucketId.split('|');
    }

    /**
     * Converts a key value to a valid key value. Returns undefined if the value is invalid.
     *
     * TODO: improve with metadata
     */
    // eslint-disable-next-line class-methods-use-this
    private getValidKeyValue(value: AnyValue): AnyValue {
        if (value == null) return value;
        if (!isScalar(value)) return (value as any)._id;
        if (typeof value === 'string') {
            if (value[0] === '#') return undefined;
            if (value.startsWith('_id:')) return value.substring(4);
            return value;
        }
        return String(value);
    }

    /**
     * Get a reference bucket id and key from a unique index and a record.
     * Returns undefined if the record does not have all the key values.
     */
    private getReferenceBucketIdAndKey(
        uniqueIndex: string[],
        record: PrefetchedRecord,
    ): { bucketId: string; recordKey: Key } | undefined {
        if (!uniqueIndex) return undefined;

        const ids = uniqueIndex.map(k => this.getValidKeyValue(record[k]));
        if (ids.some(id => id === undefined)) return undefined;

        const bucketId = this.formatBucketId(uniqueIndex);
        const recordKey = this.formatRecordKey(ids);
        return { bucketId, recordKey };
    }

    //
    // Visit methods
    //

    /** Visit an _id value found in _id property or in a reference */
    visitSysId(factory: NodeFactory, forUpdate: boolean, sysId: SysId): void {
        if (this.skipFactory(factory)) return;

        const factoryBucket = this.getFactoryBucket(factory, forUpdate);
        if (factoryBucket.pendingSysIds.has(sysId)) return;
        if (factoryBucket.fetchedRecords.has(sysId)) return;

        this.spy(factory, 'Visit add _id key', forUpdate, sysId);
        factoryBucket.pendingSysIds.add(sysId);
    }

    /** Visit a reference key */
    private visitReferenceKey(factory: NodeFactory, bucketId: string, forUpdate: boolean, recordKey: Key): void {
        const referenceBucket = this.getReferenceBucket(factory, bucketId, forUpdate);

        // If alread pending or fetched, do nothing
        if (referenceBucket.pendingRecordKeys.has(recordKey)) return;
        if (referenceBucket.fetchedSysIds.has(recordKey)) return;

        this.spy(factory, 'Visit add reference key', bucketId, forUpdate, recordKey);
        referenceBucket.pendingRecordKeys.add(recordKey);
    }

    /** Visit a collection key */
    private visitCollectionKey(factory: NodeFactory, bucketId: string, forUpdate: boolean, collectionKey: Key): void {
        if (this.skipFactory(factory)) return;

        const collectionBucket = this.getCollectionBucket(factory, bucketId, forUpdate);

        // If alread pending or fetched, do nothing
        if (collectionBucket.pendingRecordKeys.has(collectionKey)) return;
        if (collectionBucket.fetchSysIdArrays.has(collectionKey)) return;

        this.spy(factory, 'Visit add collection key', bucketId, forUpdate, collectionKey);
        collectionBucket.pendingRecordKeys.add(collectionKey);
    }

    /** Visit a unique index of the factory */
    private visitUniqueIndex(
        factory: NodeFactory,
        forUpdate: boolean,
        propertyNames: string[],
        record: PrefetchedRecord,
    ): void {
        const fk = this.getReferenceBucketIdAndKey(propertyNames, record);
        if (fk) this.visitReferenceKey(factory, fk.bucketId, forUpdate, fk.recordKey);
    }

    /** Visit all the unique indexes of the factory */
    private visitUniqueIndexes(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        return factory.uniqueKeyProperties.slice(1).forEach(propertyNames => {
            this.visitUniqueIndex(factory, forUpdate, propertyNames, record);
        });
    }

    /** Visit a reference property */
    private visitReferenceProperty(forUpdate: boolean, property: ReferenceProperty, record: PrefetchedRecord): void {
        const targetFactory = property.targetFactory;
        if (property.isVital || property.isAssociation) {
            const childRecord = record[property.name];
            if (childRecord && typeof childRecord === 'object') {
                const forUpdateNext = forUpdate;
                this.visit(
                    targetFactory,
                    forUpdateNext,
                    property.isVital
                        ? ({
                              [targetFactory.vitalParentProperty.name]: record,
                              ...childRecord,
                          } as PrefetchedRecord)
                        : record,
                );
            } else if (property.reverseReference && this.isSysIdValid(record._id)) {
                this.visitReferenceKey(targetFactory, property.reverseReference, forUpdate, String(record._id));
            }
        } else {
            const childForUpdate = property.isVitalParent || property.isAssociationParent ? forUpdate : false;
            const referenceId = this.getSysId(record[property.name]);
            if (this.isSysIdValid(referenceId)) {
                this.visitSysId(targetFactory, childForUpdate, referenceId);
            } else if (property.decorator.prefetch) {
                const prefetchKey = property.decorator.prefetch(record);
                if (!prefetchKey) return;
                const keyValues = Object.values(prefetchKey).map(this.getValidKeyValue);
                this.spy(targetFactory, 'Calling prefetch rule', property.fullName, keyValues);
                if (keyValues.some(v => typeof v === 'undefined')) return;

                const bucketId = this.formatBucketId(Object.keys(prefetchKey));
                const recordKey = this.formatRecordKey(keyValues);

                this.visitReferenceKey(targetFactory, bucketId, childForUpdate, recordKey);
            }
        }
    }

    /** Visit a collection property */
    private visitCollectionProperty(forUpdate: boolean, property: CollectionProperty, record: PrefetchedRecord): void {
        if (!property.reverseReference) return;
        const targetFactory = property.targetFactory;
        if (property.isVital || property.isAssociation) {
            const childRecords = record[property.name];
            if (childRecords && Array.isArray(childRecords)) {
                childRecords.forEach(childRecord => {
                    if (childRecord && typeof childRecord === 'object') {
                        const forUpdateNext = forUpdate; // ??? property.isAssociationParent ? false : forUpdate;
                        this.visit(targetFactory, forUpdateNext, {
                            [targetFactory.vitalParentProperty.name]: record,
                            ...childRecord,
                        } as PrefetchedRecord);
                    }
                });
            } else if (property.reverseReference && this.isSysIdValid(record._id)) {
                this.visitCollectionKey(targetFactory, property.reverseReference, forUpdate, String(record._id));
            }
        } else if (property.decorator.prefetch) {
            const childForUpdate = property.isVital || property.isAssociation ? forUpdate : false;
            const prefetchKey = property.decorator.prefetch(record);
            if (!prefetchKey) return;
            if (Object.values(prefetchKey).some(v => typeof v === 'undefined')) return;

            const bucketId = this.formatBucketId(Object.keys(prefetchKey));
            const collectionKey = this.formatRecordKey(Object.values(prefetchKey));

            this.visitCollectionKey(targetFactory, bucketId, childForUpdate, collectionKey);
        }
    }

    /** Visit all the properties of a record */
    private visitProperties(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        factory.properties.forEach(property => {
            if (property.isReferenceProperty()) {
                this.visitReferenceProperty(forUpdate, property, record);
            } else if (property.isCollectionProperty()) {
                this.visitCollectionProperty(forUpdate, property, record);
            }
        });
    }

    /** Prepare a record for the visit, by adding a reference to its parent record, if possible */
    private prepareRecordForVisit(
        factory: NodeFactory,
        forUpdate: boolean,
        record: PrefetchedRecord,
    ): PrefetchedRecord {
        if (!factory.isVitalChild) return record;
        const parentProperty = factory.vitalParentProperty;
        const parentValue = record[parentProperty.name];
        if (typeof parentValue === 'object') return record;
        if (!this.isSysIdValid(parentValue)) return record;
        const parentRecord = this.getFactoryBucket(parentProperty.targetFactory, forUpdate).fetchedRecords.get(
            String(parentValue),
        );
        return parentRecord ? { ...record, [parentProperty.name]: parentRecord } : record;
    }

    /** Visit a record, adding its keys to the buckets */
    visit(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        if (!record) return;
        if (this.skipFactory(factory)) return;

        // Visit the _id property
        if (this.isSysIdValid(record._id)) this.visitSysId(factory, forUpdate, String(record._id));

        // Get an augmented record with a reference to the parent record
        const augmentedRecord = this.prepareRecordForVisit(factory, forUpdate, record);
        this.visitUniqueIndexes(factory, forUpdate, augmentedRecord);
        this.visitProperties(factory, forUpdate, augmentedRecord);
    }

    //
    // Adding fetched records to the buckets
    //

    /** Adds a record to a reference bucket */
    private addRecordToReferenceBucket(
        factory: NodeFactory,
        bucketId: string,
        forUpdate: boolean,
        recordKey: Key,
        record: PrefetchedRecord | null,
    ): void {
        const referenceBucket = this.getReferenceBucket(factory, bucketId, forUpdate);

        if (referenceBucket.pendingRecordKeys.has(recordKey)) {
            referenceBucket.pendingRecordKeys.delete(recordKey);
        }

        const sysId = record?._id ? String(record._id) : null;
        if (!referenceBucket.fetchedSysIds.has(recordKey))
            this.spy(factory, 'Adding fetched record', bucketId, forUpdate, recordKey, sysId);

        referenceBucket.fetchedSysIds.set(recordKey, sysId);
    }

    /** Adds a record to its factory bucket, by its _id */
    private addRecordToFactoryBucket(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        if (!record._id) throw factory.logicError(`Logic error: _id missing in record: ${JSON.stringify(record)}`);

        if (!this.isSysIdValid(record._id))
            throw factory.logicError(`Logic error: _id invalid in record: ${JSON.stringify(record)}`);
        const sysId = String(record._id);

        const factoryBucket = this.getFactoryBucket(factory, forUpdate);
        if (factoryBucket.pendingSysIds.has(sysId)) {
            factoryBucket.pendingSysIds.delete(sysId);
        }
        if (!factoryBucket.fetchedRecords.has(sysId))
            this.spy(factory, 'Adding fetched record', '_id', forUpdate, sysId);

        factoryBucket.fetchedRecords.set(sysId, record);
    }

    /** Adds a record to a unique index bucket */
    private addRecordToUniqueIndexBucket(
        factory: NodeFactory,
        forUpdate: boolean,
        propertyNames: string[],
        record: PrefetchedRecord,
    ): void {
        const fk = this.getReferenceBucketIdAndKey(propertyNames, record);
        if (fk) this.addRecordToReferenceBucket(factory, fk.bucketId, forUpdate, fk.recordKey, record);
    }

    /** Adds a record to all the unique index buckets of its factory */
    private addRecordToUniqueIndexesBuckets(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        factory.uniqueKeyProperties.slice(1).forEach(propertyNames => {
            this.addRecordToUniqueIndexBucket(factory, forUpdate, propertyNames, record);
        });
    }

    /** Adds a record to all the buckets of its factory */
    addRecord(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        if (this.skipFactory(factory)) return;

        this.addRecordToFactoryBucket(factory, forUpdate, record);
        this.addRecordToUniqueIndexesBuckets(factory, forUpdate, record);
        this.visit(factory, forUpdate, record);
    }

    //
    // Fetching records from their pending keys
    //

    /** Fetches pending records from the database */
    private async fetchPendingRecords(
        factory: NodeFactory,
        bucketId: string,
        forUpdate: boolean,
        pendingKeys: Key[],
        orderBy?: OrderBy<Node>,
    ): Promise<PrefetchedRecord[] | undefined> {
        if (pendingKeys.length === 0) return undefined;

        const accessRightsFilter = await factory.getAccessRightsFilter(this.context);
        let inFilter;
        const keyParts = this.parseBucketId(bucketId);

        if (keyParts.length > 1) {
            inFilter = {
                _or: pendingKeys.map(id => zipObject(keyParts, this.parseRecordKey(id))),
            };
        } else {
            inFilter = { [bucketId]: { _in: pendingKeys.map(id => this.parseRecordKey(id)[0]) } };
        }
        const filter = { ...accessRightsFilter, ...inFilter };

        const query = await factory.createNodeQuery(this.context, { filter, forUpdate, orderBy });
        const records = await query.getDataReader().readAll();
        this.spy(factory, 'Fetched pending records', bucketId, forUpdate, pendingKeys, records.length);

        records.forEach(record => {
            this.addRecord(factory, forUpdate, record);
        });

        if (bucketId !== '_id') pendingKeys.forEach(id => this.visitReferenceKey(factory, bucketId, forUpdate, id));
        return records;
    }

    /** Fetched pending records for a reference bucket */
    private async fetchPendingReferences(referenceBucket: ReferenceBucket): Promise<void> {
        const { factory, forUpdate } = referenceBucket.factoryBucket;
        const bucketId = referenceBucket.bucketId;

        const pendingKeys = Array.from(referenceBucket.pendingRecordKeys);
        const records = await this.fetchPendingRecords(factory, bucketId, forUpdate, pendingKeys);
        if (!records) return;

        // Store null for all pending ids that are not found
        pendingKeys.forEach(pendingKey => {
            if (!referenceBucket.fetchedSysIds.has(pendingKey)) {
                this.addRecordToReferenceBucket(factory, referenceBucket.bucketId, forUpdate, pendingKey, null);
            }
        });

        // Visit the new records
        records.forEach(record => {
            this.visit(factory, forUpdate, record);
        });
    }

    /** Fetched pending records for a collection bucket */
    private async fetchPendingCollections(collectionBucket: CollectionBucket, orderBy?: OrderBy<Node>): Promise<void> {
        const { factory, forUpdate } = collectionBucket.factoryBucket;

        const bucketId = collectionBucket.bucketId;
        const pendingKeys = Array.from(collectionBucket.pendingRecordKeys);

        const records = await this.fetchPendingRecords(factory, bucketId, forUpdate, pendingKeys, orderBy);
        if (!records) return;

        // Set the fetchRecordArrays to empty arrays and then push the records into them.
        pendingKeys.forEach(recordKey => {
            collectionBucket.fetchSysIdArrays.set(recordKey, []);
        });
        const keyParts = bucketId.split('|');
        records.forEach(record => {
            const recordKey = this.formatRecordKey(keyParts.map(k => record[k]));
            const array = collectionBucket.fetchSysIdArrays.get(recordKey);
            if (!array) throw new LogicError(`${bucketId}: collection array not found: ${recordKey}`);
            array.push(String(record._id));
        });

        // Reset the pending record ids.
        collectionBucket.pendingRecordKeys.clear();

        // Visit the new records
        records.forEach(record => {
            this.visit(factory, forUpdate, record);
        });
    }

    //
    // Read and query
    //

    /**
     * Read a record from the database, given its key.
     * The record is fetched from the database if it is not already in the bucket.
     * The record (or null if not found) is stored in the bucket, so that it can be used later.
     */
    private async readKeyValue(
        factory: NodeFactory,
        bucketId: string,
        forUpdate: boolean,
        recordKey: Key,
    ): Promise<PrefetchedRecord | null | undefined> {
        const referenceBucket = this.getReferenceBucket(factory, bucketId, forUpdate);
        const factoryBucket = referenceBucket.factoryBucket;
        if (!referenceBucket.fetchedSysIds.has(recordKey)) {
            referenceBucket.pendingRecordKeys.add(recordKey);
            await this.fetchPendingReferences(referenceBucket);
        }
        const found = referenceBucket.fetchedSysIds.get(recordKey);
        if (!found) referenceBucket.fetchedSysIds.set(recordKey, null); // important: set null to avoid re-querying it
        return found ? factoryBucket.fetchedRecords.get(found) : null;
    }

    /**
     * Read a record from the database, given its _id.
     * The record is fetched from the database if it is not already in the bucket.
     * The record (or null if not found) is stored in the bucket, so that it can be used later.
     */
    private async tryReadSysId(
        factory: NodeFactory,
        forUpdate: boolean,
        sysId: SysId,
    ): Promise<PrefetchedRecord | null | undefined> {
        const factoryBucket = this.getFactoryBucket(factory, forUpdate);
        if (!factoryBucket.fetchedRecords.has(sysId)) {
            factoryBucket.pendingSysIds.add(sysId);
            const pendingSysIds = Array.from(factoryBucket.pendingSysIds).map(String);
            await this.fetchPendingRecords(factory, '_id', forUpdate, pendingSysIds);
        }
        return factoryBucket.fetchedRecords.get(sysId);
    }

    /**
     *
     * Tries to read a record from the database, given its key.
     * The record is fetched from the database if it is not already in the bucket.
     * The record (or null if not found) is stored in the bucket, so that it can be used later.
     *
     * @returns The record or null if not found, or undefined if the factory is skipped
     */
    tryRead(
        factory: NodeFactory,
        keyValues: Dict<AnyValue>,
        forUpdate: boolean,
    ): Promise<PrefetchedRecord | null | undefined> {
        if (this.skipFactory(factory)) return Promise.resolve(undefined);

        this.spy(factory, 'Try read', keyValues, forUpdate);

        return this.getFactoryBucket(factory, forUpdate).funnel(() => {
            if (keyValues._id) {
                if (!this.isSysIdValid(keyValues._id)) return Promise.resolve(undefined);
                return this.tryReadSysId(factory, forUpdate, String(keyValues._id));
            }

            const uniqueKeyProperties = factory.uniqueKeyProperties.slice(1);
            // eslint-disable-next-line no-restricted-syntax
            for (const keyPropertyNames of uniqueKeyProperties) {
                const fk = this.getReferenceBucketIdAndKey(keyPropertyNames, keyValues);
                if (fk) {
                    return this.readKeyValue(factory, fk.bucketId, forUpdate, fk.recordKey);
                }

                // Try collection with only one property of the unique key
                // If this collection was fetched then the record should have been registered with its unique key
                // so we can return null
                if (keyPropertyNames.length > 1)
                    // eslint-disable-next-line no-restricted-syntax
                    for (const bucketId of keyPropertyNames) {
                        const recordKey = keyValues[bucketId] as string;
                        if (recordKey) {
                            const records = this.getFetchedCollectionRecords(factory, bucketId, forUpdate, recordKey);
                            if (records) return Promise.resolve(null);
                        }
                    }
            }
            return Promise.resolve(undefined);
        });
    }

    /**
     * Tries to query a collection of records from the database, given the parent record _id.
     * The records are fetched from the database if they are not already in the bucket.
     * The records are stored in the bucket, so that they can be used later.
     *
     * @returns The records or null if not found, or undefined if the factory is skipped
     *
     * Note: this is only called to fetch full collections. We don't cache filtered/paged collections
     * (but we visit their records)
     * Caching full collections allows us to optimize some tryRead scenarios.
     * If f the tryRead uses a unique key for which we have prefetched a full collection, we can avoid a query
     * to the database and just return null if the record is not found in the unique index bucket.
     */
    tryQueryFullCollection(
        factory: NodeFactory,
        bucketId: string,
        forUpdate: boolean,
        parentSysId: SysId,
        orderBy: OrderBy<Node>,
    ): Promise<PrefetchedRecord[] | undefined> {
        if (this.skipFactory(factory)) return Promise.resolve(undefined);

        this.spy(factory, 'Try query full collection', bucketId, parentSysId, forUpdate);

        const collectionBucket = this.getCollectionBucket(factory, bucketId, forUpdate);

        return collectionBucket.factoryBucket.funnel(async () => {
            const collectionKey = this.formatRecordKey([parentSysId]);
            if (!collectionBucket.fetchSysIdArrays.has(collectionKey))
                await this.fetchPendingCollections(collectionBucket, orderBy);

            const sysIds = collectionBucket.fetchSysIdArrays.get(collectionKey);
            if (!sysIds) return undefined;
            return sysIds.map(sysId => this.getFetchedRecord(collectionBucket.factoryBucket, sysId));
        });
    }

    //
    // Invalidation and patching after insert, update, delete, ...
    //

    /**
     * Invalidates the collections after an insert or update.
     * We don't do a fancy job here, we just move the fetched keys back to pending keys, and we clear the fetched arrays.
     */
    private invalidateCollectionBuckets(factory: NodeFactory, forUpdate: boolean): void {
        const factoryBucket = this.getFactoryBucket(factory, forUpdate);
        // Invalidate the collections
        factoryBucket.collectionBuckets.forEach(collectionBucket => {
            Array.from(collectionBucket.fetchSysIdArrays.keys()).forEach(key => {
                collectionBucket.pendingRecordKeys.add(key);
            });
            collectionBucket.fetchSysIdArrays.clear();
        });
    }

    /**
     * Fixes the prefetcher buckets after a record insert (private method to recurse through the base factories).
     */
    private insertRecord(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        if (factory.baseFactory) this.insertRecord(factory.baseFactory, forUpdate, record);

        this.addRecord(factory, forUpdate, record);

        this.invalidateCollectionBuckets(factory, forUpdate);

        // delete the opposite bucket
        delete this.factoryBuckets[`${factory.name}.${!forUpdate}`];
    }

    /**
     * Fixes the prefetcher buckets after a record insert.
     */
    afterInsert(factory: NodeFactory, record: PrefetchedRecord): void {
        if (this.skipFactory(factory)) return;

        this.spy(factory, 'After insert', record._id);
        this.insertRecord(factory, false, record);
    }

    /**
     * Fixes the prefetcher buckets after a record update (private method to recurse through the base factories).
     */
    private updateRecord(factory: NodeFactory, forUpdate: boolean, newRecord: PrefetchedRecord): void {
        if (factory.baseFactory) this.updateRecord(factory.baseFactory, forUpdate, newRecord);

        const factoryBucket = this.getFactoryBucket(factory, forUpdate);
        const oldRecord = factoryBucket.fetchedRecords.get(String(newRecord._id));
        if (oldRecord) {
            // Remove the old record from the unique index buckets, if the key has changed
            factory.uniqueKeyProperties.slice(1).forEach(propertyNames => {
                const oldFk = this.getReferenceBucketIdAndKey(propertyNames, oldRecord);
                const newFk = this.getReferenceBucketIdAndKey(propertyNames, newRecord);
                if (oldFk && newFk && oldFk.recordKey !== newFk.recordKey) {
                    const oldReferenceBucket = this.getReferenceBucket(factory, oldFk.bucketId, forUpdate);
                    oldReferenceBucket.pendingRecordKeys.delete(oldFk.recordKey);
                    oldReferenceBucket.fetchedSysIds.delete(oldFk.recordKey);
                }
            });
        }

        this.addRecord(factory, forUpdate, newRecord);

        this.invalidateCollectionBuckets(factory, forUpdate);

        // delete the opposite bucket
        delete this.factoryBuckets[`${factory.name}.${!forUpdate}`];
    }

    /**
     * Fixes the prefetcher buckets after a record update.
     */
    afterUpdate(factory: NodeFactory, record: PrefetchedRecord): void {
        if (this.skipFactory(factory)) return;

        this.spy(factory, 'After update', record._id);
        this.updateRecord(factory, true, record);
    }

    /**
     * Fixes the prefetcher buckets after a record delete (private method to recurse through the base factories).
     */
    private deleteRecord(factory: NodeFactory, forUpdate: boolean, record: PrefetchedRecord): void {
        if (factory.baseFactory) this.deleteRecord(factory.baseFactory, forUpdate, record);

        delete this.factoryBuckets[`${factory.name}.${!forUpdate}`];

        const factoryBucket = this.getFactoryBucket(factory, forUpdate);
        // Remove the record from the unique index buckets
        factory.uniqueKeyProperties.slice(1).forEach(propertyNames => {
            const fk = this.getReferenceBucketIdAndKey(propertyNames, record);
            if (fk) {
                const referenceBucket = this.getReferenceBucket(factory, fk.bucketId, forUpdate);
                referenceBucket.pendingRecordKeys.delete(fk.recordKey);
                referenceBucket.fetchedSysIds.delete(fk.recordKey);
            }
        });

        factoryBucket.fetchedRecords.delete(String(record._id));

        // Clear the collection buckets because we don't know where it should be moved to
        factoryBucket.collectionBuckets.clear();
        // delete the opposite bucket
        delete this.factoryBuckets[`${factory.name}.${!forUpdate}`];
    }

    /**
     * Fixes the prefetcher buckets after a deleteMany operation (private method to recurse through the base factories).
     */
    private deleteMany(factory: NodeFactory): void {
        if (factory.baseFactory) this.deleteMany(factory.baseFactory);

        delete this.factoryBuckets[`${factory.name}.false`];
        delete this.factoryBuckets[`${factory.name}.true`];
    }

    /**
     * Fixes the prefetcher buckets after a deletion.
     */
    afterDelete(factory: NodeFactory, record?: AnyRecord): void {
        if (this.skipFactory(factory)) return;

        this.spy(factory, 'After delete', record?._id);
        if (record) this.deleteRecord(factory, true, record);
        else this.deleteMany(factory);
    }

    /** Resets the entire prefetcher state. This is only exposed to unit tests, via the Test class */
    reset(): void {
        this.factoryBuckets = {};
    }
}
