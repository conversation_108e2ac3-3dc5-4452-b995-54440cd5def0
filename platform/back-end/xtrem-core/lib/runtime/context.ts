/** @packageDocumentation @module runtime */
import {
    <PERSON><PERSON><PERSON><PERSON>,
    AnyValue,
    async<PERSON>rray,
    Async<PERSON>rray,
    Async<PERSON><PERSON>yReader,
    AsyncReader,
    AsyncResponse,
    funnel,
    Funnel,
    UnPromised,
} from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Datetime } from '@sage/xtrem-date-time';
import { getLocaleFromString, localizedText, localizeEnumMember } from '@sage/xtrem-i18n';
import { Logger, withClsContext } from '@sage/xtrem-log';
import { MetricsContext } from '@sage/xtrem-metrics';
import { ConnectionPool, DatabaseError, PoolConfig, SqlExecuteOptions } from '@sage/xtrem-postgres';
import {
    AccessStatus,
    AuthConfig,
    AuthorizationError,
    BaseError,
    BusinessRuleError,
    Config,
    DataInputError,
    Dict,
    ErrorWithDiagnoses,
    InitialNotification,
    InteropError,
    isDevelopmentConfig,
    LocalizeLocale,
    LogicError,
    MetaCustomFields,
    NOTIFICATION_CATEGORY_NODE_MODIFIED,
    SecurityError,
    SystemError,
    titleCase,
    unwrapError,
    User,
    ValidationSeverity,
    validator,
} from '@sage/xtrem-shared';
import { Converter } from '@sage/xtrem-ts-to-sql';
import { AxiosRequestConfig } from 'axios';
import { EventEmitter } from 'events';
import { CookieOptions, Request, Response } from 'express';
import { ExecutionResult, graphql } from 'graphql';
import * as _ from 'lodash';
import * as fsp from 'path';
import { Application, NotificationManager, ServiceOption, UiBroadcasterBucket } from '../application';
import { ServiceOptionManager } from '../application/service-option-manager';
import { GlobalCacheOptions, MemoryCache } from '../cache/index';
import { runResolver } from '../concurrency-utils';
import { IsolationLevel, NodeKey, StaticThis } from '../decorators';
import { FilterTag } from '../decorators/decorator-utils';
import { mapErrorToDiagnosis } from '../errors/error-utils';
import { LookupsArgs } from '../graphql/queries/lookup-query';
import { AccessRights } from '../graphql/security/access-rights';
import { getRequestHint } from '../graphql/utils/request';
import { getRequestId } from '../http/request';
import { NodeState } from '../node-state';
import { StateIntern } from '../node-state/state-intern';
import { Property, ReferenceArrayProperty, ReferenceProperty } from '../properties';
import { getDeviceHttpsAgent } from '../security';
import { DatabaseManager, SqlBulk, SqlConverter } from '../sql';
import { SqlSelect } from '../sql/mapper/sql-select';
import { PubSub, PubSubPayload } from '../sql/pubsub';
import { TestConfig } from '../test';
import {
    Activity,
    AggregateGroup,
    AggregateGroupItem,
    AggregateGroups,
    AggregateValue,
    AggregateValueItem,
    AggregateValues,
    AnyFilterObject,
    AnyNode,
    Diagnose,
    GroupBySelector,
    integer,
    Node,
    NodeCreateData,
    NodeQueryFilter,
    NodeQueryOptions,
    OperationError,
    QueryAggregateOptions,
    QueryAggregateReturn,
    ReadAggregateOptions,
    ReadAggregateReturn,
    ValuesOperator,
} from '../ts-api';
import { NodeSelectOptions, NodeSelector, NodeSelectResult } from '../ts-api/node-select-types';
import { BinaryStream } from '../types';
import { CollationCache } from './collation-cache';
import { ConfigurationService } from './configuration-service';
import { ContainerManager } from './container-manager';
import { ContextNaturalKeyConverter } from './context-natural-key-converter';
import { ContextPrefetcher } from './context-prefetcher';
import { ContextSqlSpy } from './context-sql-spy';
import { ContextVault } from './context-vault';
import { Debug } from './debug';
import { Introspection } from './introspection';
import { loggers } from './loggers';
import { NodeFactory } from './node-factory';
import { NodeIndex } from './node-factory-types';
import { globalRunningContext } from './running-context';
import { Transaction, TransactionOptions } from './transaction';
import { getLanguageFromLocale, stringifyLogReplacer } from './utils';

export interface NodeReadOptions {
    forUpdate?: boolean;
    isTransient?: boolean;
    isOnlyForLookup?: boolean;
}

export interface NodeCreateOptions {
    isTransient?: boolean;
    isOnlyForDefaultValues?: boolean;
    isOnlyForDuplicate?: boolean;
    duplicates?: Node;
    isOnlyForLookup?: boolean;
    writable?: boolean;
}

export interface NodeDeleteOptions {
    skipControls?: boolean;
    path?: string[];
}

export type UserAccess = {
    status: AccessStatus;
    sites: string[] | null;
    accessCodes: string[] | null;
};

export const standardOperations = ['create', 'update', 'delete', 'read', 'lookup', 'import'] as const;
export type StandardOperation = (typeof standardOperations)[number];

/**
 * Extend AccessRightsManager to include Authorization functions from xtrem-authorization
 */
export interface AccessRightsManager {
    /**
     * getUserAccessFor
     * Returns a list of Site ids to which a user has access for the specified operation on a Node
     * Empty array means full access with no restriction on Site
     * Undefined means no access
     * @param context
     * @param nodeName
     * @param propertyOrOperation a graphQL operation or property name
     * @param options optional authorizationCode added for external applications(X3)
     */
    getUserAccessFor(
        context: Context,
        nodeName: string,
        propertyOrOperation: string,
        options?: { authorizationCode?: string },
    ): AsyncResponse<UserAccess>;
    isAccessCodeAvailable(context: Context, accessCode: string): AsyncResponse<boolean>;
    getOperationSecurityFilter(
        context: Context,
        factory: NodeFactory,
        operation: string,
    ): Promise<AnyFilterObject | undefined>;
    createAdminUser(context: Context, data: UserData, options?: CreateAdminUserOptions): AsyncResponse<void>;
    createRequiredUsers(context: Context): AsyncResponse<void>;
    ensureAdminPersonaCreated(context: Context): AsyncResponse<UserInfo>;
    getUser(context: Context, code: string): AsyncResponse<UserInfo>;
    getCurrentUser(context: Context): AsyncResponse<UserInfo>;
    getUserNode(): StaticThis<Node>;
    getPermissions(context: Context, activity: string): AsyncResponse<string[]>;
    createActivities(context: Context): AsyncResponse<void>;
    updateActivities(context: Context): AsyncResponse<void>;
    deleteActivities(context: Context): AsyncResponse<void>;
    getActivityNode(): StaticThis<Node>;
    getActivitiesInfo(context: Context): AsyncResponse<ActivityInfo[]>;
    supportsPersona(context: Context): AsyncResponse<boolean>;
    getPersonaUser(context: Context, email: string): AsyncResponse<UserInfo | null>;
    getDemoPersonas(context: Context): AsyncResponse<UserInfo[]>;
    getUserNavigation?(context: Context): AsyncResponse<UserNavigationInfo>;
    invalidateAuthorizationCache(context: Context): Promise<void>;
    /**
     * Returns whether the 'Authorization access control' service option is enabled
     */
    isAuthorizationServiceOptionEnabled(context: Context): AsyncResponse<boolean>;
}

export interface LocalizationManager extends BaseManager {
    /**
     * @param context
     */
    getDefaultTenantLocale(context: Context): AsyncResponse<string>;
    isMasterLocale(context: Context): AsyncResponse<boolean>;
    createTenantLocale(context: Context, locale: string): AsyncResponse<void>;
}

export interface UserData {
    email: string;
    firstName: string;
    lastName: string;
    locale: string;
}

export interface CreateAdminUserOptions {
    skipWelcomeEmail?: boolean;
    isFirstAdminUser?: boolean;
}

export interface UserNavigationInfo {
    history?: string[];
    bookmarks?: string[];
}

export interface UserInfo extends User<BinaryStream> {
    _id: string | number;
    email: string;
    firstName?: string;
    lastName?: string;
    isActive?: boolean;
    userType?: 'application' | 'system';
    /** Is this an Admin user */
    isAdministrator?: boolean;
    /** Is this user required to be created per tenant */
    isRequired?: boolean;
    photo?: BinaryStream | null;
    // Needed for xtrem-x3 accessRightsManager
    userName?: string;
    isFirstAdminUser?: boolean;
    isDemoPersona?: boolean;
    isApiUser?: boolean;
    locale?: string;
    clientEncryptionKey?: string;
    isOperatorUser?: boolean;
}

export interface ActivityInfo {
    _id: string | number;
    name: string;
}

export interface ApiApplication {
    /** API application Id */
    id: string | undefined;
    /** API application name */
    name: string | undefined;
}

export type TableExistsCallback = (folder: string, tableName: string) => boolean;

export interface CustomRecordInterface {
    bundleId: string;
    factoryName: string;
    _customData?: any;
}

export type PackVersionInterface = {
    name: string;
    version: string;
    isHidden: boolean;
    isReleased: boolean;
    sqlSchemaVersion?: string;
};

export interface PackVersionInterfaceWithId extends PackVersionInterface {
    _id: number;
}

export enum PackAllocationStatusEnum {
    off = 1,
    preparing,
    on,
}

export type PackAllocationStatus = keyof typeof PackAllocationStatusEnum;

export type PackAllocationInterface = {
    package: Partial<PackVersionInterface>;
    isActive: boolean;
    status: PackAllocationStatus;
    isActivable: boolean;
};

export interface PackVersionOptions {
    isActivable?: boolean;
    isHidden?: boolean;
    isReleased?: boolean;
    sqlSchemaVersion?: string;
}

// Type used by getUpdateSetCommand
export type UpdateSetFunctionSet<This> = {
    [K in Exclude<keyof This, '$' | '_id' | '_sourceId' | '_sortValue'>]?:
        | UnPromised<This[K]>
        | ((this: This) => This[K] | UnPromised<This[K]>);
};

type BulkWhereFilter<This extends Node> = NodeQueryFilter<This>;

/**

 * Options for bulk updates
 */
export interface BulkUpdateOptions<This extends Node> {
    /**
     * The 'set' object that describes the properties to be updated with their new value (or functions that return a value).
     *
     * Example:
     * ```
     *       set {
     *          description: 'new description',
     *          value() { return this.otherProperty * 2},
     *       }
     * ```
     */
    set: UpdateSetFunctionSet<This>;
    /**
     * The optional 'where' function to filter the update
     *
     * Example:
     * ```
     *       where() {
     *           return this.site === 'siteCode';
     *       },
     * ```
     * or:
     * ```
     *       where: {
     *           site: 'siteCode',
     *       },
     * ```
     */
    where?: BulkWhereFilter<This>;
}

export interface BulkDeleteOptions<This extends Node> {
    /**
     * The optional 'where' function to filter the update
     *
     * Example:
     * ```
     *       where() {
     *           return this.site === 'siteCode';
     *       },
     * ```
     * or:
     * ```
     *       where: {
     *           site: 'siteCode',
     *       },
     * ```
     */
    where?: BulkWhereFilter<This>;
}

export interface BaseManager {
    initializeManager(context: Context): void;
}

export interface CustomerInfo {
    id: string;
    name: string;
}
export interface TenantInfoBase {
    id: string;
    directoryName?: string;
}

export interface TenantInfo extends TenantInfoBase {
    name: string;
    directoryName: string;
    customer: CustomerInfo;
}

/** @interface */
export interface TenantManager extends BaseManager {
    ensureTenantExists(
        context: Context,
        options: {
            customer: { id: string; name: string };
            tenant: { id: string; name: string };
        },
    ): AsyncResponse<void>;
    listTenantsIds(context: Context): AsyncResponse<string[]>;
    getTenantsInfo(context: Context, tenantId?: string): AsyncResponse<TenantInfo[]>;

    deleteTenant(application: Application, tenantId: string): AsyncResponse<void>;
}

export interface DataSettingsManager extends BaseManager {
    getSysVendorNode(): StaticThis<Node>;
    sageVendorId(context: Context): AsyncResponse<number>;
}

enum NotificationTopic {
    invalidateCategoryCache = 'invalidateCategoryCache',
}

export interface ContextCacheOptions<T extends AnyValue> extends GlobalCacheOptions<T> {}

interface ErrorParameters {
    key: string;
    message: string;
    data?: object | AnyValue[];
    innerError?: Error;
    path?: string[];
}

interface CookieDefinition {
    name: string;
    options: CookieOptions;
}

export enum ContextGetConfigurationKeyEnum {
    serviceOptionsLevel,
}

export type ContextGetConfigurationKeyType = keyof typeof ContextGetConfigurationKeyEnum;

export const rootUserEmail = '<EMAIL>';
export const supportUserEmail = '<EMAIL>';
export const supportReadonlyUserEmail = '<EMAIL>';
export const adminDemoPersonaEmail = '<EMAIL>';
export const adminDemoPersona: UserInfo = {
    _id: 100000000000, // Dummy _id for auto increment field
    email: adminDemoPersonaEmail,
    userName: adminDemoPersonaEmail,
    firstName: 'Admin',
    lastName: 'Persona',
    isActive: true,
    isAdministrator: true,
    isDemoPersona: true,
};

export const personaCookieDefinition = (tenantId: string, secure: boolean): CookieDefinition => ({
    name: `xtrem_${tenantId}_persona`,
    options: { httpOnly: true, sameSite: 'lax', secure },
});

export const lastCommitTimestampCookieDefinition = (
    clusterId: string,
    app: string,
    tenantId: string,
    secure: boolean,
): CookieDefinition => ({
    name: _.snakeCase(`xtrem_${clusterId}_${app}_${tenantId}__lastCommitTimestamp`),
    options: { httpOnly: true, sameSite: 'lax', secure },
});

function forwardedFromSecure(request: Request): boolean {
    const forwardedProtoHeaderValue = request.headers?.['x-forwarded-proto'];
    const forwardedProto = Array.isArray(forwardedProtoHeaderValue)
        ? forwardedProtoHeaderValue[0]
        : forwardedProtoHeaderValue;
    return forwardedProto != null && /^https$/i.test(forwardedProto);
}

function ensureUserName(user: UserInfo): UserInfo {
    if (user.email && !user.userName) user.userName = user.email;
    return user;
}

function isValidUser(user: UserInfo | null): user is UserInfo {
    return !!(user && user._id && user.email);
}

function userDetails(user: UserInfo | null): string {
    return user ? `${user._id}/'${user.email}'` : 'undefined/undefined';
}

export interface ContextInternal {
    userIp: string;
    hasDeviceToken: boolean;
}

/**
 * The _context_
 *
 * The context carries important information about the request which is currently being executed,
 * like the user name and the current locale.
 *
 * The context also provides method to initiate transactions, query and create nodes.
 *
 * The context is created by the framework when the framework receives a GraphQL request,
 * and disposed just after the request completes and the response is sent.
 * Applicative code does not create contexts directly, except to run unit tests (@see [Test](test) API).
 */
export class Context extends EventEmitter implements MetricsContext {
    #auth: AuthConfig = {};

    #personaUser: UserInfo | null = null;

    #loginUser: UserInfo | null = null;

    #userEmail?: string;

    #userIp?: string;

    #user: UserInfo = { _id: 0, email: '' };

    #bearerToken?: string;

    #deviceToken?: string;

    /** @internal */
    private _intern: StateIntern;

    /** @internal */
    apiApplication: ApiApplication;

    /** @internal */
    readonly pooledTransaction: Transaction;

    /** @internal */
    private isolatedTransaction: Transaction | undefined;

    /** @internal */
    debugId: number;

    /** @internal */
    debugPath: string;

    /** @internal */
    private readonly _config: Config;

    // system globals
    /** @internal */
    private _currentLocaleLanguage: string;

    /** The current locale, for example 'en-US' */
    /** @internal */
    private _currentLocale: string;

    /** The current supported locale for localization */
    /** @internal */
    private _currentLocalizeLocale: LocalizeLocale;

    currentLegislationCode: string;

    // applicative globals
    readonly globals = {} as AnyRecord;

    // applicative constants
    readonly constants = {} as AnyRecord;

    // custom fields that the query will use
    readonly customFields = {} as MetaCustomFields;

    /** @internal */
    request: Request;

    /** @internal */
    response: ResponseExtra | Response;

    #source: ContextSource;

    /** is the context isolated  */
    readonly #isIsolated: boolean;

    /**
     * The already logged messages.
     * - index is the message itself.
     * - value is the number of times the message should have been logged
     */
    private readonly _duplicatedLoggedMessages = {} as Dict<number>;

    /**
     * The nodes that were saved with the {deferred:true} option (node.$.save({deferred:true}))
     */
    private readonly _nodesWithDeferredSave: Node[] = [];

    /** @internal */
    readonly prefetcher = new ContextPrefetcher(this);

    /** @internal */
    readonly sqlSpy = new ContextSqlSpy(this);

    get source(): ContextSource {
        return this.#source || 'internal';
    }

    /**
     * @internal
     */
    set source(value: ContextSource) {
        this.#source = value;
    }

    get isIsolated(): boolean {
        return this.#isIsolated;
    }

    /**
     * @internal
     */
    get noLazyLoading(): boolean {
        return !!this.options.noLazyLoading;
    }

    #withoutTransactionUser: boolean | undefined;

    get withoutTransactionUser(): boolean {
        if (this.#withoutTransactionUser != null) return this.#withoutTransactionUser;
        this.#withoutTransactionUser = !!this.options.withoutTransactionUser;
        return this.#withoutTransactionUser;
    }

    private _contextValues: Dict<string> = {};

    /**
     * The tenantId bound to the context. Can be null when only accessing sharedTables or running SQL commands
     */
    #tenantId: string | null;

    // Offset transient _id so that it does not interfere with generated ids from payload_
    private _lastTransientId = -1000000000;

    private _allocateExternalIds = false;

    // Use 'external' _id so that it does not interfere with ids from _lastTransientId
    private _lastExternalTransientId = 0;

    testMode: boolean;

    testLayers?: string[];

    /** @internal */
    disableGetPropertyValueErrorLogger = false;

    private _collectedDependencyPaths?: string[];

    /**
     * The active services options
     * Only used by external storage for now.
     */
    #activeServiceOptionsSync?: ServiceOption[];

    readonly testConfig?: TestConfig;

    /** @internal */
    private _defaultLocale: string;

    #isMasterLocale: boolean;

    /** @internal */
    private _defaultLocaleLanguage: string;

    /** @internal */
    private _collation: string | undefined;

    #locales: string[];

    testNowMock?: string;

    /** @internal */
    processLocalizedTextAsJson = false;

    /**
     * Are we loading CSV files ?
     */
    private _inCsvLoading = false;

    /**
     * Is this context created for an application where the SQL queries are managed externally ? (X3, ...)
     * @internal
     */
    readonly managedExternal: boolean;

    /**
     * How many times did we enter withReadonlyScope from this context?
     * This counter allows us to handle reentrancy in withReadonlyScope.
     * @internal
     */
    private nestedReadonlyScopes = 0;

    /**
     * How many times did we enter withTransientScope from this context?
     * This counter allows us to handle reentrancy in withTransientScope (if we ever need it)
     * This mode is used by lookup queries.
     * @internal
     */
    private nestedTransientScopes = 0;

    /**
     * is a listener set up for context notifications ?
     * @internal
     */
    private static _subscribedToCacheNotifications = false;

    /**
     * The child contexts
     */
    private children: Context[] = [];

    /**
     * Context vault
     */
    readonly vault = new ContextVault(this);

    readonly logger: Logger;

    readonly sqlFunctionCache = {} as Dict<string>;

    /** Short lived cache which goes away with the context */
    #cache: MemoryCache;

    /** @internal Cache of service option effective state */
    #serviceOptionEnabledFlags = {} as Dict<boolean>;

    /*
     * Map of negative ids set in mutation with positive ids set by the database
     */
    mappedIds: Dict<number> = {};

    /**
     * Cost for event loop health
     */
    #eventLoopCost = 0;

    /**
     * Converter which is set when we need to convert references to natural keys in queries
     */
    #naturalKeyConverter: ContextNaturalKeyConverter | undefined;

    /**
     * UiBroadcaster bucket used for collecting CRUD operations completion
     */
    readonly #uiBroadcasterBucket = new UiBroadcasterBucket();

    /**
     * @internal
     * @param tenantId when set to null, the context can only be used to access to sharedTables
     */
    private constructor(
        readonly application: Application,
        private readonly options: ContextOptions,
        tenantId: string | null,
    ) {
        super();
        this.#userEmail = options.userEmail;
        const config =
            options.config && Object.keys(options.config).length
                ? { ...ConfigManager.current, ...options.config }
                : ConfigManager.current;

        this._config = config;
        this.managedExternal = !!config.storage!.managedExternal;

        if (!this.managedExternal) {
            if (!config.storage!.sql) throw new Error('invalid config: sql config missing');
        }
        this.#cache = new MemoryCache(tenantId || '*', 'context');

        // Add loading module to global variable
        process.env.MODULE_DIR = this.application.name;
        this.apiApplication = {
            id: config.applicationId,
            name: config.applicationName,
        };

        this._config = config;
        this.#source = this.options.source || 'internal';
        this.#isIsolated = options.isIsolated || false;
        this.pooledTransaction = new Transaction(this, { isReadonly: true });
        this.debugId = Debug.newId();
        this.request = options.request || {};
        this.response = options.response || {};
        this.testMode = !!options.testMode;
        this.testLayers = options.testLayers;
        this.testNowMock = options.testNowMock;
        if (this.testMode) this.testConfig = options.testConfig;
        if (options.testActiveServiceOptions) {
            this.activateTestServiceOptions(options.testActiveServiceOptions);
        }

        const parentContext = options.parent;
        if (parentContext) {
            this.#tenantId = parentContext.tenantId;
            this._allowedAccessCodes = parentContext._allowedAccessCodes;
            this._computingAccessRights = parentContext._computingAccessRights;
            this._contextValues = parentContext._contextValues;
            this.#userEmail = parentContext.#userEmail;
            this.request = parentContext.request;
            this.response = parentContext.response;
            this.debugPath = `${parentContext.debugPath}/${this.debugId}`;
        } else {
            this.debugPath = `${this.debugId}`;
            this.#tenantId = tenantId;
        }
        if (this.#tenantId && this.options.unsafeApplyToAllTenants) {
            throw new SystemError(
                'Cannot create a context where unsafeApplyToAllTenants is true and a tenantId is supplied',
            );
        }

        if (options.isSystem) {
            this.#userEmail = rootUserEmail;
        }
        if (this.isHttp()) {
            const locals = this.response.locals as {
                contextValues?: Dict<string>;
                auth?: AuthConfig;
                lastCommitTimestamp?: string;
            };
            if (locals.contextValues) this._contextValues = locals.contextValues;
            if (locals.auth?.login === rootUserEmail)
                throw new SystemError(`Cannot use ${rootUserEmail} as login user`);
            this.#auth = locals.auth || {};

            if (locals.lastCommitTimestamp) this.#lastCommitTimestamp = Datetime.parse(locals.lastCommitTimestamp);
        } else if (options.auth) {
            this.#auth = options.auth || {};
        }
        // explicit userEmail from option has precedence to auth login
        if (!this.managedExternal) {
            if (this.#auth.persona && !this.#auth.login) {
                throw new SystemError(
                    `Cannot initialize context with the persona '${this.#auth.persona}' but no login user`,
                );
            }
            const user = this.#user;
            user.email = this.#userEmail || this.#auth.login || '';
            if (!user.email) {
                const errorMessage = 'The context must be initialized with a non empty user email';
                const messageDetails =
                    'Please consider using application.asRoot.withXxxxContext methods for internal system contexts' +
                    ` that ${
                        this.#tenantId == null ? 'do not provide a tenant Id and/or ' : ''
                    }do not require an authenticated user.`;
                loggers.runtime.error(
                    `${errorMessage} and should not rely on development config, isHttp=${this.isHttp()}. ${messageDetails}`,
                );
                if (isDevelopmentConfig(config)) {
                    // we have to fix all such errors but we need more time for this...
                    // so for now we only throw in development mode to not create issues in prod
                    throw new SystemError(errorMessage);
                } else {
                    user.email = rootUserEmail;
                }
            }
            if (!validator.isEmail(user.email)) {
                throw new SystemError(
                    `context must be initialized with a valid user email. Got '${user.email}' userEmail=${
                        this.#userEmail
                    } isHttp=${this.isHttp()}`,
                );
            }
        }

        // paranoid verification
        if (this._contextValues == null) {
            this._contextValues = {} as Dict<string>;
        }
        if (options.contextValues) this._contextValues = { ...this._contextValues, ...options.contextValues };

        this.logger = Logger.getLogger(fsp.join(this.application.dir, 'package.json'), 'context');

        loggers.runtime.verbose(
            () =>
                `Create context from config with email '${this.options.userEmail || config.email}', tenantId '${
                    this.#tenantId
                }', loginUser=${userDetails(this.#loginUser)}`,
        );
    }

    private async init(): Promise<Context> {
        const { signal } = this.options;
        if (signal) {
            if (signal.aborted) {
                throw new SystemError('cannot init the context: signal has been aborted');
            }
            if (signal.onabort == null) {
                // set onabort callback to log the abort signal
                signal.onabort = () => {
                    this.logger.warn(`abort signal has been notified, hint: [${this.getRequestHint()}]`);
                    signal.onabort = null;
                };
            }
        }

        await this.setDefaultLocale();
        await this.setCurrentLocale(this.options.locale || 'base');

        if (
            ConfigManager.current.storage?.managedExternal &&
            !this.options.withoutSqlConnection &&
            !this.options.skipManagedExternalInit &&
            this.#activeServiceOptionsSync == null
        ) {
            this.#activeServiceOptionsSync = await this.activeServiceOptions;
        }

        if (this.options.bypassFrozen && !this.options.testMode)
            throw new LogicError('cannot create context: bypassFrozen option can only be set in test mode');
        return this;
    }

    static create(application: Application, options: ContextOptions, tenantId: string | null): Promise<Context> {
        if (options.signal?.aborted) {
            throw new SystemError('cannot create the context: signal has been aborted');
        }
        return new Context(application, options, tenantId).init();
    }

    async close(): Promise<void> {
        // close all its children
        // child.clone() modifies this.children so we clone it with slice().
        await asyncArray(this.children.slice()).forEach(child => child.close());
        if (this.children.length !== 0) throw new LogicError('some children not deleted');

        // close its isolated transaction
        if (this.isolatedTransaction) {
            this.isolatedTransaction.close();
            this.isolatedTransaction = undefined;
        }

        if (this.options.parent) {
            // this is a child context - remove it from its parent's children array
            const children = this.options.parent.children;
            children.splice(children.indexOf(this), 1);

            this.options.parent = undefined;
        }
        this.logSqlSummary();
        this.logIgnoredDuplicateMessages();
        this.emit('closed');
    }

    get schemaName(): string {
        return this.application.schemaName;
    }

    /** @internal */
    get disableAllCrudNotifications(): boolean {
        return !!this.options.disableAllCrudNotifications;
    }

    /** @internal */
    get disableTenantCrudNotifications(): boolean {
        return !!this.options.disableTenantCrudNotifications;
    }

    /** @internal */
    get bypassFrozen(): boolean {
        return !!this.options.bypassFrozen;
    }

    /** @internal */
    get withoutSqlConnection(): boolean {
        return !!this.options.withoutSqlConnection;
    }

    /**
     * Add a message to the UI broadcaster bucket for deferred broadcasting on commit
     * @internal
     */
    addDeferredBroadcastMessage(tenantId: string, category: string, payload?: string): void {
        this.#uiBroadcasterBucket.addMessage(tenantId, category, payload);
    }

    /**
     * Send the deferred broadcast messages that were collected in this context.
     * @internal
     */
    sendDeferredMessagesForBroadcast(): void {
        this.application.uiBroadcaster.addBucket(this.#uiBroadcasterBucket);
        this.#uiBroadcasterBucket.clear();
    }

    /**
     * Register a node that was saved with the {deferred:true} option (node.$.save({deferred:true}))
     * This node will only be saved to the database at the end of the transaction
     */
    queueDeferredSave(node: Node): void {
        const alreadyRegistered = this._nodesWithDeferredSave.some(
            n => n.$.state.interningKeyValues[0] === node.$.state.interningKeyValues[0],
        );
        this.logger.verbose(
            () =>
                `Deferring save of node ${node.$.factory.name}.${node._id}${
                    alreadyRegistered ? ' - already registered' : ''
                }`,
        );
        if (!alreadyRegistered) this._nodesWithDeferredSave.push(node);
    }

    /**
     * Flushes the deferred saves that were registered in the current transaction
     * i.e. saves all the nodes that were saved with `node.$.save({deferred:true})` during the transaction.
     */
    public async flushDeferredSaves(): Promise<void> {
        let loopIdx = 0;
        // We need a loop here because a deferred save can trigger other deferred saves
        // For instance, the saveEnd hook of a node can save another node with deferred save
        while (this._nodesWithDeferredSave.length > 0) {
            loopIdx += 1;
            if (loopIdx > 50) {
                throw new SystemError('Loop detected in deferred saves');
            }
            const nodesToSave = [...this._nodesWithDeferredSave];
            this._nodesWithDeferredSave.length = 0;

            await asyncArray(nodesToSave).forEach(async node => {
                // Push an empty prefix so that node.$.save() does not reset this.diagnoses
                this.diagnosesPrefixes.push('');
                try {
                    this.logger.verbose(() => `Saving deferred node ${node.$.factory.name}.${node._id}`);
                    await node.$.save();
                } finally {
                    this.diagnosesPrefixes.pop();
                }
            });
        }
    }

    /**
     * Returns the nodes that were saved with the {deferred:true} option (node.$.save({deferred:true}))
     */
    get nodesWithDeferredSave(): Readonly<Node[]> {
        return this._nodesWithDeferredSave;
    }

    /**
     * @internal
     * Keeps the event loop healthy.
     * This is called by StateGetValue.getPropertyValue.
     */
    async keepEventLoopHealthy(): Promise<void> {
        this.#eventLoopCost += 1;
        // Yield with setImmediate, to give a turn to all higher priority events
        // See https://nodejs.org/en/docs/guides/event-loop-timers-and-nexttick
        // Yielding every 2000 calls seems to be the right compromise between being fair (yielding often) and taking a performance hit.
        // We measured this on a tight loop of getPropertyValue calls and the perf hit crossed 1% between 1000 and 2000.
        if (this.#eventLoopCost % 2000 === 0) {
            this.checkNotAborted('Keep event loop healthy');
            await new Promise(setImmediate);
        }
        if (this.#eventLoopCost % 100000 === 0) this._logDetailsForExpensiveContext();
    }

    /**
     * @internal
     * Gets some context on the current request
     */
    getRequestHint(): string | null {
        const { request } = this;
        if (!request) {
            return null;
        }
        return getRequestHint(request);
    }

    /**
     * Log details for an expensive context
     */
    private _logDetailsForExpensiveContext(): void {
        const contextDescription = this.options.description;
        if (!contextDescription) return;
        this.logger.warn(`The context '${contextDescription()}' is running an expensive operation`);
    }

    businessRuleError(params: ErrorParameters): BusinessRuleError {
        return new BusinessRuleError(
            this.localize(params.key, params.message, params.data || {}),
            params.innerError,
            params.path,
        );
    }

    private mapIntegrityConstraintError(err: Error): BaseError {
        const diagnosis = mapErrorToDiagnosis(this, err);

        // If we already have an error with diagnoses we just add the new diagnoses to the existing ones
        if (err instanceof ErrorWithDiagnoses && err.extensions.diagnoses) {
            err.extensions.diagnoses.length = 0;
            err.extensions.diagnoses.push(diagnosis);
            return err;
        }

        this.diagnoses.push(_.omit(diagnosis, 'factoryName'));
        const factory = this.application.getFactoryByName(diagnosis.factoryName);
        // We have to return an error with the diagnoses instead of adding the diagnoses to this context
        // because dbError is thrown by the commit and the context variable will be out of scope after that.
        return new OperationError(this, factory, 'save', this.diagnoses, err);
    }

    /**
     * Returns whether a message should be logged (only used when we want to avoid logging the same message multiple times)
     * This callback will only be invoked when the logger is called with a ignoreCallback option set
     */
    shouldIgnoreDuplicateLogs(message: string): boolean {
        const newCount = (this._duplicatedLoggedMessages[message] || 0) + 1;
        this._duplicatedLoggedMessages[message] = newCount;
        return newCount > 1;
    }

    authorizationError(params: ErrorParameters): AuthorizationError {
        return new AuthorizationError(this.localize(params.key, params.message, params.data || {}), params.innerError);
    }

    dataInputError(params: ErrorParameters): DataInputError {
        return new DataInputError(this.localize(params.key, params.message, params.data || {}), params.innerError);
    }

    isSecure(): boolean {
        const { request } = this;
        const { connection } = request;
        return request.secure || forwardedFromSecure(request) || (!!connection && 'authorized' in connection);
    }

    isHttp(): this is { response: Response } {
        const response = this.response;
        return (
            response?.constructor?.name === 'ServerResponse' && response.locals && typeof response.cookie === 'function'
        );
    }

    /**
     * subscribeToCacheNotifications subscribes to cache notifications sent by other containers
     */
    static async subscribeToCacheNotifications(application: Application): Promise<void> {
        if (!Context._subscribedToCacheNotifications) {
            Context._subscribedToCacheNotifications = true;
            await PubSub.subscribe(
                NotificationTopic.invalidateCategoryCache,
                Context.onInvalidateCategoryCache(application),
            );
        }
    }

    #cloudflareRayID: string;

    /**
     * Gets the source of the request
     */
    get cloudflareRayID(): string {
        if (this.#cloudflareRayID != null) return this.#cloudflareRayID;
        this.#cloudflareRayID = '';

        // It is a cloudflare request
        const headers = this.request.headers;
        if (headers) {
            let cloudflareRayID = headers['cf-ray'];
            if (Array.isArray(cloudflareRayID)) {
                cloudflareRayID = cloudflareRayID[0];
            }

            if (cloudflareRayID && typeof cloudflareRayID === 'string') {
                // Validate the Cloudflare Ray ID. We accepts only alphanumerical and hyphens up with a length of 8 to 64 characters
                if (!/^[0-9a-zA-Z-]{8,64}$/.test(cloudflareRayID)) {
                    return '';
                }

                this.#cloudflareRayID = cloudflareRayID;
                return cloudflareRayID;
            }
        }
        const originId = this.getContextValue('originId');
        if (originId?.startsWith('cloudflare:') && originId.length > 10) {
            this.#cloudflareRayID = originId.slice(10);
        }
        return this.#cloudflareRayID;
    }

    /**
     * Gets the source of the request
     */
    get requestSource(): string {
        // It is a cloudflare request
        if (this.cloudflareRayID) {
            return 'cloudflare';
        }
        // graphql request or request from the container
        return ['graphql', 'rest'].includes(this.source) ? this.source : 'container';
    }

    /**
     * Gets the cloudflare id or an internal id if not available
     */
    get customerRequestId(): string {
        return this.cloudflareRayID ? this.cloudflareRayID : `${ContainerManager.containerId}-${this.debugId}`;
    }

    /** @internal */
    get clusterId(): string | undefined {
        return this._config.clusterId;
    }

    get originId(): string {
        const originId = this.getContextValue('originId');
        return originId || `${this.requestSource}:${this.customerRequestId}`;
    }

    get isAborted(): boolean {
        return !!this.options.signal?.aborted;
    }

    private checkNotAborted(message: string): void {
        if (this.isAborted) {
            throw new SystemError(`${message}: context has been aborted`);
        }
    }

    /**
     * Returns the current transaction
     */
    get transaction(): Transaction {
        this.checkNotAborted('cannot get the current transaction');
        return this.isolatedTransaction || this.pooledTransaction;
    }

    /**
     * Returns the current tenantId
     * When null, the context can only be used to access to sharedTables
     */
    get tenantId(): string | null {
        return this.#tenantId;
    }

    get app(): string | undefined {
        return this._config.app;
    }

    /**
     * Returns the unsafeApplyToAllTenants flag
     */
    get unsafeApplyToAllTenants(): boolean {
        return !!this.options.unsafeApplyToAllTenants;
    }

    private resetUsers(): void {
        this.#user = { _id: 0, email: '' };
        this.#loginUser = null;
        this.#personaUser = null;
        const config =
            this.options.config && Object.keys(this.options.config).length
                ? { ...ConfigManager.current, ...this.options.config }
                : ConfigManager.current;
        // explicit userEmail from option has precedence to auth login
        if (!this.managedExternal) {
            if (this.#auth.persona && !this.#auth.login) {
                throw new SystemError(
                    `Cannot initialize context with the persona '${this.#auth.persona}' but no login user`,
                );
            }
            const user = this.#user;
            user.email = this.#userEmail || this.#auth.login || '';
            if (!user.email) {
                if (isDevelopmentConfig(config)) {
                    user.email = this._config.email || this._config.user || '';
                }
            }
            if (!validator.isEmail(user.email)) {
                // At some point we will need to throw an error, but for the moment we just log it
                // throw new SystemError(
                loggers.runtime.error(
                    `context must be initialized with a valid user email. Got '${user.email}' userEmail=${
                        this.#userEmail
                    } isHttp=${this.isHttp()}`,
                );
            }
        }
    }

    /**
     * Set the current tenantId.
     * This function should not be used, please set the tenantId when creating the context instead
     */
    async setTenantId(tenantId: string | null): Promise<void> {
        if (this.#user.email !== rootUserEmail) {
            throw new SystemError('Cannot set a tenantId in a non root context');
        }
        if (this.source === 'graphql') {
            throw new SystemError('Cannot set the tenantId in a graphQL context');
        }
        if (tenantId != null && this.unsafeApplyToAllTenants)
            throw new SystemError('Cannot set a tenantId when unsafeApplyToAllTenants is true');
        this.#tenantId = tenantId;
        if (tenantId && this.isolatedTransaction) {
            this.#withoutTransactionUser = false;
            this.resetUsers();
            await this.transaction.resetSessionUser();
        }
        this.#cache = new MemoryCache(tenantId || '*', 'context');
    }

    /** @internal */
    get collectedDependencyPaths(): string[] | undefined {
        return this._collectedDependencyPaths;
    }

    /** Returns the dependency paths for the specified property's rule (used by xtrem-cop) */
    collectDependencyPaths(property: Property, ruleName: 'defaultValue' | 'updatedValue'): string[] {
        const converter = new SqlConverter(this, property.factory);
        const dependencyPaths: string[] = [];
        this._collectedDependencyPaths = dependencyPaths;
        try {
            converter.convertFunction(property[ruleName] as () => any);
        } finally {
            this._collectedDependencyPaths = undefined;
        }
        return dependencyPaths;
    }

    /**
     * @disabled_internal
     * The list of enabled package for the tenant
     */
    getActivePackageNames(): Promise<string[]> {
        return this.application.packageManager.getActivePackageNames(this);
    }

    /**
     * Check if current user has specified permission on an Activity
     */
    async isAuthorized(activity: Activity, permission: string): Promise<boolean> {
        if (!Context.accessRightsManager) {
            throw new Error('Access rights manager must be instantiated before calling isAuthorized');
        }

        const userAccess = await Context.accessRightsManager.getUserAccessFor(this, activity.node.name, permission);

        return userAccess.status === 'authorized';
    }

    getActivities(): Dict<Activity> {
        return this.application.activityManager.getActivities();
    }

    isNodeAccessControlled(factory: NodeFactory): boolean {
        return this.application.activityManager.isNodeAccessControlled(factory);
    }

    /**
     * Indicates whether a package is enabled for the tenant
     */
    async isPackageEnabled(packageName?: string): Promise<boolean> {
        return packageName == null
            ? true
            : (await this.application.packageManager.getActivePackageNames(this)).includes(packageName);
    }

    supportsPersona(): AsyncResponse<boolean> {
        return Context.accessRightsManager.supportsPersona(this);
    }

    // TODO: review this method - strange parameter
    async isDemoPersona(user: { isDemoPersona?: boolean } | null): Promise<boolean> {
        try {
            if (!(await this.supportsPersona())) return false;
            return !!user?.isDemoPersona;
        } catch (e) {
            // See later, we should fix all test applications but for now not all
            // have all required node factories
            if (this.application.applicationType === 'test') {
                loggers.runtime.warn(
                    `cannot get 'isDemoPersona' property of test application ${this.application.name} user email ${this.#user.email}: ${e.stack}`,
                );
                return false;
            }
            throw e;
        }
    }

    /**
     * The list of active service options for the tenant
     */
    get activeServiceOptions(): Promise<ServiceOption[]> {
        return this.serviceOptionManager.getEnabledServiceOptions(this);
    }

    /**
     * @internal
     * Broadcast a notification to invalidate a category in the in memory global cache
     */
    async notifyInvalidateCategory(category: string): Promise<void> {
        loggers.globalCache.verbose(() => `Broadcast cache RESET tenantId:${this.tenantId}, category:${category}`);
        await PubSub.publish(
            this,
            NotificationTopic.invalidateCategoryCache,
            {
                tenantId: this.tenantId,
                category,
            },
            {
                excludeSelf: true,
            },
        );
    }

    /**
     * @internal
     * onInvalidateCategoryCache is called from pubsub to invalidate caches for a category
     * */
    private static onInvalidateCategoryCache(application: Application) {
        return async (data: PubSubPayload): Promise<void> => {
            loggers.globalCache.verbose(() => `Received in-memory cache RESET event: ${JSON.stringify(data)}`);
            await application.asRoot.withCommittedContext(
                data.tenantId,
                context =>
                    context.application.globalCache.invalidateCategory(context, data.category as string, {
                        skipNotify: true,
                    }),
                { description: () => `Invalidate category cache ${data.category}` },
            );
        };
    }

    /**
     * @internal
     * Returns whether a property is enabled or not, given the service options that it may carry
     * Returns true if the property is not controlled by any service options.
     */
    isEnabledByServiceOptions(property: Property): AsyncResponse<boolean> {
        if (!property) {
            return false;
        }
        return asyncArray(property.serviceOptions).every(serviceOption => this.isServiceOptionEnabled(serviceOption));
    }

    /**
     * SYNC variant of the service options API
     * The API is duplicated to avoid disruption but this will be resolved after migration to node 16.
     * The Sync API is used by external storage. It will superseed the other one (and we will remove the Sync postfix)
     */

    /**
     * Returns `true` if the given service option is available and active
     * @param serviceOption
     */
    isServiceOptionActiveSync(serviceOption: ServiceOption): boolean {
        return !!this.#activeServiceOptionsSync?.some(
            activeServiceOption => activeServiceOption.name === serviceOption.name,
        );
    }

    /**
     * @internal
     * This is called when we parse the incoming graphql payload and we detect negative ids in this payload.
     * We need to adjust _lastExternalTransientId to avoid collisions between new ids that we allocate
     * and ids already present in the payload.
     */
    adjustLastExternalId(id: integer): void {
        if (id < this._lastExternalTransientId) this._lastExternalTransientId = id;
    }

    /** @disabled_internal */
    allocateTransientId(): number {
        if (this._allocateExternalIds) {
            this._lastExternalTransientId -= 1;
            return this._lastExternalTransientId;
        }
        this._lastTransientId -= 1;
        return this._lastTransientId;
    }

    /** The current login user */
    get loginUser(): Promise<UserInfo | null> {
        return (async () => {
            await this.resolveLoginUser();
            return this.#loginUser;
        })();
    }

    /**
     * The transaction user that will be used as createUser and updateUser in the DB transaction
     * Note: this user will be null when the context was declared as to be used with sharedTables
     */
    get transactionUser(): Promise<Pick<UserInfo, '_id' | 'email'> | null> {
        return (async () => {
            if (!this.#tenantId || this.withoutTransactionUser) {
                // This context is used to access shared tables (no update/create user columns) or tun run SQL commands.
                // Also used for creating initial users to leave create and update user empty for self reference default value
                return null;
            }
            // make sure the user is resolved
            const user = await this.user;
            if (user == null) return null;
            return (await this.loginUser) || user;
        })();
    }

    private async resolveLoginUser(): Promise<void> {
        if (!this.#auth?.login) return;
        if (isValidUser(this.#loginUser)) return;
        // do not read again if we have it from user
        if (isValidUser(this.#user) && this.#user.email === this.#auth.login) {
            this.#loginUser = { ...this.#user } as UserInfo;
        } else {
            this.#loginUser = await Context.accessRightsManager.getUser(this, this.#auth.login);
        }
        const auth0 = this.#auth?.auth0;
        const auth0Type = auth0?.split('|')[0];
        if (auth0Type === 'api' && !this.#loginUser.isApiUser) {
            throw new SystemError(`api token requires an api login user, got '${this.#loginUser.email}'`);
        } else if (auth0Type !== 'api' && this.#loginUser.isApiUser) {
            throw new SystemError(`login user '${this.#loginUser.email}' requires an api token`);
        }
        if (auth0Type === 'device' && !this.#loginUser.isOperatorUser) {
            throw new SystemError(`device token requires an operator login user, got '${this.#loginUser.email}'`);
        } else if (auth0Type !== 'device' && this.#loginUser.isOperatorUser) {
            throw new SystemError(`login user '${this.#loginUser.email}' requires a device token`);
        }
        if (!this.#loginUser.isActive)
            throw new SystemError(`cannot connect with an inactive login user '${this.#loginUser.email}'`);
        // If a user request arrives with an authenticated user (email) that matches a demo persona, we reject it.
        // This is to prevent a malicious user trying to create a user with the same email as one of the personas.
        if ((await this.isDemoPersona(this.#loginUser)) && this.source !== 'workflow')
            throw new SystemError(`cannot use the persona '${this.#loginUser.email}' as login user`);
        ensureUserName(this.#loginUser);
    }

    private async verifiedUser(): Promise<UserInfo> {
        if (
            this.#user.email !== rootUserEmail &&
            (await this.isDemoPersona(this.#user)) &&
            !isValidUser(this.#loginUser)
        )
            throw new SystemError(`cannot use the persona '${this.#user.email}' without a login user`);

        return ensureUserName(this.#user);
    }

    private async resolvePersonaUser(options?: { force: boolean }): Promise<void> {
        // we must have a login to support a persona

        if (!this.#auth?.login) return;

        if ((!options?.force && isValidUser(this.#personaUser)) || !(await this.supportsPersona())) {
            return;
        }

        let message = '';
        const isAdmin = this.#loginUser?.isAdministrator || this.#loginUser?.isFirstAdminUser;
        const demoPersonas = await Context.accessRightsManager.getDemoPersonas(this);

        // If the auth persona is set as admin demo persona, we must have an admin user
        if (
            this.#auth.persona === adminDemoPersonaEmail &&
            !demoPersonas.find(p => p.email === adminDemoPersonaEmail || p.isAdministrator || p.isFirstAdminUser)
        ) {
            message = `The login id '${this.#loginUser?.email}' does not have the '${this.#auth.persona}' persona`;
            throw new SecurityError(`${message}, and no 'administrator' persona is available for this user.`);
        }
        // Here we assume that the tenant has isDemoTenant service option is active
        // if the user request does not have a persona in the Cookie HTTP header, the server picks the first demo persona
        // the first one will be an admin demo persona if the login user is admin, if not it will be a normal demo persona
        const personaUser =
            (this.#auth.persona && (await Context.accessRightsManager.getPersonaUser(this, this.#auth.persona))) ||
            (await Context.accessRightsManager.getDemoPersonas(this))[0];

        if (!personaUser) {
            if (this.#auth.persona) {
                message = `persona ${this.#auth.persona} not found for login id ${this.#loginUser?._id}`;
            } else {
                message = `cannot assign a default persona to login id ${this.#loginUser?._id}`;
            }
            throw new SystemError(`${message}, and no ${isAdmin ? 'admin' : ''} persona is available`);
        } else if (personaUser.isApiUser) {
            throw new SystemError(`persona '${personaUser.email}' cannot be an api user`);
        } else if (personaUser.isOperatorUser) {
            throw new SystemError(`persona '${personaUser.email}' cannot be an operator user`);
        }

        // impersonates this persona
        this.#personaUser = personaUser;
        this.#user = personaUser;

        // set cookie "persona" (we might use the _id instead of email but this is not critical)
        if (this.isHttp()) {
            this.setPersonaCookie(personaUser.email);
        }
    }

    private setPersonaCookie(email: string): void {
        if (!this.tenantId) return;
        const def = personaCookieDefinition(this.tenantId, this.isSecure());
        this.response.cookie(def.name, email, def.options);
    }

    async setDemoPersona(email: string): Promise<boolean> {
        if (!this.#auth?.persona && !this.#auth?.login) return false;
        this.#auth.persona = email;
        // force resolution to skip early return
        await this.resolvePersonaUser({ force: true });
        // set cookie "persona" with email
        if (this.isHttp()) {
            this.setPersonaCookie(email);
            return true;
        }
        return false;
    }

    get rootUser(): AsyncResponse<UserInfo> {
        return Context.accessRightsManager.getUser(this, rootUserEmail);
    }

    get userId(): string | number {
        return this.#user._id;
    }

    /** @internal */
    get userIp(): string {
        if (!this.#userIp) {
            this.#userIp = this.request?.ip || globalRunningContext.natIpAddress;
        }
        return this.#userIp;
    }

    deviceTokenHttpRequest(method: string, url: string, data: any = undefined): AxiosRequestConfig {
        const request: AxiosRequestConfig = {
            method,
            url,
            timeout: 3000,
            headers: { Authorization: this.bearerToken() },
            httpsAgent: getDeviceHttpsAgent(),
            data,
        };

        return request;
    }

    private bearerToken(): string | undefined {
        if (!this.#bearerToken && this.request?.cookies?.access_token) {
            if (!this.request?.cookies?.access_token) {
                return undefined;
            }

            this.#bearerToken = `Bearer ${this.request?.cookies.access_token}.${this.request?.cookies.access_token_sign}`;
        }
        return this.#bearerToken;
    }

    private deviceToken(): string | undefined {
        if (this.tenantId && !this.#deviceToken && this.request?.cookies?.[`device_token_${this.tenantId}`]) {
            if (!this.request?.cookies?.[`device_token_${this.tenantId}`]) {
                return undefined;
            }

            this.#deviceToken = this.request?.cookies?.[`device_token_${this.tenantId}`];
        }
        return this.#deviceToken;
    }

    /** @internal */
    get hasDeviceToken(): boolean {
        const deviceToken = this.deviceToken();

        if (deviceToken) {
            return true;
        }
        return false;
    }

    setDeviceTokenCookie(name: string, deviceToken: any, options: CookieOptions): void {
        if (!this.tenantId) return;
        this.response.cookie(name, deviceToken, options);
    }

    /**
     * The current user
     * Note: this user will be null when the context was declared as to be used with sharedTables
     */
    get user(): Promise<UserInfo | null> {
        return (async () => {
            // Lazy loading of the user because we need an access rights manager to be initialized.
            // #user must have been set with a temp object without code
            if (!this.#user) return null;
            if (!Context.accessRightsManager) {
                throw new Error('Access rights manager must be instantiated before accessing user property');
            }

            if (!this.managedExternal) {
                if (isValidUser(this.#user) && isValidUser(this.#loginUser)) {
                    return ensureUserName(this.#user);
                }
            }

            // set loginUser to the authenticated user if it is not already set.
            await this.resolveLoginUser();

            let email =
                this.#user.email ||
                this.#user.userName ||
                this.#loginUser?.email ||
                this._config.email ||
                this._config.user;

            // Must be called after resolution of login user because the login user is used to determine if we will assign
            // an admin demo persona or normal demo persona
            await this.resolvePersonaUser();

            // this is the persona user
            if (isValidUser(this.#personaUser)) {
                return this.verifiedUser();
            }

            if (!this.managedExternal && !email) {
                email = rootUserEmail;
            }

            if (isValidUser(this.#user) && this.#user.userName) {
                return this.verifiedUser();
            }

            if (email) {
                if (isValidUser(this.#loginUser) && this.#loginUser.email === email) {
                    this.#user = { ...this.#loginUser };
                }
                this.#user = await Context.accessRightsManager.getUser(this, email);
                if (await this.isDemoPersona(this.#loginUser)) {
                    throw new SystemError(`cannot use the persona '${email}' as login user`);
                }
            }

            if (!this.#user)
                throw new Error(
                    `User not found for ${this._config.user ? `username: ${this._config.user}, ` : ''}${
                        this._config.email ? `email: ${this._config.email}` : ''
                    }`,
                );

            try {
                return await this.verifiedUser();
            } finally {
                loggers.runtime.debug(
                    () =>
                        `User initialized with '${this.#user.userName} [${this.#user.firstName}, ${this.#user.lastName}, ${this.#user.email}]'`,
                );
            }
        })();
    }

    async getUserInfo(): Promise<UserInfo> {
        const userInfo = await this.user;
        if (!userInfo) throw new LogicError('No user in context');
        return userInfo;
    }

    /** @internal */
    isMasterLocale(): boolean {
        return this.#isMasterLocale;
    }

    get currentLocaleLanguage(): string {
        if (this._currentLocaleLanguage) return this._currentLocaleLanguage;
        this._currentLocaleLanguage = getLanguageFromLocale(this.currentLocale);
        return this._currentLocaleLanguage;
    }

    get currentLocale(): string {
        // If _currentLocale has not been set, we are still in Context.create and we use 'base' locale
        return this._currentLocale || 'base';
    }

    /** @internal */
    async setCurrentLocale(locale: string): Promise<void> {
        try {
            this._currentLocaleLanguage = getLanguageFromLocale(locale);
            // Need to ensure that the i18n base file is used for test messages
            this._currentLocalizeLocale = getLocaleFromString(locale);
            // Set _currentLocale last so that we don't lose the last good one if there is an exception
            this._currentLocale = locale;
            // only read the value of the current locale / locale language / default locale / 'en'
            this.#locales = _.uniq([this.currentLocale, this.currentLocaleLanguage, this.defaultLanguage, 'en']);
            this.#isMasterLocale =
                this.tenantId != null &&
                Context._localizationManager &&
                (await Context.localizationManager.isMasterLocale(this));
        } catch (e) {
            if (!this._currentLocale) this._currentLocale = this.defaultLocale;
            this._currentLocaleLanguage = getLanguageFromLocale(this._currentLocale);
            this._currentLocalizeLocale = getLocaleFromString(this._currentLocale);
            throw e;
        }
    }

    /** @internal */
    async setTestLocale(locale: string): Promise<void> {
        const newLocale = locale !== 'base' ? locale : this.defaultLocale;
        await this.setCurrentLocale(newLocale);
    }

    async setDefaultLocale(): Promise<void> {
        if (this.options.withoutSqlConnection) {
            this._defaultLocale = 'base';
            return;
        }
        try {
            // get default locale from the Localization Manager
            if (this.tenantId != null && Context._localizationManager)
                this._defaultLocale = await Context.localizationManager.getDefaultTenantLocale(this);
        } catch (e) {
            switch (e.message) {
                // These errors will occur when the create-sql-schema method is called.
                // We do nothing for them
                case `relation "${this.schemaName}.locale" does not exist`:
                    break;
                default:
                    throw e;
            }
        }
        if (!this._defaultLocale) this._defaultLocale = 'base';
        loggers.runtime.verbose(() => `default locale: ${this._defaultLocale}`);
    }

    get defaultLocale(): string {
        return this._defaultLocale;
    }

    get defaultLanguage(): string {
        if (this._defaultLocaleLanguage) return this._defaultLocaleLanguage;
        this._defaultLocaleLanguage = getLanguageFromLocale(this.defaultLocale);
        loggers.runtime.verbose(() => `default language: ${this._defaultLocaleLanguage}`);
        return this._defaultLocaleLanguage;
    }

    get locales(): string[] {
        return this.#locales;
    }

    get collation(): string | undefined {
        if (this._collation) return this._collation;
        this._collation = CollationCache.getCollation(this.currentLocale, this.currentLocaleLanguage);
        return this._collation;
    }

    private static getLocalizedFallback(key: string): string | null {
        // We don't want to pollute i18n with texts for test nodes so we have a fallback for them
        // We only provide fallback for metadata keys:
        //      nodes__<my_node>__node_name -> My node
        //      nodes__<my_node>__property__<my_property> -> My property
        //      nodes__<my_node>__mutation__<my_mutation> -> My mutation
        //      nodes__<my_node>__mutation__<my_mutation>__parameter__<my_parameter> -> My parameter

        // Split the key on / and then split again on __.
        // Then we should have 3 or 4 elements and we capitalize the 2nd or 4th element
        const elements = key.split('/').pop()?.split('__') ?? [];
        if (elements.length < 3) return null;
        // Only provide a fallback for nodes prefixed by 'test_'
        if (elements[0] !== 'data_types' && !elements[1].startsWith('test_')) return null;
        return titleCase(elements[5] || elements[3] || elements[1]);
    }

    // we need to stick to that function name to allow transpiling visitor to collect the keys
    // if we want to rename it then change the xtrem-i18n/.../message-transformer.ts to manage this name
    localize(
        key: string,
        template: string,
        data: object | AnyValue[] = {},
        locale = this._currentLocalizeLocale,
    ): string {
        try {
            return localizedText(key, template, data, locale);
        } catch (e) {
            const fallback = Context.getLocalizedFallback(key);
            if (fallback == null) throw e;
            return fallback;
        }
    }

    localizeEnumMember(enumName: string, memberName: string): string {
        try {
            return localizeEnumMember(enumName, memberName, this._currentLocalizeLocale);
        } catch {
            return titleCase(memberName);
        }
    }

    /** @internal */
    get intern(): StateIntern {
        this._intern = this._intern || new StateIntern(this);
        return this._intern;
    }

    getContextValue(key: string): string | undefined {
        return this._contextValues[key];
    }

    get prefersReaderPool(): boolean {
        return (
            !this.managedExternal &&
            !this.isWritable &&
            (!!this._config.storage?.sql?.readonlyHostname || !!this._config.storage?.sql?.readonlyPort) &&
            replicaSources.includes(this.source) &&
            this.isolationLevel !== 'high'
        );
    }

    get masterPool(): ConnectionPool {
        if (this.managedExternal)
            throw new Error('Internal connection is not possible for externally managed applications');
        return DatabaseManager.getPool(this._config.storage?.sql as unknown as PoolConfig);
    }

    get replicaPool(): ConnectionPool {
        if (!this._config.storage?.sql) {
            throw new Error('No SQL configuration found');
        }

        return DatabaseManager.getPool({
            ...this._config.storage.sql,
            hostname: this._config.storage.sql.readonlyHostname ?? this._config.storage.sql.hostname,
            port: this._config.storage.sql.readonlyPort ?? this._config.storage.sql.port,
        });
    }

    get sqlPool(): ConnectionPool {
        if (this.managedExternal)
            throw new Error('Internal connection is not possible for externally managed applications');

        if (this.prefersReaderPool && this._config.storage?.sql) {
            return this.replicaPool;
        }

        return this.masterPool;
    }

    /** @internal */
    private static _managers: BaseManager[] = [];

    private static addManager(manager: BaseManager): void {
        this._managers.push(manager);
    }

    static get managers(): BaseManager[] {
        return this._managers;
    }

    /** @internal */
    private static _accessRightsManager: AccessRightsManager;

    static get accessRightsManager(): AccessRightsManager {
        if (!this._accessRightsManager) throw new Error('Access rights manager is not registered');
        return this._accessRightsManager;
    }

    static set accessRightsManager(value: AccessRightsManager) {
        this._accessRightsManager = value;
    }

    /** @internal */
    private static _localizationManager: LocalizationManager;

    static get localizationManager(): LocalizationManager {
        if (!this._localizationManager) throw new Error('Localization manager is not registered');
        return this._localizationManager;
    }

    static set localizationManager(value: LocalizationManager) {
        this.addManager((this._localizationManager = value));
    }

    get serviceOptionManager(): ServiceOptionManager {
        return this.application.serviceOptionManager;
    }

    /** @internal */
    private static _tenantManager: TenantManager;

    static get tenantManager(): TenantManager {
        if (!this._tenantManager) throw new Error('Tenant manager is not registered');
        return this._tenantManager;
    }

    static set tenantManager(value: TenantManager) {
        this.addManager((this._tenantManager = value));
    }

    /** @internal */
    private static _dataSettingsManager: DataSettingsManager;

    static get dataSettingsManager(): DataSettingsManager {
        if (!this._dataSettingsManager) throw new Error('Data settings manager is not registered');
        return this._dataSettingsManager;
    }

    static set dataSettingsManager(value: DataSettingsManager) {
        this.addManager((this._dataSettingsManager = value));
    }

    /** @internal */
    private static _notificationManager: NotificationManager;

    static get notificationManager(): NotificationManager {
        if (!this._notificationManager) throw new Error('Notification manager is not registered');
        return this._notificationManager;
    }

    static set notificationManager(value: NotificationManager) {
        this._notificationManager = value;
    }

    /**
     * Add a warning diagnose that a property in a filter is restricted
     * @internal */
    addRestrictedFilterDiagnose(restrictedFilter: string[]): void {
        this.addDiagnoseAtPath(
            ValidationSeverity.warn,
            restrictedFilter,
            this.localize(
                '@sage/xtrem-core/filter-property-unavailable-or-unauthorized',
                'The property in the filter is unavailable or unauthorized.',
                {},
            ),
        );
    }

    /**
     * Add a warning diagnose that a property in an order by is restricted
     * @internal
     * */
    addRestrictedOrderByDiagnose(restrictedOrderby: string[]): void {
        this.addDiagnoseAtPath(
            ValidationSeverity.warn,
            restrictedOrderby,
            this.localize(
                '@sage/xtrem-core/order-by-property-unavailable-or-unauthorized',
                'The property in the sort order is unavailable or unauthorized.',
                {},
            ),
        );
    }

    /**
     * Add an error diagnose that a property is restricted
     * @internal
     * */
    addRestrictedPropertyDiagnose(restrictedProperty: string[]): void {
        this.addDiagnoseAtPath(
            ValidationSeverity.error,
            restrictedProperty,
            this.localize('@sage/xtrem-core/property-unavailable', 'The property is unavailable.', {}),
        );
    }

    /** @internal */
    isAccessCodeAvailable(accessCode?: string): AsyncResponse<boolean> {
        return !accessCode || Context.accessRightsManager.isAccessCodeAvailable(this, accessCode);
    }

    // Note: all queries on nodes that have a site property will be filtered with _allowedSiteCodes
    // We may introduce an API later so that special operations can bypass the filter.
    /** @internal */
    private readonly _allowedAccessCodes: Dict<string[] | null> = {};

    // We have a reentrancy problem when computing the access rights, as the tables that contain these rights
    // have authorization codes and we need to check access on these tables.
    // This private flag allows us to temporarily disable the security check while we compute those rights.
    // TODO: this `setAllowedAccessCodes` design works for now but it is brittle. We'll have to do another pass on it.
    /** @internal */
    private _computingAccessRights = false;

    /** @internal */
    setAllowedAccessCodes(userAccess: UserAccess): void {
        try {
            this._computingAccessRights = true;
            this._allowedAccessCodes.site = userAccess.sites;
            this._allowedAccessCodes.accessCode = userAccess.accessCodes;
        } finally {
            this._computingAccessRights = false;
        }
    }

    /** @internal */
    getAllowedAccessCodes(tag: FilterTag): string[] | null {
        if (this._computingAccessRights) {
            return null;
        }
        // if (this.source === 'graphql' && !(tag in this._allowedAccessCodes))
        //    throw new Error(`Internal error: getAllowedAccessCodes('${tag}') called before setAllowedCode`);
        return this._allowedAccessCodes[tag] || null;
    }

    setSecurityFilters(filters: Dict<string[] | null>): void {
        this._allowedAccessCodes.site = filters.site;
        this._allowedAccessCodes.accessCode = filters.accessCode;
    }

    get securityFilters(): Dict<string[] | null> {
        // Return a frozen copy, we do not want to freeze the original object
        return Object.freeze({ ...this._allowedAccessCodes });
    }

    /**
     * Checks if the current user is authorized to perform an operation on a node.
     * Throws an exception if the user is not authorized.
     */
    checkThatNodeOperationIsAuthorized(nodeName: string, operationName: StandardOperation | string): Promise<void> {
        return AccessRights.checkOperationAccessRight(this, {
            nodeName,
            operationName,
            args: {},
            isPropertyAccess: false,
        });
    }

    get isWritable(): boolean {
        return this.transaction.isWritable;
    }

    get isAutoCommit(): boolean {
        return !!this.options.isAutoCommit;
    }

    #sqlExecutionCounts: Map<string, number> = new Map();

    private static overallSqlExecutionCount = 0;

    // Preventing SQL injection.
    //
    // We test that SQL statements that we send to the database don't contain any single quotes.
    // This allows us to detect string parameters which are embedded in the query rather than passed as SQL parameters.
    //
    // We relax this test in the following cases:
    // - in the x3-* packages (because the x3 sql mapper is not ready for this)
    // - when running unit tests in core, data-management, system and upgrade-test-* packages
    // - when running `xtrem upgrade` or `xtrem tenant` CLI commands.
    //
    // We will try to improve this (but it may be counter-productive to eliminate quotes from all upgrade SQL statements)
    // The real risk is on `xtrem start` and we are covering this.
    /**  */
    private static canRelaxSqlInjectionTest(): boolean {
        const cliCommand = process.argv.slice(2).find(a => a !== '--') || '';
        if (/^(upgrade|tenant)$/.test(cliCommand)) return true;

        const cwd = process.cwd();
        if (/xtrem-core|xtrem-system|xtrem-data-management|x3-/.test(cwd)) return true;
        if (/xtrem-upgrade-/.test(cwd) && cliCommand === 'test') return true;
        return false;
    }

    private static stripRaiseStatements(sql: string): string {
        return sql.replace(/\bRAISE( (DEBUG|LOG|INFO|NOTICE|WARNING|EXCEPTION))? '[^']+'/g, '');
    }

    // Returns whether the SQL statement is safe by checking that all string literals
    // are in the converter's safe list.
    private static isSafe(sql: string): boolean {
        let startIndex = 0;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            startIndex = sql.indexOf("'", startIndex);
            if (startIndex < 0) return true;
            const endIndex = sql.indexOf("'", startIndex + 1);
            if (Converter.isSafeStringLiteral(sql.substring(startIndex + 1, endIndex))) {
                // literal in the safe list, continue after this literal
                startIndex = endIndex + 1;
            } else {
                return false;
            }
        }
    }

    private static preventSqlInjections(sql: string): void {
        if (this.isSafe(sql)) return;
        if (/^NOTIFY[^']*'[^']*';$/.test(sql)) return;
        if (this.isSafe(this.stripRaiseStatements(sql))) return;
        if (this.canRelaxSqlInjectionTest()) {
            // eslint-disable-next-line no-console
            console.log(`[SECURITY] Detected risk of SQL injection: ${sql}`);
        } else {
            throw new SecurityError(`Detected risk of SQL injection: ${sql}`);
        }
    }

    /**
     * Utility method used to identify sql statements that are executed more that once for the current context
     * The log will only be output if the log level of `sage/xtrem-core/performance` is set to `debug`
     * but the logic will also be executed at verbose level, to update the counts.
     */
    private logSql(sql: string, args: AsyncResponse<AnyValue[]>): void {
        if (loggers.performance.logLevel !== 'verbose' && loggers.performance.logLevel !== 'debug') return;

        (async () => {
            const key = `${sql}, ${JSON.stringify(await args)}`;
            if (this.#sqlExecutionCounts.has(key)) {
                const currentValue = this.#sqlExecutionCounts.get(key) || 0;
                const nextValue = currentValue + 1;
                this.#sqlExecutionCounts.set(key, nextValue);

                loggers.performance.debug(
                    () => `Execution ${nextValue} of sql ${key} in the same context. \n \n ${new Error('Stack').stack}`,
                );
            } else {
                this.#sqlExecutionCounts.set(key, 1);
            }
        })().catch(err => loggers.runtime.error(err));
    }

    /**
     * Method that prints the sql execution count when the context is closed
     * The log will be output if the log level of `sage/xtrem-core/performance` is set to `debug` or `verbose`
     */
    private logSqlSummary(): void {
        if (loggers.performance.logLevel !== 'verbose' && loggers.performance.logLevel !== 'debug') return;

        let total = 0;
        [...this.#sqlExecutionCounts.keys()].forEach(key => {
            const value = this.#sqlExecutionCounts.get(key)!;
            total += value;
            // only log statements that are executed more than once
            if (value > 1) loggers.performance.verbose(() => `${key} executed ${value} times`);
        });

        Context.overallSqlExecutionCount += total;
        if (total > 0) {
            loggers.performance.verbose(
                () => `Total of sql executions: ${total} in this context, ${Context.overallSqlExecutionCount} overall`,
            );
        }
    }

    /**
     * Log the previously ignored messages (duplicate messages)
     */
    private logIgnoredDuplicateMessages(): void {
        Object.entries(this._duplicatedLoggedMessages).forEach(([message, count]) => {
            if (count > 1) loggers.runtime.info(`The following message was ignored ${count} times: ${message}`);
        });
    }

    #readFunnelMap: Map<string, Funnel> = new Map();

    /**
     * For the current context the body passed in will be execute in a funnel of size 1.
     * The funnel is determined by the key passed in, and all requests for the same key will go through the same funnel.
     * @internal
     */
    async withReadFunnel<T extends AnyValue>(key: string, body: () => AsyncResponse<T>): Promise<T> {
        let readFunnel = this.#readFunnelMap.get(key);
        if (!readFunnel) {
            readFunnel = funnel(1);
            this.#readFunnelMap.set(key, readFunnel);
        }
        const result = await readFunnel(body);
        this.#readFunnelMap.delete(key);
        return result;
    }

    #lastCommitTimestamp: Datetime | null;

    /**
     * @internal
     */
    get lastCommitTimestamp(): Datetime | null {
        if (this.#lastCommitTimestamp !== undefined) return this.#lastCommitTimestamp;
        this.#lastCommitTimestamp = this.options.lastCommitTimestamp ?? null;
        return this.#lastCommitTimestamp;
    }

    /**
     * @internal
     */
    set lastCommitTimestamp(value: Datetime | null) {
        const oldValue = this.#lastCommitTimestamp;
        if (value === oldValue) return;

        // We are resetting the last commit timestamp, because we just queried the lag and we can use the replica pool
        // We propagate the reset back to the parent if the parent shares the same last commit timestamp
        // We do not want to set the parent last commit timestamp to null if the values are different,
        // because the parent has been set to a last commit timestamp which is ahead (time always moves forward)
        if (
            value === null &&
            this.options.parent &&
            oldValue?.value === this.options.parent.lastCommitTimestamp?.value
        ) {
            this.options.parent.lastCommitTimestamp = null;
        }

        this.#lastCommitTimestamp = value;

        // We are setting the last commit timestamp, as we just committed a transaction and we need to update the parent
        // with a value that is now (latest last commit timestamp)
        if (value && this.options.parent) {
            this.options.parent.lastCommitTimestamp = value;
        }

        // The lastCommit timestamp has changed, clear the promise that checks the replica lag
        // so that we can recompute the replica lag on the next readonly transaction
        this.transaction.invalidateReplicaLag();
        if (this.isHttp() && oldValue !== null && value === null) this.setLastCommitTimestampCookie();
    }

    /** @disabled_internal */
    executeSql<T extends AnyValue = AnyValue[]>(sql: string, args: AnyValue[], opts?: SqlExecuteOptions): Promise<T> {
        if (!opts?.allowUnsafe) Context.preventSqlInjections(sql);
        this.logSql(sql, args);
        return this.transaction.executeSql(sql, args, opts);
    }

    /** @internal */
    createSqlReader<T extends AnyValue>(
        sql: string,
        args: AsyncResponse<AnyValue[]>,
        opts?: SqlExecuteOptions,
    ): AsyncReader<T> {
        if (!opts?.allowUnsafe) Context.preventSqlInjections(sql);
        this.logSql(sql, args);
        return this.transaction.createSqlReader<T>(sql, args, opts);
    }

    /**
     * Get the current database timestamp
     * @returns the current timestamp
     */
    async getSqlTimestamp(): Promise<Datetime> {
        if (this.managedExternal) {
            throw new Error('SQL timestamp is not supported by externally managed applications');
        }

        const pgTime = await this.executeSql<
            {
                current_timestamp: Date;
            }[]
        >('SELECT CURRENT_TIMESTAMP::TIMESTAMPTZ(3);', []);
        if (pgTime.length !== 1) {
            throw new Error('Expected one row');
        }
        return Datetime.fromJsDate(pgTime[0].current_timestamp);
    }

    get mayCommit(): boolean {
        return !this.options.noCommit;
    }

    /**
     * Flushes the actions that have been queued for execution before the commit.
     * Call this when you need to force the evaluation of deferredDefaultValue rules
     * in the middle of a transaction, before the commit.
     * Use this method with care as it may increase contention between concurrent transactions.
     */
    flushDeferredActions(): Promise<void> {
        return this.transaction.flushDeferredActions();
    }

    /** @internal */
    /**
     * allocates a new childContext
     */
    async createChildContext(
        options: TransactionOptions & {
            isDetachedContext?: boolean;
            isSystem?: boolean;
            source?: ContextSource;
            isIsolated?: boolean;
        },
    ): Promise<Context> {
        const childContext = await Context.create(
            this.application,
            { ...this.options, ...options, parent: this },
            this.tenantId,
        );

        // do not set an isolated transaction for readonly low isolation level contexts
        if (!(options.isReadonly && options.isolationLevel === 'low')) {
            childContext.isolatedTransaction = new Transaction(childContext, options);
        }

        // A standalone context is not a child, and will manage its own close
        if (!options.isDetachedContext) {
            this.children.push(childContext);
        }

        return childContext;
    }

    /**
     * INTERNAL USE ONLY
     * Execute the body with the root user.
     */
    async unsafeWithRootUser<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        const currentUser = this.#user;
        const currentPersonaUser = this.#personaUser;
        const currentLoginUser = this.#loginUser;
        const currentAuth = this.#auth;

        try {
            this.#user = await this.rootUser;
            this.#loginUser = null;
            this.#personaUser = null;
            this.#auth = { login: rootUserEmail };
            return await body();
        } finally {
            this.#user = currentUser;
            this.#personaUser = currentPersonaUser;
            this.#loginUser = currentLoginUser;
            this.#auth = currentAuth;
        }
    }

    /** @disabled_internal */
    /**
     * DO NOT USE OUTSIDE OF FRAMEWORK
     *
     * executes `body` inside a new context.
     *
     * Important: this call leaves the context open because the SQL transaction may be needed
     * after the body has been exited, for example to produce the response to a graphQl query or mutation.
     * The child context will be closed when its parent is closed and the caller is responsible
     * for closing the parent context.
     * @param options
     * isDetachedContext is passed as true from runInWritableContext, and flags that the context is detached from the parent, i.e. it will manage itself.
     */
    async withChildContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options: TransactionOptions & {
            isDetachedContext?: boolean;
            source?: ContextSource;
            isIsolated?: boolean;
        },
    ): Promise<T> {
        const randomInt = (min: number, max: number): number => Math.floor(Math.random() * (max - min + 1) + min);

        const handleError = async (err: Error & { innerError?: Error }, retryIndex: number): Promise<void> => {
            // Note: retryIndex = 0 when we are not in a retry loop
            if (err instanceof OperationError) throw err;

            const unwrappedError = unwrapError(err);
            const baseMillis = 60;
            if (!(unwrappedError instanceof DatabaseError)) {
                throw err;
            }
            const dbError = unwrappedError;
            // See https://www.postgresql.org/docs/current/errcodes-appendix.html
            if (retryIndex && dbError.code === '40001') {
                // use an exponential backoff retry to reduce stressing
                const randomBoundary = (retryIndex * baseMillis) / 2;
                const retryMillis = baseMillis * 2 ** retryIndex + randomInt(-randomBoundary, randomBoundary);
                this.logger.warn(
                    `transaction retry #${retryIndex} in ${retryMillis} ms for HTTP request ${getRequestId(
                        this.request,
                    )}: ${err.innerError?.stack}`,
                );
                await new Promise<void>(resolve => {
                    setTimeout(resolve, retryMillis);
                });
            } else if (dbError.code?.startsWith('23')) {
                // Class 23 — Integrity Constraint Violation
                if (dbError.detail) this.logger.error(dbError.detail);
                this.logger.error(dbError.message);
                throw this.mapIntegrityConstraintError(err);
            } else {
                throw err;
            }
        };

        if (this.isolatedTransaction) {
            // `this` is already a child context.
            // This is not an error if `this` was created as an uncommitted context (a test context)
            // So we use it directly in this case; otherwise we throw
            if (!this.mayCommit) {
                try {
                    const result = await body(this);
                    // We won't commit but we have to execute the deferred actions.
                    await this.flushDeferredActions();
                    return result;
                } catch (err) {
                    await handleError(err, 0);
                }
            }
            if (!options.isDetachedContext)
                throw new LogicError('cannot have more than one isolated transaction per context');
        }

        const maxRetries = ConfigManager.current.storage?.sql?.maxRetriesOnTransactionConflicts || 10;

        for (let i = 1; i <= maxRetries; i += 1) {
            const childContext = await this.createChildContext(options);
            const { isolatedTransaction } = childContext;
            // runtime check to avoid null assertion
            if (!isolatedTransaction && !options.isReadonly) {
                throw new LogicError('Isolated transaction must have been set by the child context');
            }

            try {
                // we need to ensure that we have a cls context initialized with the child context to have the correct behavior
                return await withClsContext(
                    () =>
                        isolatedTransaction
                            ? isolatedTransaction.withCommit(() => body(childContext))
                            : body(childContext),
                    {
                        context: childContext,
                    },
                );
            } catch (err) {
                await handleError(err, i);
            } finally {
                this.diagnoses.push(...childContext.diagnoses);
                if (options.isDetachedContext) await childContext.close();
            }
        }
        throw new SystemError(`transaction failed: too many conflict retries: ${maxRetries}`);
    }

    /** @internal */
    get isNested(): boolean {
        return !!this.options.parent;
    }

    /**
     * Runs the provided function in an isolated context.
     *
     * @param body - The function to be executed in the isolated context.
     * @param options - Optional configuration for the isolated context.
     * @returns A promise that resolves to the result of the function execution.
     */
    runInIsolatedContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: { isolationLevel?: IsolationLevel; isReadonly?: boolean; disableTenantCrudNotifications?: boolean },
    ): Promise<T> {
        this.checkNotAborted('cannot create a new isolated context');

        // if this is a test context we short-circuit the creation of a new context
        if (!this.mayCommit) {
            return runResolver(this, () => body(this));
        }
        if (this.isIsolated)
            throw new LogicError('Cannot create an isolated context, from an existing isolated context.');

        const opts = {
            contextValues: this._contextValues,
            isolationLevel: options?.isolationLevel || this.isolationLevel || 'low',
            isDetachedContext: true,
            isIsolated: true,
            isReadonly: options?.isReadonly || false,
            source: this.source, // the source of the parent context is passed to the isolated context
        } as ContextOptions;
        if (options?.disableTenantCrudNotifications !== undefined) opts.disableTenantCrudNotifications = true;

        return this.application.requestFunnel(() =>
            this.withChildContext(isolatedContext => {
                return runResolver(isolatedContext, () => body(isolatedContext));
            }, opts),
        );
    }

    /**
     * executes a `body` passing it a new writable context.
     * If the current context is writable or the source of the current context is not `listener` then an error is thrown
     * @returns a promise resolving the result of `body` in a new writable context
     */
    runInWritableContext<T extends AnyValue>(
        body: (context: Context) => AsyncResponse<T>,
        options?: { isolationLevel?: IsolationLevel; noCommit?: boolean; source?: ContextSource },
    ): Promise<T> {
        this.checkNotAborted('cannot create a new writable context');

        // if this is a test context we short-circuit the creation of a new context
        if (!this.mayCommit) {
            return runResolver(this, () => body(this));
        }
        if (this.isWritable)
            throw new LogicError('Cannot create a writable context, from an existing writable context.');
        if (!['listener', 'routing', 'customMutation', 'web-socket'].includes(this.source)) {
            throw new LogicError(
                `Cannot create a writable context outside of a listener, custom mutation or web-socket. Got ${this.source}`,
            );
        }

        const opts = {
            contextValues: this._contextValues,
            isolationLevel: options?.isolationLevel || this.isolationLevel || 'low',
            noCommit: !!options?.noCommit,
            isDetachedContext: true,
            source: options?.source,
        };

        return this.application.requestFunnel(() =>
            this.withChildContext(async writableContext => {
                const result = await runResolver(writableContext, () => body(writableContext));
                return result;
            }, opts),
        );
    }

    /**
     * Runs the provided function in an readonly context.
     * If the current context is readonly, the function is executed in the current context.
     *
     * @param body - The function to be executed in the readonly context.
     * @param options - Optional configuration for the readonly context.
     * @returns A promise that resolves to the result of the function execution.
     */
    async runInReadonlyContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: {
            source?: ContextSource;
            isIsolated?: boolean;
        },
    ): Promise<T> {
        if (!this.isWritable && !this.isolatedTransaction) return body(this);

        // isolation lvl must be low
        const childContext = await this.createChildContext({
            ...options,
            isReadonly: true,
            isolationLevel: 'low',
            isDetachedContext: true,
        });

        const result = await withClsContext(() => body(childContext), {
            context: childContext,
        });

        this.diagnoses.push(...childContext.diagnoses);

        await childContext.close();

        return result;
    }

    private diagnosesPrefixes: string[] = [];

    private _diagnoses = [] as Diagnose[];

    private _severity = ValidationSeverity.test;

    get isolationLevel(): IsolationLevel | undefined {
        return this.options.isolationLevel;
    }

    /** Returns the timestamp value from the context options. */
    get timeLimitAsTimestamp(): number {
        return this.options.timeLimitAsTimestamp || Number.MAX_SAFE_INTEGER;
    }

    /**
     * @internal
     */
    setLastCommitTimestampCookie(): void {
        if (!this.tenantId) return;
        const def = lastCommitTimestampCookieDefinition(
            this.clusterId ?? '',
            this._config.app ?? '',
            this.tenantId,
            this.isSecure(),
        );
        this.response.cookie(
            def.name,
            this.lastCommitTimestamp == null ? 'null' : this.lastCommitTimestamp.toString(),
            def.options,
        );
    }

    /**
     * Wraps the execution of a save or delete action, to get correct diagnoses when they are nested.
     * This method handles situations were save/delete is called from a saveBegin/End or deleteBegin/End
     * event of another save/delete action.
     * In this case we still want the diagnoses that have been emitted by the nested call but we need to
     * tweak their message and path so that they reflect the nested context.
     * @param body the action to execute
     * @param prefix the prefix for diagnoses, in case the action is nested
     *
     *  @internal
     */
    async withDiagnoses<T extends AnyValue>(body: () => AsyncResponse<T>, prefix: string): Promise<T> {
        if (this.diagnosesPrefixes.length === 0) {
            this._severity = ValidationSeverity.test;
            if (this.source !== 'graphql') this._diagnoses = [];
        }
        this.diagnosesPrefixes.push(prefix);
        try {
            return await body();
        } finally {
            this.diagnosesPrefixes.pop();
        }
    }

    addDiagnoseAtPath(severity: ValidationSeverity, path: string[], message: string): void {
        let diagnose: Diagnose;
        if (this.diagnosesPrefixes.length > 1) {
            // If this is a nested call, for example save or a delete called from a saveBegin/End rule,
            // we prefix the message and we discard the path (because the path is for a different object)
            const prefixedMessage = [...this.diagnosesPrefixes.slice(1), message].join(': ');
            diagnose = new Diagnose(severity, [], prefixedMessage);
        } else {
            diagnose = new Diagnose(severity, path, message);
        }
        if (this._severity < severity) this._severity = severity;
        const diagnoses = this._diagnoses;
        if (!diagnoses.some(d => Diagnose.areEqual(d, diagnose))) {
            this._diagnoses.push(diagnose);
        }
    }

    resetDiagnoses(): void {
        this._diagnoses = [];
    }

    /**
     * Executes the provided function and handles any errors that occur during its execution.
     *
     * @param body the action to execute
     * @param errorHandler the error handler to execute in case of error
     * @returns the result of the body if the body succeeded, the result of the error handler otherwise
     *
     * Note: The error handler may rethrow an error instead of returning a result.
     */
    async withErrorHandler<T>(body: () => Promise<T>, errorHandler: (error: any) => Promise<T>): Promise<T> {
        try {
            return await body();
        } catch (error) {
            const savedDiagnoses = this._diagnoses;
            // Reset the diagnoses so that the error handler can call node.$.save and node.$.delete without failing
            // because of existing diagnoses.
            this._diagnoses = [];
            try {
                return await errorHandler(error);
            } finally {
                this._diagnoses = [...savedDiagnoses, ...this._diagnoses];
            }
        }
    }

    get diagnoses(): Diagnose[] {
        return this._diagnoses;
    }

    get severity(): ValidationSeverity {
        return this._severity;
    }

    hasErrors(): boolean {
        return this._severity >= ValidationSeverity.error;
    }

    debugString(): string {
        return `*** readonly nodes *** ${Object.keys(this.transaction.readonlyNodeStates)
            .map(k => this.transaction.readonlyNodeStates[k].node.toString())
            .join('\n')} *** writable nodes *** ${Object.keys(this.transaction.writableNodeStates)
            .map(k => this.transaction.writableNodeStates[k].node.toString())
            .join('\n')}`;
    }

    get introspection(): Introspection {
        return new Introspection(this);
    }

    /** specialized API to read configuration data (coming from xtrem-config.yml) */
    get configuration(): ConfigurationService {
        return new ConfigurationService(this._config);
    }

    /**
     * Creates a node from given data.
     * @param clas
     * @param data
     */
    // Note : this function should only be called externally (from application code)
    async create<T extends Node>(
        clas: StaticThis<T>,
        data: NodeCreateData<T>,
        options?: NodeCreateOptions,
    ): Promise<T> {
        const factory = this.application.getFactoryByConstructor(clas);
        return (await NodeState.newFromContextCreate(this, factory, [], data as unknown as AnyRecord, options))
            .node as T;
    }

    /**
     * Checks if the keys are in the provided table index.
     * @param indexes
     * @param key
     * @returns boolean
     */
    /** @internal */
    private static isKeyMatchingIndices(indexes: NodeIndex[], key: NodeKey<Node>): boolean {
        // We assume that if we receive a string as a key it will match the provided index.
        if (typeof key === 'string' || typeof key === 'number') return true;
        const keys = Object.keys(key);
        // value will be unique if key contains _id as we filter by _tenantId and this constitute the primary
        // and unique value will be returned.
        if (keys.includes('_id')) return true;
        return !!indexes.find(
            index => index.isUnique === true && _.intersection(Object.keys(index.orderBy), keys).length === keys.length,
        );
    }

    private readNodeState<T extends Node>(
        clas: StaticThis<T>,
        key: NodeKey<T>,
        options: NodeReadOptions & { dontThrow?: boolean },
    ): Promise<NodeState | null> {
        const factory = this.application.getFactoryByConstructor(clas);
        const allIndexes = factory.getAllIndexes();

        if (allIndexes && !Context.isKeyMatchingIndices(allIndexes, key))
            throw new Error(`Keys don't match any unique index : ${Object.keys(key)}`);
        return NodeState.newFromRead(this, factory, key, options);
    }

    async tryRead<T extends Node>(clas: StaticThis<T>, key: NodeKey<T>, options?: NodeReadOptions): Promise<T | null> {
        if (!(await this.application.getFactoryByConstructor(clas).isEnabledByServiceOptions(this))) return null;
        const state = await this.readNodeState(clas, key, { ...options, dontThrow: true });
        return state ? (state.node as T) : null;
    }

    async read<T extends Node>(clas: StaticThis<T>, key: NodeKey<T>, options?: NodeReadOptions): Promise<T> {
        if (!(await this.application.getFactoryByConstructor(clas).isEnabledByServiceOptions(this)))
            throw new Error(`Node ${this.application.getFactoryByConstructor(clas).name} is restricted for reading.`);
        return (await this.readNodeState(clas, key, { ...options, dontThrow: false }))!.node as T;
    }

    async deleteMany<T extends Node>(
        clas: StaticThis<T>,
        filter: NodeQueryFilter<T>,
        options?: NodeDeleteOptions,
    ): Promise<number> {
        const factory = this.application.getFactoryByConstructor(clas);
        factory.checkCanDeleteMany(this);
        const result = await factory.deleteMany(this, filter as NodeQueryFilter<Node>, {
            skipControls: false,
            ...options,
        });
        if (result > 0) await factory.cache.invalidate(this);
        return result;
    }

    delete<T extends Node>(clas: StaticThis<T>, key: AnyRecord): Promise<void> {
        const factory = this.application.getFactoryByConstructor(clas);
        return factory.delete(this, key);
    }

    async executeGraphql<T extends AnyValue>(query: string): Promise<T> {
        const result = (await graphql({
            schema: await this.application.getGraphQLSchema(this),
            source: query,
            contextValue: this,
        })) as ExecutionResult<T>;
        // TODO: REVIEW THIS
        const firstError = this.diagnoses.find(d => d.severity === ValidationSeverity.error);
        if (firstError) throw new BusinessRuleError(firstError.message, undefined, firstError.path);
        if (result.errors) {
            result.errors.forEach(error => loggers.runtime.error(`executeGraphql: ${error.message}`));
            throw new InteropError(`GraphQL execution failed: ${JSON.stringify(result.errors)}`, this.diagnoses);
        }
        return result.data as T;
    }

    async executeGraphqlStream<T extends AnyValue>(query: string): Promise<T> {
        const result = (await graphql({
            schema: await this.application.getGraphQLSchema(this),
            source: query,
            contextValue: this,
        })) as ExecutionResult<T>;
        if (result.errors) {
            result.errors.forEach(error => loggers.runtime.error(`executeGraphql: ${error.message}`));
        }
        return result as T;
    }

    query<T extends Node>(clas: StaticThis<T>, options?: NodeQueryOptions<T>): AsyncArray<T> {
        return new AsyncArray(async () => {
            if (!(await this.application.getFactoryByConstructor(clas).isEnabledByServiceOptions(this))) return [];

            const result = await (await this.application.getFactoryByConstructor(clas).query(this, options)).readAll();

            if (result.length >= ConfigManager.getSetting('sage/xtrem-core/queryWarnThreshold', 1000) && options) {
                Context.logOptionsData(clas, result.length, options);
            }
            return result as T[];
        });
    }

    /**
     * Low level select
     *
     * Executes a query and returns an array of plain JavaScript objects.
     * The `selector` parameter specifies the properties included in the response.
     * The `options` are the same as for context.query.
     */
    select<NodeT extends Node, SelectorT extends NodeSelector<NodeT> = NodeSelector<NodeT>>(
        nodeConstructor: StaticThis<NodeT>,
        selector: SelectorT,
        options: NodeSelectOptions<NodeT>,
    ): Promise<NodeSelectResult<NodeT, SelectorT>[]> {
        const factory = this.application.getFactoryByConstructor(nodeConstructor);
        return SqlSelect.select(
            this,
            factory,
            selector as NodeSelector<AnyNode>,
            options as NodeSelectOptions<AnyNode>,
        ) as Promise<NodeSelectResult<NodeT, SelectorT>[]>;
    }

    /**
     * Low level select reader
     *
     * Executes a query and returns a reader of plain JavaScript objects.
     * The `selector` parameter specifies the properties included in the response.
     * The `options` are the same as for context.query.
     */
    getSelectReader<NodeT extends Node, SelectorT extends NodeSelector<NodeT> = NodeSelector<NodeT>>(
        nodeConstructor: StaticThis<NodeT>,
        selector: SelectorT,
        options: NodeSelectOptions<NodeT>,
    ): Promise<AsyncReader<NodeSelectResult<NodeT, SelectorT>>> {
        const factory = this.application.getFactoryByConstructor(nodeConstructor);
        return SqlSelect.getSelectReader(
            this,
            factory,
            selector as NodeSelector<AnyNode>,
            options as NodeSelectOptions<AnyNode>,
        ) as Promise<AsyncReader<NodeSelectResult<NodeT, SelectorT>>>;
    }

    /**
     * Runs a bulk update. The options.set and options.where must be simple enough to be parsed into sql statement.
     * If not, an error will be raised
     * @param nodeConstructor
     * @param options
     */
    bulkUpdate<This extends Node>(
        nodeConstructor: StaticThis<This>,
        options: BulkUpdateOptions<This>,
    ): Promise<number> {
        if (this.tenantId) {
            const payload = `${nodeConstructor.name}/update`;
            this.broadcastToAllUsers({ category: NOTIFICATION_CATEGORY_NODE_MODIFIED, payload, afterCommit: true });
        }
        return SqlBulk.update(nodeConstructor, this, options);
    }

    /**
     * Runs a bulk delete. The options.set and options.where must be simple enough to be parsed into sql statement.
     * If not, an error will be raised
     * @param nodeConstructor
     * @param options
     */
    bulkDeleteSql<This extends Node>(
        nodeConstructor: StaticThis<This>,
        options: BulkDeleteOptions<This>,
    ): Promise<number> {
        if (this.tenantId) {
            const payload = `${nodeConstructor.name}/delete`;
            this.broadcastToAllUsers({ category: NOTIFICATION_CATEGORY_NODE_MODIFIED, payload, afterCommit: true });
        }
        return SqlBulk.delete(nodeConstructor, this, options);
    }

    async queryWithReader<NodeT extends Node, ResultT extends AnyValue>(
        clas: StaticThis<NodeT>,
        options: NodeQueryOptions<NodeT>,
        body: (reader: AsyncReader<NodeT>) => AsyncResponse<ResultT>,
    ): Promise<ResultT> {
        if (this.isWritable) {
            // For the moment we just log an error message but when this story is done we throw SystemError https://jira.sage.com/browse/XT-64787
            // throw new SystemError
            loggers.runtime.error('cannot use queryWithReader with writable context', {
                // This error might be raised multiple times in the same transaction. We want to skip duplicates
                ignoreCallback: message => this.shouldIgnoreDuplicateLogs(message),
            });
        }
        const reader = (
            (await this.application.getFactoryByConstructor(clas).isEnabledByServiceOptions(this))
                ? await this.application
                      .getFactoryByConstructor(clas)
                      .query(this, { ...(options as NodeQueryOptions<Node>), doNotCache: true })
                : new AsyncArrayReader<Node>(() => [])
        ) as AsyncReader<NodeT>;
        try {
            return await body(reader);
        } finally {
            await reader.stop();
        }
    }

    queryCount<T extends Node>(clas: StaticThis<T>, options: NodeQueryOptions<T> = {}): Promise<number> {
        return this.application.getFactoryByConstructor(clas).queryCount(this, options);
    }

    /** @internal */
    private walkGroup(item: AggregateGroupItem<any> | undefined, result: AggregateGroup): void {
        if (item == null || typeof item !== 'object')
            throw new Error(`invalid group: ${item === null ? 'null' : typeof item}`);
        Object.keys(item).forEach(key => {
            if (key === '_by') {
                result.groupedBy = item[key] as GroupBySelector;
            } else {
                result.path.push(key);
                this.walkGroup((item as AggregateGroups<any>)[key], result);
            }
        });
    }

    /** @internal */
    private parseAggregateGroups(groups: AggregateGroups<any>): AggregateGroup[] {
        return Object.keys(groups).map(key => {
            const result = { path: [key] };
            this.walkGroup(groups[key], result);
            return result;
        });
    }

    /** @internal */
    private walkValue(item: AggregateValueItem<any>, result: AggregateValue): void {
        if (item == null || typeof item !== 'object')
            throw new Error(`invalid value: ${item === null ? 'null' : typeof item}`);
        Object.keys(item).forEach(key => {
            const val = (item as any)[key];
            if (val === true) {
                result.operator = key as ValuesOperator;
            } else {
                result.path.push(key);
                this.walkValue(val, result);
            }
        });
    }

    /** @internal */
    private parseAggregateValues(values: AggregateValues<any>): AggregateValue[] {
        return Object.keys(values).map(key => {
            const result = { path: [key] };
            this.walkValue(values[key]!, result);
            return result;
        });
    }

    /**
     * Queries an aggregate
     *
     * @param constructor the node constructor
     * @param options the query options
     *
     * Example:
     * ```
     * const results = context.queryAggregates(Invoice, {
     *     filter: { customer: { country: 'US' } },
     *     group: { date: { _by:  'month' } },
     *     values: { amount: { sum: true, avg: true } },
     * })
     * ```
     *
     * This query will return the monthly sum and average amounts of US invoices.
     * Typical result will be:
     *
     * ```
     * [{
     *     group: { date: '01-01-2020' },
     *     values: { amount: { sum: 5000, avg: 1250 } },
     * }, {
     *     group: { date: '01-02-2020' },
     *     values: { amount: { sum: 3800, avg: 950 } },
     * }, {
     *     ...
     * }]
     * ```
     */
    queryAggregate<T extends Node, GroupT extends AggregateGroups<T>, ValuesT extends AggregateValues<T>>(
        constructor: StaticThis<T>,
        options: QueryAggregateOptions<T, GroupT, ValuesT>,
    ): AsyncArray<QueryAggregateReturn<T, GroupT, ValuesT>> {
        return this.query(constructor, {
            ...options,
            aggregate: {
                groups: this.parseAggregateGroups(options.group),
                values: this.parseAggregateValues(options.values),
            },
        }) as AsyncArray<any>;
    }

    /**
     * Reads an aggregate
     *
     * @param constructor the node constructor
     * @param options the read options
     *
     * Example:
     * ```
     * const results = context.readAggregates(Invoice, {
     *     filter: { customer: { country: 'US' } },
     *     values: { amount: { sum: true, avg: true } },
     * })
     * ```
     *
     * This query will return the sum and average amounts of all US invoices.
     * Typical result will be:
     *
     * ```
     * { amount: { sum: 385000, avg: 1439 } }
     * ```
     */
    async readAggregate<T extends Node, ValuesT extends AggregateValues<T>>(
        clas: StaticThis<T>,
        options: ReadAggregateOptions<T, ValuesT>,
    ): Promise<ReadAggregateReturn<T, ValuesT>> {
        const results = (await this.query(clas, {
            ...options,
            aggregate: {
                groups: [],
                values: this.parseAggregateValues(options.values),
            },
        }).toArray()) as unknown as { values: ReadAggregateReturn<T, ValuesT> }[];

        if (results.length !== 1) throw new Error(`readAggregate returns ${results.length} results`);
        if (!results[0].values) throw new Error('invalid readAggregate result: no values');
        return results[0].values;
    }

    async exists<T extends Node>(clas: StaticThis<T>, key: NodeKey<T>): Promise<boolean> {
        // TODO: Fix exists() function ASAP. it should work the same way as tryRead (invoke defaultValues() on decorators)
        // This fix was introduced for glossaries (glossaryId was missing - see unit tests)
        return !!(await this.tryRead(clas, key));
    }

    /** wrapper to include reference data in data payloads when creating nodes */
    // note: clas arg is just there for typing
    static referenceData<T extends Node>(_clas: StaticThis<T>, data: Partial<T>): T {
        return data as T;
    }

    /** @internal */
    get cache(): MemoryCache {
        return this.#cache;
    }

    /**
     * Returns the cached value for a given key. If not found, the getValue callback will be invoked (if provided)
     * @param category a string that will identify the category of the cached value
     * @param key the key of the cached value
     * @param options
     */
    getCachedValue<T extends AnyValue>(options: ContextCacheOptions<T>): Promise<T> {
        return this.application.globalCache.getValue(this, options);
    }

    /**
     * Returns the cache category for a given node.
     * Items that will be cached with this category will be automatically invalidated when the node's table changes.
     */
    getNodeCacheCategory(nodeConstructor: StaticThis<Node>): string {
        const factory = this.application.getFactoryByConstructor(nodeConstructor);
        if (!factory.isCached) throw factory.logicError('cannot get cache category: isCached attribute is not set');
        return factory.cache.cacheCategory;
    }

    #modifiedCachedCategories: { category: string }[] = [];

    /**
     * @internal
     * This is called just before commit, to notify the other containers that cached factories have been modified
     * by the current transaction (the notifications are sent by the commit).
     */
    async notifyModifiedCachedCategories(): Promise<void> {
        this.checkNotAborted('cannot notify modified cached categories');
        await asyncArray(this.#modifiedCachedCategories).forEach(entry =>
            this.notifyInvalidateCategory(entry.category),
        );
    }

    /**
     * @internal
     * This is called just after commit, to invalidate all the modified factories in the global cache.
     */
    async commitModifiedCachedCategories(): Promise<void> {
        await asyncArray(this.#modifiedCachedCategories).forEach(entry =>
            this.application.globalCache.invalidateCategory(this, entry.category, {
                // notifications have been sent just before commit see notifyModifiedCachedCategories
                skipNotify: true,
            }),
        );
        this.#modifiedCachedCategories.length = 0;
    }

    /**
     * @internal
     * This is called during rollback.
     */
    rollbackModifiedCachedCategories(): void {
        this.#cache = new MemoryCache(this.tenantId || '*', 'context');
        this.#modifiedCachedCategories.length = 0;
    }

    /**
     * Invalidates the cache entries for a given category.
     * @param category a string that will identify the category of the cached values
     * @param options
     *
     * This is called when records are inserted, updated or deleted in the database
     * Transactions are isolated from each other so we don't propagate the change to the global cache.
     * Instead, we just record the category in an array of modified categories, and we use this list
     * to invalidate the global cache later, when the transaction is committed.
     */
    async invalidateCachedCategory(category: string, options: { skipNotify?: boolean } = {}): Promise<void> {
        // invalidate immediately the category in the context cache
        this.cache.invalidateCategory(this, category);

        // invalidate immediately the category in the global cache
        await this.application.globalCache.invalidateCategory(this, category, {
            skipNotify: options.skipNotify,
        });

        // add the factory to the list of modified cached factories
        if (!this.#modifiedCachedCategories.some(entry => entry.category === category))
            this.#modifiedCachedCategories.push({ category });
    }

    /**
     * Ensures that a body will be run exclusively, across all the xtreem servers. 2 exclusive processes can be
     * executed at the same time, as soon as they use a different key.
     * When processes 'A' and 'B' are concurrent with the same key, the second process will be blocked until the end of the
     * process A; the process B is in charge of checking if its execution is still needed at the very beginning
     * of its body (for instance, if processes A & B are upgrade processes, they should double check that they have something to
     * update : first time before invoking runAsExclusive, second time at the beginning of the body).
     */
    /** @internal */
    async runAsExclusive<ResultT extends AnyValue | void>(
        key: string,
        body: () => AsyncResponse<ResultT>,
    ): Promise<ResultT> {
        const profiler = loggers.runtime.verbose(() => `Exclusive task on key ${key}`);
        await this.application.globalLock.acquire(this, key);
        try {
            // get lock on the table
            const result = await body();
            profiler.success();
            return result;
        } catch (err) {
            profiler.fail(err.toString());
            throw err;
        } finally {
            // Nothing to do to release the lock on the table
        }
    }

    /*
    In the 'lookups' context a filter may doesn't meet all the requirements for successfully being built.
    The ignoreErrors options makes it possible for the framework to make 'its best effort' and to not crash in this
    kind of situation. A warning is then added to the response and an error is logged for the developer.
    */
    async buildFilter<T extends Node>(
        filterObject: AnyValue | ((this: Node) => AsyncResponse<AnyFilterObject>),
        node: T,
        options?: {
            ignoreErrors?: boolean;
        },
    ): Promise<AnyValue> {
        switch (typeof filterObject) {
            case 'object': {
                if (filterObject == null) return null;
                if (Array.isArray(filterObject)) {
                    return asyncArray(filterObject)
                        .map(fil => this.buildFilter(fil, node, options))
                        .toArray();
                }
                const newFilterObject: Dict<AnyValue> = {};
                await asyncArray(Object.keys(filterObject)).forEach(async fil => {
                    newFilterObject[fil] = await this.buildFilter((filterObject as AnyRecord)[fil], node, options);
                });
                return newFilterObject;
            }

            case 'function':
                try {
                    const filterFunction = filterObject as (this: Node) => AsyncResponse<AnyFilterObject>;
                    return await filterFunction.call(node);
                } catch (err) {
                    if (options?.ignoreErrors) {
                        loggers.runtime.error(`cannot build filter ${filterObject} ${err.stack}`);
                        this.addDiagnoseAtPath(ValidationSeverity.warn, [], err.message);
                        return {};
                    }
                    throw err;
                }

            default:
                return filterObject;
        }
    }

    private lookupQuery(
        lookupQueryParams: LookupQueryParameters<Node>,
        queryOptions: NodeQueryOptions<Node> = {},
    ): AsyncArray<Node> {
        return new AsyncArray(async () => {
            const filters = await this.makeLookupQueryFilters(lookupQueryParams, queryOptions);
            return this.query(filters.referencedNode, { ...queryOptions, filter: filters.filters }).toArray();
        });
    }

    private async lookupQueryCount(
        lookupQueryParams: LookupQueryParameters<Node>,
        queryOptions: NodeQueryOptions<Node> = {},
    ): Promise<integer> {
        const filters = await this.makeLookupQueryFilters(lookupQueryParams, queryOptions);
        return this.queryCount(filters.referencedNode, { ...queryOptions, filter: filters.filters });
    }

    /**
     * @internal
     *
     */
    lookup(lookupQueryParams: LookupQueryParameters<Node>, queryOptions: NodeQueryOptions = {}): AsyncArray<Node> {
        return new AsyncArray(() =>
            this.withTransientScope(() => this.lookupQuery(lookupQueryParams, queryOptions).toArray()),
        );
    }

    /**
     * @internal
     */
    lookupCount(
        nodeConstructor: StaticThis<Node>,
        propertyName: string,
        args: LookupsArgs,
        queryOptions: NodeQueryOptions = {},
    ): Promise<integer> {
        return this.withTransientScope(async () => {
            const lookupQueryParams = await this.makeLookupQueryParameters(nodeConstructor, propertyName, args);

            return this.lookupQueryCount(lookupQueryParams, queryOptions);
        });
    }

    private async makeLookupQueryFilters(
        lookupQueryParams: LookupQueryParameters<Node>,
        queryOptions: NodeQueryOptions<Node> = {},
    ): Promise<LookupQueryFilters> {
        const referenceProperty = lookupQueryParams.property;
        const referencedNode = referenceProperty.targetFactory.nodeConstructor;
        let queryFilters = referenceProperty.filters;
        const node = lookupQueryParams.node;

        const isActivePropertyName = referenceProperty.getTargetIsActivePropertyName();
        if (!referenceProperty.ignoreIsActive && isActivePropertyName) {
            if (!queryFilters) queryFilters = {};
            queryFilters.lookup = { ...queryFilters.lookup, [isActivePropertyName]: true };
        }

        const filtersArray = [] as AnyValue[];

        if (queryFilters?.lookup)
            filtersArray.push(await this.buildFilter(queryFilters?.lookup || {}, node, { ignoreErrors: true }));

        if (queryFilters?.control)
            filtersArray.push(await this.buildFilter(queryFilters?.control || {}, node, { ignoreErrors: true }));

        await asyncArray(queryFilters?.controls || []).forEach(async item => {
            filtersArray.push(await this.buildFilter(item.filter, node, { ignoreErrors: true }));
        });

        if (queryOptions?.filter && Object.keys(queryOptions.filter).length) filtersArray.push(queryOptions.filter);

        const filters = (filtersArray.length ? { _and: filtersArray } : {}) as NodeQueryFilter<Node>;
        return { filters, referencedNode };
    }

    private async makeLookupQueryParametersWithTransientScope(
        nodeConstructor: StaticThis<Node>,
        propertyName: string,
        args: LookupsArgs,
    ): Promise<LookupQueryParameters<Node>> {
        const factory = this.application.getFactoryByConstructor(nodeConstructor);
        let node: Node;
        const id = args._id || (args.data as any)?._id;
        if (!id || Number(id) < 0) {
            node = await this.create(nodeConstructor, (args.data || {}) as unknown as AnyRecord, {
                isOnlyForLookup: true,
                isTransient: true,
            });
        } else {
            // when managedExternal is set, _id is expected to be a string
            if (!this.managedExternal && Number.isNaN(Number(id))) {
                throw new Error(`${factory.name}.${propertyName}: _id passed to lookup is not a number.`);
            }
            node = await this.read(nodeConstructor, { _id: id }, { isOnlyForLookup: true });
            if (args.data) {
                await node.$.state.set({ ...args.data, _id: id });
            }
        }
        const property = factory.findProperty(propertyName);
        if (!property.isReferenceProperty() && !property.isReferenceArrayProperty())
            throw new Error(`${factory.name}.${propertyName}: invalid property type: ${property.type}`);

        return { node, property };
    }

    makeLookupQueryParameters(
        nodeConstructor: StaticThis<Node>,
        propertyName: string,
        args: LookupsArgs,
    ): Promise<LookupQueryParameters<Node>> {
        return this.withTransientScope(() =>
            this.makeLookupQueryParametersWithTransientScope(nodeConstructor, propertyName, args),
        );
    }

    async withLocalizedTextAsJson<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        const processLocalizedTextAsJson = this.processLocalizedTextAsJson;
        this.processLocalizedTextAsJson = true;
        try {
            return await body();
        } finally {
            this.processLocalizedTextAsJson = processLocalizedTextAsJson;
        }
    }

    async withoutLocalizedTextAsJson<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        const processLocalizedTextAsJson = this.processLocalizedTextAsJson;
        this.processLocalizedTextAsJson = false;
        try {
            return await body();
        } finally {
            this.processLocalizedTextAsJson = processLocalizedTextAsJson;
        }
    }

    /**
     * Scope a body with inCsvLoading set to true
     * @param body
     * @returns
     */
    withCsvLoading<T>(body: () => T): T {
        this._inCsvLoading = true;
        try {
            return body();
        } finally {
            this._inCsvLoading = false;
        }
    }

    /**
     * Returns whether we are in a withCsvLoading scope
     */
    get inCsvLoading(): boolean {
        return this._inCsvLoading;
    }

    /**
     * Activates the conversion of references to natural keys during the execution of `body`.
     * See context.convertReference.
     */
    async withReferenceAsNaturalKey<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        if (this.isWritable)
            throw new LogicError('conversion of reference to natural key is not allowed in a writable context');

        const naturalKeyConverter = this.#naturalKeyConverter;
        if (!naturalKeyConverter) this.#naturalKeyConverter = new ContextNaturalKeyConverter(this);
        try {
            return await body();
        } finally {
            this.#naturalKeyConverter = naturalKeyConverter;
        }
    }

    /**
     * Converts a reference when mapping records returned by SQL queries.
     * The id is converted if this is called from a context.withReferenceAsNaturalKey body.
     * Otherwise the id is returned.
     */
    convertReference(factory: NodeFactory, id: integer): Promise<string | integer> {
        return this.#naturalKeyConverter
            ? this.#naturalKeyConverter.convertReference(factory, id)
            : Promise.resolve(id);
    }

    private activateTestServiceOptions(activeTestServiceOptions: ServiceOption[]): void {
        // By default, service options are not set to their default (isActiveByDefault) in test contexts.
        // Instead, they are set to false!
        Object.values(this.application.serviceOptionsByName).forEach(serviceOption => {
            if (
                activeTestServiceOptions.includes(serviceOption) &&
                ServiceOptionManager.isServiceOptionStatusEnabled(serviceOption.status)
            ) {
                const enableServiceOption = (option: ServiceOption): void => {
                    this.#serviceOptionEnabledFlags[option.name] = true;
                    if (option.activates) option.activates().forEach(enableServiceOption);
                };
                enableServiceOption(serviceOption);
            } else {
                this.#serviceOptionEnabledFlags[serviceOption.name] = false;
            }
        });
    }

    async isServiceOptionEnabled(serviceOption: ServiceOption, options?: { noCache: boolean }): Promise<boolean> {
        let isEnabled = this.#serviceOptionEnabledFlags[serviceOption.name];
        if (isEnabled === undefined || options?.noCache) {
            isEnabled = await this.application.serviceOptionManager.isServiceOptionEnabled(this, serviceOption);
            this.#serviceOptionEnabledFlags[serviceOption.name] = isEnabled;
        }
        return isEnabled;
    }

    setServiceOptionsEnabledFlag(serviceOption: ServiceOption, isEnabled: boolean): void {
        this.#serviceOptionEnabledFlags[serviceOption.name] = isEnabled;
    }

    /** @internal */
    clearServiceOptionEnabledFlags(): void {
        this.#serviceOptionEnabledFlags = {};
    }

    async initializeFactory<NodeT extends Node>(clas: StaticThis<NodeT> | undefined): Promise<void> {
        if (clas) {
            await this.application.getFactoryByConstructor(clas).ensureTableExists(this, { skipDrop: true });
        }
    }

    /**
     * Returns whether one or more readonly scopes are active or not.
     * @internal
     */
    hasReadonlyScopes(): boolean {
        return this.nestedReadonlyScopes > 0;
    }

    /**
     * Executes a body in readonly mode, even if the current state is writable.
     * This is used to prevent side effects in control and compute rules.
     * @internal
     */
    async withReadonlyScope<R extends AnyValue | void>(body: () => AsyncResponse<R>): Promise<R> {
        this.nestedReadonlyScopes += 1;
        try {
            return await body();
        } finally {
            this.nestedReadonlyScopes -= 1;
        }
    }

    /**
     * Executes a body as if the withReadonlyScopes had not been entered.
     * This is used by internal methods that must bypass the readonly scopes.
     * (nothing too bad here - readonly scopes are for applicative code - internals can override).
     * @internal
     */
    async withoutReadonlyScopes<R extends AnyValue | void>(body: () => AsyncResponse<R>): Promise<R> {
        const savedNestedReadonlyScopes = this.nestedReadonlyScopes;
        this.nestedReadonlyScopes = 0;
        try {
            return await body();
        } finally {
            this.nestedReadonlyScopes = savedNestedReadonlyScopes;
        }
    }

    /**
     * Executes a body in transient mode.
     * Transient scope allows create and update of node instance, even if the scope is readonly.
     * As the scope is readonly an exception will be thrown if you try to save the instances.
     * @internal
     */
    async withTransientScope<R extends AnyValue | void>(body: () => AsyncResponse<R>): Promise<R> {
        this.nestedTransientScopes += 1;
        try {
            return await body();
        } finally {
            this.nestedTransientScopes -= 1;
        }
    }

    /** @internal */
    get isTransient(): boolean {
        return this.nestedTransientScopes > 0;
    }

    static getConfigurationValue(name: ContextGetConfigurationKeyType): string {
        switch (name) {
            case 'serviceOptionsLevel':
                return ConfigManager.current.serviceOptions!.level;
            default:
                return '';
        }
        return '';
    }

    /**
     * To be used when ids are meant to be used externaly
     * @internal
     */
    async withExternalIds<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        this._allocateExternalIds = true;
        try {
            return await body();
        } finally {
            this._allocateExternalIds = false;
        }
    }

    /**
     * To be used when getting a property value and you do not want the getPropertyValue error logger to print
     */
    async withoutGetPropertyValueErrorLogger<T extends AnyValue | void>(body: () => AsyncResponse<T>): Promise<T> {
        this.disableGetPropertyValueErrorLogger = true;
        try {
            return await body();
        } finally {
            this.disableGetPropertyValueErrorLogger = false;
        }
    }

    /**
     * Return context options that influence caching of sql statements
     * @internal
     */
    getSqlCacheKey(): AnyRecord {
        return {
            // Context options that influence the generated SELECT statement
            isolationLevel: this.isolationLevel,
            locales: this.locales,
            collation: this.collation,
            noLazyLoading: this.noLazyLoading,
            processLocalizedTextAsJson: this.processLocalizedTextAsJson,
            unsafeApplyToAllTenants: this.unsafeApplyToAllTenants,
            inCsvLoading: this.inCsvLoading,
        };
    }

    /**
     * Send a notification to the client
     */
    async notifyUser(notification: InitialNotification): Promise<void> {
        // We need a writable context here so we temporarilty set the source to web-socket so that we can get a writable
        await this.application.withCommittedContext(
            this.tenantId,
            writableContext => {
                if (notification?.payload) {
                    return this.application.notificationManager.dispatchAsyncMutationNotification(
                        writableContext,
                        notification.payload,
                    );
                }
                return this.application.notificationManager.dispatchUserNotification(writableContext, notification);
            },
            {
                userEmail: this.#userEmail,
                auth: this.#auth,
                locale: this.currentLocale,
                description: () => 'Send user notification',
            },
        );
    }

    /**
     * Logs the options data for a query.
     * @param clas - The class of the node.
     * @param length - The number of nodes returned by the query.
     * @param options - The query options.
     */
    private static logOptionsData(clas: StaticThis<Node>, length: number, options: NodeQueryOptions): void {
        const sanitized = _.pick(options, ['filter', 'orderBy', 'first', 'after', 'last', 'before']);
        loggers.sql.warn(
            `${clas.name}: 'context.query' returns ${
                length
            } nodes. Use 'context.queryWithReader' instead. Options=${JSON.stringify(sanitized, stringifyLogReplacer)}`,
        );
    }

    /**
     * Returns a unique value for a property.
     *
     * @param property
     * @param prefixToUse
     *
     * @example buildUniqueValueForProperty(property, 'foo') will return 'foo1', 'foo2', 'foo3', etc.
     * depending on what is already in the database.
     */
    async buildUniqueValueForProperty(node: Node, propertyName: string, prefix: string): Promise<string> {
        // Remove quotes from the prefix (to prevent SQL injections)
        const prefixToUse = prefix.replace(/'/g, '');
        const factory = node.$.factory;
        const property = factory.findProperty(propertyName);
        // First, retrieve from database all the records where the value of column matches the prefix
        const columnName = property.columnName;
        const wheres = [`${columnName} like '${prefixToUse}%'`];
        const args = [];
        if (!factory.isSharedByAllTenants) {
            wheres.push('_tenant_id = $1');
            args.push(this.tenantId);
        }
        const sql = `SELECT ${columnName} val FROM ${this.schemaName}.${factory.tableName} WHERE ${wheres.join(' AND ')} ORDER BY ${columnName}`;
        const valuesInDb = (
            await this.executeSql<{ val: string }[]>(sql, args, {
                // allowUnsafe to prevent an error because our sql command contains a quote
                allowUnsafe: true,
            })
        ).map(row => row.val);
        // Now, compute the next value
        const regex = new RegExp(`^${prefixToUse}(\\d+)$`);
        let maxCounter = 0;
        valuesInDb.forEach(value => {
            const match = value.match(regex);
            if (match) {
                const recordCounter = parseInt(match[1], 10);
                if (recordCounter >= maxCounter) maxCounter = recordCounter;
            }
        });

        return `${prefixToUse}${maxCounter + 1}`;
    }

    /**
     * Broadcast a notification to the UI
     *
     * @param category - for CRUD operations the format should be <node_name>/<created or updated or deleted>
     * from custom events the format should be <node_node>/<event name>
     * @param payload - for CRUD operations the payload should be empty
     * for custom events payload must be agreed between UI and server
     * @param afterCommit - if true the notification will be broadcasted only after a successful commit
     * if false/undefined the notification will be broadcasted immediatly
     */
    broadcastToAllUsers(options: { category: string; payload?: string; afterCommit?: boolean }): void {
        if (!this.tenantId) return;

        if (options?.afterCommit) {
            this.addDeferredBroadcastMessage(this.tenantId, options.category, options.payload);
        } else {
            this.application.uiBroadcaster.broadcast(this.tenantId, options.category, options.payload);
        }
    }
}

interface LookupQueryParameters<T extends Node> {
    node: T;
    property: ReferenceProperty | ReferenceArrayProperty;
}

interface LookupQueryFilters {
    filters: NodeQueryFilter<Node>;
    referencedNode: new () => Node;
}

interface ResponseExtra {
    [key: string]: any;
}

export type ContextSource =
    | 'graphql'
    | 'workflow'
    | 'listener'
    | 'routing'
    | 'internal'
    | 'customMutation'
    | 'web-socket'
    | 'import'
    | 'rest';

export const replicaSources: ContextSource[] = ['graphql', 'workflow', 'listener', 'customMutation'] as const;

export interface ContextOptions extends TransactionOptions {
    userEmail?: string;
    auth?: AuthConfig;
    parent?: Context;
    config?: Config;
    legislationCode?: string;
    locale?: string;
    source?: ContextSource;
    request?: any;
    response?: ResponseExtra | Response;
    /**
     * signal to abort this context and all its children, any transaction will be rolled back.
     * This uses the AbortController API available in node.js.
     * See https://nodejs.org/docs/latest-v20.x/api/all.html#all_globals_class-abortcontroller
     */
    signal?: AbortSignal;

    testMode?: boolean;
    testLayers?: string[];
    testNowMock?: string;
    testConfig?: TestConfig;
    /**
     * the list of service options required for the test
     */
    testActiveServiceOptions?: ServiceOption[];

    /**
     * the list of active package for tests
     */
    contextValues?: Dict<string>;

    /**
     * Disable all CRUD notifications from triggers
     */
    disableAllCrudNotifications?: boolean;

    /**
     * Disable tenant CRUD notifications from triggers
     */
    disableTenantCrudNotifications?: boolean;

    /**
     * Flag used by test contexts to force rollback instead of commit.
     */
    noCommit?: boolean;

    /** is the transaction in auto commit mode - overrides other options  */
    isAutoCommit?: boolean;

    /** Do not use lazy loading of property values */
    noLazyLoading?: boolean;

    /** Flag to indicate we are creating the root user
     * This will set transaction user to null to force the default self reference on user insert */
    withoutTransactionUser?: boolean;

    /** is this a system context */
    isSystem?: boolean;

    /** **DANGER**: This marks that the context will run SQL commands without a tenant filter regardless if they are shared tables or not. */
    unsafeApplyToAllTenants?: boolean;

    /* Timestamp limit used as a constraint for the graphql query */
    timeLimitAsTimestamp?: number;

    skipManagedExternalInit?: boolean;

    /** Bypass the isFrozen checks - only allowed in test */
    bypassFrozen?: boolean;

    /** SQL connection is not available - used to verify ts-to-sql conversions */
    withoutSqlConnection?: true;

    /** is this an isolated context */
    isIsolated?: boolean;

    /**
     * An optional description for the context
     */
    description?: () => string;
    /**
     * DB timestamp used to check if the replication instance is up to data with the master instance
     */
    lastCommitTimestamp?: Datetime;
}
