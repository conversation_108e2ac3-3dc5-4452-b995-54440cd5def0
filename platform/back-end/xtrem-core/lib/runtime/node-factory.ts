/** @ignore */ /** */
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Value,
    async<PERSON>rray,
    async<PERSON><PERSON>y<PERSON>eader,
    Async<PERSON>eader,
    AsyncResponse,
} from '@sage/xtrem-async-helper';
import { localizedText } from '@sage/xtrem-i18n';
import { ForeignKeyDeleteBehaviour, SqlCreateTableOptions } from '@sage/xtrem-postgres';
import {
    AuthorizationError,
    Dict,
    LogicError,
    nameToSqlName,
    NOTIFICATION_CATEGORY_NODE_MODIFIED,
    SystemError,
    titleCase,
} from '@sage/xtrem-shared';
import { topoSort } from '@sage/xtrem-toposort';
import * as crypto from 'crypto';
import * as _ from 'lodash';
import { Application, ServiceOption } from '../application';
import { Package, PackageValidationContext } from '../application/package';
import { BaseCollection } from '../collections/base-collection';
import {
    Decorated,
    ExternalStorageManager,
    IsolationLevel,
    IsolationLevels,
    MessageListener,
    NodeAuthorizedBy,
    NodeExtensionDecorator,
    NodeKey,
    NodeStorage,
    NotificationListener,
    NotifyOperation,
    PlainOperationDecorator,
    PropertyFilterTag,
    StaticThis,
    StringOrNumberOrNull,
    TypedPropertyDecorator,
} from '../decorators';
import type { NodeFilterTag } from '../decorators/decorator-utils';
import { chainArrays, chainEvents, decoratorsSymbol, FilterTag } from '../decorators/decorator-utils';
import { NodeEvents, NodeRuleName } from '../decorators/node-decorators/node-events';
import { NodeState, StateStatus } from '../node-state';
import { StateGetValue } from '../node-state/state-get-value';
import { StateIntern } from '../node-state/state-intern';
import { ForeignNodeProperty, foreignNodePropertyTypes, ReferenceProperty, StringProperty } from '../properties';
import { createProperty } from '../properties/create-property';
import { PropagatePropertyPath, Property } from '../properties/property';
import { DropTableOptions } from '../sql';
import { NodeInternalQueryOptions, OrderByClause, SqlConverter, SqlQuery } from '../sql/mapper';
import { Column, ForeignKey, Table } from '../sql/schema';
import { ReadTableSqlContext } from '../sql/sql-context/read-table-sql-context';
import { makeName63 } from '../sql/statements/naming';
import {
    AnyFilterObject,
    AnyNode,
    FlatOrderBy,
    maxNodeSubclassingDepth,
    Node,
    NodeQueryFilter,
    NodeQueryOptions,
    OrderBy,
} from '../ts-api';
import { NodeSelector } from '../ts-api/node-select-types';
import { safeParseInt, StringDataType } from '../types';
import { testPackageExclusions } from '../utils';
import { Context, NodeDeleteOptions, StandardOperation } from './context';
import { CoreHooks } from './core-hooks';
import { loadedOnce } from './loaded-once';
import { loggers } from './loggers';
import { NodeFactoryCache } from './node-factory-cache';
import {
    ErrorParameters,
    FactoryDecorators,
    FactoryDecoratorsDictArrayKeys,
    FactoryExtensionDecorators,
    FactoryLinkedList,
    FactoryNodeDecorator,
    FindPropertyOptions,
    NodeIndex,
    ReferringProperty,
    ReversePropertyStruct,
} from './node-factory-types';
import {
    arraysAreEqual,
    ensureNoMultipleUniqueIndexes,
    getSchemaDescription,
    sameIndexProperties,
    setChildFactoriesTable,
} from './node-factory-utils';
import { PropertyDecorator } from './property';
import { SystemProperties } from './system-properties';
import { isCompound, isScalar } from './utils';

loadedOnce('node-factory');
const logger = loggers.nodeFactory;

export type NodeFunction<ResultT extends AnyValue> = (...args: AnyValue[]) => Promise<ResultT>;

/** @disabled_internal */
export class NodeFactory {
    public nodeConstructor: typeof Node;

    /**
     * Cached value for the natural key
     * 'null' means the cache is not computed yet
     */
    private _naturalKey: string[] | undefined | null = null;

    readonly nodeDecorator: FactoryNodeDecorator;

    // Decorator warnings that have been generated, to avoid duplicate warnings
    private decoratorWarnings: Dict<boolean> = {};

    properties: Property[] = [];

    /**
     * The list of properties that will be lazy-loaded, i.e. not loaded when loading the node but only when explicitly read from the code.
     */
    lazyLoadedProperties: Property[] = [];

    storedEncryptedProperties: StringProperty[] = [];

    mutableProperties: ForeignNodeProperty[] = [];

    propertiesByName: Dict<Property> = {};

    keyProperties: Property[] = [];

    mutations: PlainOperationDecorator[] = [];

    queries: PlainOperationDecorator[] = [];

    notificationListeners: NotificationListener[] = [];

    messageListeners: MessageListener[] = [];

    events: NodeEvents<Node> = {};

    private _table: Table;

    initialized: boolean;

    publishedSystemProperties: Dict<Property> = {};

    publishedInputSystemProperties: string[] = [];

    referringProperties: ReferringProperty[] = [];

    dependsOn?: string[];

    storage?: NodeStorage;

    indexes?: NodeIndex[];

    impactingServiceOptions: string[] = [];

    decorators: FactoryDecorators;

    extensions?: any[];

    private _extendedVitalFactories?: NodeFactory[];

    private readonly _package: Package;

    private readonly _taggedProperties: Dict<Property> = {};

    private _baseFactory: NodeFactory | undefined;

    private _subFactories: NodeFactory[] = [];

    // First key is the event name (ex prepare)
    private staticMethods: Dict<any> = {};

    // First key is the event name (ex prepare)
    private nonStaticMethods: Dict<any> = {};

    // First key is the event name (ex prepare)
    /** @internal */
    systemPropertyDefiningPackage: Dict<Package> = {};

    // Array of vital properties
    readonly #foreignNodeProperties: ForeignNodeProperty[] = [];

    // Array of vital properties
    readonly #referenceProperties: ReferenceProperty[] = [];

    // Array of vital properties
    readonly #vitalProperties: ForeignNodeProperty[] = [];

    // Array of delegated properties
    readonly #delegatedProperties: Property[] = [];

    /**
     * @internal
     * The cache of SQL results.
     * The cache is a NOP if the isCached node decorator attribute is not set.
     */
    readonly cache: NodeFactoryCache;

    constructor(pack: Package, clas: StaticThis<Node>, decorators: FactoryDecorators) {
        logger.verbose(() => `${pack.name}.${clas.name}: creating node factory`);
        this._package = pack;
        this.nodeConstructor = clas as any;
        if (!decorators.properties) decorators.properties = [];
        this.decorators = decorators;
        Object.assign(this.decorators, (this.decorators as any).node);
        this.nodeDecorator = decorators.node;
        CoreHooks.communicationManager.extendClassDecorator(this);
        this.cloneDecoratorsAttributes();
        this.cache = new NodeFactoryCache(this);
    }

    private registerEvents(): void {
        const keys = Object.keys(this.nodeDecorator);
        // eslint-disable-next-line consistent-return
        keys.forEach(key => {
            switch (key) {
                case 'prepare':
                case 'prepareBegin':
                case 'prepareEnd':
                case 'createEnd':
                case 'controlBegin':
                case 'controlEnd':
                case 'controlDelete':
                case 'saveBegin':
                case 'saveEnd':
                case 'deleteBegin':
                case 'deleteEnd': {
                    const chainedEvent = this.nodeDecorator[key];
                    if (!chainedEvent) return undefined;
                    if (typeof chainedEvent !== 'function') throw this.systemError(`${key}: event is not a function`);
                    this.events[key] = async function chained(this: Node, ...args: any[]) {
                        await chainedEvent.apply(this, args);
                    } as any;
                    break;
                }
                case 'getFilters':
                    this.events[key] = this.nodeDecorator[key];
                    break;
                default:
                    break;
            }
        });
    }

    private cloneDecoratorsAttributes(): void {
        this.mutations = this.decorators.mutations ? [...this.decorators.mutations] : [];
        this.queries = this.decorators.queries ? [...this.decorators.queries] : [];
        this.notificationListeners = this.decorators.notificationListeners
            ? [...this.decorators.notificationListeners]
            : [];
        this.messageListeners = this.decorators.messageListeners ? [...this.decorators.messageListeners] : [];
        if (this.nodeDecorator.indexes) this.indexes = [...this.nodeDecorator.indexes];
        this.storage = this.nodeDecorator.storage;
        this.registerEvents();
    }

    private verifyNaturalKey(index: NodeIndex): void {
        if (!index.isNaturalKey) return;
        if (!index.isUnique) {
            throw new SystemError(`${this.name}.${index.name} natural key must be a unique index.`);
        }

        const notPublishedNoDefault = Object.keys(index.orderBy)
            .map(key => this.propertiesByName[key])
            .filter(property => !property?.isPublished && property.defaultValue === undefined);

        if (notPublishedNoDefault.length) {
            throw new SystemError(
                `Natural key index ${this.name}.${
                    index.name
                } must have these properties published or assigned a defaultValue [${notPublishedNoDefault.map(
                    p => p.name,
                )}]`,
            );
        }
    }

    /**
     * Raise an error if a factory has multiple unique indexes
     */
    private ensureNoMultipleUniqueIndexes(): void {
        ensureNoMultipleUniqueIndexes(this);
    }

    private verifyIndex(index: NodeIndex): void {
        if (index.isVerified) return;
        index.isVerified = true;
        if (this.storage === 'external') return;

        this.verifyNaturalKey(index);

        const indexProperties = Object.keys(index.orderBy).map(key => this.propertiesByName[key]);

        const invalidTypeProperties = indexProperties.filter(property => property.invalidForIndexReason);

        if (invalidTypeProperties.length > 0)
            throw new SystemError(
                `${this.name}.${
                    index.name
                } contains properties with types not allowed in an index [${invalidTypeProperties.map(
                    p => `${p.name} (${p.invalidForIndexReason})`,
                )}]`,
            );

        const nullableProperties = indexProperties.filter(
            property => property?.isNullable && !property?.allowedInUniqueIndex,
        );

        if (nullableProperties.length > 0)
            throw new SystemError(
                `${this.name}.${
                    index.name
                } contains nullable properties not allowed in this unique index. Please consider using 'allowedInUniqueIndex: true' decorator attribute for properties [${nullableProperties.map(
                    p => p.name,
                )}] among [${Object.keys(index.orderBy)}]`,
            );

        // Inherited properties cannot be used to define an index
        const inheritedProperties = indexProperties.filter(prop => prop.isInherited);
        if (inheritedProperties.length > 0)
            throw new SystemError(
                `${this.name}.${
                    index.name
                } contains the following properties from an inherited node: ${inheritedProperties
                    .map(p => p.name)
                    .join(',')}`,
            );
    }

    invalidateCache(context: Context): Promise<void> {
        return this.cache.invalidate(context);
    }

    get defaultOrderByClauses(): OrderByClause[] {
        return [
            {
                path: ['_id'],
                direction: 1,
                property: SystemProperties.idProperty(this),
                sql: 't0._id',
            },
        ];
    }

    get table(): Table {
        return this._table;
    }

    set table(table: Table) {
        this._table = table;
    }

    get externalStorageManager(): ExternalStorageManager<Node> | undefined {
        return this.nodeDecorator.externalStorageManager;
    }

    get serviceOptions(): ServiceOption[] {
        return this.nodeDecorator.serviceOptions?.() || [];
    }

    get isAbstract(): boolean {
        return !!this.nodeDecorator.isAbstract;
    }

    /**
     * Returns whether the factory is the root factory of a hierarchy.
     * It will return false when the factory does not belong to a hierarchy (not a base factory)
     */
    get isRootAbstractFactory(): boolean {
        return this.isAbstract && !this.baseFactory;
    }

    get isVitalReferenceChild(): boolean {
        return !!this.nodeDecorator.isVitalReferenceChild || !!this.baseFactory?.isVitalReferenceChild;
    }

    get isVitalCollectionChild(): boolean {
        return !!this.nodeDecorator.isVitalCollectionChild || !!this.baseFactory?.isVitalCollectionChild;
    }

    get isAssociationCollectionChild(): boolean {
        return !!this.nodeDecorator.isAssociationCollectionChild || !!this.baseFactory?.isAssociationCollectionChild;
    }

    get isVitalChild(): boolean {
        return this.isVitalReferenceChild || this.isVitalCollectionChild;
    }

    get isAssociationChild(): boolean {
        return (
            !!this.nodeDecorator.isAssociationCollectionChild ||
            !!this.nodeDecorator.isAssociationReferenceChild ||
            !!this.baseFactory?.isAssociationChild
        );
    }

    get isPublished(): boolean {
        return !!this.nodeDecorator.isPublished;
    }

    get isCached(): boolean {
        return !!this.nodeDecorator.isCached;
    }

    get isVitallyCached(): boolean {
        return this.isVitalChild ? this.vitalParentFactory.isCached : this.isCached;
    }

    get canRead(): boolean {
        // If the factory is part of a vital graph canRead is true as soon as it is true on one of its ancestors.
        return !!this.nodeDecorator.canRead || (this.isVitalChild && this.vitalParentFactory.canRead);
    }

    get canCreate(): boolean {
        if (this.baseFactory?.canCreate) return true;
        const canCreate = !!this.nodeDecorator.canCreate;
        return this.externalStorageManager?.canCreate ? this.externalStorageManager.canCreate(canCreate) : canCreate;
    }

    get canUpdate(): boolean {
        if (this.baseFactory?.canUpdate) return true;
        const canUpdate = !!this.nodeDecorator.canUpdate;
        return this.externalStorageManager?.canUpdate ? this.externalStorageManager.canUpdate(canUpdate) : canUpdate;
    }

    get canDelete(): boolean {
        if (this.baseFactory?.canDelete) return true;
        const canDelete = !!this.nodeDecorator.canDelete;
        return this.externalStorageManager?.canDelete ? this.externalStorageManager.canDelete(canDelete) : canDelete;
    }

    /* Does the node have a duplicate mutation? */
    get canDuplicate(): boolean {
        if (this.baseFactory?.canDuplicate) return true;
        return !!this.nodeDecorator.canDuplicate;
    }

    get duplicateAttachments(): boolean {
        if (this.baseFactory?.duplicateAttachments) return !!this.baseFactory.duplicateAttachments;
        return !!this.nodeDecorator.duplicateAttachments;
    }

    get notifies(): NotifyOperation[] {
        const superNotifies = this.baseFactory?.notifies ?? [];
        const thisNotifies = this.nodeDecorator.notifies ?? [];
        return _.uniq([...superNotifies, ...thisNotifies]);
    }

    get canAudit(): boolean {
        const notifies = this.notifies;
        return notifies.includes('created') && notifies.includes('updated') && notifies.includes('deleted');
    }

    /**
     * Can the node be duplicated,
     * eventually because one of its vital ancestors can be duplicated,
     * or because one of its subclasses can be duplicated
     */
    get canBeDuplicated(): boolean {
        if (this.canDuplicate) return true;
        if (this.isVitalChild && this.vitalParentFactory.canBeDuplicated) return true;
        if (this.isAbstract && this.subFactories.some(subFactory => subFactory.canBeDuplicated)) return true;
        return false;
    }

    // see later for bulkUpdate
    // get canBulkUpdate(): boolean {
    //     return !!this.nodeDecorator.canBulkUpdate;
    // }

    get canBulkDelete(): boolean {
        return !!this.nodeDecorator.canBulkDelete;
    }

    get canDeleteMany(): boolean {
        const canDeleteMany = !!this.nodeDecorator.canDeleteMany;

        return this.externalStorageManager?.canDeleteMany
            ? this.externalStorageManager.canDeleteMany(canDeleteMany)
            : canDeleteMany;
    }

    /**
     * Can this node be exported?
     */
    get canExport(): boolean {
        return this.nodeDecorator.canExport !== false;
    }

    get isSetupNode(): boolean {
        // Vital children are also setup nodes if the parent is a setup node
        // Avoid infinite recursion by checking vitalParentFactory !== this, for nodes where it's a parent of itself (eg. x3 TestDocument)
        return (
            !!this.nodeDecorator.isSetupNode ||
            (this.isVitalChild && this.vitalParentFactory !== this && this.vitalParentFactory.isSetupNode) ||
            !!this.baseFactory?.isSetupNode
        );
    }

    get hasVendorProperty(): boolean {
        // Vital children will be a setup node if the parent is one, therefore they will implicitly have a _vendor property
        // For now we exclude shared nodes and platform nodes from having a _vendor property
        // see later if we have use cases to support it
        if (this.nodeDecorator.hasVendorProperty === false) return false;
        return (
            this.storage === 'sql' &&
            !this.isSharedByAllTenants &&
            !this.isPlatformNode &&
            this.isSetupNode &&
            (!this.isVitalChild || (this.isVitalChild && this.vitalParentFactory.hasVendorProperty))
        );
    }

    get hasAttachments(): boolean {
        // If not set on the node, check the baseFactory
        return this.nodeDecorator.hasAttachments ?? !!this.baseFactory?.hasAttachments;
    }

    /**
     * Can the node have tags?
     *
     * Nodes with tags will have a new system property '_tags'. This property is managed by the framework.
     */
    get hasTags(): boolean {
        // If not set on the node, check the baseFactory
        return this.nodeDecorator.hasTags ?? !!this.baseFactory?.hasTags;
    }

    /**
     * Can the node have notes?
     *
     * Nodes with notes will have a new system property '_notes'. This property is managed by the framework.
     */
    get hasNotes(): boolean {
        // If not set on the node, check the baseFactory
        return this.nodeDecorator.hasNotes ?? !!this.baseFactory?.hasNotes;
    }

    #hasLookupAccess: boolean | null = null;

    get hasLookupAccess(): boolean {
        // Check for any properties with lookupAccess, except the default ones
        if (this.#hasLookupAccess == null)
            this.#hasLookupAccess = this.properties.some(
                prop =>
                    prop.lookupAccess &&
                    !['_id', '_customData', '_createStamp', '_updateStamp', '_sortValue'].includes(prop.name),
            );

        return this.#hasLookupAccess;
    }

    get isPlatformNode(): boolean {
        return !!this.nodeDecorator.isPlatformNode;
    }

    get isPlatformNodeExportable(): boolean {
        return !!this.nodeDecorator.isPlatformNodeExportable || (this.isPlatformNode && this.isSetupNode);
    }

    get isSkippedByLayerExtract(): boolean {
        return !!this.nodeDecorator.isSkippedByLayerExtract;
    }

    get isContentAddressable(): boolean {
        return this.baseFactory ? this.baseFactory.isContentAddressable : !!this.nodeDecorator.isContentAddressable;
    }

    get keyPropertyNames(): string[] {
        return this.nodeDecorator.keyPropertyNames ?? this.naturalKey ?? [];
    }

    #uniqueKeys: string[][];

    /**
     * Returns a consolidated list of unique indexes of the factory of the state.
     * The primary key will always be the first entry in the list (_id for services but may be a composite for X3)
     */
    get uniqueKeyProperties(): string[][] {
        if (this.#uniqueKeys) return this.#uniqueKeys;

        this.#uniqueKeys = [this.keyProperties.map(keyProperty => keyProperty.name)];

        if (this.storage === 'external') this.#uniqueKeys.push(['_id']);

        this.indexes
            ?.filter(index => index.isUnique)
            .forEach(index => {
                this.#uniqueKeys.push(Object.keys(index.orderBy));
            });

        // indexes does not include the base node indexes
        if (this.baseFactory) {
            this.#uniqueKeys.push(...this.baseFactory.uniqueKeyProperties);
        }

        // Sort because we may have unique indexes on both [p1, p2] and [p2, p1]
        this.#uniqueKeys = _.uniq(
            this.#uniqueKeys.map(keys => JSON.stringify(keys.slice().sort((s1, s2) => s1.localeCompare(s2)))),
        ).map(keys => JSON.parse(keys));

        return this.#uniqueKeys;
    }

    #vitalParentProperty: ReferenceProperty | undefined;

    get vitalParentProperty(): ReferenceProperty {
        if (this.#vitalParentProperty) return this.#vitalParentProperty;

        if (!this.isVitalChild)
            throw this.logicError('cannot access vital parent property on a node which is not a vital child');

        const vitalParentProperty = this.properties.find(p => p.isVitalParent);

        if (!vitalParentProperty) throw this.systemError('isVitalParent property not found');
        if (!vitalParentProperty.isReferenceProperty())
            throw this.propertySystemError(vitalParentProperty, 'vital parent property is not a reference');

        this.#vitalParentProperty = vitalParentProperty;
        return this.#vitalParentProperty;
    }

    get associationParentProperties(): ReferenceProperty[] {
        return this.properties.filter(p => p.isVitalParent || p.isAssociationParent) as ReferenceProperty[];
    }

    get vitalParentFactory(): NodeFactory {
        return this.vitalParentProperty.targetFactory;
    }

    /**
     * Is the factory a vital child, were the parent node is a setup node
     */
    get isSetupNodeVitalChild(): boolean {
        return (
            this.isVitalChild &&
            // Next test against `this` avoids infinite recursion on x3 TestDocument which is vital parent of itself!
            // TODO: forbid cycles on vital parent, fix x3 test and remove this test
            this.vitalParentFactory !== this &&
            this.vitalParentFactory.isSetupNode
        );
    }

    get isClearedByReset(): boolean | undefined | ((this: Node) => AsyncResponse<boolean>) {
        return (
            this.storage === 'sql' &&
            (this.nodeDecorator.isClearedByReset ||
                (this.isVitalChild && this.vitalParentProperty.targetFactory.isClearedByReset) ||
                this.baseFactory?.isClearedByReset)
        );
    }

    private _retrieveNaturalKey(): string[] | undefined {
        const naturalKeyIdx = this.indexes?.find(index => index.isNaturalKey);
        if (naturalKeyIdx != null) {
            return Object.keys(naturalKeyIdx.orderBy);
        }
        if (this.baseFactory) {
            const superNaturalKey = this.baseFactory.naturalKey;
            if (superNaturalKey) {
                // The natural key of an abstract class may contain the _constructor property, but we don't want
                // the _constructor property to be part of the natural key of the concrete classes.
                if (!this.isAbstract) return superNaturalKey.filter(key => key !== '_constructor');
                return superNaturalKey;
            }
        }
        if (this.isVitalChild && !this.isAssociationChild) {
            const parentNaturalKey = this.vitalParentFactory.naturalKey;
            if (parentNaturalKey)
                return this.isVitalCollectionChild
                    ? [this.vitalParentProperty.name, '_sortValue']
                    : [this.vitalParentProperty.name];
        }

        if (this.isAssociationChild) return this.associationParentProperties.map(p => p.name);

        return undefined;
    }

    /**
     * Returns the natural key for the factory
     * Note: the natural key decorator may come from the factory itself, from its baseFactory or from a vital parent.
     */
    get naturalKey(): string[] | undefined {
        if (this._naturalKey === null) {
            // Not resolved yet
            this._naturalKey = this._retrieveNaturalKey();
            if (this._naturalKey) Object.freeze(this._naturalKey);
        }
        return this._naturalKey;
    }

    get delegatedProperties(): Property[] {
        return this.#delegatedProperties;
    }

    get foreignNodeProperties(): ForeignNodeProperty[] {
        return this.#foreignNodeProperties;
    }

    get referenceProperties(): ReferenceProperty[] {
        return this.#referenceProperties;
    }

    get vitalProperties(): ForeignNodeProperty[] {
        return this.#vitalProperties;
    }

    get hasSelfReference(): boolean {
        // User node has an implict self reference in _create_user and _update_user
        return (
            this.referenceProperties.some(prop => prop.isSelfReference) ||
            this.name === CoreHooks.sysManager.getUserNode().name
        );
    }

    /** Does the factory have a SQL insert function. */
    get hasSqlInsertFunction(): boolean {
        return this.storage === 'sql' && !this.isAbstract;
    }

    get prepareBegin(): NodeEvents['prepareBegin'] {
        // TODO: compat code for obsolete prepare decorator - remove later
        if (this.events.prepareBegin && this.events.prepare && this.events.prepare !== this.events.prepareBegin)
            throw this.logicError('cannot have both prepare and prepareBegin rules');
        this.events.prepareBegin = this.events.prepareBegin || this.events.prepare;

        return this.events.prepareBegin;
    }

    get prepareEnd(): NodeEvents['prepareEnd'] {
        return this.events.prepareEnd;
    }

    /**
     * If `this` is abstract, returns the concrete factories that extend `this`.
     * Otherwise returns `[this]`, as `this` is a concrete factory.
     */
    getConcreteFactories(result: NodeFactory[] = []): NodeFactory[] {
        if (this.isAbstract) {
            this.subFactories.forEach(f => f.getConcreteFactories(result));
        } else {
            result.push(this);
        }
        return result;
    }

    isSubNodeOf(name: string): boolean {
        return this.name === name || !!this.baseFactory?.isSubNodeOf(name);
    }

    isAssignableTo(factory: NodeFactory): boolean {
        if (this === factory) return true;
        return !!this.baseFactory && this.baseFactory.isAssignableTo(factory);
    }

    isEnabledByServiceOptions(context: Context): Promise<boolean> {
        return asyncArray(this.serviceOptions).every(serviceOption => context.isServiceOptionEnabled(serviceOption));
    }

    private getInternalSystemProperties(): Property[] {
        return SystemProperties.getInternalSystemProperties(this);
    }

    private addProperty(property: Property, direction: 'unshift' | 'push' = 'push'): void {
        if (!this.propertiesByName[property.name]) {
            if (direction === 'unshift') {
                this.properties.unshift(property);
            } else {
                this.properties.push(property);
            }
            this.propertiesByName[property.name] = property;
        }
    }

    // Creates Property objects for each of the properties defined in the original node (decorator.node)
    createMainNodeProperties(definingPackage: Package): void {
        // Create the properties from the decorator's propertyDecorators.
        const mainNodeProperties = this.createProperties(this.decorators.properties || [], definingPackage);
        mainNodeProperties.forEach(property => this.addProperty(property));
    }

    // Add the extension to the factory's decorators attribute.
    addExtensionDecorator(extension: any): void {
        this.extensions = this.extensions || [];
        this.extensions.push(extension);
    }

    private createProperties(properties: PropertyDecorator[], definingPackage: Package): Property[] {
        return properties.map(propertyDecorator =>
            createProperty(this, propertyDecorator as TypedPropertyDecorator, definingPackage),
        );
    }

    private getExtensionDepth(extensionDecorators: FactoryExtensionDecorators): number {
        const extendedFactory = this.application.getFactoryByConstructor(extensionDecorators.nodeExtension.extends());
        return extendedFactory.subclassDepth();
    }

    // We have different extension classes for subnodes, depending of their subclassing depth:
    // SubNodeExtension1, SubNodeExtension2, ...
    // This method checks that the extension class was defined with the correct SubNodeExtensionN superclass.
    private checkExtensionDepth(extension: any, extensionDecorators: FactoryExtensionDecorators): void {
        const constructorDepth = extension._extensionDepth;
        const realDepth = this.getExtensionDepth(extensionDecorators);
        if (constructorDepth !== realDepth) {
            throw this.systemError(
                `${extension.name}: invalid extension superclass: expected SubNodeExtension${realDepth}, got SubNodeExtension${constructorDepth}`,
            );
        }
    }

    /**
     * Returns the package that defines an extension
     */
    static getExtensionPackage(extension: any): Package {
        return extension[decoratorsSymbol].package as Package;
    }

    /** Merge all the extension decorators.*/
    mergeExtensions(pack: Package): void {
        // Extensions were added to this.decorators from the root of the dependency tree to the leafs.
        // They are thus in the right order in the list this.extensions.
        this.extensions
            ?.filter(extension => extension[decoratorsSymbol].package === pack)
            .forEach(extension => {
                const extensionDecorators: FactoryExtensionDecorators = extension[decoratorsSymbol];
                this.checkExtensionDepth(extension, extensionDecorators);

                const properties = extensionDecorators.properties || [];
                // Add to the factory the new properties added by the extension.

                this.createProperties(
                    properties.filter(property => !property.isOverride),
                    pack,
                ).forEach(property => {
                    const alreadyDefinedProperty = this.propertiesByName[property.name];
                    if (alreadyDefinedProperty) {
                        const message = `The property '${property.name}' has already been registered by the factory ${alreadyDefinedProperty.factory.fullName}`;
                        throw this.systemError(message);
                    }
                    // if the extension depth is not equal to the current node, the extension is for a subNode
                    property.isInherited = this.getExtensionDepth(extensionDecorators) !== this.subclassDepth();

                    this.addProperty(property);
                });

                // Extend existing properties. No new properties are added.
                const propertyExtensions = properties.filter(property => property.isOverride);
                propertyExtensions.forEach(propExt => {
                    const extendedProp = this.findProperty(propExt.name);
                    extendedProp.addExtension(propExt as TypedPropertyDecorator);
                });

                this.mergeExtensionMethods(extension);
                this.mergeExtensionHeader(extensionDecorators.nodeExtension, pack);
                this.mergeOperations(extensionDecorators);
                this.mergeListeners(extensionDecorators);
            });
    }

    /** Merge static and non static methods from an extension. */
    mergeExtensionMethods(extension: typeof Decorated): void {
        const nonStaticFilter = (k: string): boolean => k !== 'constructor';
        this.mergeMethods(extension, { throws: true, nonStaticFilter });
    }

    /** Merge static and non static methods from superNode into subNode. */
    mergeSubNodeMethods(extension: typeof Decorated): void {
        const nonStaticFilter = (k: string): boolean => {
            // we need a try/catch here because $ is now a getter which throws if this.#$ does not exist.
            try {
                if (!(extension.prototype as Node).$) return false;
                return k !== 'constructor';
            } catch {
                return false;
            }
        };
        this.mergeMethods(extension, { throws: false, nonStaticFilter });
    }

    /** Merge static and non static methods. */
    private mergeMethods(
        extension: typeof Decorated,
        options: { throws: boolean; nonStaticFilter: (k: string) => boolean },
    ): void {
        const reservedStaticProperties = Object.getOwnPropertyNames(class Dummy {});

        // Non static methods
        Object.getOwnPropertyNames(extension.prototype)
            .filter(options.nonStaticFilter)
            .forEach(name => {
                if (options.throws && this.nonStaticMethods[name])
                    throw this.systemError(`${extension.name}.${name}: invalid non-static method override`);

                // Get the method from the prototype and store it in the factory's nonStaticMethods map
                const method = (extension.prototype as any)[name];
                this.nonStaticMethods[name] = method;

                // The decorator's method calls the factory method.
                (this.nodeConstructor.prototype as any)[name] = function f(this: Node, ...args: any[]): any {
                    return method.apply(this, args);
                };
            });

        // Static methods
        Object.getOwnPropertyNames(extension)
            .filter(name => !reservedStaticProperties.includes(name))
            .forEach(name => {
                if (options.throws && this.staticMethods[name])
                    throw this.systemError(`${extension.name}.${name}: invalid static method override`);

                // Get the method from the constructor and store it in the factory's staticMethods map
                const method = (extension as any)[name];
                this.staticMethods[name] = method;

                // The decorator's method calls the factory method.
                (this.nodeConstructor as any)[name] = function f(
                    this: typeof Node,
                    context: Context,
                    ...args: any[]
                ): any {
                    return method.apply(this, [context, ...args]);
                };
            });
    }

    private mergeOperationKind(
        extension: FactoryExtensionDecorators | FactoryDecorators,
        operationKind: FactoryDecoratorsDictArrayKeys,
    ): void {
        const extensionOperations = (extension as any)[operationKind] as any[];
        if (!extensionOperations) return;
        const nodeOperations = (this as any)[operationKind] as any[];
        extensionOperations.forEach(extensionOperation => {
            // the definingPackage of extension queries and mutations is the deepest (most specific)
            // between e.definingPackage and this.package
            extensionOperation.definingPackage = extensionOperation.definingPackage?.allDependencies.includes(
                this.package,
            )
                ? extensionOperation.definingPackage
                : this.package;

            // Async mutations are registered twice: with 'start' and 'stop' action. Ignore the 'stop' and 'requestUserNotification' when checking redefinition.
            if (
                nodeOperations.some(
                    nodeOperation =>
                        nodeOperation.name === extensionOperation.name &&
                        nodeOperation.action !== 'stop' &&
                        extensionOperation.action !== 'stop' &&
                        nodeOperation.action !== 'requestUserNotification' &&
                        extensionOperation.action !== 'requestUserNotification',
                )
            ) {
                throw new Error(`${extensionOperation.name} is already defined on a parent node of ${this.name}.`);
            }

            const extensionName = (extension as FactoryExtensionDecorators).nodeExtension?.name;
            if (extensionName && extensionOperation.decorator?.topic.startsWith(`${extensionName}/`)) {
                // The topic name was created with the extension class name rather than the node class name - fix it.
                extensionOperation.decorator.topic = `${this.name}/${extensionOperation.decorator.topic.substring(
                    extensionName.length + 1,
                )}`;
            }

            nodeOperations.push(extensionOperation);
        });
    }

    private mergeOperations(extension: FactoryExtensionDecorators | FactoryDecorators): void {
        this.mergeOperationKind(extension, 'mutations');
        this.mergeOperationKind(extension, 'queries');
    }

    private mergeListeners(extension: FactoryExtensionDecorators | FactoryDecorators): void {
        this.mergeOperationKind(extension, 'messageListeners');
        this.mergeOperationKind(extension, 'notificationListeners');
    }

    /** Merge the superNode's header characteristics into the subNode. */
    private mergeSubNodeHeader(superDecorator: FactoryNodeDecorator): void {
        const superKeys = Object.keys(superDecorator);
        const superName = superDecorator.name;

        superKeys
            .filter(key => (superDecorator as any)[key] !== undefined)
            .forEach(key => {
                switch (key) {
                    case 'storage':
                        if (!this.storage) this.storage = superDecorator[key];
                        if (this.storage !== superDecorator[key])
                            throw this.systemError(
                                `${superName}.storage can't be redefined from '${superDecorator[key]}' to '${this.storage}'`,
                            );
                        break;
                    case 'indexes':
                    case 'tableName':
                    case 'name':
                    case 'isAbstract':
                    case 'isSetupNode':
                    case 'isPublished':
                    case 'canDeleteMany':
                    case 'extends':
                    case 'isFrozen':
                    case 'canRead':
                    case 'canCreate':
                    case 'canUpdate':
                    case 'canDelete':
                    case 'canDuplicate':
                    case 'canBulkUpdate':
                    case 'canBulkDelete':
                    case 'canSearch':
                    case 'notifies':
                    case 'hasAttachments':
                    case 'hasTags':
                    case 'hasNotes':
                    case 'isVitalCollectionChild':
                    case 'isVitalReferenceChild':
                    case 'isAssociationReferenceChild':
                    case 'isAssociationCollectionChild':
                    case 'isClearedByReset':
                    case 'isCustomizable':
                    case 'isSynchronizable':
                    case 'isSynchronized':
                        break;
                    case 'prepare':
                    case 'prepareBegin':
                    case 'prepareEnd':
                    case 'createEnd':
                    case 'controlBegin':
                    case 'controlEnd':
                    case 'controlDelete':
                    case 'saveBegin':
                    case 'saveEnd':
                    case 'deleteBegin':
                    case 'deleteEnd':
                        try {
                            this.events[key] = chainEvents(superDecorator[key], this.events[key], key);
                        } catch (e) {
                            throw this.systemError(e.message);
                        }
                        break;
                    case 'getFilters':
                        try {
                            const extEvent = superDecorator.getFilters;
                            const baseEvent = this.events.getFilters;
                            this.events.getFilters = async function chainArrayEventResult(
                                this: Node,
                                ...args: any[]
                            ): Promise<NodeQueryFilter<Node>[]> {
                                const baseResult = await baseEvent?.apply(this, args);
                                const extResult = await extEvent?.apply(this, args);

                                return chainArrays(baseResult, extResult) || [];
                            };
                        } catch (e) {
                            throw this.systemError(e.message);
                        }
                        break;

                    default:
                        throw this.systemError(`${superDecorator.name}.${key} cannot be redefined.`);
                }
            });
    }

    private mergeExtensionHeader(extensionDecorator: NodeExtensionDecorator<Node>, pack: Package): void {
        const keys = Object.keys(extensionDecorator);
        keys.forEach(key => {
            switch (key) {
                case 'name':
                case 'extends':
                    break;
                case 'hasAttachments':
                    this.nodeDecorator.hasAttachments = extensionDecorator[key];
                    this.systemPropertyDefiningPackage._attachments = pack;
                    break;
                case 'hasTags':
                    this.nodeDecorator.hasTags = extensionDecorator[key];
                    this.systemPropertyDefiningPackage._tags = pack;
                    break;
                case 'hasNotes':
                    this.nodeDecorator.hasNotes = extensionDecorator[key];
                    this.systemPropertyDefiningPackage._notes = pack;
                    break;
                case 'isSynchronizable':
                    this.nodeDecorator.isSynchronizable = extensionDecorator[key];
                    break;
                case 'isSynchronized':
                    this.nodeDecorator.isSynchronized = extensionDecorator[key];
                    break;
                case 'prepare':
                case 'prepareBegin':
                case 'prepareEnd':
                case 'createEnd':
                case 'controlBegin':
                case 'controlEnd':
                case 'controlDelete':
                case 'saveBegin':
                case 'saveEnd':
                case 'deleteBegin':
                case 'deleteEnd':
                    try {
                        this.events[key] = chainEvents(this.events[key], extensionDecorator[key], key);
                    } catch (e) {
                        throw this.systemError(e.message);
                    }
                    break;
                case 'indexes':
                    this.indexes = chainArrays(this.indexes, extensionDecorator[key]);
                    break;
                case 'externalStorageManagerExtension':
                    if (this.storage !== 'external')
                        throw this.systemError('Base node not managed by an external storage.');
                    _.merge(this.externalStorageManager, extensionDecorator.externalStorageManagerExtension);
                    break;
                case 'isVitalReferenceChild':
                    this.nodeDecorator.isVitalReferenceChild = extensionDecorator.isVitalReferenceChild;
                    break;
                case 'isAssociationCollectionChild':
                    this.nodeDecorator.isAssociationCollectionChild = extensionDecorator.isAssociationCollectionChild;
                    break;
                case 'isAssociationReferenceChild':
                    this.nodeDecorator.isAssociationReferenceChild = extensionDecorator.isAssociationReferenceChild;
                    break;
                default:
                    throw this.systemError(`${key} cannot be redefined.`);
            }
        });
    }

    private subclassDepth(): number {
        const superFactory = this.getSuperFactory();
        return superFactory ? superFactory.subclassDepth() + 1 : 0;
    }

    checkSubclassDepth(): void {
        const depth = this.subclassDepth();
        if (depth > maxNodeSubclassingDepth) {
            throw this.systemError(
                `${this.name}: subclass hierarchy is too deep: currently limited to ${maxNodeSubclassingDepth} levels, got ${depth} levels`,
            );
        }
    }

    /** - add to the current subNode the queries, mutations, attributes and methods
     * brought by the superclasses. */
    mergeSubNodes(): void {
        if (!this.decorators.superDecorators) throw this.systemError(`${this.name} is not a subNode.`);

        this.checkSubclassDepth();

        const superDecorators: FactoryDecorators[] = [];

        let decorators = this.decorators;
        while (decorators.superDecorators) {
            superDecorators.push(decorators.superDecorators);
            decorators = decorators.superDecorators;
        }

        superDecorators.forEach(d => {
            this.mergeSubNodeMethods(this.package.application.getFactoryByName(d.node.name).nodeConstructor);
            this.mergeSubNodeHeader(d.node);
            this.mergeOperations(d);
            // Do not merge listeners when subclassing as only the base class should be dequeuing.
            // There is no dynamic dispatch on static methods and we don't want notifications to be broadcast
            // to static methods in all subclasses. We want them to be executed only once, by the base class.
        });
    }

    /** @internal Get super factory of current subNode.*/
    getSuperFactory(): NodeFactory | undefined {
        if (!this.decorators.superDecorators) return undefined;
        return this.package.application.getFactoryByName(this.decorators.superDecorators.node.name);
    }

    // Create in the subNode the properties of the superNodes.
    createSuperNodeProperties(definingPackage: Package): void {
        if (!this.decorators.superDecorators) throw this.systemError(`${this.name} is not a subNode.`);

        const superNodeFactories: NodeFactory[] = [];

        let superFactory: any = this.getSuperFactory();
        while (superFactory) {
            superNodeFactories.push(superFactory);
            superFactory = superFactory.getSuperFactory();
        }

        // Create properties from the most distant superNode to the closest one.
        superNodeFactories.reverse().forEach(superNodeFactory => {
            const superProperties = superNodeFactory.decorators.properties.filter(p => {
                const subProperty = this.propertiesByName[p.name];
                return p.name && !subProperty;
            });

            // Create in the subNode new properties inherited from the superNode
            this.createProperties(superProperties, definingPackage).forEach(property => {
                property.isInherited = true;
                this.addProperty(property);
            });
        });
    }

    // Create in the subNode the new properties defined in the subNode itself and extend the superNodes' properties.
    createSubNodeProperties(definingPackage: Package): void {
        if (!this.decorators.superDecorators) throw this.systemError(`${this.name} is not a subNode.`);

        // Add to the subNode the new properties added by the subNode itself
        const subNodeProperties = this.decorators.properties.filter(property => !property.isOverride) || [];
        this.createProperties(subNodeProperties, definingPackage).forEach(property => {
            if (this.propertiesByName[property.name])
                throw this.propertiesByName[property.name].logicError(
                    `invalid decorator: change it to ...PropertyOverride (in ${definingPackage.name})`,
                );
            this.addProperty(property);
        });
    }

    // Create the overrides of the properties defined in the superNodes.
    createSubNodeOverrides(): void {
        // Extend properties defined in the superNodes and redefined in the subNode, or in another superNode.
        let superNodeFactory: NodeFactory | undefined = this.getSuperFactory();
        const allSuperNodes: NodeFactory[] = [];
        while (superNodeFactory) {
            allSuperNodes.push(superNodeFactory);
            superNodeFactory = superNodeFactory.getSuperFactory();
        }

        // Reverse list from the most distant superNode to the closest
        allSuperNodes.reverse();
        const extendingNodes = [...allSuperNodes, this];

        // Add property extensions from the most distant superNode to the closest.
        extendingNodes.forEach(superNode => {
            superNode.decorators.properties
                .filter(property => property.isOverride)
                .forEach(property => {
                    this.findProperty(property.name).addExtension(property as TypedPropertyDecorator);
                });
        });
    }

    private verifyIsContentsAddressable(): void {
        // Check that the decorator is not repeated in an inheritance tree.
        if (this.nodeDecorator.isContentAddressable && this.baseFactory) {
            throw this.systemError('isContentsAddressable can only be set on root node');
        }

        if (this.isContentAddressable && this.storage !== 'sql') {
            throw this.systemError('isContentsAddressable can only be set node with storage class');
        }
        if (this.isVitalChild) {
            if (this.isContentAddressable)
                throw this.systemError('isContentsAddressable is not supported on vital graphs');
            if (this.vitalParentFactory.isContentAddressable)
                throw this.vitalParentFactory.systemError('isContentsAddressable is not supported on vital graphs');
        }
    }

    private verifyIsSynchronizable(): void {
        if (!this.nodeDecorator.isSynchronizable) return;

        if (this.storage !== 'sql') {
            throw this.systemError('isSynchronizable can only be set on nodes with sql storage');
        }
        if (this.isSharedByAllTenants) {
            throw this.systemError('isSynchronizable cannot be set on nodes shared by all tenants');
        }
        if (this.naturalKey === undefined) {
            throw this.systemError('isSynchronizable can only be set on nodes with a natural key');
        }
        // Check that the decorator is not repeated in an inheritance tree.
        if (this.baseFactory?.isSynchronizable) {
            throw this.systemError('isSynchronizable cannot be redefined on a sub node');
        }
    }

    private verifyIsSynchronized(): void {
        if (!this.nodeDecorator.isSynchronized) return;

        // Not allowed on abstract nodes because subnodes must have transforms for different source nodes.
        if (this.isAbstract) {
            throw this.systemError('isSynchronized cannot be set on an abstract node');
        }
        if (this.storage !== 'sql') {
            throw this.systemError('isSynchronized can only be set on nodes with sql storage');
        }
        if (this.naturalKey === undefined) {
            throw this.systemError('isSynchronized can only be set on nodes with a natural key');
        }
    }

    private verifyDefaultsToSingleMatch(): void {
        if (!this.nodeDecorator.defaultsToSingleMatch) return;

        if (!this.isCached) {
            throw this.systemError('defaultsToSingleMatch can only be set on cached nodes');
        }
    }

    verify(): void {
        if (this.application.skipVerifications) return;

        // Check that the vital child constraints are respected.
        if (this.isVitalChild) this._verifyVitalChildFactory();

        if (this.nodeDecorator.isClearedByReset) this._verifyClearedByResetFactory();

        // Perform checks related to the storage attribute.
        if (this.storage === 'external') {
            if (this.keyPropertyNames.length === 0)
                throw this.systemError('keyPropertyNames attribute cannot be empty');
            if (!this.externalStorageManager) throw this.missingClassDecorator('externalStorageManager');
            this.checkExternalDecorator();
        } else if (this.externalStorageManager) {
            throw this.systemError('externalStorageManager attribute supplied for non-external node.');
        } else {
            this.checkDecorators();
        }

        if (this.baseFactory?.hasAttachments && !this.hasAttachments)
            throw this.systemError(
                `Sub node cannot override hasAttachments where base node is set to true (${this.baseFactory.name})`,
            );

        if (this.baseFactory?.hasTags && !this.hasTags)
            throw this.systemError(
                `Sub-factory ${this.baseFactory.name} cannot have 'hasTags=true' while its base factory ${this.baseFactory.name} has 'hasTags=false'`,
            );

        if (this.baseFactory?.hasNotes && !this.hasNotes)
            throw this.systemError(
                `Sub-factory ${this.baseFactory.name} cannot have 'hasNotes=true' while its base factory ${this.baseFactory.name} has 'hasNotes=false'`,
            );

        if (
            Object.values(this.mutations).some(
                mutation => mutation.operationKind === 'bulkMutation' && !!mutation.authorizedBy,
            )
        )
            throw this.systemError('NYI: authorizedBy not supported on bulk mutations');

        this.verifyIsSynchronizable();
        this.verifyIsSynchronized();

        // Perform checks related to vital properties
        const vitalProperties: ForeignNodeProperty[] = [];
        this.vitalProperties.forEach(property => {
            const factory = property.targetFactory.name;
            const found = vitalProperties.find(p => factory === p.targetFactory.name);
            if (found) {
                throw this.systemError(
                    `Found two vital properties for the same vital child node ${factory}: '${property.name}' and '${found.name}'`,
                );
            } else {
                vitalProperties.push(property);
            }
        });

        // Validate that isOwnedByCustomer is only allowed if the node has a vendor property
        const propertiesWithIsOwnedByCustomer = this.properties
            .filter(prop => prop.isOwnedByCustomer)
            .map(prop => prop.name);
        if (propertiesWithIsOwnedByCustomer.length > 0) {
            if (!this.hasVendorProperty)
                throw this.systemError(
                    `The isOwnedByCustomer attribute is only allowed if the node has a vendor property. Check properties: '${propertiesWithIsOwnedByCustomer}'`,
                );
        }

        // Verify all of the factory's properties
        this.properties.forEach(property => property.verify());

        // Natural key checks
        if (this.indexes) {
            const naturalKeys = this.indexes?.filter(index => index.isNaturalKey);

            if (naturalKeys) {
                // Verify at most one natural key is specified in the factory's indexes
                if (naturalKeys.length > 1) {
                    throw this.systemError(`${this.name} cannot have more than 1 index with isNaturalKey set to true.`);
                }
            }
        }

        if (this.isSetupNode && !this.naturalKey)
            throw this.systemError(`${this.name} setup nodes must have a natural key.`);

        this.verifyIsContentsAddressable();

        // Verify all of the factory's indexes
        this.getSelfIndexes().forEach(index => {
            this.verifyIndex(index);
        });

        this.ensureNoMultipleUniqueIndexes();
        this.verifyDefaultsToSingleMatch();

        if (this.externalStorageManager?.verifyFactory) this.externalStorageManager?.verifyFactory();
    }

    /**
     * Verify a factory with isVitalChild decorator
     */
    private _verifyVitalChildFactory(): void {
        // We compare against true here because we allow isClearedByReset to be a filter function
        // in vital child nodes.
        if (this.nodeDecorator.isClearedByReset === true)
            throw this.systemError('isClearedByReset can only be tagged in parent node');

        if (this.vitalParentFactory.isSetupNode && (this.canCreate || this.canUpdate)) {
            throw this.systemError('vital children of setup nodes cannot have canCreate or canUpdate set to true');
        }

        // Validation for provides 'site' property for vital children
        if (this.isVitalChild && this.properties.some(property => property.provides?.includes('site'))) {
            // If the parent has a provides site property, the child cannot provide a site property
            if (this.vitalParentFactory.properties.some(property => property.provides?.includes('site')))
                throw this.systemError(
                    `Vital child cannot have provides properties if already defined on the vital parent  (${this.properties
                        .filter(p => p.provides?.includes('site'))
                        .map(p => p.name)
                        .join(',')})`,
                );

            // If the parent has a reference property that provides site, the child cannot provide a site property
            if (
                this.vitalParentFactory.properties.some(
                    property => property.isReferenceProperty() && property.node.provides?.includes('site'),
                )
            )
                throw this.systemError(
                    `Vital child cannot provide site property, site property is present on the vital parent (${this.vitalParentFactory.properties
                        .filter(p => p.isReferenceProperty() && p.node.provides?.includes('site'))
                        .map(p => p.name)
                        .join(',')})`,
                );
        }
    }

    /**
     * Verify a factory with isClearByReset decorator
     */
    private _verifyClearedByResetFactory(): void {
        if (this.isSetupNode && typeof this.nodeDecorator.isClearedByReset !== 'function')
            // Some children of setup nodes have isClearedByReset set, eg. UnitConversionFactor
            throw this.systemError('isClearedByReset cannot be set on setup node');

        if (this.isAbstract) {
            this._subFactories.forEach(factory => {
                if (factory.nodeDecorator.isClearedByReset) {
                    throw this.systemError(
                        'A subNode can only be tagged isClearedByReset if its base node is not tagged',
                    );
                }
            });
        }
        if (this.isSharedByAllTenants)
            throw this.systemError('isClearedByReset tag cannot be set on a node which is shared by all tenants');
    }

    /**
     * return the indexes defined (not inherited) by this factory.
     */
    getSelfIndexes(): NodeIndex[] {
        const baseIndexes = this.baseFactory?.indexes;
        if (!this.indexes || !baseIndexes) return this.indexes || [];
        return this.indexes.filter(index => baseIndexes.every(baseIndex => !sameIndexProperties(index, baseIndex)));
    }

    verifyJoins(): void {
        // Verify all of the factory's properties
        this.properties.forEach(property => {
            if (property.isForeignNodeProperty()) property.verifyJoin();
        });
    }

    /**
     * Verifies that the table is compliant with the factory definition
     */
    async verifyTable(
        context: Context,
        options: {
            doNotRecurseOnReferences?: boolean;
            factoryLinkedList?: FactoryLinkedList;
            foreignFactories?: NodeFactory[];
            compareToSqlSchema?: boolean; // set this to match against sql schema (slower)
        } = {},
    ): Promise<void> {
        let opts = options;
        // Recursively searches through factory linked list and checks if a
        // factory linked to supplied table is already in the list.
        // Will return the factory linked list item when found as well as the
        // path walked(for error message).
        const findTableInList = (
            link: FactoryLinkedList | undefined,
            tableName: string,
            path: string,
        ): { link: FactoryLinkedList | undefined; path: string } => {
            const fullPath = `${path}${link ? `=>${link.targetFactory.name}` : ''}`;
            if (!link || link.targetFactory.table.name === tableName) {
                return { link, path: fullPath };
            }
            return findTableInList(link.previousLink, tableName, fullPath);
        };

        const createUpgradeError = (msg: string): SystemError => {
            return new SystemError(`${msg}. Please upgrade the sql schema with 'xtrem schema --upgrade'`);
        };

        // Walk the factory linked list and verify if there is a nullable
        // property from the the top of the list till either the table we are
        // looking for or the start of the list is reached.
        const findIsNullableLink = (link: FactoryLinkedList | undefined, tableName: string): boolean => {
            if (!link) throw this.logicError('Unexpected end of factory list');

            if (link.targetFactory.table.name === tableName) {
                return !!link.property.isNullable;
            }

            if (link.property.isNullable) {
                return true;
            }
            return findIsNullableLink(link.previousLink, tableName);
        };

        const checkCyclicFactory = async (
            newList: FactoryLinkedList,
            referenceFactory: NodeFactory,
            path: string,
        ): Promise<void> => {
            // Here we verify that from the current reference node to the reference factory found
            // there must we at least one property in the chain of reference properties  that is a nullable property.
            // Consider a node chain sth like A->B->C->A
            // All linked by reference property dependencies. This is a cyclic dependency as you can see we hit node A twice.
            // For this to be a valid cyclic reference dependency at least one of the reference properties in the chain has to be
            // nullable.
            if (!findIsNullableLink(newList, referenceFactory.table.name)) {
                throw this.systemError(
                    `A cyclic reference exists (${path}). A nullable property is required in the dependency tree for a cyclic reference.`,
                );
            }

            // We have to check if the table exists again, just in case is was created in a cyclic dependency
            if (opts?.compareToSqlSchema && !(await referenceFactory.table.tableExists(context))) {
                throw this.systemError(
                    `The table '${referenceFactory.table.name}', referenced by '${path}' does not exist`,
                );
            }
        };

        if (this.storage !== 'sql') return;
        if (!opts) {
            // We are in the initial factory, initialize options
            opts = { factoryLinkedList: undefined, foreignFactories: [] };
        }

        if (this.baseFactory) {
            await this.baseFactory.verifyTable(context, opts);
        }

        if (opts.compareToSqlSchema && !(await this.table.tableExists(context))) {
            throw createUpgradeError(`The table '${this.table.name}' does not exist.`);
        }
        const tableSchema = opts.compareToSqlSchema
            ? await new ReadTableSqlContext(context.application).readSchema(this.table.name, {
                  skipIndexes: true,
                  skipSecurity: true,
                  skipSequences: true,
              })
            : undefined;
        const tableColumns = tableSchema?.columns?.map(column => column.name);
        await asyncArray(
            this.properties.filter(
                property =>
                    !property.isInherited &&
                    property.isStored &&
                    property.columnName &&
                    !['_createUser', '_updateUser'].includes(property.name),
            ),
        ).forEach(async property => {
            if (tableColumns && !tableColumns.includes(property.requiredColumnName)) {
                throw createUpgradeError(`No column exists for the property ${this.name}.${property.name}.`);
            }
            if (!property.isReferenceProperty()) return;
            if (property.targetFactory.storage === 'json') return;
            this.checkIncompatibleAttributes(property, 'isVital', 'isStored');
            // Append to the factory linked list
            const newList = {
                previousLink: opts?.factoryLinkedList,
                targetFactory: this,
                property,
            } as FactoryLinkedList;

            // Get reference property factory
            const referenceFactory = property.targetFactory;

            const factoryListItem = findTableInList(newList, referenceFactory.table.name, `${referenceFactory.name}`);
            if (!opts.doNotRecurseOnReferences) {
                if (!factoryListItem.link) {
                    await referenceFactory.verifyTable(context, { ...opts, factoryLinkedList: newList });
                } else {
                    // cycle detection ignores non nullable self references
                    if (!property.isSelfReference)
                        await checkCyclicFactory(newList, referenceFactory, factoryListItem.path);

                    if (opts && !opts.foreignFactories?.includes(referenceFactory))
                        opts.foreignFactories?.push(referenceFactory);
                }
            }

            if (
                !this.table.foreignKeys?.find(foreignKey =>
                    foreignKey.columnNames.find(columnName => columnName === property.columnName),
                ) &&
                !factoryListItem
            ) {
                const foreignKey = this.createForeignKey(referenceFactory, property);
                if (foreignKey) {
                    if (tableSchema && !tableSchema.foreignKeys?.find(fk => fk.name === foreignKey.name)) {
                        throw createUpgradeError(`The foreign key '${this.name}.${foreignKey.name}' is missing.`);
                    }
                }
            }
        });
    }

    computeDependencyIndex(): void {
        this.properties = topoSort(this.properties);
        this.properties.forEach((property, i) => {
            property.dependencyIndex = i + 1;
        });
    }

    setSubFactories(): void {
        // Keep track of the base node for subclass factories.
        const baseClassDecorators = this.decorators.superDecorators;
        if (baseClassDecorators) {
            this._baseFactory = this.package.application.getFactoryByName(baseClassDecorators.node.name);
            if (!this._baseFactory?._subFactories.includes(this)) this._baseFactory?._subFactories.push(this);
        }
    }

    private fillForeignNodeProperties(): void {
        this.#foreignNodeProperties.push(
            ...(this.properties.filter(property => property.isForeignNodeProperty) as ForeignNodeProperty[]),
        );
        Object.freeze(this.#foreignNodeProperties);
    }

    private fillReferenceProperties(): void {
        this.#referenceProperties.push(
            ...(this.foreignNodeProperties.filter(property => property.isReferenceProperty) as ReferenceProperty[]),
        );
        Object.freeze(this.#referenceProperties);
    }

    private fillVitalProperties(): void {
        this.#vitalProperties.push(...this.foreignNodeProperties.filter(property => property.isVital));
        Object.freeze(this.#vitalProperties);
    }

    private fillDelegatedProperties(): void {
        this.#delegatedProperties.push(...this.properties.filter(property => property.delegatesTo));
        Object.freeze(this.#delegatedProperties);
    }

    private fillMutableProperties(): void {
        this.mutableProperties = this.properties.filter(
            p => p.isForeignNodeProperty() && p.isMutable,
        ) as ForeignNodeProperty[];
        this.mutableProperties
            .filter(property => property.isCollectionProperty())
            .forEach(property => {
                if (property.isCollectionProperty()) {
                    // add the _action property to target node of the mutable collection
                    property.targetFactory.addUpdateActionProperty();
                }
            });
        Object.freeze(this.mutableProperties);
    }

    get strictMutableProperties(): ForeignNodeProperty[] {
        return this.mutableProperties.filter(property => !property.isVital);
    }

    addUpdateActionProperty(): void {
        const updateActionProperty = SystemProperties.updateActionProperty(this);
        if (this.propertiesByName[updateActionProperty.name]) return;
        this.addProperty(updateActionProperty, 'unshift');
    }

    // Complementary steps after all applications' factories have been created.
    complement(packageValidationContext?: PackageValidationContext): void {
        // Complement the properties with system properties.
        if (!this.keyProperties.length) this.addSystemProperties();

        // Complement the properties with the dependencyIndex attribute.
        this.computeDependencyIndex();

        // Post creation operations on indexes.
        this.complementFactoryIndexes();

        // Store key properties.
        if (this.storage === 'external') {
            this.keyProperties = [];
            this.keyPropertyNames.forEach(keyPropName => {
                const keyProp = this.propertiesByName[keyPropName];
                this.keyProperties.push(keyProp);
            });
        }

        // Set the table attribute.
        if (this.storage === 'sql') this.table = this.makeTable();

        // Set tagged properties
        this.properties.forEach(property => {
            if (property.provides) property.provides.forEach(tag => this.setTaggedProperty(tag, property));
        });

        // Recursively set childFactories' table attribute.
        setChildFactoriesTable(this);

        // Compute the properties' propagation paths.
        // Propagation paths are the propagatesTo attributes, based on dependsOn attributes.
        this.computePropagationPaths(packageValidationContext);

        // Complete the referringProperties attribute in factories referenced by this factory's properties.
        this.properties
            .filter(p => foreignNodePropertyTypes.includes(p.type))
            .filter(
                (p: ForeignNodeProperty) =>
                    !p.targetFactory.referringProperties.some(
                        referringProperty =>
                            this.name === referringProperty.targetFactory.name &&
                            p.name === referringProperty.property.name,
                    ),
            )
            .forEach((p: ForeignNodeProperty) => {
                p.targetFactory.referringProperties.push({ targetFactory: this, property: p });
            });

        this.lazyLoadedProperties = this.properties.filter(p => p.isStored && p.shouldLazyLoad);
        this.storedEncryptedProperties = this.properties.filter(
            p => p.isStringProperty() && p.isStoredEncrypted,
        ) as StringProperty[];

        this.fillForeignNodeProperties();
        this.fillReferenceProperties();
        this.fillMutableProperties();
        this.fillVitalProperties();
        this.fillDelegatedProperties();
    }

    /* @internal - recursively computes and returns the list of vital references, with their vital references, etc.*/
    get extendedVitalFactories(): NodeFactory[] {
        if (!this._extendedVitalFactories) {
            const extendedVitalFactories = new Set<NodeFactory>();
            this.vitalProperties.forEach(prop => {
                const factory = prop.targetFactory;
                extendedVitalFactories.add(factory);
                factory.extendedVitalFactories.forEach(fac => extendedVitalFactories.add(fac));
            });
            this._extendedVitalFactories = [...extendedVitalFactories];
        }
        return this._extendedVitalFactories;
    }

    // Post creation operations on indexes.
    private complementFactoryIndexes(): void {
        // Add indexes for vital children.
        const isStored = this.storage === 'sql';
        this.indexes = this.indexes || [];
        // Don't create index where the vital parent property was not defined on this factory but inherited from
        // the baseFactory, it will be created on the base table (BTW, this would fail as the column is not
        // part of the table but part of its base table)
        if (
            isStored &&
            this.isVitalChild &&
            !this.isAssociationChild &&
            !(this.baseFactory && this.baseFactory.propertiesByName[this.vitalParentProperty.name])
        ) {
            const orderBy = (
                this.isVitalCollectionChild
                    ? { [this.vitalParentProperty.name]: 1, _sortValue: 1 }
                    : // this is a vital reference child
                      { [this.vitalParentProperty.name]: 1 }
            ) as OrderBy<Node>;

            const newOrderByKeys = Object.keys(orderBy).join();
            const key = this.indexes.findIndex(i => {
                const existingOrderByKeys = Object.keys(i.orderBy).join();
                // The existing index may start with _constructor, so we need to check for both cases.
                return (
                    existingOrderByKeys === newOrderByKeys || existingOrderByKeys === `_constructor,${newOrderByKeys}`
                );
            });
            const hasNaturalKey = !!this.indexes?.find(idx => idx.isNaturalKey);
            if (key < 0) {
                const index =
                    this.vitalParentFactory.naturalKey && !hasNaturalKey
                        ? ({ orderBy, isUnique: true, isNaturalKey: true } as NodeIndex)
                        : // Note: if the factory already has a natural key, then the index is created as non-unique
                          ({ orderBy, isUnique: this.isVitalCollectionChild ? !hasNaturalKey : true } as NodeIndex);
                this.indexes.push(index);
            } else if (this.vitalParentFactory.naturalKey && !hasNaturalKey) {
                this.indexes[key].isNaturalKey = true;
            }
        }

        if (isStored && this.isAssociationChild) {
            // Create unique indexes for each of the parent properties (including the vital parent if present)
            // Each index will start with a different parent and contain the rest of the parents in order.
            // If no natural key is defined, the first of these indexes will be flagged as the natural key
            const existingNaturalKeyIndex = this.indexes.find(idx => idx.isNaturalKey);

            for (let i = 0; i < this.associationParentProperties.length; i++) {
                const orderBy: AnyRecord = {};

                // Start with a different property each time and add the additional properties wrapping around the list
                for (let j = 0; j < this.associationParentProperties.length; j++) {
                    orderBy[this.associationParentProperties[(j + i) % this.associationParentProperties.length].name] =
                        1;
                }

                // If no exact match for the orderBy, create a new index
                if (!this.indexes.find(idx => arraysAreEqual(Object.keys(idx.orderBy), Object.keys(orderBy)))) {
                    const index = {
                        orderBy,
                        isUnique: true,
                        isNaturalKey: i === 0 && !existingNaturalKeyIndex,
                    } as NodeIndex;

                    this.indexes.push(index);
                }
            }
        }

        if (this.isContentAddressable) {
            this.indexes.push({ orderBy: { _valuesHash: 1 } as FlatOrderBy<AnyNode>, isUnique: true });
        }

        if (this.nodeDecorator.isSynchronizable) {
            this.indexes.push({ orderBy: { _syncTick: 1 } as FlatOrderBy<AnyNode> });
        }

        // Add a name to all the indexes defined by the applicative code.
        this.indexes.forEach((index, i) => {
            if (!index.name) index.name = makeName63(`${this.tableName}_ind${i}`);
        });
    }

    // Complement the properties with system properties.
    private addSystemProperties(): void {
        // Add relevant system properties to the factory properties.
        this.getInternalSystemProperties().forEach(property => this.addProperty(property, 'unshift'));
        this.keyProperties = [this.propertiesByName._id];

        // Add published system properties.
        const publishedSystemProperties = SystemProperties.getPublishedSystemProperties(this);
        publishedSystemProperties.forEach(property => {
            this.publishedSystemProperties[property.name.substring(1)] = property;
            this.addProperty(property);
        });

        // Add published system input properties.
        this.publishedInputSystemProperties = SystemProperties.getPublishedInputSystemProperties();
    }

    // Compute the properties' propagation paths (dependsOn and propagatesTo attributes).
    private computePropagationPaths(packageValidationContext?: PackageValidationContext): void {
        this.properties
            .filter(property => property.dependsOn)
            .forEach(property => {
                try {
                    const warnings = [] as string[];
                    this.addPropagatePaths(property.dependsOn!, [property], warnings);
                    if (packageValidationContext && warnings.length) {
                        warnings.forEach(waning =>
                            packageValidationContext.warnings.add(`${this.name}.${property.name}.dependsOn: ${waning}`),
                        );
                    }
                } catch (e) {
                    throw this.propertySystemError(property, `invalid 'dependsOn' value: ${e.message}`);
                }
            });
    }

    private createNewTableColumns(properties: Property[]): Column[] {
        return properties
            .filter(property => property.isStored)
            .filter(property => !property.isInherited)
            .map(property => {
                const type = property.columnType || property.type;
                const dataType = property.dataType;
                const hasMaxLength = dataType instanceof StringDataType && dataType.maxLength;
                if (type === 'string' && !hasMaxLength) throw this.missingPropertyDecorator(property.name, 'dataType');
                return new Column(property);
            });
    }

    get defaultOrderBy(): OrderBy<Node> {
        if (this.storage === 'external') {
            return this.externalStorageManager!.defaultOrderBy;
        }
        if (this.naturalKey) {
            return this.naturalKey.reduce((r, k) => {
                const property = this.findProperty(k);
                if (property.isReferenceProperty() && property.targetFactory.naturalKey) {
                    r[k] = property.targetFactory.defaultOrderBy;
                } else {
                    r[k] = 1;
                }
                return r;
            }, {} as AnyRecord);
        }

        return { _id: 1 };
    }

    get package(): Package {
        return this._package;
    }

    get application(): Application {
        return this.package.application;
    }

    get name(): string {
        return this.nodeDecorator.name;
    }

    get fullName(): string {
        // IG: Workaround until we find why application is undefined in some cases.
        if (this.package) return `${this.package.name}/${this.name}`;
        return `application-not-found/${this.name}`;
    }

    get baseFactory(): NodeFactory | undefined {
        return this._baseFactory;
    }

    get rootFactory(): NodeFactory {
        return this._baseFactory ? this._baseFactory.rootFactory : this;
    }

    get rootCollectionFactory(): NodeFactory | undefined {
        if (this.nodeDecorator.isVitalCollectionChild) {
            return this._baseFactory && this._baseFactory.rootCollectionFactory
                ? this._baseFactory.rootCollectionFactory
                : this;
        }
        return undefined;
    }

    get rootVendorFactory(): NodeFactory | undefined {
        if (this.hasVendorProperty) {
            return this._baseFactory && this._baseFactory.hasVendorProperty
                ? this._baseFactory.rootVendorFactory
                : this;
        }
        return undefined;
    }

    get isSharedByAllTenants(): boolean {
        return !!this.nodeDecorator.isSharedByAllTenants;
    }

    get tableName(): string | undefined {
        if (this.nodeDecorator.tableName)
            return this.storage === 'sql' ? _.snakeCase(this.nodeDecorator.tableName) : this.nodeDecorator.tableName;
        return nameToSqlName(this.name);
    }

    /**
     * Same as tableName accessor but raises an error when the tableName is not set
     */
    get requiredTableName(): string {
        const tableName = this.tableName;
        if (!tableName) throw this.logicError('missing tableName');
        return tableName;
    }

    get fullTableName(): string | undefined {
        return `${this.application.schemaName}.${this.tableName}`;
    }

    async resolveReferenceId(context: Context, value: StringOrNumberOrNull | Node): Promise<number | null> {
        if (value === null) return null;
        if (typeof value === 'number') return value;
        if (typeof value !== 'string') return value instanceof Node ? value._id : value;

        if (value[0] !== '#') return safeParseInt(value, '_id');
        const result = await context.select(this.nodeConstructor, { _id: true }, { filter: this.parseNodeId(value) });
        if (result.length === 0) return null;
        if (result.length > 1)
            throw this.systemError(
                `Invalid natural key ${value}. More than 1 row returned when resolving reference id.`,
            );
        return result[0]._id;
    }

    /**
     * Get all node factories in the vital tree of a given node factory, including itself
     */
    getVitalTree(): NodeFactory[] {
        const nodes: NodeFactory[] = [];

        const addForeignNodes = (f: NodeFactory): void => {
            if (nodes.includes(f)) return;
            nodes.push(f);
            f.vitalProperties.forEach(prop => addForeignNodes(prop.targetFactory));
        };

        addForeignNodes(this);
        return nodes;
    }

    /**
     * Returns all the table names in the factory's hierarchy tree, from the root to the leaves.
     */
    getAllTableNames(): string[] {
        if (this.storage !== 'sql') return [];
        const tableNames: string[] = [];

        const walkToRoot = (f: NodeFactory): void => {
            tableNames.unshift(this.fullTableName!);
            if (f.baseFactory) walkToRoot(f.baseFactory);
        };
        walkToRoot(this);

        const walkToLeaves = (f: NodeFactory): void => {
            f._subFactories.forEach(sub => {
                tableNames.push(sub.fullTableName!);
                walkToLeaves(sub);
            });
        };
        walkToLeaves(this);

        return tableNames;
    }

    get authorizationCode(): string | undefined {
        return this.nodeDecorator.authorizationCode;
    }

    getIsolationLevel(key: keyof IsolationLevels): IsolationLevel {
        return this.nodeDecorator.isolationLevels?.[key] || 'low';
    }

    get provides(): NodeFilterTag[] | undefined {
        return this.nodeDecorator.provides;
    }

    /**
     * Returns the list of sub-classing factories
     */
    get subFactories(): NodeFactory[] {
        return [...this._subFactories];
    }

    private checkDecorators(): void {
        if (this.canDuplicate && !this.canCreate) {
            throw this.systemError('canDuplicate requires canCreate on the node decorator');
        }
        if (this.canBulkDelete && !this.canDelete) {
            throw this.systemError('canBulkDelete requires canDelete on the node decorator');
        }
        // see later for bulkUpdate
        // if (this.canBulkUpdate && !this.canUpdate) {
        //     throw this.systemError('canBulkUpdate requires canUpdate on the node decorator');
        // }
    }

    private checkExternalDecorator(): void {
        const checkExternalManagerFunctions = (optionName: string): void => {
            if (!(this.externalStorageManager as any)[optionName]) {
                throw this.systemError(
                    `missing ${optionName} function on externalStorageManager decorator member of external node`,
                );
            }
        };

        if (this.canRead) {
            checkExternalManagerFunctions('query');
            checkExternalManagerFunctions('mapRecordIn');
            checkExternalManagerFunctions('mapAggregateRecordIn');
        }

        if (this.canDelete || this.canDeleteMany) {
            checkExternalManagerFunctions('delete');
        }
        if (this.canBulkDelete) {
            throw this.systemError('bulkDelete async mutations are not supported on external nodes');
        }

        if (this.canCreate) {
            checkExternalManagerFunctions('insert');
        }

        if (this.canUpdate) {
            checkExternalManagerFunctions('update');
        }
    }

    private checkIncompatibleAttributes(prop: Property, atb1: keyof typeof prop, atb2: keyof typeof prop): void {
        if (prop[atb1] && prop[atb2]) {
            throw this.propertySystemError(prop, `'${atb1}' and '${atb2}' attributes are incompatible`);
        }
    }

    private setTaggedProperty(tag: PropertyFilterTag, prop: Property): void {
        const knownProperty = this._taggedProperties[tag];
        if (knownProperty && knownProperty !== prop)
            throw this.propertySystemError(prop, `${knownProperty.name}: ambiguous ${tag} property`);
        if (tag === 'isActive' && !prop.isBooleanProperty())
            throw this.propertySystemError(prop, 'Properties that provide isActive can only be boolean');

        this._taggedProperties[tag] = prop;
    }

    getTaggedProperty(tag: PropertyFilterTag): Property | undefined {
        return this._taggedProperties[tag];
    }

    private findReverseProperty(
        otherFactory: NodeFactory,
        otherProperty: ForeignNodeProperty,
    ): ForeignNodeProperty | undefined {
        const structuresToCheck: ReversePropertyStruct[] = this.properties
            .filter(property => foreignNodePropertyTypes.includes(property.type))
            .filter((property: ForeignNodeProperty) => property.targetFactory === otherFactory)
            .map((property: ForeignNodeProperty) => ({ prop: property, join: property.join || {} }));

        const thisStructure = structuresToCheck
            .filter(structure => !!structure.join)
            .find(structure =>
                Object.keys(structure.join).every(key => {
                    const value = structure.join[key];
                    return typeof value === 'string' && otherProperty.join ? otherProperty.join[value] === key : true;
                }),
            );

        return thisStructure?.prop;
    }

    private warnDecorator(message: string): void {
        if (this.decoratorWarnings[message]) return;
        this.decoratorWarnings[message] = true;
        logger.warn(`${this.name}: ${message}`);
    }

    private checkPropagatesTo(fromProperty: Property, referenceProperty: ForeignNodeProperty): void {
        if (this.application.skipVerifications) return;
        if (!referenceProperty.isVitalParent && !referenceProperty.isAssociationParent) return;
        ['canCreate', 'canUpdate', 'canDelete', 'canDeleteMany'].forEach(flag => {
            if ((this.nodeDecorator as any)[flag]) {
                this.warnDecorator(
                    `${flag} should not be set because of a dependsOn attribute on ${fromProperty.name} in its vital parent`,
                );
            }
        });
    }

    private addPropagatePaths(dependsOn: any, paths: PropagatePropertyPath, warnings: string[]): void {
        if (Array.isArray(dependsOn)) {
            dependsOn.forEach(depOn => this.addPropagatePaths(depOn, paths, warnings));
        } else if (typeof dependsOn === 'string') {
            const fromProperty = this.findProperty(dependsOn);
            fromProperty.propagatesTo = fromProperty.propagatesTo || [];
            fromProperty.propagatesTo.push(paths);
        } else {
            const nonVitalReferences = new Set<string>();
            Object.keys(dependsOn).forEach(key => {
                const fromProperty = this.findProperty(key);
                if (!fromProperty.isForeignNodeProperty()) {
                    throw this.propertySystemError(fromProperty, 'property must be a reference or a collection');
                }
                const fromFactory = fromProperty.targetFactory;
                const reverseReference = fromFactory.findReverseProperty(this, fromProperty);
                if (reverseReference === undefined) {
                    if (!this.application.skipVerifications) {
                        warnings.push(
                            `${this.name}.${
                                fromProperty.name
                            } is not part of the vital graph. The rule '${JSON.stringify(dependsOn)}' will be ignored.`,
                        );
                    }
                    // Let's replace the dependency on the reference's property by a dependency on the reference:
                    nonVitalReferences.add(key);
                } else {
                    fromFactory.checkPropagatesTo(fromProperty, reverseReference);
                    fromFactory.addPropagatePaths(dependsOn[key], [reverseReference, ...paths], warnings);
                    fromProperty.propagatesTo = fromProperty.propagatesTo || [];
                    fromProperty.propagatesTo.push(paths);
                }
            });
            // Add dependencies on references:
            [...nonVitalReferences.values()].forEach(nonVitalReference =>
                this.addPropagatePaths(nonVitalReference, paths, warnings),
            );
        }
    }

    systemError(message: string): SystemError {
        return new SystemError(`${this.name}: ${message}`);
    }

    authorizationError(context: Context, params: ErrorParameters): SystemError {
        const localized = context.localize(params.key, params.message, params.data || {});
        return new AuthorizationError(`${this.name}: ${localized}`, params.innerError);
    }

    logicError(message: string, innerError?: Error): LogicError {
        return new LogicError(`${this.name}: ${message}`, innerError);
    }

    propertySystemError(property: PropertyDecorator | Property, message: string): SystemError {
        return new SystemError(`${this.name}.${property.name}: ${message}`);
    }

    checkCanMutate(context: Context, verb: string): void {
        if (
            (!context.isWritable || context.hasReadonlyScopes()) &&
            (this.storage === 'sql' || this.storage === 'external')
        ) {
            throw this.systemError(`cannot ${verb}: context is readonly`);
        }

        if (this.isSharedByAllTenants && !this.isPlatformNode && context.application.applicationType !== 'admin-tool') {
            throw this.authorizationError(context, {
                key: '@sage/xtrem-core/shared-by-all-tenants',
                message: 'cannot {{verb}}: shared by all tenants.',
                data: { verb },
            });
        }
    }

    checkCanCreate(context: Context): void {
        this.checkCanMutate(context, 'create');
    }

    checkCanDelete(context: Context): void {
        this.checkCanMutate(context, 'delete');
    }

    checkCanUpdate(context: Context): void {
        this.checkCanMutate(context, 'update');
    }

    checkCanDeleteMany(context: Context): void {
        this.checkCanDelete(context);
        if (!this.canDeleteMany) {
            throw this.systemError(
                'deleteMany operation is not allowed. Consider adding "canDeleteMany" in the decorator',
            );
        }
    }

    addPropertyAccessors(property: Property): void {
        if (!Object.getOwnPropertyDescriptor(this.nodeConstructor.prototype, property.name)) {
            try {
                if (property.name === '_id') {
                    Object.defineProperty(this.nodeConstructor.prototype, property.name, {
                        get() {
                            return StateGetValue.getPropertyValueSync((this as Node).$.state, property);
                        },
                        // This one is invalid with async/await. It goes away in node 16
                        set(value): void {
                            // this should be forbidden but some applicative code is doing it (see businessEntityCreatedOrUpdated)
                            // throw property.systemError('cannot set node._id');
                            (this as Node).$.state.values._id = value;
                        },
                        enumerable: true,
                    });
                } else if (property.isCollectionProperty()) {
                    Object.defineProperty(this.nodeConstructor.prototype, property.name, {
                        get() {
                            return StateGetValue.getPropertyValueSync((this as Node).$.state, property);
                        },
                        // No setter
                        enumerable: true,
                    });
                } else {
                    Object.defineProperty(this.nodeConstructor.prototype, property.name, {
                        get(): Promise<AnyValue> {
                            return (this as Node).$.state.getPropertyValue(property);
                        },
                        enumerable: true,
                    });
                }
            } catch (ex) {
                throw this.systemError(ex.message);
            }
        }
    }

    getAllIndexes(): NodeIndex[] {
        const allIndexes = [];
        for (let f: NodeFactory | undefined = this; f; f = f.baseFactory) {
            if (f.indexes) allIndexes.push(...f.indexes);
        }
        return allIndexes;
    }

    getKeyValues(
        context: Context,
        data: AnyRecord,
        options?: {
            allocateTransient: boolean;
            isTransient?: boolean;
            isOnlyForDefaultValues?: boolean;
            isOnlyForDuplicate?: boolean;
            writable?: boolean;
            isOnlyForLookup?: boolean;
            collection?: BaseCollection;
        },
    ): Dict<AnyValue> {
        if (this.storage === 'sql' && data._id != null) {
            return { _id: data._id };
        }
        if (this.storage === 'external') {
            return this.externalStorageManager!.getKeyValues(context, data, options);
        }
        if (options?.allocateTransient) {
            return { _id: context.allocateTransientId() };
        }
        throw this.systemError('cannot get key values: class is not persistent');
    }

    getLocalizedTitleKey(): string {
        return `${this.package.name}/nodes__${_.snakeCase(this.name)}__node_name`;
    }

    getLocalizedTitle(context: Context, options?: { useTitleCase: boolean }): string {
        try {
            return context.localize(this.getLocalizedTitleKey(), this.name);
        } catch (error) {
            logger.warn(`Could not resolve node name: ${this.name}: ${error?.message}`);
            return options?.useTitleCase ? titleCase(this.name) : this.name;
        }
    }

    getLocalizedOperationFailedMessage(context: Context, operation: string): string {
        switch (operation) {
            case 'create':
                return localizedText('@sage/xtrem-core/record-was-not-created', 'The record was not created.');
            case 'update':
                return localizedText('@sage/xtrem-core/record-was-not-updated', 'The record was not updated.');
            case 'delete':
                return localizedText('@sage/xtrem-core/record-was-not-deleted', 'The record was not deleted.');
            case 'duplicate':
                return localizedText('@sage/xtrem-core/record-was-not-duplicated', 'The record was not duplicated.');
            case 'save':
                return localizedText('@sage/xtrem-core/record-was-not-saved', 'The record was not saved.');

            default:
                try {
                    const operationKind = this.mutations.find(mutation => mutation.name === operation)?.operationKind;
                    const key = `${this.package.name}/nodes__${_.snakeCase(this.name)}__${operationKind}__${operation}__failed`;
                    return localizedText(key, `${titleCase(operation)} failed.`);
                } catch {
                    return `${titleCase(operation)} failed.`;
                }
        }
    }

    async delete(context: Context, key: AnyRecord): Promise<void> {
        const state = new NodeState(context, this, key, StateStatus.updatable, {
            forUpdate: false,
            isThunk: true,
        });
        await state.delete();
    }

    async deleteMany(
        context: Context,
        keyOrFilter: NodeQueryFilter<Node>,
        options?: NodeDeleteOptions,
    ): Promise<number> {
        if (
            this.nodeDecorator.controlDelete ||
            this.nodeDecorator.deleteBegin ||
            this.nodeDecorator.deleteEnd ||
            this.storage === 'external'
        ) {
            // If the node has a 'controlDelete', 'deleteBegin' or 'deleteEnd' event, we can't use the 'deleteMany'
            // All the nodes must be deleted one by one so that their control events can be invoked
            let states: NodeState[] = [];
            if (isCompound(keyOrFilter)) {
                // keyOrFilter is a filter object
                states = await (await this.query(context, { filter: keyOrFilter, forUpdate: true }))
                    .map(n => n.$.state)
                    .readAll();
            } else {
                // keyOrFilter is a key (string or number)
                const state = await NodeState.newFromRead(context, this, keyOrFilter, { forUpdate: true });
                if (!state) return 0;
                states = [state];
            }
            await asyncArray(states).forEach(state => state.delete(options));
            return states.length;
        }

        if (isCompound(keyOrFilter)) {
            // keyOrFilter is a filter object
            this.checkCanDelete(context);
            // Using the filter remove the states of the records being deleted and the their vital children from the interning cache
            await StateIntern.removeStateWithData(context, this, keyOrFilter);
            const count = await this.table.delete(context, keyOrFilter);
            if (count > 0) context.prefetcher.afterDelete(this);
            return count;
        }

        // keyOrFilter is a key (string or number)
        const state = await NodeState.newFromRead(context, this, keyOrFilter, { forUpdate: true });
        if (!state) return 0;

        await state.delete();
        return state.context.hasErrors() ? 0 : 1;
    }

    async query(context: Context, options?: NodeQueryOptions): Promise<AsyncReader<Node>> {
        const opts: NodeQueryOptions = options ? { ...options } : {};
        const query = await this.createNodeQuery(context, opts);
        if (options?.aggregate) return query.getAggregateReader();

        // Temporary fix. These nodes modify records inside a query reader, which is forbidden.
        // This fix should be removed once https://jira.sage.com/browse/XT-64787 is fixed.
        // One of the stock-engine mocha tests fails without this exclusion.
        if (this.name.startsWith('FifoValuation')) return query.getNodeReader();

        // Read all the records to prefetch all of them before business logic processes them individually
        const records = await query.getDataReader().readAll();
        records.forEach(record => context.prefetcher.visit(this, !!options?.forUpdate, record));
        return asyncArrayReader(records).map(
            record => NodeState.newFromQuery(context, this, record, !!options?.forUpdate).node,
        );
    }

    private async getAllowedAccessCodes(context: Context, tag: FilterTag): Promise<string[] | null> {
        let allowedAccessCodes = context.getAllowedAccessCodes(tag);
        if (this.storage === 'external' && this.externalStorageManager!.getAllowedAccessCodes) {
            // if storage is external, we allow the storage manager to get the allowed codes, in case certain
            // nodes have different rules for getting allowed codes
            allowedAccessCodes = await this.externalStorageManager!.getAllowedAccessCodes(
                context,
                tag,
                allowedAccessCodes,
            );
        }
        return allowedAccessCodes;
    }

    private async getParentPropertyFilter(
        context: Context,
        tag: FilterTag,
        factory: NodeFactory,
    ): Promise<AnyFilterObject | undefined> {
        let filter: AnyFilterObject | undefined;
        if (factory.vitalParentFactory.isVitalChild) {
            filter = await this.getParentPropertyFilter(context, tag, factory.vitalParentFactory);
            if (filter) {
                return { [factory.vitalParentProperty.name]: filter };
            }
        }

        const property = factory.vitalParentFactory.getTaggedProperty(tag);
        if (!property) return undefined;

        const allowedAccessCodes = await this.getAllowedAccessCodes(context, tag);

        if (allowedAccessCodes) {
            if (property.isNullable) {
                return {
                    _or: [
                        { [factory.vitalParentProperty.name]: { [property.name]: { _in: allowedAccessCodes } } },
                        { [factory.vitalParentProperty.name]: { [property.name]: null } },
                    ],
                } as AnyFilterObject;
            }
            return {
                [factory.vitalParentProperty.name]: { [property.name]: { _in: allowedAccessCodes } },
            } as AnyFilterObject;
        }
        return filter;
    }

    private async getPropertyFilter(context: Context, tag: FilterTag): Promise<AnyFilterObject | undefined> {
        // For the site tag, if it's a vital child, we need to check the filter from the parent if present
        if (tag === 'site' && this.isVitalChild) {
            const parentPropertyFilter = await this.getParentPropertyFilter(context, tag, this);
            if (parentPropertyFilter) return parentPropertyFilter;
        }

        const property = this.getTaggedProperty(tag);
        if (!property) return undefined;

        const allowedAccessCodes = await this.getAllowedAccessCodes(context, tag);

        if (property && allowedAccessCodes) {
            if (property.isNullable) {
                return {
                    _or: [{ [property.name]: { _in: allowedAccessCodes } }, { [property.name]: null }],
                } as AnyFilterObject;
            }
            return {
                [property.name]: { _in: allowedAccessCodes },
            } as AnyFilterObject;
        }
        return undefined;
    }

    private async getNodeFilter(context: Context, tag: NodeFilterTag): Promise<AnyFilterObject | undefined> {
        if (this.provides && this.provides.length > 0 && this.provides.includes(tag)) {
            const allowedAccessCodes = await this.getAllowedAccessCodes(context, tag);

            if (allowedAccessCodes) {
                return {
                    _id: { _in: allowedAccessCodes },
                } as AnyFilterObject;
            }
        }
        return undefined;
    }

    async getConstructorFilter(context: Context, operation: 'read' | 'lookup'): Promise<AnyFilterObject | undefined> {
        if (!this.isAbstract || this.storage === 'external') return undefined;
        const concreteFactories = this.getConcreteFactories();
        if (concreteFactories.length === 0) {
            logger.warn(`No concrete factories found for abstract node ${this.name}`);
            // We dont throw here as we have nodes that have collections to base nodes but no concrete nodes in the lower
            // package.
            return { _id: -1 };
        }

        const unauthorizedFactoryNames = await asyncArray(concreteFactories)
            .filter(async factory => {
                const status = (await Context.accessRightsManager.getUserAccessFor(context, factory.name, operation))
                    .status;
                return status !== 'authorized';
            })
            .map(factory => factory.name)
            .toArray();
        if (unauthorizedFactoryNames.length > 0) return { _constructor: { _nin: unauthorizedFactoryNames } };
        return undefined;
    }

    async getAccessRightsFilter(context: Context): Promise<AnyFilterObject | undefined> {
        const globalNodeFilters = (this.events.getFilters && (await this.events.getFilters?.(context))) || [];
        const sitesFilter = await this.getPropertyFilter(context, 'site');
        const accessCodesFilter = await this.getPropertyFilter(context, 'accessCode');
        const sitesNodeFilter = await this.getNodeFilter(context, 'site');
        const allFilters = [...globalNodeFilters, sitesFilter, accessCodesFilter, sitesNodeFilter].filter(
            filter => !!filter,
        );
        return allFilters.length > 0 ? { _and: allFilters } : undefined;
    }

    /**
     * Lazy load the value of some properties.
     * This function should only be used when a node has already been loaded but some of its
     * properties were not loaded (mainly stream properties)
     * @param idValue the '_id' of the record we want to lazy load the properties for.
     * @param properties the list of property to load
     * @returns
     */
    createLazyLoadedValueQuery(context: Context, idValue: number, properties: Property[]): Promise<SqlQuery> {
        const invalidProp = properties.find(p => !p.shouldLazyLoad);
        if (invalidProp) {
            throw new Error(
                `Property ${this.name}.${invalidProp.name}: can only lazy-load properties where shouldLazyLoad is true`,
            );
        }
        return SqlQuery.create(context, this, {
            filters: [
                {
                    _id: idValue,
                },
            ],
            orderBy: { _id: 1 },
            onlyProperties: properties,
        });
    }

    async createNodeQuery(
        context: Context,
        options: NodeQueryOptions,
        selector?: NodeSelector<Node>,
    ): Promise<SqlQuery> {
        const opts = options || {};
        const filters = Array.isArray(opts.filter) ? opts.filter : opts.filter ? [opts.filter] : [];

        const accessRightsFilter = await this.getAccessRightsFilter(context);
        if (accessRightsFilter) filters.push(accessRightsFilter);

        const sqlQueryOptions: NodeInternalQueryOptions = {
            ...opts,
            // Optimistic mode is not supported any more. Override it!
            forUpdate: opts.forUpdate,
            filters,
            selector,
        };

        return SqlQuery.create(context, this, sqlQueryOptions);
    }

    async queryCount(context: Context, options: NodeQueryOptions): Promise<number> {
        const sitesFilter = await this.getAccessRightsFilter(context);
        const notEmpty = (f: NodeQueryFilter<Node> | undefined): boolean => f != null && Object.keys(f).length > 0;
        const allFilters = notEmpty(options.filter)
            ? notEmpty(sitesFilter)
                ? { _and: [options.filter, sitesFilter] }
                : options.filter
            : sitesFilter;

        const result = await (
            await this.createNodeQuery(context, { ...options, filter: allFilters as any, count: true })
        ).getCount();

        if (result == null) throw this.systemError('getCount: empty result');
        if (typeof result !== 'number') {
            throw this.logicError(`${this.name}: getCount: bad result type: ${typeof result}`);
        }
        return result;
    }

    async exists(context: Context, key: NodeKey<Node>): Promise<boolean> {
        return (await this.queryCount(context, { filter: key as NodeQueryFilter<Node> })) > 0;
    }

    /**
     * Returns a token that uniquely identifies a Node (based on its PK) WITHIN ITS FACTORY.
     * @param values
     */
    getToken(values: Dict<AnyValue>): string {
        return this.keyProperties.map(prop => String(values[prop.name] || '')).join('~');
    }

    findProperty(name: string, options?: FindPropertyOptions): Property {
        const includeSystemProperties = options && options.includeSystemProperties;
        let property;
        if (includeSystemProperties && name.startsWith('_'))
            property = this.publishedSystemProperties[name.substring(1)];

        property = property || this.propertiesByName[name];
        if (!property) throw this.logicError(`${this.name}.${name} : property not found`);
        return property;
    }

    parseOrderBy(context: Context, orderBy: OrderBy<Node> | undefined): OrderByClause[] {
        if (!orderBy) return [];
        if (this.storage === 'external') {
            return this.externalStorageManager!.parseOrderBy(context, orderBy).map((clause: OrderByClause) => {
                const property = this.findProperty(clause.property.name);
                return {
                    ...clause,
                    property,
                } as OrderByClause;
            });
        }
        const queryConverter = new SqlConverter(context, this);
        return queryConverter.convertOrderBy(orderBy);
    }

    getValuesHash(values: Dict<AnyValue>): string {
        const hashedPropertyNames = this.properties
            .filter(property => property.isStored && !property.isSystemProperty)
            .map(property => property.name);
        const hashedData = _.zipObject(
            hashedPropertyNames,
            hashedPropertyNames.map(key => {
                const value = values[key];
                return value instanceof Node ? value._id : (value ?? this.findProperty(key).getTypeDefaultValue());
            }),
        );
        // Use a replacer to sort the keys of all objects, including those nested inside JSON properties.
        const replacer = (_key: any, value: any): any =>
            !isScalar(value) && !Array.isArray(value)
                ? _.pick(
                      value,
                      Object.keys(value).sort((s1, s2) => (s1 > s2 ? 1 : -1)),
                  )
                : value;
        return crypto.createHash('SHA256').update(JSON.stringify(hashedData, replacer)).digest('base64');
    }

    getTableColumns(): Column[] {
        return this.createNewTableColumns(this.properties);
    }

    private static getOnDeleteBehavior(property: ReferenceProperty, isBase: boolean): ForeignKeyDeleteBehaviour {
        if (property.isVitalParent || property.isAssociationParent || isBase) return 'cascade';
        if (property.isNullable) return 'noAction';
        return 'restrict';
    }

    /**
     * Creates a foreignKey
     */
    createForeignKey(referenceFactory: NodeFactory, property: ReferenceProperty, isBase = false): ForeignKey | null {
        const decorator = this.nodeDecorator;

        const onDeleteBehaviour = NodeFactory.getOnDeleteBehavior(property, isBase);

        const columnNames = [property.requiredColumnName];

        const targetColumnNames = [SystemProperties.idColumn(this).columnName];

        if (!referenceFactory.isSharedByAllTenants) {
            columnNames.unshift(SystemProperties.tenantIdColumn(this).columnName);
            targetColumnNames.unshift(SystemProperties.tenantIdColumn(this).columnName);
        }

        const name = makeName63(`${this.tableName}_${nameToSqlName(property.name)}_fk`);

        return new ForeignKey({
            name,
            columnNames,
            targetColumnNames,
            targetTable:
                decorator.name === referenceFactory.name
                    ? this.requiredTableName
                    : referenceFactory.tableName || nameToSqlName(referenceFactory.name),
            onDeleteBehaviour,
            isDeferrable: true,
        });
    }

    getForeignKeys(): ForeignKey[] {
        const foreignKeys: ForeignKey[] = [];
        this.properties
            .filter(property => property.isReferenceProperty() && property.isStored && !property.isInherited)
            .forEach((property: ReferenceProperty) => {
                const referenceFactory = property.targetFactory;
                const foreignKey = this.createForeignKey(referenceFactory, property);
                if (foreignKey) foreignKeys.push(foreignKey);
            });
        const baseFactory = this.baseFactory;
        if (baseFactory) {
            // Create a FK to the _id of the base table
            const idProp = this.propertiesByName._id as ReferenceProperty;
            const baseForeignKey = this.createForeignKey(baseFactory, idProp, true);
            if (baseForeignKey) foreignKeys.push(baseForeignKey);
        }

        if (!this.isSharedByAllTenants) {
            if (testPackageExclusions.includes(this.application.mainPackage.name)) {
                // Hard-coded exclusions: these packages do not depend on xtrem-system, so the sys-tenant table will not exist
            } else {
                // Manually add a foreign key to the sys_tenant table
                // This FK cannot be managed through a classic reference as sys_tenant.tenant_id is a string and not a number
                foreignKeys.push(
                    new ForeignKey({
                        name: makeName63(`${this.tableName}__tenant_id_fk`),
                        columnNames: ['_tenant_id'],
                        targetColumnNames: ['tenant_id'],
                        targetTable: 'sys_tenant',
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    }),
                );
            }
        }

        return foreignKeys;
    }

    private makeTable(): Table {
        const columns = this.getTableColumns();
        if (this.storage === 'sql' || this.storage === 'external') {
            this.properties
                .filter(property => property.isForeignNodeProperty())
                .forEach(property => {
                    if (!property.targetFactory) throw this.missingPropertyDecorator(property.name, 'node');
                });
        }

        // Populate foreign keys from node decorator
        const foreignKeys = this.storage === 'sql' ? this.getForeignKeys() : [];

        return new Table({
            factory: this,
            columns,
            foreignKeys,
        });
    }

    missingClassDecorator(memberName: keyof FactoryNodeDecorator): SystemError {
        return this.systemError(`${memberName} decorator missing`);
    }

    missingPropertyDecorator(propertyName: string, memberName: keyof PropertyDecorator): SystemError {
        return this.systemError(`${propertyName}: ${memberName} decorator missing or incomplete`);
    }

    getNodeSchemaDescription(): string {
        return getSchemaDescription(this.name, this.name);
    }

    /**
     * Fixes the sequences for the list of provided factories
     * @param application
     * @param factories the list of factories to process (will process all the factories if not provided)
     * @returns the factories where the sequences were fixed
     */
    static async fixSequences(application: Application, tenantId: string | null): Promise<NodeFactory[]> {
        const factoriesToProcess = application.getAllSortedFactories();
        await asyncArray(factoriesToProcess).forEach(factory => factory.fixAutoIncrementSequences(tenantId));
        return factoriesToProcess;
    }

    /**
     * Fix the sequences of the table of the factory
     */
    async fixAutoIncrementSequences(tenantId: string | null): Promise<void> {
        if (this.externalStorageManager) return;
        await this.table.fixAutoIncrementSequences(tenantId);
        if (this.baseFactory) await this.baseFactory.fixAutoIncrementSequences(tenantId);
    }

    async ensureTableExists(context: Context, options?: SqlCreateTableOptions): Promise<void> {
        await this.table.ensureTableExists(context, options);
    }

    async ensureAllTableColumnsExists(context: Context): Promise<void> {
        await this.table.ensureAllTableColumnsExists(context);
    }

    async dropTable(context: Context, options?: DropTableOptions): Promise<void> {
        if (await this.table.tableExists(context)) {
            await this.table.dropTable(context, options);
        }
    }

    /**
     * Walk the value for the natural key of the factory, parsing the _id  filter value to a filter object consisting of
     * the natural key properties.
     * Example:
     *  MainFactory natural key: [code, reference]
     *  ReferenceFactory natural key: [code, innerReference]
     *  InnerRefenceFactory natural key: [code]
     *  Therefore the value can be passed as follows: '#mainCode|referenceCode|innerReferenceCode'
     *  This will resolve to {code:'mainCode',reference:{code:'referenceCode',innerReference:{code:'innerReferenceCode'}}}
     * @param value
     * @param path
     * @returns
     */
    private resolveKeyFilter(value: string | string[], path: string[]): Dict<AnyValue> {
        const values = Array.isArray(value) ? value : value.split('|').reverse();
        const key = this.storage === 'external' ? this.keyPropertyNames : this.naturalKey;
        if (key) {
            return key.reduce((r, k) => {
                const property = this.findProperty(k);
                if (property.isReferenceProperty()) {
                    const valueToCheck = values[values.length - 1];
                    if (valueToCheck === '' || valueToCheck === undefined) {
                        values.pop();
                        r[k] = null;
                    } else {
                        const targetFactory = property.targetFactory;
                        if (targetFactory.naturalKey || targetFactory.storage === 'external') {
                            r[k] = targetFactory.resolveKeyFilter(values, [...path, ...[k, targetFactory.name]]);
                        }
                    }
                } else {
                    const val = values.pop();
                    r[k] = val === '' || val === undefined ? null : val;
                }

                return r;
            }, {} as Dict<AnyValue>);
        }

        throw new Error(`Node ${this.name} does not have a natural key index.`);
    }

    /**
     * Parse the node id passed returning an object value representing the a filter.
     * If the factory has a natural key then the value can be passed as a string starting with a #.
     * For the the # values we walk and resolve the factories natural key and recursively of any reference factories.
     * @param val can be passed in as a number or string beginning with #.
     * @returns
     */
    parseNodeId(val: string | number | undefined): Dict<AnyValue> {
        if (Number.isFinite(Number(val))) return { _id: Number(val) };
        if (!val) return {};
        if (typeof val === 'string')
            return this.resolveKeyFilter(val.startsWith('#') ? val.substring(1) : val, [this.name]);
        throw new Error(`${this.name}: Cannot parse value ${val}.`);
    }

    /**
     * Returns the natural key string value for the given data.
     * If a property value is an object and a reference property, it recursively calls the target factory's getNaturalKeyStringValue method.
     * @param data - The data object containing the property values.
     * @returns The natural key string value.
     * @throws LogicError - If the factory does not have a natural key.
     */
    getNaturalKeyStringValue(context: Context, data: Dict<AnyValue>): Promise<string> {
        if (!this.naturalKey) throw this.logicError('no natural key');

        return asyncArray(this.naturalKey)
            .map(async name => {
                const property = this.findProperty(name);
                const value = data[property.name];
                if (!value) return '';
                if (property.isReferenceProperty()) {
                    if (value && typeof value === 'object') {
                        return property.targetFactory.getNaturalKeyStringValue(context, value as Dict<AnyValue>);
                    }

                    if (property.targetFactory.naturalKey) {
                        const id =
                            Number.isFinite(value) && property.targetFactory.storage === 'sql'
                                ? Number(value)
                                : `#${value}`;
                        const node = await context.read(property.targetFactory.nodeConstructor, {
                            _id: id,
                        });
                        return property.targetFactory.getNaturalKeyStringValue(context, node.$.state.values);
                    }
                }

                return value ? String(value) : '';
            })
            .join('|');
    }

    /**
     * Returns the natural key value for the given node instance.
     * If a property is a reference property, it recursively calls the target factory's getNaturalKeyValueFromNode method.
     * @param node - The node instance.
     * @returns The natural key as an object.
     * @throws LogicError - If the factory does not have a natural key.
     */
    getNaturalKeyValueFromNode(node: Node): Promise<AnyRecord> {
        if (!this.naturalKey) throw this.logicError('no natural key');

        return asyncArray(this.naturalKey).reduce(async (r, name) => {
            const property = this.findProperty(name);
            const value = await node.$.get(property.name);
            if (value && property.isReferenceProperty()) {
                if (value instanceof Node) {
                    if (property.targetFactory.naturalKey) {
                        r[name] = await property.targetFactory.getNaturalKeyValueFromNode(value);
                    } else {
                        r[name] = value._id;
                    }
                }
            } else {
                r[name] = value;
            }

            return r;
        }, {} as AnyRecord);
    }

    /**
     * Walks the natural key properties and returns the length of the natural key.
     * If a property is a reference property, it recursively calls the target factory's getNaturalKeyValueLength method.
     * This will get us the true length of the natural key value that will be concatenated with `|`
     * @returns The natural key length.
     * @throws LogicError - If the factory does not have a natural key.
     */
    getNaturalKeyValueLength(): number {
        if (!this.naturalKey) throw this.logicError('no natural key');

        return this.naturalKey.reduce((r, name) => {
            const property = this.findProperty(name);
            let result = r;
            if (property.isReferenceProperty() && property.targetFactory.naturalKey) {
                result += property.targetFactory.getNaturalKeyValueLength();
            } else {
                result += 1;
            }

            return result;
        }, 0);
    }

    /**
     * Retrieves the reverse vital property associated with this node factory.
     * A reverse vital property is a property that references this node factory as its target.
     *
     * @returns The reverse vital property, or undefined if it doesn't exist.
     * @throws An error if the vital parent property doesn't have a unique child property with the same name as this factory.
     */
    getReverseVitalProperty(): Property | undefined {
        if (this.vitalParentFactory) {
            const vitalChildren = this.vitalParentFactory.properties.filter(
                prop => prop.isReferenceProperty() && prop.targetFactory.name === this.name,
            );
            if (vitalChildren.length !== 1) {
                throw new Error(
                    `Vital parent property ${this.vitalParentProperty.name} must have a unique child property ${this.name}`,
                );
            }
            return vitalChildren[0];
        }
        return undefined;
    }

    /**
     * Returns the list of operations that require admin privileges.
     */
    get adminOperations(): string | (keyof Node | StandardOperation)[] | undefined {
        return this.nodeDecorator.adminOperations;
    }

    /** Can this node be customized with customFields
     * In the case of sub nodes, if a value is set for the isCustomizable decorator on the sub node,
     * it will override the value set on the base node, otherwise, if no value set on the sub node,
     * the value of the base node will be inherited
     */
    get isCustomizable(): boolean {
        // If isCustomizable is set on the node, return the value
        if (this.nodeDecorator.isCustomizable !== undefined) return this.nodeDecorator.isCustomizable;

        // If this is a sub node, return the base node isCustomizable, recursively
        if (this.baseFactory) {
            return this.baseFactory.isCustomizable;
        }

        // default false if nothing set
        return false;
    }

    /** Is this node a synchronization source */
    get isSynchronizable(): boolean {
        return !!this.nodeDecorator.isSynchronizable || !!this.baseFactory?.isSynchronizable;
    }

    /** Is this node a synchronization target */
    get isSynchronized(): boolean {
        return !!this.nodeDecorator.isSynchronized;
    }

    get defaultsToSingleMatch(): boolean {
        return !!this.nodeDecorator.defaultsToSingleMatch;
    }

    get denyReadOnLookupOnlyAccess(): boolean {
        return !!this.nodeDecorator.denyReadOnLookupOnlyAccess;
    }

    getOperationByName(operationName: string, action?: string): PlainOperationDecorator | undefined {
        return (
            this.mutations.find(decorator => {
                const isBulkOrAsync = ['bulkMutation', 'asyncMutation'].includes(decorator.operationKind);
                return (
                    decorator.name === operationName &&
                    ((isBulkOrAsync && decorator.action === (action ?? 'start')) || !isBulkOrAsync)
                );
            }) ||
            this.queries.find(decorator => {
                const isBulkOrAsync = ['bulkMutation', 'asyncMutation'].includes(decorator.operationKind);
                return (
                    decorator.name === operationName &&
                    ((isBulkOrAsync && decorator.action === (action ?? 'start')) || !isBulkOrAsync)
                );
            })
        );
    }

    get authorizedBy(): NodeAuthorizedBy | undefined {
        return this.nodeDecorator.authorizedBy;
    }

    executeRule<ResultT extends AnyValue>(
        state: NodeState,
        ruleName: NodeRuleName,
        ...args: AnyValue[]
    ): Promise<ResultT> {
        // The `isFrozen` rule is attached to `nodeDecorator` rather than `events`.
        const method =
            (this as any).events[ruleName] || ((this as any).nodeDecorator[ruleName] as NodeFunction<ResultT>);
        return state.context.sqlSpy.withRuleMetrics({ nodeName: this.name, propertyName: '', ruleName }, () =>
            method.apply(state.node, args),
        );
    }

    /**
     * Send successful CRUD operations to be broadcasted
     * @param operationType
     */
    broadcastCrudMessage(context: Context, operationType: string): void {
        if (!context.managedExternal) {
            const payload = `${this.name}/${operationType}`;
            context.broadcastToAllUsers({
                category: NOTIFICATION_CATEGORY_NODE_MODIFIED,
                payload,
                afterCommit: true,
            });
        }
    }
}
export { FactoryDecorators };
