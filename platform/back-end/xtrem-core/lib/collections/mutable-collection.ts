/** @packageDocumentation @module runtime */
import { Any<PERSON><PERSON><PERSON>, asyncArray, Async<PERSON><PERSON><PERSON><PERSON>eader, AsyncReader } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Dict, integer, setSortValues } from '@sage/xtrem-shared';
import { assert } from 'console';
import { NodeState, StateStatus } from '../node-state';
import { StateGetValue } from '../node-state/state-get-value';
import { StateIntern } from '../node-state/state-intern';
import { StateInvalidate } from '../node-state/state-invalidate';
import { StateUtils } from '../node-state/state-utils';
import { NodeFactory } from '../runtime/node-factory';
import { Node, NodeCreateData, NodeQueryOptions, NodeStatus } from '../ts-api';
import { BaseCollection } from './base-collection';

export class MutableCollection extends BaseCollection {
    get isVitalCollectionChild(): boolean {
        // if this factory is a vital collection child we know there is a sortValue
        return this.factory.isVitalCollectionChild;
    }

    get isAssociationChild(): boolean {
        // if this factory is an association, there is no sortValue
        return this.factory.isAssociationChild;
    }

    get hasSortValue(): boolean {
        return this.isVitalCollectionChild && !this.isAssociationChild;
    }

    /** @internal */
    getQueryForUpdate(options?: NodeQueryOptions): boolean {
        if (options?.forUpdate !== undefined) return options?.forUpdate;
        if (this.property.isMutable) {
            // Loading a collection of a node that is just created is an edge case for external nodes.
            // The mutable collection may not have been provided in the parent node create data but the collection entries were created on the external system.
            // In this case the collection nodes will be loaded via the getSource, and the resulting states should be writable (read for update).

            return (
                this.sourceState.forUpdate ||
                (this.factory.storage === 'external' && this.sourceState.status === StateStatus.inserted)
            );
        }
        return false;
    }

    /** @internal */
    getReader(): AsyncReader<Node> {
        return new AsyncArrayReader(async () => {
            if (!this._nodes) this._nodes = await this.queryFullCollection();
            return this.getSource();
        });
    }

    // mutations
    /** @internal */
    private async checkWritable(): Promise<void> {
        if (this.sourceState.isReadonly) {
            throw this.error('cannot modify collection of readonly node');
        }

        if (
            !this.property.isOwnedByCustomer &&
            !ConfigManager.current.ignoreVendorProtection &&
            this.factory.isVitalChild &&
            this.factory.vitalParentFactory.hasVendorProperty &&
            (await this.sourceState.vendor)
        ) {
            throw this.sourceState.propertyDataInputError(this.property, {
                message: 'Cannot add to or delete from vendor protected collections.',
                key: '@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor',
            });
        }
    }

    /** @internal */
    private async createNode(path: string[], data: NodeCreateData<Node>): Promise<Node> {
        const createPath = MutableCollection.fillPath(path, data);

        let node: Node;
        await StateUtils.withValidationErrorRethrow(this.sourceNode.$.state, this.property, createPath, async () => {
            node = (
                await NodeState.newFromContextCreate(
                    this.sourceNode.$.context,
                    this.factory,
                    createPath,
                    {
                        ...(data as AnyRecord),
                        ...((await this.joinValues) as AnyRecord),
                    },
                    {
                        writable: true,
                        isTransient: this.sourceState.isTransient || this.property.isTransientInput,
                        isOnlyForDefaultValues: this.sourceState.isOnlyForDefaultValues,
                        isOnlyForDuplicate: this.sourceState.isOnlyForDuplicate,
                        isOnlyForLookup: this.sourceState.isOnlyForLookup,
                        collection: this,
                    },
                )
            ).node;
        });
        return node!;
    }

    /** @internal */
    private async invalidateSourceNode(): Promise<void> {
        if (this.sourceState.status === StateStatus.updatable) {
            this.sourceState.status = StateStatus.modified;
        }
        await StateInvalidate.invalidateDependantProperties(this.sourceState, this.property);
    }

    /** @internal */
    private async spliceWithNodes(start: number, deleteCount: number, array: Node[] = []): Promise<void> {
        await this.invalidateSourceNode();

        array.forEach(node => {
            if (node.$.state.collection && node.$.state.collection !== this) {
                throw new Error(
                    `${node.$.keyToken}: invalid attempt to associate this node with two collections: ${this.fullName} and ${node.$.state.collection.fullName}`,
                );
            }
            node.$.state.collection = this;
        });
        const oldNodes = (await this.nodes).splice(start, deleteCount, ...array);
        oldNodes.forEach(node => {
            node.$.state.collection = undefined;
        });
    }

    /** @internal */
    private static async allocateSortValues(
        factory: NodeFactory,
        nodes: Node[],
        start: number,
        end: number,
    ): Promise<void> {
        if (!factory.isVitalCollectionChild || factory.isAssociationChild) return;
        await setSortValues(
            {
                length: nodes.length,
                setSortValue: (index, value) => nodes[index].$.state.setSortValue(value),
                getSortValue: index => nodes[index].$.state.sortValue,
            },
            start,
            end,
            { preserveOldValues: factory.naturalKey?.includes('_sortValue') }, // We do not allow overriding for factories were the naturalKey contains the _sortValue
        );
    }

    /** @internal */
    private async allocateSortValues(start: number, end: number): Promise<void> {
        await MutableCollection.allocateSortValues(this.factory, await this.nodes, start, end);
    }

    /** @internal */
    private async spliceWithData(
        start: number,
        deleteCount: number,
        array: NodeCreateData<Node>[] = [],
    ): Promise<void> {
        await this.spliceWithNodes(
            start,
            deleteCount,
            await asyncArray(array)
                .map(data => this.createNode([this.property.name], data))
                .toArray(),
        );
        await this.allocateSortValues(start, start + array.length - deleteCount);
    }

    override async reset(): Promise<void> {
        await this.checkWritable();
        if (this._nodes && this._nodes.length > 0) {
            this._nodes.forEach(node => {
                node.$.state.collection = undefined;
            });
        }
        this._nodes = [];
    }

    override async fill(array: NodeCreateData<Node>[]): Promise<void> {
        await this.reset();
        await this.spliceWithData(0, 0, array);
    }

    override async insert(i: number, data: NodeCreateData<Node>): Promise<void> {
        await this.checkWritable();
        await this.spliceWithData(i, 0, [data]);
    }

    /** @internal */
    override async appendNode(node: Node): Promise<void> {
        await this.checkWritable();
        await this.spliceWithNodes((await this.nodes).length, 0, [node]);
    }

    override async append(data: NodeCreateData<Node>): Promise<void> {
        await this.checkWritable();
        await this.spliceWithData((await this.nodes).length, 0, [data]);
    }

    override async delete(start: number, deleteCount = 1): Promise<void> {
        await this.checkWritable();
        await this.spliceWithData(start, deleteCount);
    }

    /** @internal */
    private async allocateSortValuesForInsertedNodes(
        sortValues: (number | undefined)[],
        start: integer,
        end: integer,
    ): Promise<void> {
        type Gap = { start: integer; end?: integer };
        const gaps: Gap[] = [];
        let gap: Gap | undefined;
        for (let i = start; i < end; i += 1) {
            if (sortValues[i] !== undefined) {
                if (gap) {
                    gap.end = i;
                    gaps.push(gap);
                    gap = undefined;
                }
            } else if (!gap) {
                gap = { start: i };
            }
        }
        if (gap) {
            gap.end = end;
            gaps.push(gap);
        }

        // Process the gaps in reverse order, because allocateSortValues may modify values at end or beyond
        // to handle collisions when generating new numbers.
        await asyncArray(gaps.reverse()).forEach(g => this.allocateSortValues(g.start, g.end!));
    }

    // Updates the _sortValue properties of the nodes, based on the sortValues array.
    // The sortValues array contains numbers for the nodes that existed before, and undefined for the newly inserted nodes
    //
    // We try to minimize the number of _sortValues that we modify in existing nodes, with the following algorithm:
    // 1) If the existing values have not been reordered, we just allocate intermediate values for the inserted nodes.
    // 2) Otherwise:
    //    - We decompose the array in 3 segments: head - middle - tail such that the _sortValue values are stricly increasing
    //      in head, in tail, and in head + tail (values in head are all lower than values in tail).
    //    - In head and tail, we don't modify the existing nodes, we just allocate intermediate values for the inserted nodes.
    //    - In the middle, we regenerate all the values.
    //
    // For example:
    // 1) if sortValues contains [0, 1, undefined, 2, 5, undefined, undefined, 7], the existing values are still in order
    //   so we obtain [0, 1, 1.5, 2, 5, 5.5, 6, 7]
    // 2) if sortValues contains [0, 3, undefined, 5, 2, undefined, undefined, 7], the existing values a not in order
    //   the head is [0, 3, undefined, 5]
    //   the tail is [7] (2 is excluded because head + tail would be out of order if we included 2 in the tail)
    //   so we keep the values for 0, 3, 5 and 7 and we allocate new values for all other nodes,
    //   which gives [0, 3, 4, 5, 5.5, 6, 6.5, 7]
    /** @internal */
    private async updateSortValues(sortValues: (number | undefined)[]): Promise<void> {
        if (!this.hasSortValue) return;
        // Find end of head
        let start = 0;
        let lowValue = -Infinity;
        while (start < sortValues.length && (sortValues[start] === undefined || sortValues[start]! > lowValue)) {
            if (sortValues[start] !== undefined) lowValue = sortValues[start]!;
            start += 1;
        }
        // If start reached the end of the array, all existing sort values are correctly sorted.
        // So we just allocate values for the inserted nodes (case 1)
        if (start === sortValues.length) {
            await this.allocateSortValuesForInsertedNodes(sortValues, 0, sortValues.length);
            return;
        }

        // We have to decompose the array in head + middle + tail (case 2)
        // start is beyond the head, we need to move it backwards to a valid _sortValue
        start -= 1;
        while (start >= 0 && sortValues[start] === undefined) start -= 1;
        assert(start >= 0);
        // Head is from 0 to start (included). It is correctly sorted

        // Find beginning of tail
        let end = sortValues.length - 1;
        let highValue = Infinity;
        while (end >= 0 && (sortValues[end] === undefined || sortValues[end]! < highValue)) {
            if (sortValues[end] !== undefined) highValue = sortValues[end]!;
            end -= 1;
        }
        assert(end >= 0);
        // end is before the tail, we need to move it forward to a valid _sortValue
        end += 1;
        while (end < sortValues.length && sortValues[end] === undefined) end += 1;
        assert(end < sortValues.length);
        // Tail is from end (included) to the end of the array. It is correctly sorted

        // Now, we have to ensure that head + tail is correctly sorted
        // To do this we may have to trim head or tail. We trim the shortest one.
        if (start >= sortValues.length - 1 - end) {
            // sorted head is longur than (or equal to) sorted tail, keep head
            // increase end to eliminate tail items that are <= sortValues[start]
            while (
                end < sortValues.length &&
                (sortValues[end] === undefined || !(sortValues[start]! < sortValues[end]!))
            )
                end += 1;
        } else {
            // keep tail
            // decrease start to eliminate head items that are >= sortValues[end]
            while (start >= 0 && (sortValues[start] === undefined || !(sortValues[start]! < sortValues[end]!)))
                start -= 1;
        }

        // We have the boundaries for head, middle and tail. We can set the _sortValues.
        // We proceed backward (from tail to end) to allocate the values because the algorithm that allocates values
        // may modify values after the end of its range, to handle collisions.

        // On tail, we keep the existing values and we allocate new ones for the inserted elements
        await this.allocateSortValuesForInsertedNodes(sortValues, end, sortValues.length);
        // between head and tail, we reallocate all values
        await this.allocateSortValues(start + 1, end);
        // on head, we keep the existing values and we allocate new ones for the inserted elements
        await this.allocateSortValuesForInsertedNodes(sortValues, 0, start);
    }

    /** @internal */
    private mixedFullPartialError(): Error {
        return this.error('invalid update data: mix of rows with and without _action');
    }

    /** @internal */
    private async deleteOldNodes(oldMap: Dict<NodeState>): Promise<void> {
        // if collection nodes are persisted with their parent node or the collection is a transient property we don't try to delete them.
        if (this.factory.storage !== 'external' && !this.property.isTransientInput) {
            // delete keys that we did not find.
            await asyncArray(Object.keys(oldMap)).forEach(async oldToken => {
                const oldState = oldMap[oldToken];
                if (!oldState.isNew) {
                    const oldKey = oldState.getKeyValues();
                    await this.factory.deleteMany(this.sourceState.context, oldKey);
                } else {
                    StateIntern.removeState(oldState);
                }
            });
        }
    }

    /**
     * Force the lazy-loading of the collection in the old.state of the sourceNode
     * This function is mainly invoked before an item is deleted from the collection (so that node.$.old.collection is valid)
     */
    private async _forceLazyloadingForSourceOld(): Promise<void> {
        if (this.sourceNode.$.status === NodeStatus.added) {
            // Do not manage the '$.old' for objects that were created in the current transaction
            return;
        }
        const parentOld = await this.sourceNode.$.old;
        const collection = StateGetValue.getCollectionValue(parentOld.$.state, this.property);
        // Reading the length of the collection will force its lazy loading
        await collection.length;
    }

    /**
     * Find the key of the oldState from the oldMap
     * @param dataWithKey
     * @param oldMap
     * @returns
     */
    private findOldTokenFromData(dataWithKey: AnyRecord, oldMap: Dict<NodeState>): string | undefined {
        const keys = Object.values(StateIntern.getInterningKeysFromValues(this.factory, dataWithKey));
        return Object.keys(oldMap).find(oldKey => {
            const oldState = oldMap[oldKey];
            const oldInterningKeys = oldState.interningKeyValues;

            return oldInterningKeys.some(oldInterningKey => keys.includes(oldInterningKey));
        });
    }

    /** @internal */
    private async fullUpdate(
        path: string[],
        values: AnyRecord[],
        joinValues: AnyRecord,
        oldMap: Dict<NodeState>,
    ): Promise<void> {
        await this.reset();

        // We use spliceWithNodes which does not allocate _sortValue
        // Instead, we collect the gaps made by new nodes and we allocate their sort values at the end
        const sortValues = Array(values.length) as (number | undefined)[];
        await asyncArray(values).forEach(async (data, i) => {
            if (data._action != null) {
                throw this.mixedFullPartialError();
            }
            const dataWithKey = { ...joinValues, ...data };
            const token = this.findOldTokenFromData(dataWithKey, oldMap);

            if (token) {
                const oldState = oldMap[token];
                if (this.hasSortValue) sortValues[i] = await oldState.sortValue;
                await oldState.set(data, MutableCollection.fillPath(path, data));
                await this.spliceWithNodes(i, 0, [oldState.node]);
                delete oldMap[token];
            } else {
                if (this.hasSortValue) sortValues[i] = data._sortValue as number;
                await this.spliceWithNodes(i, 0, [await this.createNode(path, dataWithKey)]);
            }
        });

        await this.updateSortValues(sortValues);
        // Load the collection in the parent.$.old before the item is deleted
        await this._forceLazyloadingForSourceOld();
        await this.deleteOldNodes(oldMap);
    }

    /** @internal */
    private appendedNodeNotInTailError(): Error {
        return this.error('invalid update array: some new entries without _sortValue are not at the end of the list');
    }

    /** @internal */
    private async compareSortValues(n1: Node, n2: Node): Promise<number> {
        const v1 = await n1.$.state.sortValue;
        const v2 = await n2.$.state.sortValue;
        if (v1 === v2) throw this.error(`cannot update collection: duplicate _sortValue ${v1}`);
        return v1 - v2;
    }

    /**
     * @internal
     * the _id value will be added to the path, which will be used in the diagnoses. If the _id is undefined, we will use
     * an empty string.
     */
    static fillPath(path: string[], data: NodeCreateData<Node> | Node): string[] {
        return [...path, data._id != null ? String(data._id) : ''];
    }

    /** @internal */
    private async partialUpdate(
        path: string[],
        values: AnyRecord[],
        joinValues: AnyRecord,
        oldMap: Dict<NodeState>,
    ): Promise<void> {
        // do we need to sort the list at the end.
        let needsSort = false;

        const deletedNodes: Node[] = [];
        const appendedNodes: Node[] = [];
        this._nodes = await this.nodes;

        if (values.length > 0) await this.invalidateSourceNode();

        await asyncArray(values).forEach(async data => {
            needsSort = await this.partialUpdateAction(data, path, deletedNodes, appendedNodes, joinValues, oldMap);
        });

        // remove deleted nodes
        if (deletedNodes.length > 0) this._nodes = this._nodes.filter(node => !deletedNodes.includes(node));
        // sort the nodes that already have a _sortValue, if necessary
        if (needsSort) await this.sortBySortValue();
        // append the nodes collected in appendedNodes and set their _sortValue
        this._nodes.push(...appendedNodes);
        await this.allocateSortValues(this._nodes.length - appendedNodes.length, this._nodes.length);
    }

    /** @internal */
    private async partialUpdateAction(
        data: AnyRecord,
        path: string[],
        deletedNodes: Node[],
        appendedNodes: Node[],
        joinValues: AnyRecord,
        oldMap: Dict<NodeState>,
    ): Promise<boolean> {
        let needsSort = false;

        // Need to spread the data object here, so the delete does not change the original object, otherwise future
        // steps will go to fullUpdate, in the update function below, since the _action is missing
        const dataCopy = { ...data };
        const action = dataCopy._action;
        if (action == null) throw this.mixedFullPartialError();
        if (typeof action !== 'string') {
            throw this.error(`_action in update data must be a string, got ${typeof action}`);
        }
        delete dataCopy._action;

        const dataWithKey = { ...joinValues, ...dataCopy };
        const token = this.findOldTokenFromData(dataWithKey, oldMap);

        const oldState = token ? oldMap[token] : undefined;

        switch (action) {
            case 'create': {
                await this.checkWritable();
                if (oldState) throw this.error(`cannot create entry: ${token} already exists`);
                const node = await this.createNode(path, dataWithKey);
                if (dataCopy._sortValue) {
                    if (appendedNodes.length > 0) throw this.appendedNodeNotInTailError();
                    this._nodes.push(node);
                    needsSort = true;
                } else {
                    appendedNodes.push(node);
                }
                await this.invalidateSourceNode();
                break;
            }
            case 'update': {
                if (!oldState) throw this.error(`cannot update entry: ${JSON.stringify(dataCopy)} not found`);
                if (appendedNodes.length > 0) throw this.appendedNodeNotInTailError();
                if (dataCopy._sortValue != null && dataCopy._sortValue !== (await oldState.sortValue)) {
                    needsSort = true;
                }

                await oldState.set(dataCopy, MutableCollection.fillPath(path, dataWithKey));
                delete oldMap[token!];
                break;
            }
            case 'delete': {
                await this.checkWritable();
                if (!oldState) throw this.error(`cannot delete entry: ${JSON.stringify(dataCopy)} not found`);

                await StateUtils.withValidationErrorRethrow(
                    this.sourceNode.$.state,
                    this.property,
                    MutableCollection.fillPath(path, dataWithKey),
                    async () => {
                        // path passed in will be used by the control, but the if the delete of the record fails
                        // withValidationErrorRethrow will catch this and add to the diagnoses with the correct path
                        // Only delete states that have been saved already
                        if (!oldState.isNew) {
                            // Load the collection in the parent.$.old before the item is deleted
                            await this._forceLazyloadingForSourceOld();
                            await oldState.delete({ path });
                            const index = this._nodes.indexOf(oldState.node);
                            if (index >= 0) await this.delete(index);
                        } else {
                            StateIntern.removeState(oldState);
                        }
                        deletedNodes.push(oldState.node);
                        delete oldMap[token!];
                    },
                );
                await this.invalidateSourceNode();
                break;
            }

            default:
                throw this.error(`invalid _action in update data: ${action}`);
        }

        return needsSort;
    }

    /** @internal */
    async sortBySortValue(): Promise<void> {
        if (!this.hasSortValue) return;
        await asyncArray(this._nodes).sort(this.compareSortValues.bind(this)).toArray();
    }

    /** @internal */
    async update(path: string[], values: AnyRecord[]): Promise<void> {
        const joinValues = await this.sourceState.getJoinValues(this.property);
        const oldMap = {} as Dict<NodeState>;
        // If the property is transient we should override the collection value with the value passed and we do not need
        // to merge it, as transients will not persist to the node it is linked to.
        if (!this.property.isTransientInput && this.sourceState.status !== StateStatus.constructed) {
            await this.forEach(node => {
                oldMap[node.$.state.interningKeyValues[0]] = node.$.state;
            });
        }
        if (values.length > 0 && values[0]._action != null) {
            await this.partialUpdate(path, values, joinValues, oldMap);
        } else {
            await this.fullUpdate(path, values, joinValues, oldMap);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    isValid(): boolean {
        // content is managed explicitly => it is valid
        return true;
    }
}
