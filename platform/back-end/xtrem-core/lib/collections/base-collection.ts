/** @packageDocumentation @module runtime */
import { AnyVal<PERSON>, AsyncArray, Async<PERSON>eader, asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse } from '@sage/xtrem-shared';
import { NodeState, StateStatus } from '../node-state';
import { StateGetValue } from '../node-state/state-get-value';
import { StateOld } from '../node-state/state-old';
import { CollectionProperty, ReferenceProperty } from '../properties';
import { applyPagingOptions } from '../runtime/array-utils';
import { NodeFactory } from '../runtime/node-factory';
import {
    Collection,
    Node,
    NodeCreateData,
    NodeQueryFilter,
    NodeQueryOptions,
    OrderBy,
    SqlWhereInterface,
} from '../ts-api';
import { SqlCollection } from './sql-collection';

export interface CollectionCreateOptions {
    nodes?: Node[];
}

export abstract class BaseCollection extends AsyncArray<Node> implements Collection<Node> {
    /** @internal */
    protected _nodes: Node[];

    /** @internal */
    orderBy: OrderBy<Node>;

    private nodesPromise: Promise<Node[]> | null = null;

    /** @internal */
    constructor(
        public sourceNode: Node,
        readonly property: CollectionProperty,
    ) {
        super(() => {
            // If the nodes are already loaded, return them, as the promise will have the initial loaded value and not the
            // updated collection
            if (this._nodes) return this._nodes;

            // We use an unawaited Promise to avoid loading the collection more than once
            // If getSource is called more than once, the collection nodes will be loaded only once in a single Promise
            if (this.nodesPromise == null) this.nodesPromise = this.getNodes();

            return this.nodesPromise;
        });
        if (property.orderBy) this.orderBy = property.orderBy;
    }

    private async getNodes(): Promise<Node[]> {
        if (this._nodes) return this._nodes;
        if (this.property.computeValue) {
            this._nodes = (await StateGetValue.callComputeValue(this.sourceState, this.property)) as unknown as Node[];
        } else if (this.property.getValue) {
            this._nodes = (await StateGetValue.callGetValue(this.sourceState, this.property)) as unknown as Node[];
        } else if (this.property.join || this.property.getFilter) {
            const canRead = this.sourceState.canBeFetched();
            if (canRead) {
                this._nodes = await this.queryFullCollection();
            } else {
                this._nodes = [];
            }
        } else {
            this._nodes = [];
        }
        this._nodes = await StateOld.mapNodesIfOld(this.sourceNode.$.state, this.property, this._nodes);
        return this._nodes;
    }

    get joinValues(): AsyncResponse<NodeQueryFilter<Node>> {
        // do not cache it in collection as _id of sourceNode changes when sourceNode is inserted into db.
        return this.sourceState.getJoinValues(this.property);
    }

    get name(): string {
        return this.property.name;
    }

    /** @internal */
    get factory(): NodeFactory {
        if (!this.property.node) {
            throw this.error('class decorator missing');
        }
        return this.property.targetFactory;
    }

    /** @internal */
    get sourceState(): NodeState {
        return this.sourceNode.$.state;
    }

    /** @internal */
    get sourceFactory(): NodeFactory {
        return this.sourceState.factory;
    }

    /** @internal */
    protected error(message: string): Error {
        return new Error(`${this.sourceFactory.name}.${this.property.name}: ${message}`);
    }

    /**
     * Tries to prefetch the records of the collection.
     * @param forUpdate
     * @returns The records of the collection or undefined if the prefetching is not possible.
     */
    private async prefetchRecords(forUpdate: boolean): Promise<Node[] | undefined> {
        // Only use prefetcher if property has reverseReference
        if (!this.property.reverseReference) return undefined;

        const sourceId = this.sourceState.values._id;
        // If source record has not been inserted yet, we cannot prefetch
        if (typeof sourceId !== 'number' || sourceId <= 0) return [];

        const context = this.sourceState.context;
        const reverseProperty = this.factory.findProperty(this.property.reverseReference) as ReferenceProperty;
        const records = await context.prefetcher.tryQueryFullCollection(
            this.factory,
            reverseProperty.name,
            forUpdate,
            String(sourceId),
            this.orderBy,
        );

        if (!records) return undefined;

        return asyncArray(records)
            .map(record => {
                const node = NodeState.newFromQuery(context, this.factory, record, forUpdate).node;
                node.$.state.collection = this;
                return node;
            })
            .toArray();
    }

    /** @internal */
    abstract getQueryForUpdate(options?: NodeQueryOptions): boolean;

    /**
     * @internal
     * Returns the FULL collection (no filter, no paging)
     */
    protected async queryFullCollection(options?: NodeQueryOptions): Promise<Node[]> {
        const forUpdate = this.getQueryForUpdate(options);

        const context = this.sourceState.context;

        if (!context.managedExternal && !this.factory.externalStorageManager) {
            const records = await this.prefetchRecords(forUpdate);
            if (records) return records;
        }

        const nodeQuery = await this.factory.createNodeQuery(context, {
            forUpdate,
            filter: await this.combineFilters(),
            orderBy: options?.orderBy || this.orderBy,
            collection: this,
        });

        return nodeQuery
            .getNodeReader()
            .map(node => {
                // Set the current collection instance on the state of each result.
                node.$.state.collection = this;
                return node;
            })
            .readAll();
    }

    private async combineFilters(options?: NodeQueryOptions): Promise<NodeQueryFilter<Node>> {
        const filters = [] as NodeQueryFilter<Node>[];
        if (this.property.reverseReference || this.property.join) filters.push(await this.joinValues);
        if (this.property.getFilter) filters.push(await this.property.getFilter?.call(this.sourceNode));
        if (filters.length === 0)
            throw this.property.logicError(
                'invalid collection decorator: reverseReference, getFilter, join, getValue and computeValue are all absent',
            );
        if (options?.filter) filters.push(options?.filter);

        return filters.length === 1 ? filters[0] : { _and: filters };
    }

    /** @internal */
    async queryPage(options?: NodeQueryOptions): Promise<{
        items: Node[];
        totalCount: number;
    }> {
        if (!this._nodes && !this.sourceState.context.transaction) return { items: [], totalCount: 0 };
        await this.getSource();
        const fullCln = this._nodes || (await this.queryFullCollection(options));
        // The full collection has been fetched from the database
        // Any orderBy/filter/first/.... clause will now be computed in memory
        return applyPagingOptions(this.factory, this.sourceState.context, fullCln, options);
    }

    /** @internal */
    abstract getReader(): AsyncReader<Node>;

    async withReader<R extends AnyValue>(body: (reader: AsyncReader<Node>) => R): Promise<R> {
        const reader = this.getReader();
        try {
            return body(reader);
        } finally {
            await reader.stop();
        }
    }

    override join(sep?: string): Promise<string> {
        return this.join(sep);
    }

    get fullName(): string {
        return `${this.sourceState.factory.name}.${this.name}`;
    }

    /** @internal */
    get nodes(): Promise<Node[]> {
        return (async () => {
            if (!this._nodes) {
                const status = this.sourceState.status;
                const canRead =
                    status !== StateStatus.created &&
                    status !== StateStatus.constructed &&
                    status !== StateStatus.deleted;
                this._nodes = canRead ? await this.getSource() : [];
            }
            return this._nodes;
        })();
    }

    /**
     * indicates if the the collection's nodes are loaded
     */
    get isLoaded(): boolean {
        return !!this._nodes;
    }

    // indexed access
    override get length(): Promise<number> {
        return (async () => {
            return (await this.nodes).length;
        })();
    }

    /** @internal */
    abstract isValid(): boolean;

    // mutations
    /** @internal */
    private cannotModify(): never {
        throw this.error('cannot modify non vital collection');
    }

    reset(): Promise<void> {
        this.cannotModify();
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    fill(array: NodeCreateData<Node>[]): Promise<void> {
        this.cannotModify();
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    insert(i: number, data: NodeCreateData<Node>): Promise<void> {
        this.cannotModify();
    }

    /** @internal */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    appendNode(node: Node): Promise<void> {
        this.cannotModify();
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    append(data: NodeCreateData<Node>): Promise<void> {
        this.cannotModify();
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    delete(start: number, deleteCount = 1): Promise<void> {
        this.cannotModify();
    }

    /**
     * @internal
     * Returns node page (filtered, sorted and paged)
     */
    private async queryPagedCollection(options?: NodeQueryOptions): Promise<Node[]> {
        const filter = await this.combineFilters(options);
        const queryOptions = { orderBy: this.orderBy, ...options, filter, collection: this };

        // Do not set the current collection instance on the state of each result.
        return this.sourceState.context.query(this.factory.nodeConstructor, queryOptions).toArray();
    }

    /**
     * @internal
     * Returns total count
     */
    private async queryTotalCount(options?: NodeQueryOptions): Promise<number> {
        const filter = await this.combineFilters(options);
        return this.sourceState.context.queryCount(this.factory.nodeConstructor, { filter, collection: this });
    }

    /**
     * This is called by the graphql resolver of collection properties
     * @internal
     */
    async readPage(options: NodeQueryOptions & { selections: string[] }): Promise<{
        items: Node[];
        totalCount: number;
    }> {
        if (this._nodes || this.property.getValue || this.property.computeValue || this.getQueryForUpdate(options))
            return this.queryPage(options);

        const items = options.selections.includes('edges') ? await this.queryPagedCollection(options) : [];
        const totalCount = options.selections.includes('totalCount') ? await this.queryTotalCount(options) : 0;

        if (this.factory.storage === 'external') {
            // If the collection is external, we need to apply the paging options manually
            const manualPagingResult = await applyPagingOptions(this.factory, this.sourceState.context, items, options);

            return { items: manualPagingResult.items, totalCount };
        }
        // totalCount is wrong - fix later
        return { items, totalCount };
    }

    query(options: NodeQueryOptions): AsyncArray<Node> {
        return new AsyncArray(async () => (await this.queryPage(options)).items);
    }

    where(condition?: (node: Node) => AsyncResponse<boolean>): SqlWhereInterface<Node> {
        return new SqlCollection(this, condition);
    }

    async takeOne(condition: (node: Node) => AsyncResponse<boolean>): Promise<Node | null> {
        if (!this.where) throw new Error('takeOne requires a where clause');
        // Execute it with nodes (that we will have in memory most of the time) rather than with a SQL query
        const found = await this.find(condition);
        return found ?? null;
    }
}
