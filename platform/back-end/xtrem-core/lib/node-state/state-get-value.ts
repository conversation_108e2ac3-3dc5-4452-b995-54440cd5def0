/** @ignore */ /** */
import { AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { StateDependency } from '..';
import { BaseCollection, ImmutableCollection, MutableCollection } from '../collections';
import { CollectionProperty, Property, ReferenceArrayProperty, ReferenceProperty } from '../properties';
import { loggers } from '../runtime/loggers';
import { Node } from '../ts-api';
import { lazyLoadedMarker } from './lazy-loaded-marker';
import { NodeState, StateStatus } from './node-state';
import { StateIntern } from './state-intern';
import { StateInvalidate } from './state-invalidate';
import { StateJoin } from './state-join';
import { StateLoad } from './state-load';
import { StateNew } from './state-new';
import { StateOld } from './state-old';
import { StateUtils } from './state-utils';

/**
 * This static class implements the methods to get property values from a node state.
 *
 * @internal
 */
export abstract class StateGetValue {
    /**
     * Returns the property's value if the property is a scalar (any type except reference and collection).
     *
     * This method takes care of lazy loading.
     */
    private static async getScalarValue(state: NodeState, property: Property): Promise<AnyValue> {
        let value = state.values[property.name];

        // If val is undefined because we only have a thunk, try to load the record
        if (value === undefined && state.isThunk) {
            await StateLoad.load(state);
            value = state.values[property.name];
        }
        if (value === lazyLoadedMarker) {
            // The value must be fetched from the database
            value = await StateLoad.resolveLazyLoadedValue(state, property);
        }
        // Undefined is only valid within the framework and should never be exposed to callers.
        if (value === undefined) {
            return property.getTypeDefaultValue();
        }
        if (property.isStringProperty() && property.isLocalized) {
            if (value === null) {
                return '';
            }

            if (
                (value as string).startsWith('{') &&
                (value as string).endsWith('}') &&
                !state.context.processLocalizedTextAsJson &&
                state.factory.storage === 'sql'
            ) {
                const locales = [...state.context.locales, ...['base']];
                const strings = JSON.parse(value as string);
                const locale = locales.find(l => strings[l]);
                if (!locale) return value;
                return strings[locale];
            }
        }

        return value;
    }

    /**
     * Should the reference be loaded in writable mode or not?
     * Only vital reference are loaded in writable mode, and only if their parent is writable.
     */
    private static isReferenceWritable(state: NodeState, property: ReferenceProperty): boolean {
        return property.isMutable && state.isWritable;
    }

    /**
     * If we have references cached in references we have to look them up in the interning cache
     * because the references may be non-vital but may also be loaded from a vital collection.
     */
    private static getReferenceFromStateCache(state: NodeState, property: ReferenceProperty): Node {
        const writable = StateGetValue.isReferenceWritable(state, property);
        const refNode = state.references.get(property.name)!;
        if (refNode) {
            const refState = StateIntern.findState(refNode.$.state, writable);
            if (refState) {
                state.references.set(property.name, refState.node);
                return refState.node;
            }
        }
        return refNode;
    }

    /** Get the value of a reference property */
    static async getReferenceValue(state: NodeState, property: ReferenceProperty): Promise<Node | null> {
        const references = state.references;
        const name = property.name;

        const writable = StateGetValue.isReferenceWritable(state, property);
        // If we already have it in the references map, we are ok.
        if (references.has(name)) {
            return this.getReferenceFromStateCache(state, property);
        }

        if (property.isVital && !state.canBeFetched()) return null;

        // The reference is stored into the target table.
        if (
            property.reverseReference ||
            state.factory.externalStorageManager?.isReverseReferenceProperty(property.name)
        ) {
            // We cannot use a thunk because we have to read the target table to find if it is null or not.
            const joinValues = await StateJoin.getJoinValues(state, property);

            // We have created a transient node for a lookup or default query. The vital children of this node are not included in
            // node create data and the join values to the parent are not defined, so we cannot load the reference.
            // Therefore the reference has to be null.
            if (
                (state.isOnlyForDefaultValues || state.isOnlyForLookup) &&
                Object.keys(joinValues).filter(joinKey => joinValues[joinKey] !== undefined).length === 0
            ) {
                references.set(name, null);
                return null;
            }

            const ref = await state.context.tryRead(property.targetFactory.nodeConstructor, joinValues, {
                forUpdate: writable ? state.forUpdate : false,
                isTransient: state.isTransient,
                isOnlyForLookup: state.isOnlyForLookup,
            });
            references.set(name, ref);
            return ref;
        }

        // The reference is stored in the source table.
        const rawValue = await StateGetValue.getScalarValue(state, property);

        if (!rawValue) {
            // The reference is null
            references.set(name, null);
            return null;
        }

        if (
            state.factory.storage === 'external' &&
            state.factory.keyProperties.map(keyProp => keyProp.name).includes(property.name)
        ) {
            if (
                (state.isOnlyForDefaultValues || state.isOnlyForDuplicate || state.isOnlyForLookup) &&
                state.factory.externalStorageManager?.isKeyPropertyTransient(property.name, rawValue)
            ) {
                // If the parent state is from a default value, duplicate or lookup query then the reference value
                // is allocated a transient value, we do not load the reference as a thunk as the record does not
                // exist and cannot be loaded, we set the reference to null.
                // This is a special case for externally managed nodes, as the key property can be a reference property
                references.set(name, null);
                return null;
            }
        }

        // Load the reference as a thunk.
        // If we only read key values afterwards, we don't need to read the record at all.
        const ref = (
            await StateNew.newReferenceThunk(
                state,
                property.targetFactory,
                property,
                await StateJoin.getJoinValues(state, property),
                writable,
            )
        ).node;
        references.set(name, ref);
        return ref;
    }

    /**
     * If we have references cached in reference arrays we have to look them up in the interning cache
     * because the references may be non-vital but may also be loaded from a vital collection.
     */
    private static getReferenceArrayFromStateCache(state: NodeState, property: ReferenceArrayProperty): Node[] | null {
        const writable = StateGetValue.isReferenceWritable(state, property);
        const refNodes = state.referenceArrays.get(property.name)!;
        if (refNodes == null) {
            return refNodes;
        }
        const refs = refNodes.map(refNode => {
            const refState = StateIntern.findState(refNode.$.state, writable);
            return refState?.node || refNode;
        });

        state.referenceArrays.set(property.name, refs);
        return refs;
    }

    /** Get the value of a reference array property */
    static async getReferenceArrayValue(state: NodeState, property: ReferenceArrayProperty): Promise<Node[] | null> {
        const referenceArrays = state.referenceArrays;
        const name = property.name;

        // If we already have it in the referenceArrays map, we are ok.
        if (referenceArrays.has(name)) return this.getReferenceArrayFromStateCache(state, property);

        // The reference array is stored in the source table.
        const rawValues = await StateGetValue.getScalarValue(state, property);
        if (!rawValues) {
            // The reference array is null
            referenceArrays.set(name, null);
            return null;
        }

        // Load the references in th array as thunks.
        const refs = await asyncArray(rawValues as AnyValue[])
            .map(
                async (_, i) =>
                    (
                        await StateNew.newReferenceThunk(
                            state,
                            property.targetFactory,
                            property,
                            await StateJoin.getArrayJoinValues(state, property, i),
                            false,
                        )
                    ).node,
            )
            .toArray();
        referenceArrays.set(name, refs);
        return refs;
    }

    /** Instantiate the collection value for a collection property. */
    private static createCollection(state: NodeState, property: CollectionProperty): BaseCollection {
        return (property.isTransientInput || property.isMutable) && !state.isOld
            ? new MutableCollection(state.node, property)
            : new ImmutableCollection(state.node, property);
    }

    /**
     * Get the value of a collection property.
     * This call creates the collection instance but does not try to load it. It will be lazy loaded.
     */
    static getCollectionValue(state: NodeState, property: CollectionProperty): BaseCollection {
        const name = property.name;
        let collection = state.collections.get(name);

        if (!collection || !collection.isValid()) {
            collection = this.createCollection(state, property);
            state.collections.set(name, collection);
        }
        return collection;
    }

    /** Returns a placeholder value when callback throws and isOnlyForDefaultValues or isOnlyForLookup is set */
    static getPlaceholderValue(property: Property): AnyValue {
        // Return empty array for collections to avoid a catastrophic call to collection.queryFullCollection
        // which would return the entire table.
        return property.isCollectionProperty() ? [] : null;
    }

    static async getComputedValue(
        state: NodeState,
        property: Property,
        ruleName: 'computeValue' | 'getValue',
    ): Promise<AnyValue> {
        let value: AnyValue = null;
        const fetchValue = (): Promise<AnyValue> => {
            return !property.isLocalized
                ? state.context.withoutLocalizedTextAsJson(() =>
                      state.context.withReadonlyScope(() => property.executeRule(state, ruleName)),
                  )
                : state.context.withReadonlyScope(() => property.executeRule(state, ruleName));
        };
        try {
            if (property.cacheComputedValue) {
                // If the property was invalidated, invalidate the context memory cache and recompute the value.
                if (state.status !== StateStatus.readonly)
                    await StateInvalidate.validatePropertyValue(state, property, [property.name]);
                const cacheKey = state.keyToken;
                const cacheCategory = property.fullName;
                const cacheValue = state.context.cache.fetchValue(cacheCategory, cacheKey);

                if (!cacheValue) {
                    // Cache miss, execte computeValue or getValue and store the result in the context memory cache
                    value = await fetchValue();
                    state.context.cache.storeValue(
                        cacheCategory,
                        cacheKey,
                        { value },
                        { category: cacheCategory, key: cacheKey, getValue: () => ({ value }), ttlInSeconds: 3600 },
                    );
                } else {
                    value = cacheValue;
                }
            } else {
                value = await fetchValue();
            }
        } catch (error) {
            // If this is a transient state loaded from a lookup, default value or getDuplicate query, then we return null on an error
            // executing the getValue
            if (state.isOnlyForDefaultValues || state.isOnlyForLookup || state.isOnlyForDuplicate) {
                loggers.runtime.warn(
                    `failed to ${ruleName} of ${property.fullName} during ${
                        state.isOnlyForDefaultValues ? 'default' : 'lookup'
                    } query: ${error.message}`,
                );
                value = this.getPlaceholderValue(property);
            } else {
                loggers.runtime.error(error.stack);
                throw error;
            }
        }
        return value;
    }

    /** Get the value if the property has a 'getValue' rule. */
    static callGetValue(state: NodeState, property: Property): Promise<AnyValue> {
        const getValue = property.getValue;
        if (typeof getValue !== 'function') throw state.propertySystemError(property, 'getValue is not callable.');
        return this.getComputedValue(state, property, 'getValue');
    }

    /** Get the value if the property has a 'computeValue' rule. */
    static callComputeValue(state: NodeState, property: Property): Promise<AnyValue> {
        const computeValue = property.computeValue;
        if (typeof computeValue !== 'function')
            throw state.propertySystemError(property, 'computeValue is not callable.');
        return this.getComputedValue(state, property, 'computeValue');
    }

    /** Get the value if the property has a 'join' decorator attribute. */
    static async getJoinReferenceValue(state: NodeState, property: ReferenceProperty): Promise<AnyValue> {
        const join = property.decorator.join;
        if (!join) throw property.logicError('no join');

        const joinValues = await StateJoin.getJoinValues(state, property);
        if (property.isNullable) return state.context.tryRead(property.targetFactory.nodeConstructor, joinValues);
        return state.context.read(property.targetFactory.nodeConstructor, joinValues);
    }

    private static async getDelegatedValue(state: NodeState, property: Property): Promise<AnyValue> {
        const { reference, childProperty } = property.getDelegatingInfo();
        const referenceValue = (await state.getPropertyValue(reference)) as Node | null;
        return referenceValue ? referenceValue.$.getValue(childProperty.name) : null;
    }

    /* dispatches according to the type of property. */
    private static async dispatchGetPropertyValue(state: NodeState, property: Property): Promise<AnyValue> {
        // First try delegatesTo
        if (property.delegatesTo) return StateGetValue.getDelegatedValue(state, property);

        // Then give priority to getValue hook if present.
        if (property.getValue) return StateGetValue.callGetValue(state, property);

        // or to computeValue hook if present.
        if (property.computeValue) return StateGetValue.callComputeValue(state, property);

        // or to decorator join if present.
        if (property.isReferenceProperty() && property.decorator.join)
            return StateGetValue.getJoinReferenceValue(state, property);

        // Check that we are reading dependencies in the correct order.
        StateDependency.checkDependencyLimit(state, property);

        // If the property was invalidated, set its new value with the updatedValue rule, if any.
        if (state.status !== StateStatus.readonly)
            await StateInvalidate.validatePropertyValue(state, property, [property.name]);

        // Dispatch based on the property type.
        if (property.isReferenceProperty())
            return StateOld.mapNodeIfOld(state, property, await StateGetValue.getReferenceValue(state, property));

        if (property.isReferenceArrayProperty()) return StateGetValue.getReferenceArrayValue(state, property);
        if (property.isCollectionProperty()) return StateGetValue.getCollectionValue(state, property) || null;
        return StateGetValue.getScalarValue(state, property);
    }

    static getInactiveValue(state: NodeState, property: Property): AnyValue {
        loggers.core.verbose(
            () =>
                `Getting value of inactive property ${property.factory.name}.${
                    property.name
                } depending on service options [${property.serviceOptions.map(o => o.name)}]`,
        );
        if (property.isNullable) {
            return null;
        }
        switch (property.type) {
            case 'boolean':
            case 'string':
            case 'textStream':
            case 'binaryStream':
            case 'collection':
                return property.getTypeDefaultValue();
            default:
                break;
        }
        throw state.propertySystemError(property, 'cannot get the value of an inactive property');
    }

    static checkDeferredProperty(state: NodeState, property: Property): void {
        if (state.isPropertyValueDeferred(property)) {
            throw state.propertySystemError(
                property,
                'Cannot read property as its default value may be deferred and not yet set. Do not read this property or call context.flushDeferredActions()',
            );
        }
    }

    /**
     * Get the value of a property.
     */
    static async getPropertyValue(state: NodeState, property: Property): Promise<AnyValue> {
        await state.context.keepEventLoopHealthy();

        if (property.type === 'collection' || property.name === '_id')
            return this.getPropertyValueSync(state, property);

        try {
            // throw if we are trying to read from a stale state.
            StateUtils.checkNotStale(state);

            // throw if the property is controlled by a disabled service option.
            if (!(await StateUtils.isEnabledByServiceOptions(state, property))) {
                return StateGetValue.getInactiveValue(state, property);
            }
            // throw if the property has a deferred default value and deferred actions have not be flushed
            if (property.deferredDefaultValue) this.checkDeferredProperty(state, property);

            const value = await StateGetValue.dispatchGetPropertyValue(state, property);
            // Check for nullable values. We need this to detect invalid nulls when executing node-level events
            // like createEnd or prepare, or other applicative code between the init phase and the control phase.
            if (
                value == null &&
                !property.isNullable &&
                !state.isTransient &&
                !state.isOnlyForLookup &&
                !state.isOnlyForDuplicate
            ) {
                if (property.decorator.defaultValue !== undefined && state.isOnlyForDefaultValues) {
                    // The 'null' value comes from the 'defaultValue' decorator which is allowed to return
                    // 'null' even for non-nullable properties. The user will have to provide a valid value
                    // before saving the node
                    return value;
                }
                throw StateUtils.requiredPropertyError(state, property);
            }

            return value;
        } catch (err) {
            if (state.isOnlyForDefaultValues || state.isOnlyForLookup || state.isOnlyForDuplicate) return null;
            if (!state.context.disableGetPropertyValueErrorLogger)
                loggers.runtime.error(
                    `${state.factory.name}.${property.name}: error getting property value: ${err.message}`,
                );
            throw err;
        }
    }

    /**
     * Sync variant of getPropertyValue, for _id and collections
     */
    static getPropertyValueSync(state: NodeState, property: Property): AnyValue {
        try {
            StateUtils.checkNotStale(state);

            // TODO: the checks below are preformed on all properties - we won't be able to support all
            // of them on _id and collection - see later

            // throw if the property is controlled by a disabled service option.
            // if (!await (StateUtils.isEnabledByServiceOptions(state, property))) {
            //     return StateGetValue.getInactiveValue(state, property);
            // }

            // Give priority to getValue hook if present.
            // if (property.getValue) return StateGetValue.callGetValue(state, property);

            // or to computeValue hook if present.
            // if (property.computeValue) return StateGetValue.callComputeValue(state, property);

            // Check that we are reading dependencies in the correct order.
            StateDependency.checkDependencyLimit(state, property);

            // If the property was invalidated, set its new value with the updatedValue rule, if any.
            // if (state.status !== 'readonly')
            //     await (StateInvalidate.validatePropertyValue(state, property, [property.name]));

            if (property.name === '_id') {
                // _id is always set, except with external storage. Return '' in this case
                if (state.values._id) return state.values._id;

                return state.factory.storage === 'external'
                    ? state.factory.externalStorageManager?.getId(state.context, state.values)
                    : null;
            }
            if (property.isCollectionProperty()) {
                return this.getCollectionValue(state, property);
            }
            throw property.systemError('cannot read property value synchronously');
        } catch (err) {
            if (!state.context.disableGetPropertyValueErrorLogger)
                loggers.runtime.error(
                    `${state.factory.name}.${property.name}: error getting property value: ${err.message}`,
                );
            throw err;
        }
    }

    // for reference and reference array properties where the target node has a naturalKey
    // we resolve the value exported to the naturalKey property values concatenated by pipes (|)
    static getNaturalKeyValue(state: NodeState): Promise<string> {
        if (!state.factory.naturalKey) throw state.factory.logicError('no natural key');
        return asyncArray(state.factory.naturalKey)
            .map(async name => {
                const property = state.factory.findProperty(name);
                const value = await state.getPropertyValue(property);
                if (property.isReferenceProperty()) {
                    const node = value as Node;
                    return node ? this.getNaturalKeyValue(node.$.state) : '';
                }
                return value;
            })
            .join('|');
    }
}
