/** @ignore */ /** */
import { AnyRecord, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { LogicError } from '@sage/xtrem-shared';
import { BaseCollection } from '../collections';
import { Property, ReferenceProperty } from '../properties';
import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../runtime/node-factory';
import { Node } from '../ts-api';
import { NodeState, StateStatus } from './node-state';
import { StateDependency } from './state-dependency';
import { StateGetValue } from './state-get-value';
import { StateIntern } from './state-intern';
import { StateSetValue } from './state-set-value';
import { StateUpdate } from './state-update';
import { StateUtils } from './state-utils';

/**
 * This static class provides the methods that create and initialize a state for a new node.
 * (created with context.create, not read from the database)
 *
 * @internal
 */
export abstract class StateInit {
    static queueDeferredDefaultValue(state: NodeState, property: Property): void {
        if (state.isTransient || state.isOnlyForDefaultValues || state.isOnlyForDuplicate || state.isOnlyForLookup)
            return;
        state.deferredProperties[property.name] = true;
        const deferredDefaultValue = property.deferredDefaultValue;
        state.context.transaction.queueDeferredAction(async () => {
            const value =
                typeof deferredDefaultValue === 'function'
                    ? await property.executeRule(state, 'deferredDefaultValue')
                    : deferredDefaultValue;
            await StateUpdate.atomicUpdate(state, { [property.name]: value });
            delete state.deferredProperties[property.name];
        });
    }

    static async getSingleReferenceDefaultValue(state: NodeState, property: ReferenceProperty): Promise<Node | null> {
        const nodes = await state.context.lookup({ node: state.node, property }, { first: 2 }).toArray();
        return nodes.length === 1 ? nodes[0] : null;
    }

    /**
     * Returns the default value for a property.
     * It calls the `defaultValue` rules, on the property or its data type, if any.
     *
     * This method is also called when the `updatedValue` rule returns undefined.
     */
    static getDefaultValue(state: NodeState, property: Property): Promise<AnyValue> {
        return StateDependency.withDependenciesRestriction(state, property, async () => {
            try {
                const defaultValue = property.defaultValue;
                if (defaultValue !== undefined && (await StateUtils.isEnabledByServiceOptions(state, property))) {
                    if (typeof defaultValue === 'function') {
                        // Note : the node is set as read-only when invoking the defaultValue() function to protect it.
                        // A 'defaultValue' function should only be used to return a value and must not update the object.
                        if (!property.isLocalized) {
                            // if the property is not a localized string => extract the localized value
                            return await state.context.withoutLocalizedTextAsJson(() =>
                                state.context.withReadonlyScope(() => property.executeRule(state, 'defaultValue')),
                            );
                        }
                        return await state.context.withReadonlyScope(() => property.executeRule(state, 'defaultValue'));
                    }
                    return property.defaultValue;
                }
                if (property.dataType) {
                    // The property has no default value.
                    // Return the default value defined in the data type if there is one.
                    const value = await state.context.withReadonlyScope(() =>
                        property.dataType?.defaultValue(state.node),
                    );
                    // Because References properties that have a datatype and no default value have to pass to the next if
                    if (value !== undefined) {
                        return value;
                    }
                }
                if (property.isReferenceProperty() && !property.isNullable) {
                    if (property.isMutable) return {};
                    if (property.targetFactory.defaultsToSingleMatch && state.isOnlyForDefaultValues) {
                        return await this.getSingleReferenceDefaultValue(state, property);
                    }
                }
            } catch (error) {
                // If this is a transient state loaded from a lookup, default value or getDuplicate query, then we return null on an error
                // executing the getDefaultValue
                if (state.isOnlyForDefaultValues || state.isOnlyForLookup || state.isOnlyForDuplicate) {
                    loggers.runtime.warn(
                        `failed to get default value of ${property.fullName} during ${
                            state.isOnlyForDefaultValues ? 'default' : 'lookup'
                        } query: ${error.message}`,
                    );
                    return StateGetValue.getPlaceholderValue(property);
                }
                loggers.runtime.error(error.stack);
                throw error;
            }
            return undefined;
        });
    }

    /** Initializes a property with a value passed to context.create */
    private static async initProperty(
        state: NodeState,
        property: Property,
        path: string[],
        value: AnyValue,
    ): Promise<void> {
        await StateUtils.withValidationErrorRethrow(state, property, path, async () => {
            if (property.name === '_id') return;

            if (property.isAutoIncrement && value) {
                throw state.propertyDataInputError(property, {
                    message: 'a value is provided for an auto-increment property',
                    key: '@sage/xtrem-core/value-provided-auto-increment',
                });
            }

            if (StateUtils.isPropertyGetterOnly(property)) return;

            await StateDependency.withDependenciesRestriction(state, property, async () => {
                let useDefault = value === undefined || (!property.isNullable && value === null);
                if (property.deferredDefaultValue) {
                    // Force default value rule if the value is empty string
                    if (value === '') useDefault = true;
                    // But skip default rules if state is transient, so that getDefaults queries don't return nanoids.
                    if (state.isTransient) useDefault = false;
                }
                const val = useDefault ? await StateInit.getDefaultValue(state, property) : value;
                await StateSetValue.setPropertyValue(state, property, path, val);
                if (useDefault && property.deferredDefaultValue !== undefined) {
                    if (!property.isLocalized) {
                        // if the property is not a localized string => extract the localized value
                        await state.context.withoutLocalizedTextAsJson(() =>
                            this.queueDeferredDefaultValue(state, property),
                        );
                    } else {
                        this.queueDeferredDefaultValue(state, property);
                    }
                }
            });
        });
    }

    /** Initializes a state with the values passed to context.create */
    private static async init(state: NodeState, path: string[], initValues: AnyRecord): Promise<void> {
        const values = StateUtils.restructureDelegatedInputValues(state.factory, initValues);

        const properties = [...state.factory.properties, ...Object.values(state.factory.publishedSystemProperties)];
        await asyncArray(properties).forEach(property =>
            StateInit.initProperty(state, property, [...path, property.name], values[property.name]),
        );
    }

    static cannotCreateAbstractNode(factory: NodeFactory): LogicError {
        return new LogicError(`Could not create an instance of '${factory.name}', this node is declared as abstract`);
    }

    /** Creates a new state for a context.create call */
    static async newFromContextCreate(
        context: Context,
        factory: NodeFactory,
        path: string[],
        data: AnyRecord,
        options?: {
            isTransient?: boolean;
            isOnlyForDefaultValues?: boolean;
            isOnlyForDuplicate?: boolean;
            duplicates?: Node;
            writable?: boolean;
            isOnlyForLookup?: boolean;
            collection?: BaseCollection;
        },
    ): Promise<NodeState> {
        if (factory.isAbstract) {
            if (typeof data._constructor === 'string') {
                const concreteFactory = context.application.getFactoryByName(data._constructor);
                if (concreteFactory.isAbstract) throw this.cannotCreateAbstractNode(concreteFactory);
                if (!concreteFactory.isSubNodeOf(factory.name))
                    throw concreteFactory.logicError(
                        `factory ${concreteFactory.name} is not a sub-node of ${factory.name}`,
                    );
                return StateInit.newFromContextCreate(context, concreteFactory, path, data, options);
            }
            // baseClasses can't be created directly.
            throw this.cannotCreateAbstractNode(factory);
        }
        if (
            !options?.isTransient &&
            !options?.isOnlyForDefaultValues &&
            !options?.isOnlyForLookup &&
            !options?.isOnlyForDuplicate
        ) {
            factory.checkCanCreate(context);
        }

        // We dont fetch forUpdate because the created vital nodes don't exist yet.
        context.prefetcher.visit(factory, false, data);

        const keyValues = factory.getKeyValues(context, data, {
            allocateTransient: true,
            isTransient: options?.isTransient,
            isOnlyForDefaultValues: options?.isOnlyForDefaultValues,
            isOnlyForDuplicate: options?.isOnlyForDuplicate,
            isOnlyForLookup: options?.isOnlyForLookup,
            collection: options?.collection,
        });

        let state = new NodeState(context, factory, keyValues, StateStatus.constructed, {
            isTransient: options?.isTransient,
            isOnlyForDefaultValues: options?.isOnlyForDefaultValues,
            isOnlyForDuplicate: options?.isOnlyForDuplicate,
            duplicates: options?.duplicates,
            isOnlyForLookup: options?.isOnlyForLookup,
            collection: options?.collection,
        });
        state = StateIntern.intern(state, { forCreate: true });

        await StateInit.init(state, path, data);

        // A "normal" node should be in 'constructed' state but a content-addressable node
        // will already be in 'created' state if its content matches an existing node.
        if (state.status !== StateStatus.constructed && !factory.isContentAddressable)
            throw state.logicError(`invalid node status at the end of create: ${state.status}`);
        state.status = StateStatus.created;

        if (
            state.factory.events.createEnd &&
            !state.isOnlyForDefaultValues &&
            !state.isOnlyForLookup &&
            !state.isOnlyForDuplicate
        ) {
            await state.factory.executeRule(state, 'createEnd');
        }

        return state;
    }
}
