/** @ignore */ /** */
import { AnyNonNullableValue, AnyRecord, AnyValue, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Decimal } from '@sage/xtrem-decimal';
import { Dict } from '@sage/xtrem-shared';
import { DependingItem, topoSort } from '@sage/xtrem-toposort';
import * as _ from 'lodash';
import { isEqual } from 'lodash';
import { MutableCollection } from '../collections';
import { CollectionProperty, Property, ReferenceArrayProperty, ReferenceProperty } from '../properties';
import { valueComparator } from '../runtime/array-utils';
import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { isCompound, isScalar } from '../runtime/utils';
import { Node, NodeUpdateData } from '../ts-api';
import { lazyLoadedMarker } from './lazy-loaded-marker';
import { NodeState, StateStatus } from './node-state';
import { StateDependency } from './state-dependency';
import { StateGetValue } from './state-get-value';
import { StateInit } from './state-init';
import { StateIntern } from './state-intern';
import { StateInvalidate } from './state-invalidate';
import { StateJoin } from './state-join';
import { StateLoad } from './state-load';
import { StateNew } from './state-new';
import { StateOld } from './state-old';
import { StateUtils } from './state-utils';

/**
 * This static class provides the methods to set property values.
 *
 * @internal
 */
export abstract class StateSetValue {
    /** Sets a raw value (column value) in the state's value */
    static async setRawValue(state: NodeState, property: Property, value: AnyValue): Promise<void> {
        // If the state is a thunk and the property is not a key property
        // we must load the record, so that we don't lose the value when loading later.
        if (state.isThunk && !state.factory.keyProperties.includes(property)) await StateLoad.load(state);

        // Assign the value
        if (Array.isArray(value)) {
            state.values[property.name] = Object.freeze([...value]);
        } else {
            state.values[property.name] = value;
        }
    }

    /** Sets a reference, either a vital or non-vital one */
    private static async setReference(
        state: NodeState,
        property: ReferenceProperty,
        reference: Node | null,
    ): Promise<void> {
        state.references.set(property.name, reference);
        if (reference) {
            await StateJoin.assignJoinedProperties(state, property, reference);
        } else {
            await StateSetValue.setRawValue(state, property, null);
        }
    }

    /** Sets a reference array */
    private static async setReferenceArray(
        state: NodeState,
        property: ReferenceArrayProperty,
        references: Node[] | null,
    ): Promise<void> {
        state.referenceArrays.set(property.name, references);
        if (references != null) {
            const ids = references.map(reference => reference._id);
            await this.setRawValue(state, property, ids);
        } else {
            await StateSetValue.setRawValue(state, property, null);
        }
    }

    /** Creates a new state for a vital reference, and assign it */
    private static async setNewVitalReference(
        state: NodeState,
        property: ReferenceProperty,
        path: string[],
        data: AnyRecord,
    ): Promise<void> {
        // Do not modify data if node is content addressable
        if (!property.targetFactory.isContentAddressable) {
            StateJoin.initVitalReferenceJoin(state, property, data);
        }

        // Create the vital reference as a writable node
        const vitalRef = await StateInit.newFromContextCreate(state.context, property.targetFactory, path, data, {
            isOnlyForDefaultValues: state.isOnlyForDefaultValues,
            isOnlyForDuplicate: state.isOnlyForDuplicate,
            isOnlyForLookup: state.isOnlyForLookup,
            isTransient: state.isTransient,
        });
        await StateSetValue.setReference(state, property, vitalRef.node);
    }

    /** Sets a value for a vital reference */
    private static async setVitalReferenceValue(
        state: NodeState,
        property: ReferenceProperty,
        path: string[],
        value: AnyValue,
    ): Promise<void> {
        // Scalar values (integer, string, date, ...) cannot be assigned to a vital reference.
        // We can only assign an object containing a partial paylod for the vital child, or null/undefined.
        if (value != null && isScalar(value)) {
            throw state.propertyDataInputError(property, {
                message: 'invalid value for vital reference: typeof={{type}}, value={{value}}',
                key: '@sage/xtrem-core/invalid-value-vital-reference',
                data: { type: typeof value, value },
            });
        }

        if (value) {
            const reference = await StateGetValue.getReferenceValue(state, property);
            if (reference) {
                if (property.targetFactory.isContentAddressable) {
                    // If node is content-addressable, do not clobber the reference.
                    // Instead, create a new node with the merged values.
                    // Reference may be a thunk so we have to load it to get the values.
                    if (reference.$.state.isThunk) await StateLoad.load(reference.$.state);
                    // Load lazy loaded properties before computing the hash
                    await StateLoad.resolveLazyLoadedValues(reference.$.state);
                    const oldValues = reference.$.state.values;
                    const mergedValues = _.omit({ ...oldValues, ...(value as object) }, '_id');
                    const newValuesHash = property.targetFactory.getValuesHash(mergedValues);
                    const oldValuesHash = oldValues._valuesHash || property.targetFactory.getValuesHash(oldValues);
                    if (newValuesHash !== oldValuesHash) {
                        await StateSetValue.setNewVitalReference(state, property, path, mergedValues);
                    }
                } else {
                    await reference.$.state.set(value as AnyRecord);
                }
            } else {
                await StateSetValue.setNewVitalReference(state, property, path, value as AnyRecord);
            }
        } else {
            const reference = await StateGetValue.getReferenceValue(state, property);
            if (reference) {
                state.references.delete(property.name);
                if (property.targetFactory.isContentAddressable) {
                    state.values[property.name] = null;
                } else {
                    // remove the state and its vital children from the interning cache
                    await StateIntern.removeStateWithVitalChildren(state);
                    // Only delete states that have been saved already
                    if (!reference.$.isNew) {
                        await reference.$.state.delete({ path: [property.name] });
                    } else {
                        StateIntern.removeState(reference.$.state);
                    }
                }
            }
        }
    }

    /** Sets a value for a vital reference provided as a Node instance */
    private static async setVitalNodeReferenceValue(
        state: NodeState,
        property: ReferenceProperty,
        value: Node,
    ): Promise<void> {
        const targetFactory = property.targetFactory;
        if (
            targetFactory.storage === 'sql' ||
            targetFactory.storage === 'external' ||
            targetFactory.keyPropertyNames.length > 0
        ) {
            const oldReference = (await state.getPropertyValue(property)) as Node | null;
            // Only delete states that have been saved already
            if (oldReference && !oldReference.$.factory.isContentAddressable) {
                if (!oldReference.$.isNew) {
                    await oldReference.$.state.delete({ path: [property.name] });
                } else {
                    StateIntern.removeState(oldReference.$.state);
                }
            }

            state.references.set(property.name, value);
            if (property.isVital) {
                // set the parent reference on the new reference
                const parentProperty = targetFactory.vitalParentProperty;
                value.$.state.values[parentProperty.name] = state.values._id;
            }
        } else if (property.setValue) {
            await property.executeRule(state, 'setValue', value);
        } else {
            throw state.propertyDataInputError(property, {
                message: 'cannot update reference, decorator lacks key information',
                key: '@sage/xtrem-core/decorators-lacks-key-information',
            });
        }
    }

    /** Looks up a reference from the context's interning cache */
    private static resolveReference(state: NodeState): Node {
        return (StateIntern.findState(state, state.isWritable) || state).node;
    }

    /** Sets a non vital reference value */
    private static async setNonVitalReferenceValue(
        state: NodeState,
        property: ReferenceProperty,
        value: AnyValue,
    ): Promise<void> {
        // value is falsy for a sql node or the current and target factory is external
        // and the value is falsy (0, '', or false...) but not null or undefined.
        // These may be valid values for the external node, so we set the raw value in this case,
        // so that when the reference is loaded all join values are provided.
        if (
            value ||
            (state.factory.storage === 'external' && property.targetFactory.storage === 'external' && value != null)
        ) {
            if (value instanceof Node) {
                const reference = StateSetValue.resolveReference(value.$.state);
                await StateSetValue.setReference(state, property, reference);
            } else if (isCompound(value)) {
                if (typeof value._id === 'string' && property.targetFactory.storage === 'sql') {
                    value._id = parseInt(value._id, 10);
                }
                const reference = (
                    await StateNew.newReferenceThunk(state, property.targetFactory, property, value, false)
                ).node;
                await StateSetValue.setReference(state, property, reference);
            } else if (property.targetFactory.storage === 'sql' && typeof value === 'string') {
                await this.setNonVitalReferenceValue(state, property, property.targetFactory.parseNodeId(value));
            } else {
                await StateSetValue.setRawValue(state, property, value);
                if (Number(value) > 0) {
                    state.context.prefetcher.visitSysId(property.targetFactory, false, String(value));
                }
            }
        } else {
            // If we have a falsy value other than null, caller did something bad. So throw an error.
            if (state.factory.storage !== 'external' && value !== null)
                throw state.propertyDataInputError(property, {
                    message: 'invalid reference value: {{type}}, {{value}}',
                    key: '@sage/xtrem-core/invalid-reference-value',
                    data: { type: typeof value, value },
                });

            await StateSetValue.setReference(state, property, null);
        }
    }

    /** Sets a non vital reference value */
    private static async setReferenceArrayPropertyValue(
        state: NodeState,
        property: ReferenceArrayProperty,
        value: AnyValue,
    ): Promise<void> {
        if (value) {
            if (!Array.isArray(value))
                throw state.propertyDataInputError(property, {
                    message: 'invalid reference array value: {{type}}, {{value}}',
                    key: '@sage/xtrem-core/invalid-reference-array-value',
                    data: { type: typeof value, value },
                });

            const checkReferenceExists = (reference: AnyRecord | Node): void => {
                if ((typeof reference._id === 'number' && reference._id < 0) || Number.isNaN(reference._id))
                    throw state.propertyDataInputError(property, {
                        message: 'invalid value supplied. Record in array does not exist',
                        key: '@sage/xtrem-core/record-does-not-exist',
                    });
            };
            if (value.every((v: AnyValue) => v instanceof Node)) {
                const references = value.map((v: Node) => {
                    checkReferenceExists(v);
                    return StateSetValue.resolveReference(v.$.state);
                });
                await StateSetValue.setReferenceArray(state, property, references);
            } else if (value.every((v: AnyValue) => isCompound(v))) {
                const references = await asyncArray(value)
                    .map(async (v: AnyRecord) => {
                        if (typeof v._id === 'string' && property.targetFactory.storage === 'sql') {
                            v._id = parseInt(v._id, 10);
                        }

                        checkReferenceExists(v);

                        return (await StateNew.newReferenceThunk(state, property.targetFactory, property, v, false))
                            .node;
                    })
                    .toArray();

                await StateSetValue.setReferenceArray(state, property, references);
            } else {
                // reference ids may be passed as string - convert them to integer
                const val = value.map((v: AnyValue) => {
                    const ref = property.targetFactory.storage === 'sql' && typeof v === 'string' ? parseInt(v, 10) : v;
                    checkReferenceExists({ _id: ref });
                    return ref;
                });

                await StateSetValue.setRawValue(state, property, val);
            }
        } else {
            await StateSetValue.setReferenceArray(state, property, null);
        }
    }

    /** Sets a value for a reference property, vital or non vital */
    private static async setReferencePropertyValue(
        state: NodeState,
        property: ReferenceProperty,
        path: string[],
        value: AnyValue,
    ): Promise<void> {
        if (property.isMutable) {
            if (value instanceof Node) {
                await StateSetValue.setVitalNodeReferenceValue(state, property, value);
            } else {
                await StateSetValue.setVitalReferenceValue(state, property, path, value);
            }
        } else {
            await StateSetValue.setNonVitalReferenceValue(state, property, value);
        }
    }

    /** Updates the contents of a collection. (vital collection only) */
    private static async updateCollectionItems(
        state: NodeState,
        property: CollectionProperty,
        path: string[],
        values: AnyRecord[],
    ): Promise<void> {
        const collection = await state.getPropertyValue(property);
        if (!(collection instanceof MutableCollection))
            throw state.propertyDataInputError(property, {
                message: 'cannot update immutable collection',
                key: '@sage/xtrem-core/cannot-update-immutable-collection',
            });

        await collection.update(path, values);
    }

    /** Converts an array of Node instances to an array of node values, if necessary */
    // TODO: see if we can eliminate this API option (passing node instances).
    private static unwrapNodes(values: (Node | AnyRecord)[]): AnyRecord[] {
        if (values[0] instanceof Node)
            return values.map((value: Node) => ({ ...value.$.state.values, _id: undefined }));
        return values as AnyRecord[];
    }

    /** Sets a value for a collection property */
    private static async setCollectionPropertyValue(
        state: NodeState,
        property: CollectionProperty,
        path: string[],
        values: (AnyRecord | Node)[],
    ): Promise<void> {
        if (!Array.isArray(values)) {
            throw state.propertyDataInputError(property, {
                message: 'invalid collection value: not an array',
                key: '@sage/xtrem-core/invalid-collection-value-not-array',
            });
        }
        if (property.setValue) {
            await property.executeRule(state, 'setValue', values);
        } else {
            await StateSetValue.updateCollectionItems(state, property, path, StateSetValue.unwrapNodes(values));
        }
    }

    /** Sets a value for a localized text */
    private static setLocalizedValue(state: NodeState, property: Property, values: AnyValue): AnyValue {
        let newValue = values;
        if (state.context.processLocalizedTextAsJson && values && state.values[property.name]) {
            // We have to merge the 2 jsons in order not to lose texts stored in the database:
            const oldLocalizedText = state.values[property.name] as string;
            const newLocalizedText = values as string;
            if (oldLocalizedText.startsWith('{') && newLocalizedText.startsWith('{')) {
                // The oldValue was read with context.processLocalizedTextAsJson = true
                try {
                    const oldValues = JSON.parse(oldLocalizedText);
                    const newValues = JSON.parse(newLocalizedText);
                    newValue = JSON.stringify({ ...oldValues, ...newValues });
                } catch {
                    throw state.propertyDataInputError(property, {
                        message: 'cannot parse the localized text {{value}}',
                        key: '@sage/xtrem-core/cannot-parse-localized-text',
                        data: { value: `${newLocalizedText}` },
                    });
                }
            }
        }
        return newValue;
    }

    /** Throws if the property cannot be written to */
    private static async getValueOfNotWritableProperty(
        state: NodeState,
        property: Property,
        value: AnyValue,
    ): Promise<AnyValue> {
        if (value !== null && !(await StateUtils.isEnabledByServiceOptions(state, property))) {
            loggers.core.verbose(
                () =>
                    `Setting value of inactive property ${property.factory.name}.${
                        property.name
                    } depending on service options [${property.serviceOptions.map(
                        o => o.name,
                    )}]. Value has not been changed`,
            );
            // return the unchanged value
            return state.values[property.name];
        }
        if (StateUtils.isPropertyGetterOnly(property))
            throw state.propertyDataInputError(property, {
                message: 'cannot set value on computed property',
                key: '@sage/xtrem-core/cannot-set-value-on-computed-property',
            });
        return value;
    }

    /** Returns whether two values for a reference property are equal or not */
    private static areReferencePropertyValuesEqual(
        property: ReferenceProperty,
        nodeVal: Node,
        value: AnyValue,
    ): boolean {
        if (property.join && !(value instanceof Node)) {
            const joinPropName = Object.keys(property.join).find(
                propName => property.join?.[propName] === property.name,
            );
            if (joinPropName) {
                return valueComparator((nodeVal as unknown as Dict<AnyValue>)[joinPropName], value) === 0;
            }
        }

        return valueComparator(nodeVal, value) === 0;
    }

    /**
     * Checks if the new values of localized string is part of current values.
     * @param context - The context object.
     * @param property - The reference property.
     * @param newVal - The new value.
     * @param oldVal - The old value.
     * @returns A promise that resolves to a boolean indicating whether the values are equal.
     */

    private static areLocalizedStringValueUnchanged(
        context: Context,
        newVal: AnyNonNullableValue,
        oldVal: AnyNonNullableValue,
    ): boolean {
        if (context.processLocalizedTextAsJson) {
            if (!((oldVal as string).startsWith('{') && (oldVal as string).endsWith('}'))) return false;
            // if the value is a string and starts with '{' and ends with '}' it means it is a JSON
            if ((newVal as string).startsWith('{') && (newVal as string).endsWith('}')) {
                // We have to merge the 2 jsons in order not to lose texts stored in the database:
                const oldLocalizedText = oldVal as string;
                const newLocalizedText = newVal as string;

                const oldValues = JSON.parse(oldLocalizedText);
                const newValues = JSON.parse(newLocalizedText);
                const newValue = JSON.stringify({ ...oldValues, ...newValues });
                return _.isEqual(newValue, oldLocalizedText);
            }

            // if the value is a string
            const locales = [...context.locales, ...['base']];
            const oldStrings = JSON.parse(oldVal as string);
            const locale = locales.find(l => oldStrings[l]?.length);
            return locale ? newVal === oldStrings[locale] : newVal === '';
        }
        return valueComparator(oldVal, newVal, context.locales) === 0;
    }

    private static async areContentAddressableValuesEqual(
        context: Context,
        property: ReferenceProperty,
        newVal: AnyNonNullableValue,
        oldVal: AnyNonNullableValue,
    ): Promise<boolean> {
        const getValues = async (val: AnyNonNullableValue): Promise<Dict<AnyValue>> => {
            if (val instanceof Node) {
                await StateLoad.resolveLazyLoadedValues(val.$.state);
                return val.$.state.values;
            }
            if (typeof val === 'number') {
                const node = await context.read(property.targetFactory.nodeConstructor, { _id: val });
                await StateLoad.resolveLazyLoadedValues(node.$.state);
                return node.$.state.values;
            }
            if (typeof val !== 'object') throw property.logicError(`invalid value: ${typeof val}, ${val}`);
            return val as Dict<AnyValue>;
        };

        const oldValues = await getValues(oldVal);
        const newValues = await getValues(newVal);

        const oldHash = property.targetFactory.getValuesHash(oldValues);
        const newHash = property.targetFactory.getValuesHash({ ...oldValues, ...newValues });
        return oldHash === newHash;
    }

    /** Returns whether two values for a property are equal or not */
    private static arePropertyValuesEqual(
        context: Context,
        property: Property,
        newVal: AnyValue,
        oldVal: AnyValue,
    ): Promise<boolean> {
        // handle null and undefined first, treat them as different.
        if (newVal == null || oldVal == null) return Promise.resolve(newVal === oldVal);

        if (property.isJsonProperty()) {
            return Promise.resolve(isEqual(newVal, oldVal));
        }

        if (property.isReferenceProperty() && property.targetFactory.isContentAddressable) {
            return this.areContentAddressableValuesEqual(context, property, newVal, oldVal);
        }

        if (property.isReferenceProperty() && newVal instanceof Node) {
            return Promise.resolve(StateSetValue.areReferencePropertyValuesEqual(property, newVal, oldVal));
        }

        if (property.isLocalized)
            return Promise.resolve(StateSetValue.areLocalizedStringValueUnchanged(context, newVal, oldVal));

        if (property.isRangeProperty() && newVal.toString && oldVal.toString) {
            return Promise.resolve(newVal.toString() === oldVal.toString());
        }

        return Promise.resolve(valueComparator(oldVal, newVal) === 0);
    }

    /**
     * Returns if a property is ready to be set.
     * Perform all the necessary checks.
     * Returns false if the property value does not change.
     */
    private static async isPropertyReadyToBeSet(
        state: NodeState,
        property: Property,
        inputValue: AnyValue,
    ): Promise<{ isPropertyReadyToBeSet: boolean; value: AnyValue }> {
        let value = inputValue;
        // Ignore undefined values, computed, system and disabled properties
        if (
            value === undefined ||
            property.name === '_etag' ||
            !(await StateUtils.isEnabledByServiceOptions(state, property))
        ) {
            return { isPropertyReadyToBeSet: false, value };
        }

        if (value != null && !property.isValueTypeValid(value)) {
            if (!property.needsTypeConversion(value))
                throw state.propertyDataInputError(property, {
                    message: 'invalid value: {{type}}, {{value}}',
                    key: '@sage/xtrem-core/invalid-value',
                    data: { type: typeof value, value },
                });

            // Map the input value:
            value = property.mapInputValue(value);
        }

        StateUtils.checkNotStale(state);

        if (property.isReferenceProperty() && typeof value === 'string' && value[0] === '#') {
            const reference = await state.context.read(
                property.targetFactory.nodeConstructor,
                property.targetFactory.parseNodeId(value),
            );
            value = reference.$.id;
        } else {
            value = await StateSetValue.getValueOfNotWritableProperty(state, property, value);
        }

        if (state.status !== StateStatus.constructed && property.type !== 'collection') {
            // compare old and new values, if the same then nothing to do
            let oldVal = state.values[property.name];

            // If we have a value (or value is null) and oldVal is lazy loaded, we must load oldVal to compare it with the new value.
            if (value !== undefined && oldVal === lazyLoadedMarker) {
                oldVal = await StateLoad.resolveLazyLoadedValue(state, property);
            }

            if (await StateSetValue.arePropertyValuesEqual(state.context, property, value, oldVal))
                return { isPropertyReadyToBeSet: false, value };
        }

        return { isPropertyReadyToBeSet: true, value };
    }

    /**
     * Performs pre and post actions around the assignment of the property value.
     * Takes care of creating the old state before assigning, if necessary.
     * Updates the status and invalidates dependant properties, if necessary.
     */
    private static async withChange(
        state: NodeState,
        property: Property,
        body: () => AsyncResponse<void>,
    ): Promise<void> {
        if (state.status === StateStatus.updatable) {
            // It's time to backup the values into oldData
            await StateOld.keepOldState(state);
        }

        await body();

        if (state.status === StateStatus.updatable) {
            state.status = StateStatus.modified;
            state.skipSave = false;
        }

        StateInvalidate.clearInvalidated(state, property);

        if (
            state.status !== StateStatus.constructed ||
            state.collection?.sourceNode.$.state.status === StateStatus.updatable ||
            state.collection?.sourceNode.$.state.status === StateStatus.modified
        )
            await StateInvalidate.invalidateDependantProperties(state, property);
    }

    /**
     * Adapts the property value.
     * This is called after assigning the raw value, so that the adaptValue rule can retrieve the value.
     */
    private static async adaptPropertyValue(state: NodeState, property: Property, value: AnyValue): Promise<void> {
        if (value == null) return;

        let val: AnyValue = value;

        // convert JS numbers if property type is decimal.
        if (property.type === 'decimal' && typeof val === 'number') val = Decimal.make(val);

        // Call adaptValue method of the data type first.
        const propDataType = property.dataType;
        // Do not adapt foreign node properties ?
        if (propDataType && !property.isForeignNodeProperty()) {
            val = await StateDependency.withDependenciesRestriction(state, property, () =>
                propDataType.adaptValue(state.node, val),
            );
            await StateSetValue.setRawValue(state, property, val);
        }

        // Call adaptValue method of the property.
        if (property.adaptValue) {
            val = await StateDependency.withDependenciesRestriction(state, property, () =>
                property.executeRule(state, 'adaptValue', val),
            );
            await StateSetValue.setRawValue(state, property, val);
        }
        /** encrypt property value */
        if (property.isStringProperty() && property.isStoredEncrypted) {
            await StateSetValue.setRawValue(
                state,
                property,
                state.context.vault.recordValue(await state.context.vault.encrypt(val as string)),
            );
        }
    }

    /** Returns whether the property is protected by a factory or not */
    private static async isProtectedByVendor(state: NodeState, property: Property): Promise<boolean> {
        if (
            property.isOwnedByCustomer ||
            ConfigManager.current.ignoreVendorProtection ||
            !state.factory.hasVendorProperty
        )
            return false;
        const vendor = await state.vendor;
        return !!vendor;
    }

    /**
     * Throws if the property is not writable.
     * This method takes care of the readonly status, frozen state and the factory.
     */
    private static async ensurePropertyIsWritable(
        state: NodeState,
        property: Property,
        value: AnyValue,
    ): Promise<void> {
        if (state.isEffectivelyReadonly && !state.isTransient)
            throw state.dataInputError({
                message: 'node is readonly',
                key: '@sage/xtrem-core/node-is-readonly',
            });

        if (
            state.status !== StateStatus.constructed &&
            state.status !== StateStatus.created &&
            !state.isOnlyForLookup &&
            !state.isOnlyForDefaultValues &&
            !state.isOnlyForDuplicate
        ) {
            if (await state.isNodeFrozen())
                throw state.dataInputError({
                    message: 'node is frozen',
                    key: '@sage/xtrem-core/node-is-frozen',
                });
            if ((await state.isPropertyFrozen(property)) && value !== state.values[property.name])
                throw state.propertyDataInputError(property, {
                    message: 'cannot set value on frozen property',
                    key: '@sage/xtrem-core/cannot-set-value-on-frozen-property',
                });
            if (await StateSetValue.isProtectedByVendor(state, property))
                if (!property.isCollectionProperty()) {
                    throw state.propertyDataInputError(property, {
                        message: 'cannot set value protected by a vendor',
                        key: '@sage/xtrem-core/cannot-set-value-protected-by-a-vendor',
                    });
                }
        }
    }

    /** Sets a property value. */
    static async setPropertyValue(
        state: NodeState,
        property: Property,
        path: string[],
        inputValue: AnyValue,
    ): Promise<void> {
        await StateUtils.withValidationErrorRethrow(state, property, path, async () => {
            // eslint-disable-next-line prefer-const
            let { isPropertyReadyToBeSet, value } = await StateSetValue.isPropertyReadyToBeSet(
                state,
                property,
                inputValue,
            );
            if (!isPropertyReadyToBeSet) return;

            if (property.columnName) state.dirtyColumnNames[property.columnName] = true;

            await StateSetValue.ensurePropertyIsWritable(state, property, value);
            await StateSetValue.withChange(state, property, async () => {
                if (property.setValue) {
                    await property.executeRule(state, 'setValue', value);
                    return;
                }
                if (property.isCollectionProperty()) {
                    await StateSetValue.setCollectionPropertyValue(state, property, path, value as AnyRecord[]);
                } else if (property.isReferenceProperty()) {
                    await StateSetValue.setReferencePropertyValue(state, property, path, value);
                } else if (property.isReferenceArrayProperty()) {
                    await StateSetValue.setReferenceArrayPropertyValue(state, property, value);
                } else if (property.isLocalized) {
                    value = StateSetValue.setLocalizedValue(state, property, value);
                } else {
                    await StateSetValue.setRawValue(state, property, value);
                }
                await StateSetValue.adaptPropertyValue(state, property, value);

                // Update the keys of the state in the interning cache
                StateIntern.updatePropertyInterningKeys(state, property);
            });
        });
    }

    /** Set the state and its vital children */
    static async setState(state: NodeState, updateData: NodeUpdateData<Node>, path: string[] = []): Promise<void> {
        if (state.factory.isContentAddressable) {
            // Content addressable nodes cannot be updated
            throw state.logicError(
                'Content-addressable node instances are immutable. You cannot modify them directly with node.$.set().',
            );
        }
        const data = StateUtils.restructureDelegatedInputValues(state.factory, updateData);

        const properties = Object.keys(data).map(propertyName => state.factory.findProperty(propertyName));
        // .filter(prop => !prop.isStoredOutput);
        // Set to null nullable references whose _id was set with a negative value.
        // We collect them into valuesBeforeReset
        const valuesBeforeReset = await asyncArray(
            properties.filter(prop => {
                const value = data[prop.name];
                return prop.isReferenceProperty() && typeof value === 'number' && value < 0 && prop.isNullable;
            }),
        )
            .map(async property => {
                await StateSetValue.setPropertyValue(state, property, [...path, property.name], null);
                return property.name;
            })
            .reduce((r, k) => ({ ...r, [k]: data[k] as number }), {} as Dict<number>);

        const propertiesToSet = properties.filter(property => !(property.name in valuesBeforeReset));
        const sortedProperties = StateSetValue.sortPropertiesForSetState(state, propertiesToSet, data);

        await asyncArray(sortedProperties).forEach(async property => {
            const value = data[property.name];
            /**
                     * This is wrong because some function must set the value.
                     * Before it used to assign it with node.prop = value and bypass this check
                     * but in async/await the only way to assign is with node.$.set({ prop: value })
                     * So this check cannot be done here.
                     * We should be fine as we exclude these properties from node input types in the graphql schema
                     *
                    if (property.isStoredOutput) {
                        throw state.propertySystemError(property, 'Cannot set value - property is stored output.');
                    }
                    */
            await StateSetValue.setPropertyValue(state, property, [...path, property.name], value);
        });

        // Restore references in state.values:
        Object.keys(valuesBeforeReset).forEach(k => {
            state.values[k] = valuesBeforeReset[k];
        });
    }

    /**
     * Sorts the properties in the order they should be set (in setState function)
     * @param state the state
     * @param propertiesToSet the properties to set
     * @param data the data to set
     */
    private static sortPropertiesForSetState(
        state: NodeState,
        propertiesToSet: Property[],
        data: AnyRecord,
    ): Property[] {
        // First, build a list of dependencies (based on dependsOn attribute) for every property
        const dependsOnByProperties: Dict<Property[]> = {};
        propertiesToSet.forEach(property => {
            property.dependsOn?.forEach(dependentPropertyName => {
                if (typeof dependentPropertyName !== 'string') return;
                const dependentProperty = state.factory.propertiesByName[dependentPropertyName];
                if (dependentProperty == null) return;
                if (dependsOnByProperties[dependentPropertyName] == null) {
                    dependsOnByProperties[dependentPropertyName] = [];
                }
                dependsOnByProperties[dependentPropertyName].push(property);
            });
        });

        // Now, build a list of dependencies for every property, and sort them topologically
        const dependencies: DependingItem[] = [];
        let atLeastOneDependencySet = false;
        propertiesToSet.forEach(property => {
            const value = data[property.name];
            if (value == null) return;
            if (property.isCollectionProperty()) {
                if (Array.isArray(value) && value.some((row: AnyRecord) => row._action === 'delete')) {
                    // We have to delete an item in this collection of Node A
                    // All the properties (from the same node) that dependOn the current property must be processed before the deletion
                    // For instance, BusinessEntity.contacts dependsOn BusinessEntity.addresses
                    // If we have a payload like { addresses: [{ _id: ..., _action: 'delete' }], contacts: [{ _id: ..., _action: 'delete' }] }
                    // The contacts must be deleted before the addresses (otherwise the FK constraint will fail)
                    const dependentProperties = dependsOnByProperties[property.name];
                    if (dependentProperties) {
                        dependencies.push({
                            name: property.name,
                            dependsOn: dependentProperties.map(p => p.name),
                            object: property,
                        });
                        atLeastOneDependencySet = true;
                        return;
                    }
                }
            }
            dependencies.push({ name: property.name, dependsOn: [], object: property });
        });

        if (!atLeastOneDependencySet) return propertiesToSet;

        return topoSort(dependencies).map(d => d.object) as Property[];
    }
}
