/** @ignore */ /** */
import { AnyRecord, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse, DataInputError } from '@sage/xtrem-shared';
import * as lodash from 'lodash';
import { Property } from '../properties';
import { SqlQuery } from '../sql';
import { AnyFilterObject } from '../ts-api';
import { lazyLoadedMarker } from './lazy-loaded-marker';
import { NodeState, StateStatus } from './node-state';
import { StateIntern } from './state-intern';
import { StateUtils } from './state-utils';

/**
 * This static class handles the lazy loading of node records.
 *
 * References are first created as thunks which only contain key values found in the source table.
 * If we try to read a non-key property of the reference, we lazy load the reference by reading it from the
 * database.
 *
 * @internal
 */
export abstract class StateLoad {
    /** Reads the record from the database */
    private static async readFromDatabase(state: NodeState, key: AnyRecord): Promise<AnyRecord | null> {
        const data = await state.context.prefetcher.tryRead(state.factory, key, state.forUpdate);
        if (data !== undefined) return data;

        const record = await (
            await state.factory.createNodeQuery(state.context, {
                filter: key,
                forUpdate: state.forUpdate,
                singleResultRequest: true,
            })
        ).getNodeData();
        if (record) state.context.prefetcher.addRecord(state.factory, state.forUpdate, record);
        return record;
    }

    /** Get the filter to select the record with state's key. */
    private static getKeyFilter(state: NodeState): AnyFilterObject {
        // We have an _id value,
        // If it is a number and greater than 0 then parse it is {_id:XX}
        // If it is a string and the factory is external it is {_id:'XX'} and will be parsed by the storage manager
        // If it is a string and the factory is sql it is {key1:'XX', key2:'XX'}
        if (
            (state.values._id && Number.isFinite(Number(state.values._id)) && Number(state.values._id) > 0) ||
            typeof state.values._id === 'string'
        ) {
            return state.factory.storage === 'sql'
                ? state.factory.parseNodeId(state.values._id as string | number)
                : { _id: state.values._id };
        }

        // Check that the keys in state.values are all valid and can be mapped to SQL
        // TODO: improve this to also check that the keys map to a unique index
        // Not so easy with properties defined by a getValue
        Object.keys(state.values).forEach(key => {
            // TODO: investigate why _constructor is not a property and improve this.
            if (key === '_constructor') return;
            const prop = state.factory.findProperty(key);
            // prop must be mappable to SQL
            if (!(prop.isStored || prop.getValue)) {
                throw new DataInputError(`${prop.fullName}: invalid key property`);
            }
        });

        return lodash.omit(state.values, '_id');
    }

    /** Loads the record from inside the funnel */
    private static loadFromFunnel(state: NodeState): AsyncResponse<boolean> {
        // if the state is not thunk, it was already loaded and we retrieved it from the interning cache
        if (!state.isThunk) return state.status !== StateStatus.stale;

        // Get the key filter of the passed in state.
        // This could be {_id:...} or some other filter on a unique index {foo:...}
        const key = StateLoad.getKeyFilter(state);
        // If the filter is empty or only contains undefined values, skip the query (that would return the whole table) and return false
        if (Object.values(key).every(v => v === undefined)) {
            StateIntern.removeState(state);
            state.status = StateStatus.stale;
            return false;
        }

        // the interning keys is a list of the unique keys of the node
        // the node primary will usually be the first in the array, if it is populated,
        // otherwise it will be the first unique index that is in the values
        const funnelKey = state.interningKeyValues[0];

        return state.context.withReadFunnel(funnelKey, async () => {
            // if the state is not thunk then it was loaded by a previous request on this funnel
            if (!state.isThunk) return state.status !== StateStatus.stale;

            // Compute the filter (key + access rights).
            // The global cache key is based on this filter, not just the key.
            const accessRightsFilter = await state.factory.getAccessRightsFilter(state.context);
            const filters = [key];
            if (accessRightsFilter) filters.push(accessRightsFilter);

            // Read the values, going through the cache.
            let data = await state.factory.cache.fetch(state.context, {
                getKey: () => JSON.stringify({ filters, context: state.context.getSqlCacheKey() }),
                getValue: async () => {
                    return {
                        value: await this.readFromDatabase(state, key),
                        storedEncryptedProperties: state.factory.storedEncryptedProperties,
                    };
                },
                // The creation of an entity won't update an existing one: the cache can be leveraged
                isolateInContext: state.forUpdate,
                ignoreCache: state.forUpdate,
            });

            // If no data and there is an accessRightsFilter, do the select without the accessRightsFilter
            // to check if the data is not found because of the accessRightsFilter
            if (!data && accessRightsFilter) {
                const checkData = await (
                    await SqlQuery.create(state.context, state.factory, {
                        filters: [key],
                        forUpdate: false,
                        singleResultRequest: true,
                    })
                ).getNodeData();

                if (checkData) {
                    // Include the values of the natural key in state.values for more user friendly error messages
                    state.factory.naturalKey?.forEach(p => {
                        if (state.values[p] !== checkData[p]) state.values[p] = checkData[p];
                    });

                    StateIntern.removeState(state);
                    state.status = StateStatus.unauthorized;
                    return false;
                }
            }

            // If the context is transient node.state will be modified directly so we have to clone it here.
            // Otherwise, the node.state will be cloned the first time we update one of its properties
            // See StateOld.keepOldState
            if (data && state.context.isTransient) data = { ...data };

            // Reset isThunk. This must be done inside the funnel.
            state.isThunk = false;

            // If no data, the record does not exist.
            if (!data) {
                StateIntern.removeState(state);
                state.status = StateStatus.stale;
                return false;
            }

            // Update state.values.
            state.values = data;
            state.setLazyLoadMarkers();
            // intern the loaded state, filling the missing index keys in the interning cache
            if (!state.factory.isAbstract) StateIntern.updateInternCache(state);
            return true;
        });
    }

    /** Tries to lazy load the state's values. Returns true if success, false otherwise. */
    static tryLoad(state: NodeState): AsyncResponse<boolean> {
        // Internal assertions.
        if (state.factory.storage !== 'sql' && state.factory.storage !== 'external') {
            throw state.systemError(`cannot load: bad class storage: ${String(state.factory.storage)}`);
        }

        // If this is not the first execution in the funnel, state.isThunk was set by a previous call.
        // So we have to check it here
        if (!state.isThunk) return state.status !== StateStatus.stale;

        // Load inside a funnel to handle concurrent calls.
        return StateLoad.loadFromFunnel(state);
    }

    /** Lazy loads the state's values. Throws an error if the record was not found. */
    static async load(state: NodeState): Promise<void> {
        if (!(await this.tryLoad(state))) {
            if (state.status === StateStatus.unauthorized) {
                throw state.authorizationError({
                    message: 'Unauthorized: {{values}}',
                    key: '@sage/xtrem-core/record-not-found-unauthorized',
                    data: {
                        values: StateUtils.formatStateValues(state.values),
                    },
                });
            }

            throw state.dataInputError({
                message: 'record not found: {{values}}',
                key: '@sage/xtrem-core/record-not-found',
                data: { values: JSON.stringify(state.values) },
            });
        }
    }

    /**
     * Resolve (by fetching from the database) a lazy loaded value
     */
    static async resolveLazyLoadedValue(state: NodeState, property: Property): Promise<AnyValue> {
        if (state.values[property.name] !== lazyLoadedMarker) {
            return state.values[property.name];
        }
        const funnelKey = `${state.interningKeyValues[0]}:${property.name}`;
        // Load inside a funnel to handle concurrent calls.
        const resolved = await state.context.withReadFunnel(funnelKey, () =>
            StateLoad._resolveLazyLoadedValueFromFunnel(state, property),
        );
        if (resolved == null)
            throw new Error(
                `Lazy-loading of ${state.factory.name}[${state.id}].${property.name} failed, state=${state.status}`,
            );
        state.values[property.name] = resolved[property.name];
        return resolved[property.name];
    }

    /**
     * Resolve (by fetching from the database) all lazy loaded values for a given state
     */
    static async resolveLazyLoadedValues(state: NodeState): Promise<void> {
        if (state.factory.lazyLoadedProperties.length > 0) {
            await asyncArray(state.factory.lazyLoadedProperties).forEach(async prop => {
                await StateLoad.resolveLazyLoadedValue(state, prop);
            });
        }
    }

    /**
     * Resolve (by fetching from the database) a lazy loaded value
     */
    private static async _resolveLazyLoadedValueFromFunnel(
        state: NodeState,
        property: Property,
    ): Promise<AnyRecord | null> {
        const query = await state.factory.createLazyLoadedValueQuery(state.context, state.id, [property]);
        return query.getNodeData();
    }
}
