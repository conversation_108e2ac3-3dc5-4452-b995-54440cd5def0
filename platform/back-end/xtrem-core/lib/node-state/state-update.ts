/** @ignore */ /** */
import { AnyRecord, asyncArray } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { NodeFactory } from '../runtime/node-factory';
import { isScalar } from '../runtime/utils';
import { NodeState, StateStatus } from './node-state';

/**
 * This static class provides the methods to update property values in the node and in the database
 *
 * @internal
 */
export abstract class StateUpdate {
    /*
     * This call updates the node instance and its record in the database.
     * It can only be used to update simple values, not vital collections nor references.
     * It bypasses the control and propagation rules.
     * So, only use it for simple updates with values that you have controlled.
     *
     * It is used to update properties with deferred default values.
     */
    static async atomicUpdate(state: NodeState, data: AnyRecord): Promise<void> {
        if (
            !(
                state.status === StateStatus.updatable ||
                state.status === StateStatus.created ||
                state.status === StateStatus.inserted
            )
        )
            throw state.logicError(`cannot fast update, bad status ${state.status}`);
        const dataByFactory = {} as Dict<{ factory: NodeFactory; data: AnyRecord }>;
        const keys = Object.keys(data);
        keys.forEach(key => {
            const value = data[key];
            if (!isScalar(value)) throw state.logicError(`value for ${key} is too complex`);

            const property = state.factory.findProperty(key).rootProperty;
            const factory = property.factory;
            if (!dataByFactory[factory.name]) dataByFactory[factory.name] = { factory, data: { [key]: value } };
            else dataByFactory[factory.name].data[key] = value;
            state.values[key] = value;
        });
        await asyncArray(Object.values(dataByFactory)).forEach(async item => {
            await state.context.bulkUpdate(item.factory.nodeConstructor, {
                set: item.data,
                where: { _id: state.id },
            });
            if (state.id > 0)
                state.context.prefetcher.afterUpdate(state.factory, { ...state.values, ...item.data, _id: state.id });
        });
    }
}
