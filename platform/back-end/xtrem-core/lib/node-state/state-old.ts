/** @ignore */ /** */
import { asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse } from '@sage/xtrem-shared';
import { CollectionProperty, ForeignNodeProperty } from '../properties';
import { Node, NodeStatus } from '../ts-api';
import { NodeState, StateStatus } from './node-state';
import { StateLoad } from './state-load';

/**
 * This static class manages the 'old' state of a node (node.$.old)
 *
 * @internal
 */
export abstract class StateOld {
    /**
     * Creates state.oldState, if not already created.
     * This is called the first time one of the node's properties is modified.
     */
    static async keepOldState(state: NodeState): Promise<void> {
        if (state.oldState) return;
        if (state.isThunk) {
            // The node has not been fully loaded yet, we need to load all values before creating the old state
            await StateLoad.load(state);
        }
        state.oldState = new NodeState(state.context, state.factory, state.values, StateStatus.readonly, {
            isOld: true,
            isOnlyForDefaultValues: state.isOnlyForDefaultValues,
            isOnlyForDuplicate: state.isOnlyForDuplicate,
            isOnlyForLookup: state.isOnlyForLookup,
        });
        // State may come from the cache so we clone it to avoid altering cached data.
        state.values = { ...state.values };
    }

    /** Returns the old state */
    static async getOldState(state: NodeState): Promise<NodeState> {
        if (state.status === StateStatus.created || state.status === StateStatus.inserted) {
            throw new Error("'old' not accessible in creation status");
        }
        if (state.isReadonly) return state;
        await StateOld.keepOldState(state);
        return state.oldState!;
    }

    static mapNodeIfOld(
        fromState: NodeState,
        property: ForeignNodeProperty,
        node: Node | null,
    ): AsyncResponse<Readonly<Node> | null> {
        if (!node) return node;
        if (!property.isVitalParent && !property.isAssociationParent && !property.isMutable) return node;
        if (!fromState.isOld) return node;
        if (node.$.state.isOld) return node;
        // If child state is added then there is no old state as it was just created via the parent
        if (node.$.status === NodeStatus.added) return node;
        return node.$.old;
    }

    static async mapNodesIfOld(fromState: NodeState, property: CollectionProperty, nodes: Node[]): Promise<Node[]> {
        if (!fromState.isOld) return nodes;
        return (await asyncArray(nodes)
            .map(node => StateOld.mapNodeIfOld(fromState, property, node))
            .toArray()) as Node[];
    }
}
