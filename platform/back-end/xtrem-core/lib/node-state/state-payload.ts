/** @ignore */ /** */
import { AnyR<PERSON><PERSON>, AnyValue, AsyncResponse, asyncArray } from '@sage/xtrem-async-helper';
import { clsContext, withClsContext } from '@sage/xtrem-log';
import * as lodash from 'lodash';
import { BaseCollection } from '../collections';
import { Property } from '../properties';
import { isScalar } from '../runtime';
import { AnyNode, Node, NodePayloadOptions } from '../ts-api';
import { NodeState } from './node-state';
import { StateGetValue } from './state-get-value';
import { StateUtils } from './state-utils';

/**
 * This static class implements method to generate the state's payload.
 *
 * The payload contains all the values of all the node's properties,
 * recursing through of all its vital children.
 *
 * @internal
 */
export abstract class StatePayload {
    /** Get the payload for a property */
    private static async getPropertyPayload(
        state: NodeState,
        property: Property,
        options: NodePayloadOptions<AnyNode>,
    ): Promise<AnyValue> {
        const value = await StateGetValue.getPropertyValue(state, property);

        const getChildPayload = (childNode: Node, nestedPropertyNames: any): Promise<AnyRecord> => {
            // When trying to get the payload of a reference property that has filters applied, we offer the nested propertyNames to the childOptions
            // so we apply the filters at each level accordingly
            const childOptions = { ...options, propertyNames: nestedPropertyNames };

            // If a child property is not vital and not transient input then we need to only return the thunk data of the child node
            if (!property.isTransientInput && !property.isMutable) {
                childOptions.thunkOnly = true;
            }

            // If a child property is transient input then we need to return the complete payload of the child node,
            if (property.isTransientInput) {
                childOptions.thunkOnly = false;
            }

            return (
                childNode && StatePayload.getNodePayload(childNode.$.state, childOptions as NodePayloadOptions<AnyNode>)
            );
        };

        const childPropertyNames = options.propertyNames ? options.propertyNames[property.name] : undefined;

        if (property.isReferenceProperty()) {
            const child = value as Node;
            if (!child) return null;
            return getChildPayload(child, childPropertyNames);
        }

        if (property.isReferenceArrayProperty()) {
            const children = value as Node[];
            if (!children) return null;
            return asyncArray(children)
                .map(child => getChildPayload(child, childPropertyNames))
                .toArray();
        }

        if (property.isCollectionProperty()) {
            const coln = value as BaseCollection;
            if (options.limitCollections) {
                const { items } = await coln.queryPage({ first: options.limitCollections });
                return asyncArray(items)
                    .map(child => getChildPayload(child, childPropertyNames))
                    .filter(v => !!v)
                    .toArray();
            }
            return coln
                .map(child => getChildPayload(child, childPropertyNames))
                .filter(v => !!v)
                .toArray();
        }
        return value;
    }

    /** Get the state's payload */
    private static async getNodePayload(
        state: NodeState,
        options: NodePayloadOptions<AnyNode> = {},
    ): Promise<AnyRecord> {
        if (options.propertyNames && lodash.isEmpty(options.propertyNames)) return {};

        const factory = state.factory;
        let props = options.propertyNames
            ? Object.keys(options.propertyNames)
                  .filter(name => !!factory.propertiesByName[name])
                  .map(name => factory.propertiesByName[name])
            : factory.properties;
        props = await asyncArray(props)
            .filter(prop => this.filterPayloadProps(state, prop, options))
            .toArray();
        if (options?.thunkOnly) {
            if (factory.storage === 'external') {
                props = factory.keyProperties;
            } else {
                props = [props.find(prop => prop.name === '_id')!];
            }

            if (options.withNaturalKeyWhenThunk && factory.naturalKey && factory.naturalKey.length > 0) {
                const naturalKeyProps = factory.naturalKey
                    .filter(naturalKey => !props.find(p => p.name === naturalKey))
                    .map(key => factory.propertiesByName[key]);
                if (naturalKeyProps.length > 0) {
                    props = props.concat(naturalKeyProps);
                }
            }
        }
        if (!options.withIds) {
            props = props.filter(prop => prop.name !== '_id' && prop.name !== '_sourceId');
        }
        if (!options.withLayer) {
            props = props.filter(prop => prop.name !== '_layer');
        }
        return asyncArray(props).reduce(async (r, prop) => {
            if (
                options.inputValuesOnly &&
                (prop.getValue || prop.computeValue || prop.isVitalParent || prop.isAssociationParent)
            )
                return r;
            let val = await StatePayload.getPropertyPayload(state, prop, options);
            if (val === undefined) return r;
            if (options.omitDefaultValues && prop.type !== 'collection' && val === prop.getTypeDefaultValue()) return r;
            if (prop.isEnumProperty() || prop.isEnumArrayProperty()) {
                const enumDataType = prop.dataType;
                if (options.returnEnumsAsNumber) {
                    val = enumDataType.numberValue(val as string);
                    val = val === -1 ? null : val;
                }
            }

            // A callback can be supplied to convert scalar values to a required format, for instance if you require to convert
            // Datevalue, Decimal, TextStream, BinaryStream, etc.
            if (val != null && options.convertScalarValue && isScalar(val)) {
                val = options.convertScalarValue(val);
            }

            r[prop.name] = val;
            return r;
        }, {} as AnyRecord);
    }

    private static filterPayloadProps(
        state: NodeState,
        prop: Property,
        options: NodePayloadOptions<AnyNode> = {},
    ): AsyncResponse<boolean> {
        if (options.propertyNames?.[prop.name]) return true;
        if (prop.name === '_factory') return false;
        const shouldIncludeExcludedProps =
            !prop.excludedFromPayload ||
            (!!options.withTimeStamps && ['_createStamp', '_updateStamp'].includes(prop.name));

        return (
            (!options.thunkOnly || state.factory.keyProperties.some(p => p.name === prop.name)) &&
            shouldIncludeExcludedProps &&
            !(options.withoutCustomData && prop.name === '_customData') &&
            !['_createUser', '_updateUser'].includes(prop.name) &&
            StateUtils.isEnabledByServiceOptions(state, prop)
        );
    }

    static async getPayload(state: NodeState, options: NodePayloadOptions<AnyNode> = {}): Promise<AnyRecord> {
        // The vital graph is a tree so, naively, getPayload should not recurse infinitely.
        // BUT computed properties may call node.$.payload() which may cause infinite recursion.
        // Detect this with a map of visited states in the CLS context, to get a clear diagnosis instead
        // of a process crash (OOM, bus error, ...)
        const cls = clsContext<{ payloadMap?: Map<NodeState, boolean> }>();

        // If there is no CLS context, recurse with one
        // TODO: This hack is only needed with async/await and we will improve it after full migration.
        if (!cls) {
            return withClsContext(() => this.getPayload(state, options), {});
        }

        const oldMap = cls.payloadMap;
        if (oldMap && oldMap.has(state))
            throw new Error(`getPayload recurses on ${state.factory.name}:${state.values._id}`);

        const map = oldMap || new Map();
        cls.payloadMap = map;
        try {
            map.set(state, true);
            return await this.getNodePayload(state, options);
        } finally {
            cls.payloadMap = oldMap;
        }
    }
}
