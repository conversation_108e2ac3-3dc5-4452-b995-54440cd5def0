/** @ignore */ /** */
import { <PERSON><PERSON><PERSON><PERSON>, AnyValue } from '@sage/xtrem-async-helper';
import { Property } from '../properties';
import { Context, NodeReadOptions } from '../runtime/context';
import { NodeFactory } from '../runtime/node-factory';
import { NodeConstructOptions } from '../runtime/node-factory-types';
import { isCompound } from '../runtime/utils';
import { NodeState, StateStatus } from './node-state';
import { StateIntern } from './state-intern';
import { StateLoad } from './state-load';
import { StateUtils } from './state-utils';

/**
 * This static class provides methods that instantiate new node states.
 *
 * @internal
 */
export abstract class StateNew {
    /** Finds the factory in which a polymorphic reference should be instantiated */
    private static async findConcreteFactory(
        state: NodeState,
        factory: NodeFactory,
        property: Property,
        id: number,
    ): Promise<NodeFactory> {
        // Check if constructor for the reference property is part of state values
        if (state.values[`_${property.name}_constructor`]) {
            return state.context.application.getFactoryByName(state.values[`_${property.name}_constructor`] as string);
        }

        // If no constructor value was found in state values, lookup constructor from base table.
        // This will happen when reading a record with a null reference and setting a value.
        // In this case we need to do the lookup in the base table using the new value set for the reference.
        const result = await state.context.executeSql<{ _constructor: string }[]>(
            `SELECT _constructor from ${state.context.schemaName}.${factory.table.sqlTableName} where _tenant_id = $1 and _id = $2`,
            [state.context.tenantId, id],
        );
        if (!result[0]) throw factory.systemError(`${id}: read _constructor failed`);
        state.values[`_${property.name}_constructor`] = result[0]._constructor;
        return state.context.application.getFactoryByName(result[0]._constructor);
    }

    /** Create a new thunk state for a reference */
    static async newReferenceThunk(
        state: NodeState,
        factory: NodeFactory,
        property: Property,
        values: AnyRecord,
        writable: boolean,
    ): Promise<NodeState> {
        if (factory.isAbstract && typeof values._id === 'number' && values._id > 0) {
            // recurse with concrete factory
            const concreteFactory = await StateNew.findConcreteFactory(state, factory, property, values._id);
            values._constructor = concreteFactory.name;
            const newState = await StateNew.newReferenceThunk(state, concreteFactory, property, values, writable);
            return StateIntern.intern(newState);
        }

        const status = writable ? StateStatus.updatable : StateStatus.readonly;
        const newNodeState = new NodeState(state.context, factory, values, status, { isThunk: true });
        return StateIntern.intern(newNodeState);
    }

    /** Create a new node state with a _constructor value read from the database */
    private static newConcreteState(context: Context, state: NodeState, nodeOptions: NodeConstructOptions): NodeState {
        if (!state.factory.isAbstract) return state;

        if (!state.values._constructor) {
            throw state.factory.systemError('cannot create concrete node (_constructor is missing)');
        }
        const concreteFactory = context.application.getFactoryByName(state.values._constructor as string);
        // Recreate a new node from the concrete factory.
        // The node status must still be a 'thunk', this way, the values for the missing properties (for now, we
        // only have the values for the properties defined by the abstract factory) will be lazy loaded, when needed.
        return new NodeState(
            context,
            concreteFactory,
            state.values,
            // Create a thunk so that all the missing properties will be lazy loaded
            state.isReadonly ? StateStatus.readonly : StateStatus.updatable,
            { ...nodeOptions, isThunk: true },
        );
    }

    /** Create a new node state for a query */
    static newFromQuery(context: Context, factory: NodeFactory, record: AnyRecord, forUpdate: boolean): NodeState {
        const status = forUpdate ? StateStatus.updatable : StateStatus.readonly;
        // Note : record is sth like { code : xxxx, description : yyy, ...}
        const nodeOptions = {
            forUpdate,
        };
        let state = new NodeState(context, factory, record, status, nodeOptions);

        if (factory.isAbstract) {
            // The function should not return a node from an abstract factory but only nodes from concrete factories
            state = StateNew.newConcreteState(context, state, nodeOptions);
        }

        state.setLazyLoadMarkers();
        return forUpdate ? StateIntern.intern(state) : state;
    }

    /** Converts the key passed to context.read to a key object that we can use to query the database */
    private static getReadKeyValues(context: Context, factory: NodeFactory, key: AnyValue): AnyRecord {
        if (key && isCompound(key)) {
            return key;
        }
        if (factory.storage === 'external') {
            return factory.externalStorageManager!.getKeyValues(context, key);
        }
        return { _id: key };
    }

    /** Create a new node state for a node read individually */
    static async newFromRead(
        context: Context,
        factory: NodeFactory,
        key: AnyValue,
        options: NodeReadOptions & { dontThrow?: boolean },
    ): Promise<NodeState | null> {
        const opts = { ...options };
        const forUpdate = !!opts.forUpdate;

        if (forUpdate && !context.isWritable) {
            throw factory.systemError('cannot read for update: context is readonly');
        }

        const values = StateNew.getReadKeyValues(context, factory, key);

        const nodeStatus = forUpdate ? StateStatus.updatable : StateStatus.readonly;
        const nodeOptions = {
            forUpdate,
            isThunk: true,
            isTransient: opts.isTransient,
            isOnlyForLookup: opts.isOnlyForLookup,
        };

        let state = new NodeState(context, factory, values, nodeStatus, nodeOptions);
        // Store the interning keys prior to the state being loaded
        // We need these keys as some reference values that are part of unique keys are passed in like #foo,
        // and will be resolved to _id values during the load. After the load we check if these preload keys are
        // in the state, if they are not we remove the resolved keys from the intern cache.
        const keysBeforeLoad = Object.values(StateIntern.getInterningKeysFromValues(state.factory, values));

        state = StateIntern.intern(state);

        // for external nodes load will call the sql query object where we can hook in the query reader results
        if (!(await StateLoad.tryLoad(state))) {
            if (opts.dontThrow) return null;

            if (state.status === StateStatus.unauthorized) {
                throw state.dataInputError({
                    message: 'Unauthorized: {{values}}',
                    key: '@sage/xtrem-core/record-not-found-unauthorized',
                    data: {
                        values: StateUtils.formatStateValues(state.values),
                    },
                });
            }

            throw state.dataInputError({
                message: 'record not found: {{values}}',
                key: '@sage/xtrem-core/record-not-found',
                data: { values: JSON.stringify(key) },
            });
        }

        // Remove all redundant keys from the interning cache
        keysBeforeLoad.forEach(k => {
            if (!state.interningKeyValues.includes(k)) StateIntern.deleteKey(context, k);
        });

        if (state.factory.isAbstract) {
            // We have loaded the values from the "abstract" table : we must have a '_constructor' property.
            // The 'read' function should not return a node from an abstract factory but only nodes from concrete factories
            state = StateNew.newConcreteState(context, state, nodeOptions);
            StateIntern.updateInternCache(state);
        }

        state.setLazyLoadMarkers();
        return state;
    }
}
