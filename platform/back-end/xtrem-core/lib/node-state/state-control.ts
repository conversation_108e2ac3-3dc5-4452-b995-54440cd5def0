/** @ignore */ /** */
import { AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import * as decimalLib from '@sage/xtrem-decimal';
import { AsyncResponse, Dict, ValidationSeverity, unwrapError } from '@sage/xtrem-shared';
import { AnyRecord } from 'dns';
import { MutableCollection } from '../collections';
import { NodeRuleName } from '../decorators/node-decorators/node-events';
import { PropertyRuleName } from '../decorators/property-decorators/base-property-events';
import { CollectionProperty, Property, ReferenceArrayProperty, ReferenceProperty } from '../properties';
import { matchesFilter } from '../runtime/array-utils';
import { loggers } from '../runtime/loggers';
import { Node, NodeQueryFilter, ValidationContext } from '../ts-api';
import { isInt32 } from '../types';
import { NodeState, StateStatus } from './node-state';
import { StateGetValue } from './state-get-value';
import { StateLoad } from './state-load';
import { StatePrepare } from './state-prepare';
import { StateUtils } from './state-utils';

/**
 * This static class provides the methods to control a node state before saving to the database.
 *
 * @internal
 */
export abstract class StateControl {
    private static async callControl(
        state: NodeState,
        property: Property | null,
        ruleName: NodeRuleName | PropertyRuleName,
        path: string[],
        value?: AnyValue,
    ): Promise<void> {
        const cx = new ValidationContext(state.context, path);
        if (property) {
            if ((property as any)[ruleName])
                await state.context.withReadonlyScope(() =>
                    property.executeRule(state, ruleName as PropertyRuleName, cx, value),
                );
        } else if ((state.factory.events as any)[ruleName])
            await state.context.withReadonlyScope(() =>
                state.factory.executeRule(state, ruleName as NodeRuleName, cx, value),
            );
    }

    private static async controlScalarProperty(state: NodeState, property: Property, path: string[]): Promise<void> {
        // For required properties we test the raw value rather than the value returned by getPropertyValue
        // because getPropertyValues calls typeGetDefaultValue which sets non null (even non falsy) values (on dates, enums, etc.)
        if (state.values[property.name] == null) {
            // Do not throw error if the property is a delegated property because we are short-circuiting getPropertyValue
            if (property.isRequired && property.delegatesTo == null) {
                throw StateUtils.requiredPropertyError(state, property);
            }
        }

        const value = await StateGetValue.getPropertyValue(state, property);

        if (value == null) {
            if (!property.isNullable) {
                throw StateUtils.requiredPropertyError(state, property);
            }
            await StateControl.callControl(state, property, 'control', path, null);
            return;
        }

        if (property.isStringProperty()) {
            if (
                (property.isRequired || property.isNotEmpty) &&
                (StateControl.isEmptyLocalizedValue(property, value) || value === '')
            ) {
                state.context.addDiagnoseAtPath(ValidationSeverity.error, path, 'string cannot be empty');
                // do not execute control rule
                return;
            }
        }

        if (property.isArrayProperty()) {
            if (property.isRequired && (value as AnyValue[]).length === 0) {
                state.context.addDiagnoseAtPath(ValidationSeverity.error, path, 'array cannot be empty');
                return;
            }
        }

        if (property.isNumberProperty()) {
            if (property.isNotZero && decimalLib.eq(value, 0)) {
                state.context.addDiagnoseAtPath(ValidationSeverity.error, path, 'property cannot be equal to 0');
                return;
            }
            if (
                !property.name.startsWith('_') && // exclude internal properties
                property.isIntegerProperty() &&
                !isInt32(value)
            ) {
                state.context.addDiagnoseAtPath(
                    ValidationSeverity.error,
                    path,
                    'integer property cannot represent non 32-bit signed integer value',
                );
            }
        }

        // Validate the data type's controls
        if (property.dataType) {
            const cx = new ValidationContext(state.context, path);
            const dataType = property.dataType;
            if (
                property.isLocalized &&
                state.context.processLocalizedTextAsJson &&
                typeof value === 'string' &&
                value.startsWith('{') &&
                value.endsWith('}')
            ) {
                // We are processing a string that contains JSON object with localized texts
                // Every value must be controled
                try {
                    const parsedValue = JSON.parse(value) as Dict<string>;
                    const localizedTexts = Object.values(parsedValue);
                    await asyncArray(localizedTexts).forEach(async localizedText => {
                        await dataType.controlValue(state.node, cx, localizedText);
                    });
                    return;
                } catch {
                    // Nothing to do, the value is not a valid JSON, it has
                    // to be controlled as a "classic" string
                }
            }

            await dataType.controlValue(state.node, cx, value);
        }

        // Validate the property's controls
        if (property.control) await StateControl.callControl(state, property, 'control', path, value);
    }

    private static async controlCollectionProperty(
        state: NodeState,
        property: CollectionProperty,
        path: string[],
    ): Promise<void> {
        if (!property.isMutable) return;

        await StateControl.callControl(state, property, 'controlBegin', path, null);

        const nodes = (await state.getPropertyValue(property)) as MutableCollection;

        if (property.isRequired && (await nodes.length) === 0) {
            state.context.addDiagnoseAtPath(ValidationSeverity.error, path, 'required data collection cannot be empty');
            return;
        }

        await nodes.forEach(async child => {
            // The path will include the _id of the current collection node, to identify the collection entry
            // in the diagnoses.
            const childPath = MutableCollection.fillPath(path, child);
            await StateUtils.withValidationErrorRethrow(
                state,
                property,
                childPath,
                // eslint-disable-next-line no-void
                async () => void (await StateControl.controlState(child.$.state, childPath)),
            );
        });

        await StateControl.callControl(state, property, 'controlEnd', path, null);
    }

    private static async getIsActiveFilter(
        property: ReferenceProperty | ReferenceArrayProperty,
        parentNode: Node,
    ): Promise<NodeQueryFilter<Node> | undefined> {
        const isActivePropertyName = property.getTargetIsActivePropertyName();

        /** Returns whether the property ignoreIsActive is true or not */
        const ignoreIsActiveFn = property.ignoreIsActive;
        const ignoreIsActive =
            typeof ignoreIsActiveFn === 'function'
                ? await parentNode.$.state.context.withReadonlyScope(() => ignoreIsActiveFn.call(parentNode))
                : !!ignoreIsActiveFn;

        if (!ignoreIsActive && isActivePropertyName) {
            if (
                parentNode.$.state.status === StateStatus.created ||
                (parentNode.$.state.status === StateStatus.modified &&
                    parentNode.$.state.values[property.name] !== (await parentNode.$.old).$.state.values[property.name])
            ) {
                return { [isActivePropertyName]: true };
            }
        }
        return undefined;
    }

    private static async controlReferenceFilter(
        state: NodeState,
        property: ReferenceProperty | ReferenceArrayProperty,
        path: string[],
        value: Node,
    ): Promise<void> {
        const parentNode = state.node;

        const controlFilterItem = async (item: {
            filter: any;
            getErrorMessage: (this: Node) => AsyncResponse<string>;
        }): Promise<boolean> => {
            if (!item.filter) return true;
            const context = state.context;
            const control = (await context.buildFilter(item.filter, parentNode)) as NodeQueryFilter<Node, Node>;
            if (await matchesFilter(control, value, context.locales)) return true;
            context.addDiagnoseAtPath(ValidationSeverity.error, path, await item.getErrorMessage.call(state.node));
            return false;
        };

        const isActiveFilter = await this.getIsActiveFilter(property, parentNode);
        if (
            !(await controlFilterItem({
                filter: isActiveFilter,
                getErrorMessage() {
                    return this.$.context.localize(
                        '@sage/xtrem-core/cannot-reference-inactive-record',
                        'The record cannot be referenced because it is inactive.',
                    );
                },
            }))
        ) {
            return;
        }

        const controlFilter = property.filters?.control;
        if (
            !(await controlFilterItem({
                filter: controlFilter,
                getErrorMessage() {
                    return this.$.context.localize(
                        '@sage/xtrem-core/control-filter-not-satisfied',
                        'The record is not valid. You need to select a different record.',
                    );
                },
            }))
        ) {
            return;
        }

        const controlFilters = property.filters?.controls;
        if (controlFilters) {
            await asyncArray(controlFilters).every(item => controlFilterItem(item));
        }
    }

    private static async controlReferenceProperty(
        state: NodeState,
        property: ReferenceProperty,
        path: string[],
    ): Promise<void> {
        const value = (await state.getPropertyValue(property)) as Node | null;
        if (value == null) {
            if (!property.isNullable) {
                throw StateUtils.requiredPropertyError(state, property);
            }
            await StateControl.callControl(state, property, 'control', path, null);
            return;
        }

        const referenceState = value.$.state;
        // Load the whole node
        // We could skip this and rely on the foreign keys to detect invalid references.
        // The problem would be that some of these errors may not be reported during the validate phase, only
        // during the save phase.
        if (referenceState.isThunk) await StateLoad.load(referenceState);

        if (property.isMutable) {
            await StateControl.controlState(referenceState, path);
        } else {
            await StateControl.controlReferenceFilter(state, property, path, value);
        }

        await StateControl.callControl(state, property, 'control', path, value);
    }

    private static async controlReferenceArrayProperty(
        state: NodeState,
        property: ReferenceArrayProperty,
        path: string[],
    ): Promise<void> {
        const value = (await state.getPropertyValue(property)) as Node[] | null;
        if (value == null) {
            if (!property.isNullable) {
                throw StateUtils.requiredPropertyError(state, property);
            }
            await StateControl.callControl(state, property, 'control', path, null);
            return;
        }

        if (property.isRequired && (value as AnyValue[]).length === 0) {
            state.context.addDiagnoseAtPath(ValidationSeverity.error, path, 'array cannot be empty');
        }

        await asyncArray(value).forEach(async (v, i) => {
            const referenceState = v.$.state;

            // Load the whole node
            // We could skip this and rely on the foreign keys to detect invalid references.
            // The problem would be that some of these errors may not be reported during the validate phase, only
            // during the save phase.
            if (referenceState.isThunk) await StateLoad.load(referenceState);

            await StateControl.controlReferenceFilter(state, property, [...path, String(i)], v);
        });

        await StateControl.callControl(state, property, 'control', path, value);
    }

    private static async controlProperty(state: NodeState, property: Property, path: string[]): Promise<void> {
        await StateUtils.withValidationErrorRethrow(state, property, path, async () => {
            if (await StatePrepare.skipPropertyInPrepareAndControl(state, property)) return;

            if (property.isReferenceProperty()) {
                await StateControl.controlReferenceProperty(state, property, path);
            } else if (property.isReferenceArrayProperty()) {
                await StateControl.controlReferenceArrayProperty(state, property, path);
            } else if (property.isCollectionProperty()) {
                await StateControl.controlCollectionProperty(state, property, path);
            } else {
                await StateControl.controlScalarProperty(state, property, path);
            }
        });
    }

    private static async controlState(state: NodeState, path: string[]): Promise<boolean> {
        if (state.skipSave) return true;

        await StateControl.callControl(state, null, 'controlBegin', path);

        await asyncArray(state.factory.properties).forEach(prop =>
            StateControl.controlProperty(state, prop, [...path, prop.name]),
        );

        await StateControl.callControl(state, null, 'controlEnd', path);

        return !state.context.diagnoses.some(diagnose => diagnose.severity >= ValidationSeverity.error);
    }

    static async control(state: NodeState): Promise<boolean> {
        try {
            return (await StatePrepare.prepareState(state, [])) && (await StateControl.controlState(state, []));
        } catch (err) {
            const unwrappedError = unwrapError(err);
            // serializable errors must be rethrown so that the transaction can be retried
            if (unwrappedError.code === '40001') {
                throw err;
            }
            loggers.runtime.error(err.stack);
            state.context.addDiagnoseAtPath(ValidationSeverity.exception, err.path ?? [], err.message);
            return false;
        }
    }

    private static isEmptyLocalizedValue(property: Property, value: AnyValue): boolean {
        return (
            property.isLocalized &&
            ((typeof value === 'string' && value === '{}') ||
                (typeof value === 'object' && !Object.keys(value as AnyRecord).length))
        );
    }
}
