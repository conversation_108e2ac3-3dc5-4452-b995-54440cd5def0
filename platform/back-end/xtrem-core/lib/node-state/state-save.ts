/** @ignore */
import { AnyRecord, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { Dict, LogicError } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { BaseCollection, MutableCollection } from '../collections';
import { ExternalStorageManager } from '../decorators';
import { CollectionProperty, Property, ReferenceProperty } from '../properties'; /** */
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../runtime/node-factory';
import { SystemProperties } from '../runtime/system-properties';
import { OutputColumn } from '../sql';
import { SqlValueConverter } from '../sql/mapper/sql-value-converter';
import { AnyNode, Node, NodeQueryFilter, NodeSaveOptions, ValidationContext } from '../ts-api';
import { NodeState, StateStatus } from './node-state';
import { StateControl } from './state-control';
import { StateIntern } from './state-intern';
import { StateInvalidate } from './state-invalidate';
import { StateLoad } from './state-load';
import { StateOld } from './state-old';

/**
 * This static class provides method to save a node state to database.
 *
 * @internal
 */
export abstract class StateSave {
    /** Save the content of a collection property, if vital */
    private static async saveCollectionProperty(
        state: NodeState,
        property: CollectionProperty,
        path: string[],
    ): Promise<void> {
        const val = (await state.getPropertyValue(property)) as BaseCollection;
        if (property.getValue && property.targetFactory.storage === 'json') return;
        if (!property.isMutable) return;
        if (property.isTransientInput) return;
        if (property.saveBegin) {
            await property.executeRule(state, 'saveBegin');
        }
        // The path will include the _id of the current collection node, to identify the collection entry
        // in the diagnoses.
        await asyncArray(await val.nodes).forEach(child =>
            StateSave.saveState(child.$.state, path.concat(property.name, String(child._id)), state),
        );
        if (property.saveEnd) {
            await property.executeRule(state, 'saveEnd');
        }
    }

    /** Save the content of a reference property, if vital */
    private static async saveReferenceProperty(
        state: NodeState,
        property: ReferenceProperty,
        path: string[],
    ): Promise<void> {
        if (property.isTransientInput) return;
        if (property.getValue && property.targetFactory.storage === 'json') return;
        if (!property.isMutable) return;
        const val = (await state.getPropertyValue(property)) as Node;
        if (val) await StateSave.saveState(val.$.state, path.concat(property.name), state);
        if (property.targetFactory.isContentAddressable) {
            if (val && val._id < 0) throw property.logicError(`negative _id after save: ${val._id}`);
            state.values[property.name] = val ? val._id : null;
            // remove the reference to force a reload with the new _id
            state.references.delete(property.name);
        }
    }

    /** Save a collection or reference property */
    private static async saveProperty(state: NodeState, property: Property, path: string[]): Promise<void> {
        if (property.isCollectionProperty()) {
            await StateSave.saveCollectionProperty(state, property, path);
        } else if (property.isReferenceProperty()) {
            await StateSave.saveReferenceProperty(state, property, path);
        }
    }

    /**
     * Propagates a key change to the transaction's interning node cache.
     * This propagation is necessary when we insert a new record and the database returns
     * a new _id which has been obtained from a sequence.
     */
    private static propagateKeyChangeToTransaction(state: NodeState, newValue: AnyValue): void {
        state.values._id = newValue;
        StateIntern.updateInternCache(state);
    }

    /** Returns the key to lookup a negative id mapping in context.mappedIds */
    private static getMappedIdsKey(factory: NodeFactory, value: number): string {
        return `${factory.rootFactory.name}:${value}`;
    }

    /** Assigns a value returned by an auto-increment, user and stamp properties. Handles key change if necessary. */
    private static updatePropertyValue(state: NodeState, property: Property, value: AnyValue): void {
        // Important: get the previous key before assigning the new value

        const isIdProperty = SystemProperties.idProperty(property.factory) === property;
        if (isIdProperty) {
            const mappedIdKey = this.getMappedIdsKey(state.factory, state.values[property.name] as number);
            state.context.mappedIds[mappedIdKey] = value as number;
        }

        if (property.isReferenceProperty() && state.values[property.name] !== value) {
            // remove it from cache to force reloading
            state.references.delete(property.name);
        }

        if (property.isReferenceArrayProperty() && state.values[property.name] !== value) {
            // remove it from cache to force reloading
            state.referenceArrays.delete(property.name);
        }

        if (isIdProperty) StateSave.propagateKeyChangeToTransaction(state, value);
        state.values[property.name] = value;
    }

    private static async saveContentAddressableNodeToSqlStorage(state: NodeState): Promise<void> {
        // Load lazy loaded properties before computing the hash
        await StateLoad.resolveLazyLoadedValues(state);
        const hash = state.factory.getValuesHash(state.values);
        if (hash !== state.values._valuesHash || Number(state.values._id) < 0) {
            // Call keepOldState to ensure state.values is cloned before we modify it
            await StateOld.keepOldState(state);
            state.values._valuesHash = hash;
            let existing = await state.context.select(
                state.factory.nodeConstructor,
                { _id: true },
                { filter: { _valuesHash: hash } as NodeQueryFilter<AnyNode> },
            );
            if (existing.length === 1) {
                state.values._id = existing[0]._id;
            } else {
                // Insert in a separate context to reduce lock contention.
                // This may create an orphan, if the inner context commits successfully but the outer one does not.
                // But no real harm because we have a process to purge orphans.
                const returnedValuesFromInsert = await state.context.runInIsolatedContext(childContext => {
                    return state.factory.table.insert(childContext, _.omit(state.values, '_id'), {
                        onConflictDoNothing: true,
                    });
                });
                // Invalidate context cache because the record was created by childContext or by a concurrent request
                await state.factory.invalidateCache(state.context);
                if (returnedValuesFromInsert._id && returnedValuesFromInsert._id > 0) {
                    await StateSave.updateReturingValues(state, returnedValuesFromInsert);
                } else {
                    existing = await state.context.select(
                        state.factory.nodeConstructor,
                        { _id: true },
                        { filter: { _valuesHash: hash } as NodeQueryFilter<AnyNode> },
                    );
                    if (existing.length !== 1)
                        throw new Error(
                            `Select content addressable node should should return one record, got ${existing.length} returned`,
                        );
                    state.values._id = existing[0]._id;
                }
                state.context.prefetcher.afterInsert(state.factory, state.values);
            }
        }
        state.status = state.status === StateStatus.created ? StateStatus.inserted : StateStatus.modified;
    }

    /** Returns the list of columns to update */
    private static getColumnsToUpdate(state: NodeState): Dict<true> {
        // SQL update is skipped if the onlyColumns option is empty but we have to force an update on
        // tables that have an _update_stamp column to get a different _etag value (_etag is a hash on _update_stamp).
        // So we add '_update_stamp' to columnsToUpdate to force an update on these tables.
        // Note that the _update_stamp column value is set by a trigger and returned by the update statement
        // so we don't have to set it here.
        const updateStampColumn = state.factory.table.columnsByColumnName._update_stamp;
        const columnsToUpdate = state.dirtyColumnNames;
        if (updateStampColumn && Object.keys(columnsToUpdate).length === 0) columnsToUpdate._update_stamp = true;
        return columnsToUpdate;
    }

    /** Saves the state's record to its table */
    private static async saveToSqlStorage(state: NodeState, options?: NodeSaveOptions): Promise<void> {
        switch (state.status) {
            case StateStatus.created: {
                const returningValues = await state.factory.table.insert(state.context, state.values, options);
                state.status = StateStatus.inserted;
                await StateSave.updateReturingValues(state, returningValues);
                await state.factory.cache.invalidate(state.context);
                state.context.prefetcher.afterInsert(state.factory, state.values);
                break;
            }
            case StateStatus.modified: {
                const returningValues = await state.factory.table.update(state.context, state.values, {
                    onlyColumns: this.getColumnsToUpdate(state),
                });
                if (returningValues.length > 1)
                    throw new Error(
                        `update should have modified at most one record, got ${returningValues.length} modified`,
                    );
                if (returningValues.length) await StateSave.updateReturingValues(state, returningValues[0]);
                // columns have been updated so we can reset dirtyColumnNames
                state.dirtyColumnNames = {};
                await state.factory.cache.invalidate(state.context);
                break;
            }
            case StateStatus.updatable:
                break;
            default:
                throw new LogicError(`cannot save ${state.factory.name}:${state.node}: bad status: ${state.status}`);
        }
    }

    private static async updateReturingValues(state: NodeState, returningValues: AnyRecord): Promise<void> {
        const factory = state.factory;
        const properties = [...factory.properties, ...SystemProperties.getSystemProperties(factory)];
        await asyncArray(Object.keys(returningValues)).forEach(async colName => {
            // _constructor is not inside properties, so handle it separately
            if (colName === '_constructor') {
                state.values._constructor = returningValues._constructor;
                return;
            }
            const property = properties.find(p => p.columnName === colName);
            if (!property) throw new Error(`cannot set returning value of ${colName}: no property found`);
            const outputColumn = {
                sql: '',
                property,
                type: property.type,
            } as OutputColumn;
            const newVal = await SqlValueConverter.fromSql(state.context, outputColumn, returningValues[colName]);
            StateSave.updatePropertyValue(state, property, newVal);
        });
    }

    private static updateExternalReturningValues(state: NodeState, returningValues: AnyRecord): void {
        const factory = state.factory;
        Object.keys(returningValues).forEach(name => {
            const property = factory.findProperty(name, { includeSystemProperties: true });
            const newVal = returningValues[name];
            StateSave.updatePropertyValue(state, property, newVal);
        });
    }

    /** Saves the state's record to external storage */
    private static async saveToExternalStorage(
        state: NodeState,
        externalStorageManager: ExternalStorageManager<Node>,
        validationContext: ValidationContext,
    ): Promise<void> {
        switch (state.status) {
            case StateStatus.created: {
                if (!externalStorageManager.insert)
                    throw state.systemError('externalStorageManager.insert decorator missing');
                const returningValues = await externalStorageManager.insert(state.node, validationContext);
                state.status = StateStatus.inserted;
                StateSave.updateExternalReturningValues(state, returningValues);
                break;
            }
            case StateStatus.modified: {
                if (!externalStorageManager.update)
                    throw state.systemError('externalStorageManager.update decorator missing');
                const returningValues = await externalStorageManager.update(state.node, validationContext);
                if (returningValues.length > 0) StateSave.updateExternalReturningValues(state, returningValues[0]);
                break;
            }
            case StateStatus.updatable:
            case StateStatus.readonly:
                break;
            default:
                throw new LogicError(`cannot save ${state.node}: bad status: ${state.status}`);
        }
    }

    /** Saves the state's record to storage */
    private static async saveToStorage(state: NodeState, path: string[], options?: NodeSaveOptions): Promise<void> {
        state.context.transaction.incrementFactoryTick(state.factory);
        if (state.factory.storage === 'sql') {
            if (state.factory.isContentAddressable) await StateSave.saveContentAddressableNodeToSqlStorage(state);
            else await StateSave.saveToSqlStorage(state, options);
        } else if (state.factory.storage === 'external') {
            await StateSave.saveToExternalStorage(
                state,
                state.factory.externalStorageManager!,
                new ValidationContext(state.context, path),
            );
        } else throw new Error(`cannot save ${state.node}: invalid storage type: ${state.factory.storage}`);
    }

    /**
     * Fixes the state value for a property
     * Returns true if the value was not negative or if it could be updated with a lookup in context.mappedIds
     * Return false if no fix was found and the value is still negative.
     */
    private static fixNegativeId(
        state: NodeState,
        property: ReferenceProperty,
        oldValue = state.values[property.name],
    ): boolean {
        const oldId = Number(oldValue);
        if (!Number.isFinite(oldId) || oldId >= 0) return true;

        const mappedIdKey = this.getMappedIdsKey(property.targetFactory, oldId);
        const mappedValue = state.context.mappedIds[mappedIdKey];
        if (mappedValue > 0) {
            state.values[property.name] = mappedValue;
            return true;
        }
        return false;
    }

    /** Save the state and its vital children */
    private static async saveState(
        state: NodeState,
        path: string[],
        parentState?: NodeState,
        options?: NodeSaveOptions,
    ): Promise<void> {
        if (state.status === StateStatus.constructed)
            throw state.logicError('Cannot save node which has not been fully constructed');

        if (state.skipSave) return;

        if (state.factory.events.saveBegin) {
            await state.factory.executeRule(state, 'saveBegin');
        }

        const operationType = state.status === StateStatus.modified ? 'update' : 'create';
        state.broadcastCrudMessage(operationType);

        // Set to null nullable references whose _id was set with a negative value
        const propertiesToUpdate = [] as { property: ReferenceProperty; oldValue: number }[];
        // List of self-referenced properties whose _id was set with a negative value
        const selfReferencedProperties = [] as { property: ReferenceProperty; oldValue: number }[];

        await asyncArray(state.factory.properties).forEach(async prop => {
            if (!prop.isReferenceProperty()) return;

            // Save vital content addressable references before saving the main node,
            // to satisfy non null constraints on the main node.
            if (prop.isMutable && prop.targetFactory.isContentAddressable) {
                await StateSave.saveProperty(state, prop, path);
            } else if (!this.fixNegativeId(state, prop) && prop.isNullable) {
                propertiesToUpdate.push({
                    property: prop,
                    oldValue: state.values[prop.name] as number,
                });
            } else if (
                prop.isSelfReference &&
                state.values[prop.name] === state.values._id &&
                Number(state.values[prop.name]) < 0
            ) {
                selfReferencedProperties.push({
                    property: prop,
                    oldValue: state.values[prop.name] as number,
                });
            }
        });

        propertiesToUpdate.forEach(entry => {
            state.values[entry.property.name] = null;
        });

        await StateSave.saveToStorage(state, path, options);

        state.context.intern.flushState(state);

        // Mutable properties that are not content addressable must be saved AFTER 'this'
        // so that the autoIncrementals of 'this' (_id, ...) can be fixed with the values
        // provided by the db engine
        await asyncArray(
            state.factory.properties.filter(
                prop => prop.isMutable && !(prop.isReferenceProperty() && prop.targetFactory.isContentAddressable),
            ),
        ).forEach(prop => StateSave.saveProperty(state, prop, path));

        if (state.factory.events.saveEnd) {
            await state.factory.executeRule(state, 'saveEnd');
        }

        if (state.status === StateStatus.modified || state.status === StateStatus.inserted) {
            // Modifications were saved
            state.status = StateStatus.updatable;
            // Do not trigger updateValue rules on properties that were invalidated after being saved.
            // This would do more harm than good, as this might set values that are different from the ones saved,
            // unless we save the state again, but then we might end up in an infinite loop.
            // TODO: improve this when we refactor the save process to batch the insert/update operations.
            state.invalidProperties.clear();
        }

        if (parentState) {
            // Propagate the list of updated states to the parent
            parentState.statesToUpdate = [...parentState.statesToUpdate, ...state.statesToUpdate];
        }
        if (propertiesToUpdate.length) {
            // Update negative references
            propertiesToUpdate.forEach(entry => this.fixNegativeId(state, entry.property, entry.oldValue));
            state.status = StateStatus.modified;
            state.statesToUpdate.push([state, path]);
        }

        if (selfReferencedProperties.length) {
            // Update negative references for self-referenced properties
            selfReferencedProperties.forEach(entry => this.fixNegativeId(state, entry.property, entry.oldValue));
        }

        if (parentState === undefined) {
            // Save all updated states
            await asyncArray(state.statesToUpdate).forEach(async stateToUpdate => {
                await StateSave.saveToStorage(stateToUpdate[0], stateToUpdate[1]);
                stateToUpdate[0].status = StateStatus.updatable;
            });
        }

        if (options?.flushDeferredActions) await state.context.flushDeferredActions();
    }

    /**
     * We are saving a vital child directly. Check for the parent in the interning cache,
     * - if found in the readonly cache, then then remove the reference or collection from the state, forcing us to reload the collection/reference when requested
     * - if found in the writable cache, and it is a new child, then append. At the end re-sort the collection.
     * @param state
     */
    private static async clearVitalParentChildren(state: NodeState): Promise<void> {
        const factory = state.factory;
        const vitalParentProperty = factory.vitalParentProperty;
        const vitalParentFactory = factory.vitalParentFactory;
        const parentId = state.values[vitalParentProperty.name];

        // Should only be one key in the array as we only passed in _id above
        const parentInterningKey = Object.values(
            StateIntern.getInterningKeysFromValues(vitalParentFactory, {
                _id: parentId,
            }),
        )[0];

        // Get all vital foreign node properties of the parent factory where the target factory is the current state's factory
        const vitalProperties = vitalParentFactory.properties.filter(
            prop => prop.isForeignNodeProperty() && prop.isVital && prop.targetFactory === factory,
        );

        const cleanVitalChildren = async (writable: boolean): Promise<void> => {
            const parentState = state.context.intern.getState(parentInterningKey, writable);
            if (parentState) {
                await asyncArray(vitalProperties).forEach(async vitalProp => {
                    if (vitalProp.isCollectionProperty()) {
                        const collection = parentState.collections.get(vitalProp.name) as MutableCollection;
                        // The collection is cached on the parent state and the nodes of the collection is loaded
                        if (collection && collection.isLoaded) {
                            if (writable) {
                                // - If the parent exists in the writable interning cache then, if its a new node, add it to the collectio
                                // and at the end sort the collection.
                                // - If the node exists in the collection then this save will update the same state pointer
                                const node = await collection.find(collectionNode => collectionNode._id === state.id);

                                if (!node) {
                                    // The node is not in the collection we need to append it and re-sort the collection
                                    await collection.appendNode(state.node);
                                }

                                // We need to re-sort the nodes array, as we have appended or possibly updated the sortValue
                                if (!collection.hasSortValue) await collection.sortBySortValue();
                            } else {
                                // the parent state is readonly, so we can simply remove the cached collection
                                // and the collection will be reloaded when requested again
                                parentState.collections.delete(vitalProp.name);
                            }
                        }
                    } else if (vitalProp.isReferenceProperty()) {
                        const reference = parentState.references.get(vitalProp.name);
                        if (!writable) {
                            // For readonly parent, if the reference is cached on the parent state and the reference value is the pointing to the current state
                            // we delete it from the reference set, this will cause the reference to be reloaded if it is requested again.
                            if (reference && reference._id === state.id) {
                                parentState.references.delete(vitalProp.name);
                            }
                        }
                        // For writable vital references, this state should be the same pointer as the one cached in the parent node so it should be updated
                    }
                });
            }
        };

        // Clean writable states
        await cleanVitalChildren(true);

        // Clean readonly states
        await cleanVitalChildren(false);
    }

    /**
     * Remove the mutable references and collections from the state so that they are reloaded.
     * @param state
     */
    private static clearMutableChildren(state: NodeState): void {
        state.factory.strictMutableProperties.forEach(property => {
            if (property.isCollectionProperty()) {
                const collection = state.collections.get(property.name) as MutableCollection;
                // The collection is cached on the parent state and the nodes of the collection is loaded
                if (collection && collection.isLoaded) {
                    state.collections.delete(property.name);
                }
            } else if (property.isReferenceProperty()) {
                const reference = state.references.get(property.name);
                if (reference && reference._id === state.id) {
                    state.references.delete(property.name);
                }
            }
        });
    }

    /**
     * Recursively computes the skipSave flag of a node state.
     * This is done at the beginning of the save process to optimise the control and save operations.
     *
     * @param state - The NodeState object to compute skip save for.
     * @returns A Promise that resolves when the computation is complete.
     */
    private static async computeSkipSave(state: NodeState): Promise<void> {
        if (state.factory.storage !== 'sql') return;

        // Start by setting the flag to true if the state belongs to a vital collection child and if it has not been modified
        // (its status is still 'updatable').
        state.skipSave =
            state.status === StateStatus.updatable &&
            (state.factory.isVitalCollectionChild || state.factory.isAssociationCollectionChild);

        // Iterate over the properties to set the flag to false if the state should not be skipped.
        await asyncArray(state.factory.properties).forEach(async property => {
            // We need to revalidate properties that have been invalidated.
            // Otherwise the save operation might leave some properties invalidated, which would prevent some states to
            // transition from 'updatable' to 'modifed', and which may cause race conditions when properties are read concurrently
            // by graphql resolvers.
            // We have to ensure that all properties are valid after saving.
            if (state.status !== StateStatus.readonly)
                await StateInvalidate.validatePropertyValue(state, property, [property.name]);

            // Handle vital collections
            if (property.isCollectionProperty() && property.isMutable) {
                // If the forceFullSave decorator attribute is set, we force skipSave to false and we do not recurse.
                if (property.forceFullSave) {
                    state.skipSave = false;
                    return;
                }
                const val = state.collections.get(property.name);
                if (!val) return;
                // Recurse on the child states
                await val.forEach(async node => {
                    const childState = node.$.state;
                    if (!childState) return;
                    await this.computeSkipSave(childState);
                    // If at least one child state is not skipped, this state cannot be skipped.
                    // But we have to process the other children to set their skipSave flag.
                    if (!childState.skipSave) state.skipSave = false;
                });
                return;
            }
            // Handle vital references
            if (property.isReferenceProperty() && property.isMutable) {
                const val = state.references.get(property.name);
                if (!val) return;
                const childState = val.$.state;
                await this.computeSkipSave(childState);
                // If a child state is not skipped, this state cannot be skipped.
                if (!childState.skipSave) state.skipSave = false;
            }
        });
        if (state.skipSave) loggers.runtime.verbose(() => `Skipping save: ${state.factory.name}.${state.keyToken}`);
    }

    /** Tries to save. Returns false if save fails (see node.$.trySave) */
    static async trySave(state: NodeState, options: NodeSaveOptions = {}): Promise<boolean> {
        const factory = state.factory;
        factory.checkCanUpdate(state.context);

        await this.computeSkipSave(state);

        if (options.deferred) {
            // The node will be saved at the end of the transaction
            state.context.queueDeferredSave(state.node);
            return Promise.resolve(true);
        }

        return state.context.withDiagnoses(async () => {
            if (!factory.externalStorageManager?.skipValidation && !(await StateControl.control(state))) {
                return false;
            }
            if (!options.controlOnly) {
                if (factory.storage !== 'sql' && factory.storage !== 'external')
                    throw new Error(`Save ${factory.name} is a forbidden operation : storage is not sql or external.`);

                await StateSave.saveState(state, [], undefined, options);

                // We are saving a vital child directly, so we need to check the interning cache and manage the reference or collection cached on the parent state
                // If we do not manage the vital collection or reference, then any computed property value on the parent that depends on the vital child will be incorrect.
                // As this is an edge case we will take the hit of reloading the readonly reference/collection
                if (factory.isVitalChild) {
                    await this.clearVitalParentChildren(state);
                }

                // Clean mutable children
                this.clearMutableChildren(state);
            }
            return true;
        }, `While saving ${factory.name}(${state.keyToken})`);
    }
}
