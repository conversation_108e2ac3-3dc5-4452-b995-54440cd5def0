/** @ignore */ /** */
import { LogicError, ValidationSeverity } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { nanoid } from 'nanoid';
import { MutableCollection } from '../collections';
import { ValidationError } from '../errors/validation-error';
import { ForeignNodeProperty } from '../properties/foreign-node-property';
import { Context, NodeDeleteOptions } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../system-exports';
import { ValidationContext } from '../ts-api';
import { NodeState, StateStatus } from './node-state';
import { StateIntern } from './state-intern';
import { StateLoad } from './state-load';
import { StateVisit, Walker } from './state-visit';

/**
 * This static class implements the methods to delete a node and its state.
 *
 * @internal
 */
export abstract class StateDelete {
    /**
     * Low level delete of a node state.
     * Get its key and then delete it from its table.
     */
    private static async deleteFromTable(state: NodeState): Promise<number> {
        // Special case for node that was loaded through a secondary index and _id is missing
        // Fixes issue in one of the delete-on-cascade-test unit tests.
        if (state.isThunk && !state.values._id) await StateLoad.load(state);

        const keyValues = state.getKeyValues();
        // Delete record from its table (with cascade delete)
        const count = await state.factory.table.delete(state.context, keyValues);
        state.context.prefetcher.afterDelete(state.factory, state.values);
        return count;
    }

    /**
     * Returns whether a reference/collection property needs to be visited during delete.
     * This filter allows us to optimize by short-circuiting queries for child records that
     * are safely deleted by SQL cascade.
     */
    private static visitFilter(property: ForeignNodeProperty): boolean {
        // If the target factory has a delete event, we have to visit to call the event.
        const events = property.targetFactory.events;
        return !!(events.controlDelete || events.deleteBegin || events.deleteEnd);
    }

    /** Calls the deleteControl events on all the nodes that we visit. See visitFactory. */
    private static callControlEvents(rootState: NodeState, walk: Walker<NodeState>, path?: string[]): Promise<boolean> {
        return StateVisit.visitVitalTree<NodeState>(
            rootState.factory,
            {
                async visit(state, visitPath): Promise<boolean> {
                    const controlDelete = state.factory.events.controlDelete;
                    if (!controlDelete) return true;

                    const validationContext = new ValidationContext(
                        state.context,
                        state.collection ? MutableCollection.fillPath(visitPath, state.node) : visitPath,
                    );
                    try {
                        return await state.context.withReadonlyScope(async () => {
                            await state.factory.executeRule(
                                state,
                                'controlDelete',
                                validationContext,
                                visitPath.length > 0,
                            );
                            return !state.context.hasErrors();
                        });
                    } catch (err) {
                        loggers.runtime.verbose(err.stack);
                        validationContext.addDiagnose(ValidationSeverity.error, err.message);
                        return false;
                    }
                },
                walk,
            },
            rootState,
            path,
        );
    }

    /** Delete the state from storage. */
    private static async deleteFromStorage(state: NodeState, path: string[] = []): Promise<void> {
        state.context.transaction.incrementFactoryTick(state.factory);
        if (state.factory.storage === 'sql') {
            // If status is 'created' the record does not exist in the database
            if (state.status === StateStatus.created) return;
            // Delete it from the database
            await StateDelete.deleteFromTable(state);
        } else if (state.factory.storage === 'external') {
            // Call external node delete hook here
            if (!state.factory.externalStorageManager?.delete)
                throw state.systemError('externalStorageManager.delete missing');

            state.status = StateStatus.deleted;
            await state.factory.externalStorageManager.delete(state.node, new ValidationContext(state.context, path));
        } else {
            throw state.systemError(`cannot delete: invalid storage: ${state.factory.storage}`);
        }
    }

    /**
     * Calls the deleteBegin/End events on all the nodes of the vital graph, and deletes the root record
     */
    private static async callDeleteEvents(
        startState: NodeState,
        walk: Walker<NodeState>,
        startPath: string[] = [],
    ): Promise<void> {
        await StateVisit.visitVitalTree<NodeState>(
            startState.factory,
            {
                async before(state, path) {
                    const deleteBegin = state.factory.events.deleteBegin;
                    if (deleteBegin) await state.factory.executeRule(state, 'deleteBegin', path.length > 0);
                    return true;
                },
                async after(state, path) {
                    const deleteEnd = state.factory.events.deleteEnd;
                    if (deleteEnd) await state.factory.executeRule(state, 'deleteEnd', path.length > 0);

                    // If the the start path and current path are equal we are at the start of the walk and we will delete the
                    // vital parent cascading to the vital children.
                    // If the the start path and current path are not equal, we are on a vital child on the walk, the delete from storage
                    // should not be executed on the child, as it was deleted during the delete of it's parent.
                    // An example could be when we are deleting a vital child directly,
                    // when using the _action = delete on a vital collection, the startPath will have a depth of 1.
                    if (_.isEqual(startPath, path)) await StateDelete.deleteFromStorage(state, path);
                    // Delete the cached states for the state deleted and all its cached vital children
                    await StateIntern.removeStateWithVitalChildren(state);
                    state.status = StateStatus.stale;
                    return true;
                },
                walk,
            },
            startState,
            startPath,
        );
    }

    /** Throws an error if state status does not allow delete */
    private static checkStatus(rootState: NodeState): void {
        switch (rootState.status) {
            case StateStatus.created:
            case StateStatus.inserted:
            case StateStatus.modified:
            case StateStatus.updatable:
                break;
            default:
                throw new LogicError(`cannot delete ${rootState.node}: bad status: ${rootState.status}`);
        }
    }

    /** Deletes the state by walking through its vital child nodes. */
    private static async deleteWithWalker(rootState: NodeState, options?: NodeDeleteOptions): Promise<boolean> {
        // Throw if status is invalid.
        StateDelete.checkStatus(rootState);

        // Compute the walker that we will use to visit the vital graph.
        // The walker is optimized. It does not visit nodes that are safely deleted by foreign key cascades.
        const walk = await StateVisit.getWalker(rootState.factory, StateDelete.visitFilter);

        if (!options?.skipControls) {
            // Force the loading of the root node if it is a thunk, to ensure that we have its _id
            if (rootState.isThunk && !rootState.node._id) await StateLoad.load(rootState);
            // Call the controlDelete events on the nodes of the vital graph.
            if (!(await StateDelete.callControlEvents(rootState, walk, options?.path))) return false;
        }

        // The vital graph has been controlled
        // We can call the deleteBegin and deleteEnd events, and actually delete the nodes.
        await StateDelete.callDeleteEvents(rootState, walk, options?.path);
        return true;
    }

    /**
     * Broadcast delete notifiaction for each vital node
     * By using the node factory instead of node state a notification will be sent for each node, regardless if it was loaded or not
     * This makes sure that notifications are send for all the vital tree nodes
     * @param context
     * @param factory
     */
    private static broadcastDelete(context: Context, factory: NodeFactory): void {
        factory.getVitalTree().forEach(f => {
            f.broadcastCrudMessage(context, 'delete');
        });
    }

    /** delete method used both by tryDelete and delete */
    private static async doDelete(state: NodeState, options?: NodeDeleteOptions): Promise<boolean> {
        const result = await state.context.withDiagnoses(() => {
            state.factory.checkCanDelete(state.context);
            return StateDelete.deleteWithWalker(state, {
                ...options,
                skipControls: options && options.skipControls ? options.skipControls : false,
            });
        }, `While deleting ${state.factory.name}(${state.keyToken})`);

        this.broadcastDelete(state.context, state.factory);
        if (result) await state.factory.cache.invalidate(state.context);
        return result;
    }

    /** Tries to delete the state.  See node.$.tryDelete. */
    static async tryDelete(state: NodeState, options?: NodeDeleteOptions): Promise<boolean> {
        // We need a savepoint to handle the case where delete is rejected by a constraint.
        // When this happens we must restore the SQL connection to a valid state.
        // Otherwise we get a "current transaction is aborted, commands ignored until end of transaction block"
        // exception during rollback.
        const savePointName = `sp_${nanoid().replace(/-/g, '_')}`;
        await state.context.executeSql(`SAVEPOINT ${savePointName}`, []);
        try {
            await this.delete(state, options);
            return true;
        } catch (e) {
            if (e.innerError?.table && e.innerError?.constraint) {
                await state.context.executeSql(`ROLLBACK TO SAVEPOINT ${savePointName}`, []);
                return false;
            }
            if (e instanceof ValidationError) {
                await state.context.executeSql(`ROLLBACK TO SAVEPOINT ${savePointName}`, []);
                return false;
            }
            throw e;
        } finally {
            await state.context.executeSql(`RELEASE SAVEPOINT ${savePointName}`, []);
        }
    }

    /** Returns whether the property is protected by a factory or not */
    private static async isProtectedByVendor(state: NodeState): Promise<boolean> {
        if (!state.factory.hasVendorProperty) return false;
        const vendor = await state.vendor;
        return !!vendor;
    }

    /** Deletes the state.  See node.$.delete */
    // TODO: review error handling
    static async delete(state: NodeState, options?: NodeDeleteOptions): Promise<void> {
        if (await this.isProtectedByVendor(state)) {
            throw state.context.businessRuleError({
                key: '@sage/xtrem-core/could-not-delete-blocked-by-vendor-code',
                message: 'The record is protected by a vendor code and cannot be deleted.',
            });
        }

        let result = false;
        try {
            result = await StateDelete.doDelete(state, options);
        } catch (e) {
            if (!e.innerError) throw e;
            const innerError = e.innerError;
            if (!(innerError.table && innerError.constraint)) throw e;
            const table = state.context.application.getTableByName(innerError.table);
            const foreignKey = table.getForeignKeyByName(innerError.constraint);
            if (foreignKey && foreignKey.columnNames.length) {
                throw state.context.businessRuleError({
                    key: '@sage/xtrem-core/could-not-delete-blocked-by-property',
                    message: 'Could not delete: blocked by property {{factory}}.{{property}}',
                    data: {
                        factory: table.factory.nodeConstructor.name,
                        property: table.factory.properties.find(
                            p => p.columnName === foreignKey.columnNames[foreignKey.columnNames.length - 1],
                        )?.name,
                    },
                    innerError,
                });
            }
            throw e;
        }
        if (!result) {
            throw state.getValidationError('delete');
        }
    }
}
