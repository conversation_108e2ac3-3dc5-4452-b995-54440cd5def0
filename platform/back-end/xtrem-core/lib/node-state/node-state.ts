/** @packageDocumentation @module runtime */
import { Any<PERSON><PERSON><PERSON>, AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { Decimal } from '@sage/xtrem-decimal';
import { DataInputError, Dict, LogicError, SystemError, ValidationSeverity } from '@sage/xtrem-shared';
import { AuthorizationError } from '..';
import { InternalAttachmentAssociationData, InternalNoteAssociationData } from '../application';
import { BaseCollection } from '../collections';
import { ValidationError } from '../errors/validation-error';
import { CollectionProperty, Property } from '../properties';
import { Context, NodeDeleteOptions, NodeReadOptions } from '../runtime/context';
import { NodeFactory } from '../runtime/node-factory';
import { NodeConstructOptions } from '../runtime/node-factory-types';
import { SyncInfo } from '../synchronization/sync-info';
import {
    AnyNode,
    Collection,
    Node,
    NodePayloadOptions,
    NodeSaveOptions,
    NodeStatus,
    NodeUpdateData,
    OrderBy,
    datetime,
} from '../ts-api';
import { lazyLoadedMarker } from './lazy-loaded-marker';
import { StateCursor } from './state-cursor';
import { StateDelete } from './state-delete';
import { StateDuplicate } from './state-duplicate';
import { StateGetValue } from './state-get-value';
import { StateInit } from './state-init';
import { StateIntern } from './state-intern';
import { StateJoin } from './state-join';
import { StateNew } from './state-new';
import { StateOld } from './state-old';
import { StatePayload } from './state-payload';
import { StateSave } from './state-save';
import { StateSetValue } from './state-set-value';

/**
 * The status of a node state.
 *
 * This status is more detailed than NodeStatus.
 *
 * @internal
 */
export enum StateStatus {
    readonly = 'readonly',
    constructed = 'constructed',
    created = 'created',
    inserted = 'inserted',
    deleted = 'deleted',
    updatable = 'updatable',
    modified = 'modified',
    stale = 'stale',
    unauthorized = 'unauthorized',
}

interface ErrorParameters {
    key: string;
    message: string;
    data?: object | AnyValue[];
    innerError?: Error;
    path?: string[];
}

/**
 * NodeState instances manage the _state_ of a node instance.
 *
 * There is exactly one NodeState instance for each node instance, accessible with `node.$.state`.
 * The entire state of the node instance is tracked in its `node.$.state` instance.
 *
 * @disabled_internal
 */
export class NodeState {
    /** The node instance */
    readonly node: Node;

    /**
     * Was the node read for update
     */
    readonly forUpdate: boolean;

    /**
     * The values of the properties.
     */
    // TODO: change it to a Map, and make it readonly
    values: AnyRecord = {};

    /*
    List of states which have to be updated after inserts when using negative ids in mutations
    */
    statesToUpdate: [NodeState, string[]][] = [];

    /**
     * The values of the reference properties, as node instances.
     * For _stored_ reference properties, state.values contains the `_id` of the referenced node,
     * whereas state.references contains the node instance.
     */
    readonly references = new Map<string, Node | null>();

    /**
     * The values of the reference array properties, as node instance arrays.
     * For _stored_ reference array properties, state.values contains the array of `_id` of the referenced nodes,
     * whereas state.referenceArrays contains the node instance array.
     */
    readonly referenceArrays = new Map<string, Node[] | null>();

    /**
     * The values of the collection properties, as collection instances.
     * The state.values does not contain any value for collection properties.
     */
    readonly collections = new Map<string, BaseCollection>();

    /**
     * The internal status of the node.
     * This status is more detailed than the status returned by node.$.status (which is a NodeStatus, not a StateStatus)
     */
    status: StateStatus;

    /**
     * Is the state a thunk?
     * A thunk is a partial state in which `state.values` does not have values for all the _stored_ properties.
     * The missing values will be lazy loaded by reading the record from the database. This will reset the thunk flag.
     */
    isThunk: boolean;

    /**
     * Is the state for a transient node instance?
     */
    readonly isTransient: boolean;

    /**
     * Was this state created only to obtain default values?
     */
    readonly isOnlyForDefaultValues: boolean;

    /**
     * Is the state created only for a lookup query?
     */
    readonly isOnlyForLookup: boolean;

    /**
     * Is the state created only for a duplicate query?
     */
    readonly isOnlyForDuplicate: boolean;

    /**
     * The node we are duplicating, if any.
     */
    readonly duplicates: Node | undefined;

    /**
     * Old state that we saved the first time this state was modified.
     * This is used by `node.$.old`.
     */
    oldState: NodeState | undefined;

    /**
     * Is this the _old_ state of a node
     */
    readonly isOld: boolean;

    /** Set of properties that have been invalidated because properties upon which they depend have been modified */
    readonly invalidProperties = new Set<string>();

    /** Set of properties that have been invalidated because properties upon which they depend have been modified */
    readonly deferredProperties = {} as Dict<boolean>;

    /** list of column names that have been modified and not saved yet */
    dirtyColumnNames: Dict<true> = {};

    /**
     * Index of the last property that we are allowed to access while executing an event.
     *
     * This limit is used to verify that we only access properties that P _depends upon_
     * when we execute rules attached to P (a property).
     */
    dependencyLimit = Number.MAX_SAFE_INTEGER;

    /** Unique debug id allocated by incrementing `NodeState.nextDebugId` */
    readonly debugId: number = 0;

    /** Will this state be skipped during control and save */
    skipSave = false;

    /** Global counter to allocate `state.debugId` */
    private static nextDebugId = 0;

    #context: Context;

    /** The state's constructor */
    constructor(
        context: Context,
        readonly factory: NodeFactory,
        values: AnyRecord,
        status: StateStatus,
        options: NodeConstructOptions,
    ) {
        this.debugId += NodeState.nextDebugId;
        NodeState.nextDebugId += 1;

        this.#context = context;

        // Do not clone values here, caller did it if necessary.
        this.values = values;

        // Initial status is:
        // - 'constructed' if the node is being created with context.$.create
        // - 'readonly' or 'updatable' if the node is being read from the database.
        if (status !== StateStatus.constructed && status !== StateStatus.readonly && status !== StateStatus.updatable)
            throw new Error(`invalid state status: ${status}`);
        this.status = status;

        this.forUpdate = !!options.forUpdate;
        this.collection = options.collection;
        this.isThunk = !!options.isThunk;
        this.isTransient = !!options.isTransient;
        this.isOnlyForDefaultValues = !!options.isOnlyForDefaultValues;
        this.isOnlyForDuplicate = !!options.isOnlyForDuplicate;
        this.duplicates = options.duplicates;
        this.isOnlyForLookup = !!options.isOnlyForLookup;
        this.isOld = !!options.isOld;

        // Allocate the node instance
        // eslint-disable-next-line new-cap
        this.node = new factory.nodeConstructor(this);
    }

    /**
     * Unique key values for node interning
     * Format: <Root factory name>:<key property1 name>:<key property1 value>,<key property2 name>:<key property2 value>, ...
     * Examples:
     *      Currency:_id:1
     *      Currency:code:USD
     *      BaseDocument:_id:1
     *      BaseDocumentLine:_id:1
     *      BaseDocumentLine:_sortValue:100,document:1
     */
    get interningKeys(): Dict<string> {
        return StateIntern.getInterningKeys(this);
    }

    get interningKeyValues(): string[] {
        return Object.values(this.interningKeys);
    }

    /** Can the state be fetched from storage */
    canBeFetched(): boolean {
        if (typeof this.values._id === 'number' && this.values._id < 0) return false;
        if (this.status === StateStatus.constructed || this.status === StateStatus.deleted) return false;
        return true;
    }

    /** The collection to which the node belongs. Only set if the node is loaded from a collection */
    private _collection?: BaseCollection;

    /**
     * The collection to which the node belongs. Only set if the node is loaded from a collection
     */
    get collection(): BaseCollection | undefined {
        return this._collection;
    }

    /**
     * @internal
     * Set the collection the current state belongs to. Externally the collection will be readonly
     */
    set collection(newCollection: BaseCollection | undefined) {
        this._collection = newCollection;
    }

    get context(): Context {
        // TODO: I need more time to find out how to make this one work in async/await mode
        // const currentContext = clsContext()?.context;
        // if (currentContext && currentContext.isNested && this.#context !== currentContext)
        //     throw this.logicError('cannot use a node in a context other than the one it was created');
        return this.#context;
    }

    // Status API

    /**
     * The node status
     *
     * This accessor maps the internal StateStatus values to NodeStatus values.
     * See node.$.status.
     */
    get nodeStatus(): NodeStatus {
        switch (this.status) {
            case StateStatus.readonly:
            case StateStatus.updatable:
                return NodeStatus.unchanged;
            case StateStatus.constructed:
            case StateStatus.created:
            case StateStatus.inserted:
                return NodeStatus.added;
            case StateStatus.deleted:
                return NodeStatus.deleted;
            case StateStatus.modified:
                return NodeStatus.modified;
            case StateStatus.stale:
            case StateStatus.unauthorized:
                return NodeStatus.invalid;
            default:
                throw this.systemError(`invalid status: ${this.status}`);
        }
    }

    /** Is the state readonly? */
    get isReadonly(): boolean {
        return this.status === StateStatus.readonly && !this.isTransient && !this.context.isTransient;
    }

    /** Is the state writable? (opposite of isReadonly) */
    get isWritable(): boolean {
        return !this.isReadonly;
    }

    /** Checks if status of the current state is created/constructed */
    get isNew(): boolean {
        return [StateStatus.created, StateStatus.constructed].includes(this.status);
    }

    /**
     * Is the state effectively readonly?
     *
     * Writable states are put in readonly mode during the execution of some rules
     * (defaultValue, updatedValue, control, controlDelete).
     * This accessor returns true while these rules are executed.
     */
    // TODO: use a context-level rather than state-level flag to enforce readonly on all nodes during rule execution.
    get isEffectivelyReadonly(): boolean {
        return this.isReadonly || this.context.hasReadonlyScopes();
    }

    /** Is the state stale? */
    get isStale(): boolean {
        return this.status === StateStatus.stale;
    }

    // Getters and setters for property values

    /** Returns the value of a property */
    getPropertyValue(property: Property): Promise<AnyValue> {
        return StateGetValue.getPropertyValue(this, property);
    }

    /** Returns the value of a property */
    getPropertyValueSync(property: Property): AnyValue {
        return StateGetValue.getPropertyValueSync(this, property);
    }

    /** Returns the value of a property, given the property name */
    async getValue<T extends AnyValue>(propertyName: string): Promise<T> {
        const property = this.factory.findProperty(propertyName, { includeSystemProperties: true });
        return (await StateGetValue.getPropertyValue(this, property)) as T;
    }

    /** Returns the value of a property */
    getValueSync<T extends AnyValue>(propertyName: string): T {
        const property = this.factory.findProperty(propertyName, { includeSystemProperties: true });
        return StateGetValue.getPropertyValueSync(this, property) as T;
    }

    /** Set the value of a property */
    async setPropertyValue(property: Property, value: AnyValue): Promise<void> {
        await StateSetValue.setPropertyValue(this, property, [property.name], value);
    }

    /** Set the value of a property, given the property name */
    setValue(propertyName: string, value: AnyValue): Promise<void> {
        const property = this.factory.findProperty(propertyName);
        return StateSetValue.setPropertyValue(this, property, [property.name], value);
    }

    /** Set new values into the state. See node.$.set */
    async set(data: NodeUpdateData<Node>, path?: string[]): Promise<void> {
        await StateSetValue.setState(this, data, path);
    }

    isPropertyValueDeferred(property: Property): boolean {
        return !!this.deferredProperties[property.name];
    }

    /** The old state. See node.$.old */
    get old(): Promise<Node> {
        return (async () => {
            return (await StateOld.getOldState(this)).node;
        })();
    }

    // Specialized getters and setter for property values

    /** The _id of the node */
    get id(): number {
        return this.values._id as number;
    }

    /** The _sortValue of the node */
    get sortValue(): Promise<number> {
        if (!this.factory.isVitalCollectionChild)
            throw this.systemError('cannot get _sortValue, node is not a vital collection child');
        if (this.factory.isAssociationChild)
            throw this.systemError('cannot get _sortValue, node is an association child');
        return this.getValue('_sortValue');
    }

    /** The vendor of the node */
    get vendor(): Promise<Node | null> {
        if (!this.factory.hasVendorProperty) return Promise.resolve(null);
        return this.getValue<Node | null>('_vendor');
    }

    /** Set the _sortValue of the node */
    async setSortValue(value: number): Promise<void> {
        if (!this.factory.isVitalCollectionChild)
            throw this.systemError('cannot set _sortValue, node is not a vital collection child');
        if (this.factory.isAssociationChild)
            throw this.systemError('cannot set _sortValue, node is an association child');
        await this.setValue('_sortValue', value);
    }

    /** The _createUser of the node */
    get createdBy(): Promise<Node | number> {
        return this.getValue('_createUser');
    }

    /** The _updateUser of the node */
    get updatedBy(): Promise<Node | number> {
        return this.getValue('_updateUser');
    }

    /** The _createStamp of the node */
    get createStamp(): Promise<datetime> {
        return this.getValue('_createStamp');
    }

    /** The _updateStamp of the node */
    get updateStamp(): Promise<datetime> {
        return this.getValue('_updateStamp');
    }

    /** The _syncTick of the node (sync source only) */
    get syncTick(): Promise<Decimal> {
        return this.getValue('_syncTick');
    }

    /** The _syncInfo of the node (sync target only) */
    get syncInfo(): Promise<SyncInfo> {
        return this.getValue('_syncInfo');
    }

    /** The _etag entity tag of the node (updateStamp as a string)  */
    get etag(): Promise<string> {
        return this.getValue('_etag');
    }

    /** The custom property data of the node */
    get customData(): Promise<object> {
        return this.getValue('_customData');
    }

    /** The custom property data of the node */
    get valuesHash(): Promise<string> {
        if (!this.factory.isContentAddressable) throw this.factory.logicError('factory is not content addressable');
        return this.getValue('_valuesHash');
    }

    /** attachments collection */
    get attachments(): Collection<InternalAttachmentAssociationData & Node> {
        return this.getValueSync<Collection<InternalAttachmentAssociationData & Node>>('_attachments');
    }

    /** notes collection */
    get notes(): Collection<InternalNoteAssociationData & Node> {
        return this.getValueSync<Collection<InternalNoteAssociationData & Node>>('_notes');
    }

    // Validation and saving API

    /** Adds a diagnose to the state's context */
    addDiagnose(severity: ValidationSeverity, path: string[], message: string): void {
        this.context.addDiagnoseAtPath(severity, path, message);
    }

    /** Validates the state. See node.$validate */
    control(options?: NodeSaveOptions): Promise<boolean> {
        return StateSave.trySave(this, { ...options, controlOnly: true });
    }

    /** Tries to save the state. See node.$.trySave */
    async trySave(options?: NodeSaveOptions): Promise<boolean> {
        if (!(await StateSave.trySave(this, options))) return false;

        if (!options?.deferred) {
            // When the save is deferred, we MUST NOT reset the oldState otherwise it will be
            // be rebuilt as soon as the 'old' accessor will be called.
            // It would then be rebuilt from the current values of the state (and not the original one)
            this.oldState = undefined;
        }
        return true;
    }

    getValidationError(operation: string): ValidationError {
        return new ValidationError(this.context, this.factory, operation);
    }

    getSaveOperationName(): string {
        if (this.duplicates) return 'duplicate';
        if (this.status === StateStatus.created || this.status === StateStatus.inserted) return 'create';
        return 'update';
    }

    /** Saves the state. See node.$.save */
    async save(options?: NodeSaveOptions): Promise<void> {
        const ok = await this.trySave(options);
        if (!ok) {
            throw this.getValidationError(this.getSaveOperationName());
        }
    }

    // Delete API

    /** Tries to delete the state. See node.$.tryDelete */
    tryDelete(options?: NodeDeleteOptions): Promise<boolean> {
        return StateDelete.tryDelete(this, options);
    }

    /** Deletes the state.  See node.$.delete */
    delete(options?: NodeDeleteOptions): Promise<void> {
        return StateDelete.delete(this, options);
    }

    // Key API

    getKeyValues(): AnyRecord {
        return this.factory.getKeyValues(this.context, this.values);
    }

    /** The key token, a string that uniquely identifies a node within its factory */
    get keyToken(): string {
        return this.factory.getToken(this.values);
    }

    getNaturalKeyValue(): Promise<string> {
        return StateGetValue.getNaturalKeyValue(this);
    }

    /** The cursor value for a given ordering. Used by paging API */
    getCursorValue(orderBy: OrderBy<Node>): Promise<string> {
        return StateCursor.getCursorValue(this, orderBy);
    }

    /** Returns the join values. Used by collections */
    getJoinValues(property: CollectionProperty): AsyncResponse<AnyRecord> {
        return StateJoin.getJoinValues(this, property);
    }

    // Payload API

    // Get the payload for the vital graph. See node.$.payload
    payload(options?: NodePayloadOptions<AnyNode>): Promise<AnyRecord> {
        return StatePayload.getPayload(this, options);
    }

    // Get a duplicate of the state
    duplicate(data?: AnyRecord): Promise<NodeState> {
        return StateDuplicate.duplicate(this, data);
    }

    // Error utilities

    /** Returns an error prefixed by the factory and property name */
    propertySystemError(property: Property, message: string): SystemError {
        return new SystemError(`${this.factory.name}.${property.name}: ${message}`);
    }

    /** Returns an error prefixed by the factory and property name */
    propertyDataInputError(property: Property, params: ErrorParameters): DataInputError {
        const localized = this.context.localize(params.key, params.message, params.data || {});
        return new DataInputError(
            `${this.factory.name}.${property.name}: ${localized}`,
            params.innerError,
            params.path,
        );
    }

    /** Returns an error prefixed by the factory name */
    systemError(message: string): SystemError {
        return new SystemError(`${this.factory.name}: ${message}`);
    }

    /** Returns an error prefixed by the factory name */
    logicError(message: string): LogicError {
        return new LogicError(`${this.factory.name}: ${message}`);
    }

    /** Returns an error prefixed by the factory name */
    dataInputError(params: ErrorParameters): DataInputError {
        const localized = this.context.localize(params.key, params.message, params.data || {});
        return new DataInputError(`${this.factory.name}: ${localized}`, params.innerError, params.path);
    }

    /** Returns an error prefixed by the factory name */
    authorizationError(params: ErrorParameters): DataInputError {
        const localized = this.context.localize(params.key, params.message, params.data || {});
        return new AuthorizationError(`${this.factory.name}: ${localized}`, params.innerError, params.path);
    }

    // Static 'new' calls

    /** Returns a new NodeState for a record created with context.create */
    static newFromContextCreate(
        context: Context,
        factory: NodeFactory,
        path: string[],
        data: AnyRecord,
        options?: {
            isTransient?: boolean;
            isOnlyForDefaultValues?: boolean;
            isOnlyForDuplicate?: boolean;
            duplicates?: Node;
            writable?: boolean;
            isOnlyForLookup?: boolean;
            collection?: BaseCollection;
        },
    ): Promise<NodeState> {
        return StateInit.newFromContextCreate(context, factory, path, data, options);
    }

    /** Returns a new NodeState for a record read individually from the database */
    static newFromRead(
        context: Context,
        factory: NodeFactory,
        key: AnyValue,
        options: NodeReadOptions & { dontThrow?: boolean },
    ): Promise<NodeState | null> {
        return StateNew.newFromRead(context, factory, key, options);
    }

    /** Returns a new NodeState for a record read by a database query */
    static newFromQuery(context: Context, factory: NodeFactory, record: AnyRecord, forUpdate: boolean): NodeState {
        return StateNew.newFromQuery(context, factory, record, forUpdate);
    }

    /**
     * Put in place all the required lazyLoadedMarker on the values array.
     * @internal
     */
    setLazyLoadMarkers(): void {
        // Mark the lazy-loaded values
        if (this.factory.storage !== 'sql' || this.#context.noLazyLoading) return;
        this.factory.lazyLoadedProperties.forEach(property => {
            this.values[property.name] = lazyLoadedMarker;
        });
    }

    /** Returns whether the node is frozen
     */
    isNodeFrozen(): AsyncResponse<boolean> {
        if (this.context.bypassFrozen) return false;
        const isFrozen = this.factory.nodeDecorator.isFrozen;
        if (typeof isFrozen !== 'function') return !!isFrozen;
        return this.context.withReadonlyScope(() => this.factory.executeRule(this, 'isFrozen'));
    }

    /**
     * Returns whether the property is frozen
     */
    isPropertyFrozen(property: Property): AsyncResponse<boolean> {
        if (this.context.bypassFrozen) return false;
        const isFrozen = property.isFrozen;
        if (typeof isFrozen !== 'function') return !!isFrozen;
        return this.context.withReadonlyScope(() => property.executeRule(this, 'isFrozen'));
    }

    /**
     * Send successful CRUD operations to be broadcasted
     * @param operationType
     */
    broadcastCrudMessage(operationType: string): void {
        this.factory.broadcastCrudMessage(this.context, operationType);
    }
}
