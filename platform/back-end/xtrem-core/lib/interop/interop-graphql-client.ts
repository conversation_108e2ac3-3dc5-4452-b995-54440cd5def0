import { ConfigManager } from '@sage/xtrem-config';
import { Datetime } from '@sage/xtrem-date-time';
import { Logger } from '@sage/xtrem-log';
import {
    AnyValue,
    AppConfig,
    Diagnosis,
    Dict,
    HttpError,
    InteropError,
    LogicError,
    ValidationSeverity,
} from '@sage/xtrem-shared';
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import * as contentType from 'content-type';
import { ExecutionPatchResult, ExecutionResult } from 'graphql';
import * as jwt from 'jsonwebtoken';
import * as _ from 'lodash';
import { IncomingMessage } from 'node:http';
import { Context } from '../runtime/context';
import { CoreHooks } from '../runtime/core-hooks';
import { SecretManager } from '../runtime/secret-manager';
import { getInteropHttpsAgent } from '../security';
import { json5Stringify } from '../utils/log-helper';

export const logger = Logger.getLogger(__filename, 'interop');

type InteropClientError = Error | AxiosError | AggregateError;

function isAggregateError(err: InteropClientError): err is AggregateError {
    return (err as AggregateError).errors !== undefined;
}

function isAxiosError(err: InteropClientError): err is AxiosError {
    return (err as AxiosError).isAxiosError !== undefined;
}

function tryParseJson(data: string, defaultValue: any = {}): any {
    try {
        return JSON.parse(data);
    } catch {
        return defaultValue;
    }
}

/** Options passed to the App constructor. */
export interface AppConstructOptions {
    /** The name of the app */
    name: string;

    /** The version of the app (semver) */
    version: string;
}

export interface AppInteropToken {
    tenantId: string;
    appName: string;
    scope: string;
    sourceUserEmail: string;
}

export interface AppInteropClaims extends AppInteropToken {
    iat: number;
    exp: number;
}

export interface GraphQlResponse<ResponseDataT = unknown, RequestDataT = {}>
    extends AxiosResponse<ResponseDataT, RequestDataT> {}

export interface GraphQlRequestOptions {
    appName: string;
    scope: string;
    query: string;
    variables?: Dict<AnyValue>;
    config?: AxiosRequestConfig;
}

export interface AppHealthInfo {
    /** The timestamp of the health check */
    timestamp: Datetime;
    /** The duration of the health check */
    duration: number;
    /** The status of the health check */
    status: number;
    /** The status text of the health check */
    statusText?: string;
    /** Whether the app is alive or not */
    isAlive: boolean;
}

function logIncomingMessageData(message: IncomingMessage): void {
    let data = '';

    message.on('data', chunk => {
        data += chunk;
    });

    message.on('end', () => {
        logger.verbose(() => `graphql stream response ${String(data)}`);
    });
}

/**
 * InteropGraphqlClient is a client to send GraphQL (or ping) requests to other apps.
 */
export class InteropGraphqlClient {
    private static getSignatureKey(tenantId: string): Promise<string> {
        const config = {
            xtremEnv: process.env.XTREM_ENV,
            clusterId: ConfigManager.current?.clusterId,
            // secretCacheTimeoutInMinutes defaults is 5 * 60 * 1000
        };
        return SecretManager.getTenantEncryptionKey(config, tenantId);
    }

    /**
     * Returns a bearer jwt token for a tenant id
     * @param payload the payload of the token
     * @returns the bearer token
     * @throws an error if the tenant id is missing
     */
    private static async getBearerToken(payload: AppInteropToken): Promise<string> {
        const { tenantId } = payload;
        if (!tenantId) {
            throw new LogicError('missing tenantId');
        }
        const key = await this.getSignatureKey(tenantId);
        return jwt.sign(payload, key, { algorithm: 'HS256', expiresIn: '2m' });
    }

    /**
     * Parse a bearer token and return the claims
     * @param token the bearer token
     * @returns the claims
     * @throws an error if the token is invalid
     */
    static async parseBearerToken(token: string): Promise<AppInteropToken> {
        const { tenantId } = jwt.decode(token) as AppInteropToken;
        const key = await this.getSignatureKey(tenantId);
        const decoded = jwt.verify(token, key) as AppInteropToken;
        if (!decoded.tenantId || !decoded.appName || !decoded.scope || !decoded.sourceUserEmail)
            throw new Error('invalid bearer token');
        return decoded;
    }

    /**
     * Returns the email of the user for the given scope
     * @param scope the scope
     * @returns the email of the user
     */
    static getScopeUserEmail(scope: string): string {
        return `scope-${scope}@localhost.domain`;
    }

    /**
     * Returns the url of the app for the given service url key (appUrl, interopUrl, metricsUrl)
     * @param appName the name of the app
     * @param key the service url key
     * @returns the url of the app
     */
    static getUrl(appName: string, key: keyof AppConfig): string {
        const url = ConfigManager.current.apps?.[appName]?.[key];
        if (!url) throw new LogicError(`${appName}: ${key} is not configured`);
        if (typeof url !== 'string') throw new LogicError(`${appName}: ${key} is not a string`);
        return url;
    }

    /**
     * Returns the url of the app
     * @param appName the name of the app
     * @returns the url of the app
     */
    static getAppUrl(appName: string): string {
        return InteropGraphqlClient.getUrl(appName, 'appUrl');
    }

    /**
     * Returns the url of the app for interop
     * @param appName the name of the app
     * @returns the url of the app for interop
     */
    static getInteropUrl(appName: string): string {
        return InteropGraphqlClient.getUrl(appName, 'interopUrl');
    }

    private static getRequestConfig(headers: Dict<string>, config: AxiosRequestConfig = {}): AxiosRequestConfig {
        return _.merge(
            {
                headers,
                validateStatus: (status: number) => status === 401 || (status >= 200 && status < 300),
                httpsAgent: getInteropHttpsAgent(),
            },
            config,
        );
    }

    /**
     * Sends a GraphQL request to this app.
     * @param context the context which provides the tenant id and the user's email
     * @param options a { query, variables } object
     * @returns the GraphQl response
     */
    private static async sendAnyGraphqlRequest(
        context: Context,
        { appName, scope, query, variables = {}, config = {} }: GraphQlRequestOptions,
    ): Promise<
        GraphQlResponse<
            ExecutionResult<unknown> | AsyncGenerator<ExecutionResult<unknown> | ExecutionPatchResult<unknown>>
        >
    > {
        const url = this.getInteropUrl(appName);
        // we might want to cache this
        const interopApp = await CoreHooks.interopManager.getInteropAppInfo(context, appName);
        const apiUrl = `${url}${interopApp.isConnector ? '/proxy' : '/api'}`;
        const { tenantId, cloudflareRayID, originId } = context;
        if (!tenantId) throw new LogicError('missing tenantId');
        const tenantApps = await CoreHooks.interopManager.getTenantApps(tenantId);
        if (!tenantApps || !tenantApps.includes(appName)) {
            throw new Error(`App ${appName} is not provisioned for tenant ${tenantId}`);
        }
        const sourceUserEmail = (await context.user)?.email;
        if (!sourceUserEmail) throw new LogicError('missing sourceUserEmail');
        const bearerToken = await this.getBearerToken({ appName, tenantId, scope, sourceUserEmail });
        const axiosConfig = InteropGraphqlClient.getRequestConfig(
            {
                Authorization: `Bearer ${bearerToken}`,
                'accept-language': context.currentLocale,
                accept: 'application/json',
                'cf-ray': cloudflareRayID,
                'x-request-id': originId,
            },
            config,
        );
        logger.debug(
            () =>
                `sending graphql query ${query} with ${JSON.stringify(variables)}, axiosConfig=${JSON.stringify(axiosConfig)}`,
        );
        try {
            const response = await axios.post<ExecutionResult<unknown, any>>(apiUrl, { query, variables }, axiosConfig);
            if (response.status === 401) {
                logger.error(
                    `Authentication error: ${response.status} ${response.statusText} - ${JSON.stringify(response.data || 'no data')}`,
                );
                throw new Error(`Request failed with status code ${response.status} ${response.statusText}`);
            }
            if (logger.isActive('debug')) {
                if (response.data instanceof IncomingMessage) {
                    logIncomingMessageData(response.data);
                } else {
                    logger.debug(
                        () =>
                            `graphql response [${response.data ? JSON.stringify(response.data.constructor.name) : JSON.stringify(response.data)}] ${String(JSON.stringify(response.data))}`,
                    );
                }
            }
            return response;
        } catch (err) {
            throw InteropGraphqlClient.interopError(appName, err);
        }
    }

    private static interopError(appName: string, err: InteropClientError): InteropError {
        let message = err.message;
        let http: HttpError | undefined;
        if (isAxiosError(err)) {
            if (!message) {
                message = `Request failed with code ${err.code}`;
            }
            if (err.response) {
                const parsedType = contentType.parse(err.response.headers?.['content-type'] ?? '');
                const data = err.response.data;
                message = `${err.response.status} ${err.response.statusText}`;
                http = {
                    status: err.response.status,
                    statusText: err.response.statusText,
                } as HttpError;

                const truncationLimit = 1000;
                if (typeof data === 'string') {
                    // Limit to truncationLimit characters
                    const length = data.length;
                    http.body =
                        length > truncationLimit
                            ? `${data.substring(0, truncationLimit)}[${length - truncationLimit} more...]`
                            : data;
                    if (parsedType.type === 'text/plain') {
                        message = http.body;
                    }
                } else if (Buffer.isBuffer(data)) {
                    const length = data.length;
                    // Convert buffer to hex string in case of control characters and limit to truncationLimit characters
                    http.body =
                        length > truncationLimit
                            ? `${data.subarray(0, truncationLimit).toString('hex')}[${length - truncationLimit} more...]`
                            : data.toString('hex');
                } else if (typeof data === 'object') {
                    http.body = `[object: ${data?.constructor?.name}]`;
                }
                if (parsedType.type === 'application/json' && !(data instanceof IncomingMessage)) {
                    http.body = tryParseJson(String(data), http.body);
                }
            }
        }
        const diagnoses: Diagnosis[] = [];
        if (isAggregateError(err) && Array.isArray(err.errors)) {
            if (!message) {
                message = `Request failed with code ${err.errors[0]?.code ?? '<unknown>'}`;
            }
            err.errors.forEach((e: Error) => {
                diagnoses.push({
                    severity: ValidationSeverity.error,
                    message: e.message,
                    path: [],
                });
                logger.error(e.toString());
            });
        }
        message = `[${appName}] ${message}`;
        logger.error(`interop error: ${message}, http=${json5Stringify(http)}`);
        return new InteropError(message, diagnoses, http, err);
    }

    /**
     * Sends a GraphQL request to this app.
     * @param context the context which provides the tenant id and the user's email
     * @param options the graphql request options
     * @returns the GraphQl response
     */
    static sendGraphqlRequest<ResponseDataT>(
        context: Context,
        options: GraphQlRequestOptions,
    ): Promise<GraphQlResponse<ExecutionResult<ResponseDataT>>> {
        return this.sendAnyGraphqlRequest(context, options) as Promise<GraphQlResponse<ExecutionResult<ResponseDataT>>>;
    }

    /**
     * Sends a GraphQL stream request to this app.
     * @param context the context which provides the tenant id and the user's email
     * @param options the graphql request options
     * @returns the GraphQl response
     */
    static sendStreamGraphqlRequest<FirstResultT, PatchResultT>(
        context: Context,
        options: GraphQlRequestOptions,
    ): Promise<GraphQlResponse<AsyncGenerator<ExecutionResult<FirstResultT> | ExecutionPatchResult<PatchResultT>>>> {
        // The combination of chunked and compressed encoding is not appropriate for graphql stream because we need to process the whole response to get the data.
        // So we force the server to send the response in uncompressed chunks.
        // see https://en.wikipedia.org/wiki/Chunked_transfer_encoding#Use_with_compression
        const config = _.merge(options.config, { headers: { 'accept-encoding': 'identity' } });
        return this.sendAnyGraphqlRequest(context, { ...options, config }) as Promise<
            GraphQlResponse<AsyncGenerator<ExecutionResult<FirstResultT> | ExecutionPatchResult<PatchResultT>>>
        >;
    }

    /**
     * Get the health info of an app
     * @param appName the name of the app
     * @returns the health info of the app
     */
    static async getAppHealth(appName: string): Promise<AppHealthInfo> {
        const now = Date.now();
        try {
            const axiosConfig = InteropGraphqlClient.getRequestConfig(
                {},
                {
                    validateStatus: () => true,
                },
            );
            // we must use the interopUrl to get the health of the app because the appUrl is not exposed in kubernetes
            // and the ping route should be removed from the main app in the future
            const url = `${InteropGraphqlClient.getInteropUrl(appName)}/ping`;

            const result = await axios.get<ExecutionResult<unknown, any>>(url, axiosConfig);
            const timestamp = Datetime.now(true);
            return {
                timestamp,
                duration: timestamp.value - now,
                status: result.status,
                statusText: result.statusText,
                isAlive: result.status === 200,
            };
        } catch (err) {
            const timestamp = Datetime.now(true);
            return {
                timestamp,
                duration: timestamp.value - now,
                status: err.response?.status || 500,
                statusText: err.response?.statusText || err.message,
                isAlive: false,
            };
        }
    }
}
