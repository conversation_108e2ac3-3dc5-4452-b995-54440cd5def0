/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-console */
/** @packageDocumentation @module test */
import { AnyRecord, AnyValue, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Datetime, datetime } from '@sage/xtrem-date-time';
import { AuthConfig, Config, Diagnosis, Dict, LocalizeLocale, LogicError } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { CookieOptions, NextFunction, Request, Response } from 'express';
import * as fs from 'fs';
import { ExecutionResult, FormattedExecutionResult, graphql } from 'graphql';
import * as fsp from 'path';
import { SinonStub } from 'sinon';
import { Application, ApplicationCreateOptions, ServiceOption } from '../application';
import { createMetadataSchema } from '../graphql/metadata-schema';
import { StateStatus } from '../node-state';
import { NodeFactory } from '../runtime';
import { Context, ContextOptions, ContextSource, rootUserEmail } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { TestStatus } from '../sql/schema/table';
import { Node } from '../ts-api';
import { ConditionVariable } from './condition-variable';
import { Mocker, mockPlugins, Plugin } from './mocker';
import { dropTestTable } from './tables';
import { TestResetTables } from './test-reset-tables';

export { CookieOptions } from 'express';

const logger = loggers.test;

interface TestContextOptions extends ContextOptions {
    /**
     * The (optional) tenantId to use for the context
     */
    tenantId?: string;
}

/**
 * Single interface used to populate and propagate test options
 */
export interface TestOptions {
    /**
     * The tenantId to use for the context. If not set, Test.defaultTenantId will be used
     */
    tenantId?: string;
    config?: any;
    source?: ContextSource;
    mocks?: string[];
    directory?: string;
    scenario?: string;
    today?: string;
    now?: string;
    user?: { email: string; userName?: string };
    auth?: AuthConfig;
    locale?: LocalizeLocale;
    currentLegislationCode?: string;
    skipMocks?: boolean;
    plugins?: Plugin[];
    testAttributes?: AnyRecord;

    /**
     * the list of service options required for tests
     */
    testActiveServiceOptions?: ServiceOption[];

    /**
     * Disable all CRUD notifications from triggers
     */
    disableAllCrudNotifications?: boolean;

    /**
     * Disable tenant CRUD notifications from triggers
     */
    disableTenantCrudNotifications?: boolean;

    /** Do not use lazy loading of property values */
    noLazyLoading?: boolean;

    /** **DANGER**: This marks that the context will run SQL commands without a tenant filter regardless if they are shared tables or not. */
    unsafeApplyToAllTenants?: boolean;

    /* Timestamp used to limit the lifespan of a graphql query */
    timeLimitAsTimestamp?: number;

    /**
     * An optional description for the context
     */
    description?: () => string;
}

export interface TestConfig {
    mocks?: string[];
    directory?: string;
    scenario?: string;
    testAttributes?: AnyRecord;
}

export interface UserTestOptions {
    user?: string;
    cookie?: boolean | ((name: string, val: string, options: CookieOptions) => void);
    tenantId?: string;
    auth?: AuthConfig;
}

export interface Cookie {
    value: string;
    options: CookieOptions;
}

function makeUserTestOption(options: UserTestOptions, testActiveServiceOptions?: ServiceOption[]): TestOptions {
    const opts: TestOptions = {
        skipMocks: true,
        testActiveServiceOptions,
    };
    if (options.user) {
        opts.user = { email: options.user, userName: '' };
    }
    if (options.cookie) {
        const auth = {
            tenantId: options.tenantId || Test.defaultTenantId,
            ...options.auth,
        } as AuthConfig;
        opts.config = {
            response: {
                constructor: { name: 'ServerResponse' },
                cookie: typeof options.cookie === 'function' ? options.cookie : () => {},
                locals: {
                    auth,
                },
            },
        };
        if (auth.login) {
            opts.user = { email: auth.login, userName: '' };
        }
    } else if (options.auth) {
        opts.auth = options.auth;
    }
    if (options.tenantId) opts.config = { ...opts.config, tenantId: options.tenantId };

    return opts;
}

/**
 * Gets test specific setup module if found.
 * @param {string} applicationDir
 * @returns Setup module or undefined
 */
const getSetupModule = (applicationDir: string): any => {
    if (!fs.existsSync(fsp.join(applicationDir, 'test/setup.ts'))) {
        return undefined;
    }
    delete require.cache[require.resolve(fsp.join(applicationDir, 'test/setup.ts'))];
    return require(fsp.join(applicationDir, 'test/setup'));
};

/**
 * Runs the 'setup' function from 'test/setup.ts'
 *
 * @param {string} applicationDir
 */
function runSetup(applicationDir: string): void {
    const setupModule = getSetupModule(applicationDir);
    if (setupModule) {
        if (typeof setupModule.setup === 'function') {
            setupModule.setup();
        } else {
            console.warn(
                `'setup.ts' file under '${fsp.join(
                    applicationDir,
                    'test/graphql',
                )}' does not contain any function called 'setup'!`,
            );
        }
    }
}

/**
 * Restore time mocks to original values
 * @param {Context} context
 * @param {TestOptions} mocks
 */
const applyContextMocks = async (context: Context, options: TestOptions): Promise<void> => {
    if (options.locale) {
        await context.setTestLocale(options.locale);
    }
    if (options.currentLegislationCode) {
        context.currentLegislationCode = options.currentLegislationCode;
    }
};

interface ApiRouter {
    use(cb: (req: Request, res: Response, next: NextFunction) => void): void;
}

interface TimeStubs {
    todayStub?: SinonStub;
    nowStub?: SinonStub;
}

type State = {
    layers?: string[];
    nowMock?: string;
    application?: string;
};

export class Test {
    private static _application: Application;

    private static _config: Config;

    private static _timeStubs: TimeStubs;

    static _schemaExists: Dict<boolean> = {};

    /**
     * The id of the default tenant used for tests
     */
    static readonly defaultTenantId = '7'.repeat(21);

    static readonly defaultEmail = '<EMAIL>';

    static lastCommitTimestamp: Datetime | null = null;

    /**
     * The name of the default tenant used for tests
     */
    static readonly defaultTenantName = 'Tenant for tests (automatic creation)';

    static get application(): Application {
        return this._application;
    }

    static set application(application: Application) {
        this._application = application;
    }

    /**
     * Creates the 777...777 tenant for tests if it does not exist.
     */
    static async ensureTestTenantExists(context: Context): Promise<void> {
        if (context.application.mainPackage.name === '@sage/xtrem-cop-test') {
            // This package does not depend on xtrem-system, so no sys-tenant table
            return;
        }

        await Context.tenantManager.ensureTenantExists(context, {
            tenant: {
                id: Test.defaultTenantId,
                name: Test.defaultTenantName,
            },
            customer: {
                id: Test.defaultTenantId,
                name: 'Customer for tests (automatic creation)',
            },
        });
    }

    /**
     * Creates a committed context, bound to the tenantId used for tests, unless a specific tenantId is provided in the options.
     */
    static withCommittedContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: TestContextOptions,
    ): Promise<T> {
        const wrappedBody = (context: Context): AsyncResponse<T> => {
            context.on('closed', () => {
                // TODO: Remove later
                logger.debug(() => `Test writable context closed: ${context.lastCommitTimestamp}`);
                Test.lastCommitTimestamp = context.lastCommitTimestamp;
            });

            return body(context);
        };

        return Test.application.withCommittedContext(options?.tenantId || Test.defaultTenantId, wrappedBody, options);
    }

    /**
     * Creates a uncommitted context, bound to the tenantId used for tests, unless a specific tenantId is provided in the options.
     */
    static withUncommittedContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: TestContextOptions,
    ): Promise<T> {
        return Test.application.withUncommittedContext(options?.tenantId || Test.defaultTenantId, body, options);
    }

    /**
     * Creates a read-only context, bound to the tenantId used for tests, unless a specific tenantId is provided in the options.
     */
    static withReadonlyContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: TestContextOptions,
    ): Promise<T> {
        const readonlyOptions = { ...options, lastCommitTimestamp: Test.lastCommitTimestamp ?? undefined };
        const wrappedBody = (context: Context): AsyncResponse<T> => {
            // TODO: Remove later
            logger.debug(() => `Test readonly context closed: ${context.lastCommitTimestamp}`);
            context.on('closed', () => {
                Test.lastCommitTimestamp = context.lastCommitTimestamp;
            });

            return body(context);
        };
        return Test.application.withReadonlyContext(
            options?.tenantId || Test.defaultTenantId,
            wrappedBody,
            readonlyOptions,
        );
    }

    /** @internal */
    private static _state: State;

    static get state(): State {
        /** @disabled_internal */
        return this._state;
    }

    /** @internal */
    static set state(value: State) {
        this._state = value;
    }

    private static get options(): { config: Config; application: Application } {
        if (!this.application) throw new Error('invalid test context: Test.application is undefined');
        return { config: { ...ConfigManager.current, ...this._config }, application: this.application };
    }

    static cliActiveServiceOptions: ServiceOption[];

    static isAllocationLoaded: boolean;

    /**
     * Update config with passed parameter
     * @param patch
     */
    static patchConfig(patch: AnyRecord): void {
        this._config = { ...this._config, ...patch };
    }

    // Needed to test the localizationManager
    static configForTest(): Config {
        return this._config;
    }

    static fixRequestMiddleWare(req: Request, res: Response, next: NextFunction): void {
        res.locals.config = ConfigManager.current;
        res.locals.context = {
            email: Test.defaultEmail,
            tenantId: Test.defaultTenantId,
        };
        res.locals.auth = {
            ...res.locals.auth,
            login: res.locals.context.email,
            tenantId: res.locals.context.tenantId,
            auth0: 'auth0',
        };
        next();
    }

    /**
     * Convert the passed TestOptions to ContextOptions
     * @param options
     */
    static convertOptions(options: TestOptions | undefined): TestContextOptions {
        const opt = this.options as TestContextOptions;

        const defaultEmail = '<EMAIL>';
        const defaultLayers = ['setup', 'test'];

        const layers = options?.config?.layers || defaultLayers;

        if (!opt.config) opt.config = ConfigManager.current;
        if (opt.config.storage?.managedExternal) {
            const defaultUserName = 'ADMIN';
            opt.config.user = options?.user
                ? options.user?.userName || defaultUserName
                : options?.config?.userName || defaultUserName;
        } else {
            // If the user is not supplied the user will default to the login user
            // If both the user and login is supplied in the options the login will take precedence
            if (!options?.auth?.login) {
                opt.config.email = options?.user
                    ? options.user?.email || defaultEmail
                    : options?.config?.email || defaultEmail;
                opt.userEmail = opt.config.email;
                opt.config.user = opt.config.email;
            }
            opt.auth = options?.auth;
        }

        opt.response = options?.config?.response || {};
        opt.request = options?.config?.request || {};
        opt.testNowMock = options?.now || '';
        opt.source = options?.source;
        opt.testLayers = layers;
        opt.testMode = true;
        opt.testConfig = options && { ...options };
        opt.locale = options?.locale || 'base';
        opt.noLazyLoading = options?.noLazyLoading;

        opt.testActiveServiceOptions = this.getTestActiveServiceOptions(options);
        opt.tenantId = options?.tenantId;
        opt.timeLimitAsTimestamp = options?.timeLimitAsTimestamp;
        return opt;
    }

    /**
     * Returns whether the status of a service option is enabled or not by the config.
     * It uses the test config which may be have been patched by Test.patchConfig.
     */
    static isServiceOptionEnabledByTestConfig(serviceOption: ServiceOption): boolean {
        const configLevel = this._config?.serviceOptions?.level;
        if (configLevel === 'released') {
            return serviceOption.status === 'released';
        }
        if (configLevel === 'experimental') {
            return serviceOption.status !== 'workInProgress';
        }
        return true;
    }

    /**
     * Returns the list of service options which should be activated by the test.
     *
     * If the testActiveServiceOptions option was not set and storage is external, we return undefined.
     * The serviceOptionManager will get the service option states from the external storage in this case.
     *
     * This is called by the Context constructor. The options returned by this call are set in the
     * context but are not persisted in the database. This is an efficient way to manage the service options
     * in all tests that use uncommitted contexts.
     */
    static getTestActiveServiceOptions(options?: TestOptions): ServiceOption[] | undefined {
        const result = [] as ServiceOption[];
        const addServiceOption = (serviceOption: ServiceOption): void => {
            if (!result.includes(serviceOption)) result.push(serviceOption);
            if (serviceOption.activates) serviceOption.activates().forEach(addServiceOption);
        };

        if (options?.testActiveServiceOptions) {
            // Service options are explicitly set for this test. We only activate the service options defined
            // by this test option and their dependencies.
            options.testActiveServiceOptions.forEach(addServiceOption);
        } else {
            // No specific list of service options given for this test.
            // If storage is external, we query the database to get the state of the service options
            if (ConfigManager.current.storage?.managedExternal) return undefined;

            // Otherwise we activate all the application's service options that are active by default.
            Object.values(this.application.serviceOptionsByName)
                .filter(serviceOption => serviceOption.isActiveByDefault)
                .forEach(addServiceOption);

            // We also set the options that were specified via CLI options
            // TODO: is this feature really used? If not remove it.
            if (Test.cliActiveServiceOptions) {
                Test.cliActiveServiceOptions.forEach(addServiceOption);
            }
        }
        return result.filter(serviceOption => this.isServiceOptionEnabledByTestConfig(serviceOption));
    }

    /**
     * set plugins from the passed TestOptions
     * @param options
     */
    static createPlugins(options: TestOptions): TestOptions {
        if (!options?.mocks) return options;
        if (!options?.directory) throw new Error('Mocks provided without a directory.');
        options.directory = options.directory.replace(`${fsp.sep}build${fsp.sep}`, fsp.sep);

        if (!options?.scenario) options.scenario = fsp.parse(options.directory).name;
        assert.isArray(options.mocks, 'mocks must be an array.');

        options.plugins = options.plugins || [];
        // check if plugin passed is part of mocks list
        if (options.mocks)
            options.plugins
                .map(p => p.name)
                .forEach(pluginName => {
                    if (options.mocks?.includes(pluginName))
                        throw new Error(`Cannot mock and supply a plugin for the same module, ${pluginName}`);
                });

        options.plugins = [
            ...options.plugins,
            ...options.mocks.map(m => {
                const Mock = mockPlugins[m];
                assert.exists(Mock, `No plugin found for '${m}' mock.`);
                return new Mock(m, fsp.join(options.directory!, m), options.scenario);
            }),
        ];

        return options;
    }

    /** Preliminary steps */
    private static async preliminarySteps(options: TestOptions): Promise<{
        testOptions: TestOptions;
        contextOptions: TestContextOptions;
    }> {
        if (options.directory) options.directory = options.directory.replace(`${fsp.sep}build${fsp.sep}`, fsp.sep);
        Test.applyDateMocks(options);
        const testOptions = this.createPlugins(options);
        const contextOptions = this.convertOptions(testOptions);

        if (!contextOptions.config) contextOptions.config = ConfigManager.current;
        if (!contextOptions.config.storage?.managedExternal) {
            const schemaName = this.application.schemaName;
            if (!Test._schemaExists[schemaName]) {
                Test._schemaExists[schemaName] = true;
                await Application.createDbSchema(schemaName);
            }
        }

        return { testOptions, contextOptions };
    }

    /**
     * Execute the passed body with a readonly transaction
     *  converting the passed test options to context options and applying any mocks passed
     * @param body
     * @param opts
     */
    static withContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options: TestOptions = {},
    ): Promise<T> {
        // TODO: do we really need to deal with invalid factories here ?
        const isInvalid = (factory: NodeFactory): boolean => {
            if (factory.baseFactory && factory.baseFactory.table && isInvalid(factory.baseFactory)) return true;
            return factory.table && factory.table.testStatus !== TestStatus.loaded;
        };
        const invalidFactories = Test.application.getSqlPackageFactories().filter(isInvalid);

        return this.withMocks(async (testOptions, contextOptions) => {
            await asyncArray(invalidFactories).forEach(async factory => {
                try {
                    await factory.fixAutoIncrementSequences(null);
                } catch {
                    logger.warn(`Could not fix auto increment sequence for ${factory.name}`);
                }
                factory.table.testStatus = TestStatus.loaded;
            });
            return Test.withUncommittedContext(
                context => Test.withContextMocks(context, { testOptions, contextOptions }, () => body(context)),
                contextOptions,
            );
        }, options);
    }

    private static async withMocks<T extends AnyValue | void>(
        body: (testOptions: TestOptions, contextOptions: ContextOptions) => AsyncResponse<T>,
        options: TestOptions = {},
    ): Promise<T> {
        const { testOptions, contextOptions } = await this.preliminarySteps(options);
        try {
            return await body(testOptions, contextOptions);
        } finally {
            if (this._timeStubs) Test.restoreDateMocks();
        }
    }

    /**
     * This method activates a list of service options, executes the test body, and then restores the service options
     * to their prior state.
     *
     * Unlike the service options returns by Test.getTestActiveServiceOptions(options), the changes of service option
     * states are persisted into the database.
     * This is used by test which query the database from other contexts to get the state of service options,
     * typically tests that activate the isDemoTenant service option.
     */
    static async withCommittedServiceOptions<T extends AnyValue>(
        tenantId: string,
        body: () => AsyncResponse<T>,
        serviceOptions: ServiceOption[],
    ): Promise<T> {
        if (!serviceOptions.length) return body();
        const changedServiceOptions = await this.application.asRoot.withCommittedContext(tenantId, async context => {
            const serviceOptionsToChange = await asyncArray(serviceOptions)
                .filter(async serviceOption => !(await context.isServiceOptionEnabled(serviceOption)))
                .toArray();
            return this.application.serviceOptionManager.activateServiceOptions(context, serviceOptionsToChange);
        });
        try {
            return await body();
        } finally {
            await this.application.asRoot.withCommittedContext(tenantId, context =>
                this.application.serviceOptionManager.deactivateServiceOptions(context, changedServiceOptions),
            );
        }
    }

    static withUserContext<T extends AnyValue>(
        body: (context: Context) => AsyncResponse<T>,
        options: UserTestOptions,
        serviceOptions?: ServiceOption[],
    ): Promise<T> {
        // we need to call the service option activation/deactivation using a separate context with that uses the root user
        // to avoid the resolution of the user when evaluating the transaction user.
        // This is required for isDemoTenant service option
        const contextOptions = this.convertOptions({
            skipMocks: true,
            testActiveServiceOptions: serviceOptions,
        });

        return this.withCommittedServiceOptions(
            contextOptions.tenantId || Test.defaultTenantId,
            () => Test.withContext(body, makeUserTestOption(options, serviceOptions)),
            serviceOptions || [],
        );
    }

    /**
     * Execute the passed body with a readonly transaction
     *  converting the passed test options to context options and applying any mocks passed
     * @param body
     * @param opts
     */
    /** @deprecated */
    static readonly<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options: TestOptions = {},
    ): Promise<T> {
        return this.withMocks(
            (testOptions, contextOptions) =>
                Test.withReadonlyContext(
                    context => Test.withContextMocks(context, { testOptions, contextOptions }, () => body(context)),
                    contextOptions,
                ),
            options,
        );
    }

    /**
     * Execute the passed body with a committed transaction
     *  converting the passed test options to context options and applying any mocks passed
     * @param body
     * @param opts
     */
    /** @deprecated */
    static committed<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options: TestOptions = {},
    ): Promise<T> {
        return this.withMocks(
            (testOptions, contextOptions) =>
                Test.withCommittedContext(
                    context => Test.withContextMocks(context, { testOptions, contextOptions }, () => body(context)),
                    contextOptions,
                ),
            options,
        );
    }

    /**
     * Execute the passed body with a uncommitted transaction
     *  converting the passed test options to context options and applying any mocks passed
     * @param body
     * @param opts
     */
    /** @deprecated */
    static uncommitted<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options: TestOptions = {},
    ): Promise<T> {
        return this.withMocks(
            (testOptions, contextOptions) =>
                Test.withUncommittedContext(
                    context => Test.withContextMocks(context, { testOptions, contextOptions }, () => body(context)),
                    contextOptions,
                ),
            options,
        );
    }

    static async setup(application: Application): Promise<void> {
        const config = ConfigManager.load(application.dir, 'test');

        if (!config.storage?.managedExternal) {
            const t0 = Date.now();
            await Application.createDbSchema(application.schemaName);
            logger.info(`created application schema ${application.schemaName} (${Date.now() - t0} ms)`);
        }
        this.application = application;
        // Call this to set application property in NodeFactory
        application.getAllNodes();
    }

    static getTestSchemaName = (dir: string): string => {
        const packageInfo = require(fsp.resolve(dir, 'package.json'));
        return `${packageInfo.name.replace(/@[a-z][a-z0-9.-]*\//, '').replace(/[-~]/g, '_')}_test`;
    };

    static getDirectory = (dir: string, fileName: string): string => {
        const filepath = fsp.join(dir, fileName);
        if (fs.existsSync(filepath) && !fs.statSync(filepath).isDirectory()) return dir;
        return '';
    };

    static async createCliApplication(options: ApplicationCreateOptions): Promise<Application> {
        if (options.api) options.api = { ...options.api, nodes: { ...options.api.nodes } };
        const dir = fsp.resolve(options.buildDir!, '..');
        ConfigManager.load(dir);
        const application = await Application.create({
            schemaName: this.getTestSchemaName(dir),
            applicationType: 'dev-tool',
            ...options, // may override previous options
        });

        return application;
    }

    static async createTestApplication(options: ApplicationCreateOptions): Promise<Application> {
        ConfigManager.load(__dirname);

        const application = await Application.create({
            schemaName:
                options.schemaName || ConfigManager.current.storage?.managedExternal
                    ? 'xtrem_external'
                    : this.getTestSchemaName(fsp.resolve(options.buildDir!, '..')),
            applicationType: 'test',
            ...options,
        });

        if (ConfigManager.current.storage?.managedExternal) return application;

        const contextOptions = {
            testLayers: ['setup', 'test'],
            userEmail: rootUserEmail,
            withoutTransactionUser: true,
        };
        application.activityManager.resolvePermissions();
        await TestResetTables.resetTestTables(application, contextOptions);

        application.startListeners(['testListen']);

        return application;
    }

    static async setupApi(application: Application, apiApp: ApiRouter): Promise<Application> {
        await Test.setup(application);
        apiApp.use((req: Request, res: Response, next: NextFunction) => {
            res.locals.config = ConfigManager.current;
            res.locals.config.email = res.locals.config.email || '<EMAIL>';
            next();
        });
        return application;
    }

    // Temporary call - tests that call this should probably be moved to another package
    static initializeManagers(context: Context): void {
        Context.managers.forEach(manager => manager.initializeManager(context));
    }

    static async cleanUp(application: Application): Promise<void> {
        const factories = application.getAllSortedFactories().slice().reverse();
        await asyncArray(factories).forEach(f => dropTestTable(f.nodeConstructor));
    }

    static applyDateMocks(options: TestOptions): void {
        this._timeStubs = {};

        if (options.now && options.today) {
            throw new LogicError("specify 'now' or 'today' in test options but not both");
        }

        if (options.now) {
            datetime.overrideNow(options.now);
        } else if (options.today) {
            datetime.overrideNow(`${options.today}T12:00:00Z`);
        }
    }

    static restoreDateMocks = (): void => {
        // Restore date mocks to original values
        // if (Test._timeStubs.nowStub) Test._timeStubs.nowStub.restore();
        datetime.overrideNow(null);
    };

    private static withContextMocks<T extends AnyValue | void>(
        context: Context,
        options: { testOptions: TestOptions; contextOptions: ContextOptions },
        body: () => AsyncResponse<T>,
    ): AsyncResponse<T> {
        if (options.testOptions.skipMocks) return body();
        // execute test with mocks
        return Mocker.withMocks(async () => {
            runSetup(context.application.dir);
            await applyContextMocks(context, options.testOptions);
            return body();
        }, options.testOptions.plugins);
    }

    /**
     * Creates a condition variable.
     * @param name the name of the condition variable (for logs)
     * @returns the condition variable.
     */
    static createConditionVariable(name: string): ConditionVariable {
        return new ConditionVariable(name);
    }

    /**
     * **WARNING:**
     * This rolls back the transaction state cache and makes all previously cached node states stale. Please use wisely
     *
     * @param context
     */
    static async rollbackCache(context: Context): Promise<void> {
        if (context.application.applicationType !== 'test') throw new Error('Cannot rollback cache outside of tests');
        await context.flushDeferredSaves();
        await context.flushDeferredActions();
        context.transaction.rollbackCache();
        context.prefetcher.reset();
    }

    static resetPrefetcher(context: Context): void {
        context.prefetcher.reset();
    }

    private static mapLineNumber(str: string): string {
        return str.replace(/-1000\d{6}/g, '<???>');
    }

    private static mapDiagnosisPaths(diagnosis: Diagnosis): Diagnosis {
        return {
            message: this.mapLineNumber(diagnosis.message),
            path: diagnosis.path.map(this.mapLineNumber),
            severity: diagnosis.severity,
        };
    }

    private static mapDiagnosesPaths(diagnoses: Diagnosis[]): Diagnosis[] {
        return diagnoses.map(diagnosis => Test.mapDiagnosisPaths(diagnosis));
    }

    static assertDeepEqualDiagnoses(actual: Diagnosis[], expected: Diagnosis[]): void {
        assert.deepEqual(this.mapDiagnosesPaths(actual), expected);
    }

    /**
     * Sends a graphql request to the test application.
     */
    static async graphql<ResultT extends AnyValue>(context: Context, request: string): Promise<{ data: ResultT }> {
        return graphql({
            schema: await Test.application.getGraphQLSchema(),
            source: request,
            contextValue: context,
        }) as Promise<{ data: ResultT }>;
    }

    /**
     * Sends a graphql request to the test application.
     */
    static metadataGraphql<ResultT extends AnyValue>(context: Context, request: string): Promise<{ data: ResultT }> {
        return graphql({
            schema: createMetadataSchema(Test.application),
            source: request,
            contextValue: context,
        }) as Promise<{ data: ResultT }>;
    }

    /** Test hacks that we should eliminate */
    static hacks = {
        isNodeStateUpdatable(node: Node): boolean {
            return node.$.state.status === StateStatus.updatable;
        },
        setNodeStateUpdatable(node: Node): void {
            node.$.state.status = StateStatus.updatable;
        },
    };
}

/**
 * graphql 16.1 preserves the error objects instead of converting them to plain objects.
 * We convert them here to get the same result in direct in-memory calls as with HTTP transport.
 * This facilitates error checking in unit tests.
 */
export function mapGraphQlResult<T extends AnyValue>(result: ExecutionResult<T>): FormattedExecutionResult<T> {
    if (!result.errors) return result;
    return {
        ...result,
        errors: result.errors.map(err => err.toJSON()),
    };
}
