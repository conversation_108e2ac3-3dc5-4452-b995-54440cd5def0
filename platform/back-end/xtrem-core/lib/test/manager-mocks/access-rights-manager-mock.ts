import { AsyncResponse, asyncArray } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { ServiceOption } from '../../application';
import { NodeKey, decorators } from '../../decorators';
import {
    AccessRightsManager,
    ActivityInfo,
    Context,
    NodeFactory,
    UserAccess,
    UserInfo,
    adminDemoPersona,
} from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { AnyFilterObject, Node } from '../../ts-api';
import { StringDataType } from '../../types';
import { isDemoTenant } from '../service-options';
import { TestUserType, TestUserTypeDataType } from './test-user-type-enum';
import { userData } from './user-data';

export const serviceOptionsMock: Dict<ServiceOption> = {
    isDemoTenant,
};

const dataTypes = {
    name: new StringDataType({ maxLength: 80 }),
    email: new StringDataType({ maxLength: 320 }),
};

@decorators.node<TestUser>({
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isPublished: true,
    indexes: [
        {
            orderBy: { email: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
})
export class TestUser extends Node {
    @decorators.stringProperty<TestUser, 'email'>({
        isStored: true,
        isFrozen: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.email,
    })
    readonly email: Promise<string>;

    @decorators.booleanProperty<TestUser, 'isFirstAdminUser'>({ isStored: true })
    readonly isFirstAdminUser: Promise<boolean>;

    @decorators.stringProperty<TestUser, 'firstName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.name,
    })
    readonly firstName: Promise<string>;

    @decorators.stringProperty<TestUser, 'lastName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.name,
    })
    readonly lastName: Promise<string>;

    @decorators.booleanProperty<TestUser, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<TestUser, 'displayName'>({
        isPublished: true,
        async getValue() {
            return `${await this.lastName}, ${await this.firstName}`;
        },
    })
    readonly displayName: Promise<string>;

    @decorators.enumProperty<TestUser, 'userType'>({
        isPublished: true,
        isStored: true,
        dataType: () => TestUserTypeDataType,
        defaultValue: 'application',
    })
    readonly userType: Promise<TestUserType>;

    @decorators.booleanProperty<TestUser, 'isAdministrator'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isAdministrator: Promise<boolean>;

    @decorators.booleanProperty<TestUser, 'isDemoPersona'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        serviceOptions: () => [serviceOptionsMock.isDemoTenant],
    })
    readonly isDemoPersona: Promise<boolean>;

    @decorators.booleanProperty<TestUser, 'isApiUser'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isApiUser: Promise<boolean>;

    async toUserInfo(): Promise<UserInfo> {
        return {
            _id: this._id,
            email: await this.email,
            isActive: await this.isActive,
            userType: await this.userType,
            firstName: await this.firstName,
            lastName: await this.lastName,
            isAdministrator: await this.isAdministrator,
            isApiUser: await this.isApiUser,
            isDemoPersona: await this.isDemoPersona,
            userName: await this.email,
        };
    }
}

@decorators.node<TestActivity>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    tableName: 'Activity',
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
    isSharedByAllTenants: true,
    isPlatformNode: true,
})
export class TestActivity extends Node {
    @decorators.stringProperty<TestActivity, 'name'>({
        isStored: true,
        isFrozen: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly name: Promise<string>;
}

// Our mocks are synchronous but real manager may call async function with wait_.
// So we check that wait_ can be called in every mock.
async function checkCanWait(): Promise<void> {
    await new Promise<void>(resolve => {
        resolve();
    });
}

const userMap: Dict<UserInfo> = userData.reduce((r, u) => {
    r[u.email] = u;
    return r;
}, {} as Dict<UserInfo>);

/**
 * A mock for AccessRightsManager: should only be used for unit-tests
 */
export const accessRightsManagerMock: AccessRightsManager = {
    async getUserAccessFor(
        context: Context,
        nodeName: string,
        propertyOrOperation = '',
        options?: { authorizationCode?: string },
    ): Promise<UserAccess> {
        await checkCanWait();
        const standardAuthorizationOption: Dict<string> = {
            create: 'C',
            update: 'M',
            delete: 'S',
            read: '',
        };

        const operation = standardAuthorizationOption[propertyOrOperation] || propertyOrOperation;
        const functionCode =
            options?.authorizationCode || context.application.getFactoryByName(nodeName)?.authorizationCode;

        const userAccess: UserAccess = { sites: null, accessCodes: null, status: 'authorized' };
        const userEmail = (await context.user)?.email;

        if (nodeName === 'TestFish' && userEmail === '<EMAIL>' && propertyOrOperation === 'read') {
            return { sites: null, accessCodes: null, status: 'unauthorized' };
        }

        // Mock behaviour to allow only specific test cases
        switch (functionCode) {
            case 'FCT1':
                if (!['C', 'M', 'Y', 'createOperation', 'lookup'].includes(operation)) {
                    userAccess.status = 'unauthorized';
                    userAccess.sites = [];
                }
                break;
            case 'SECURE':
                switch (userEmail) {
                    case '<EMAIL>':
                        userAccess.sites = ['1', '3'];
                        break;
                    case '<EMAIL>':
                        break;
                    case '<EMAIL>':
                        userAccess.sites = [];
                        break;
                    case '<EMAIL>':
                        userAccess.status = 'unauthorized';
                        userAccess.sites = [];
                        break;
                    case '<EMAIL>':
                        switch (propertyOrOperation) {
                            case 'read':
                            case 'lookup':
                                break;
                            case 'site':
                                userAccess.status = 'readonly';
                                userAccess.sites = [];
                                break;
                            case 'access':
                                userAccess.status = 'unavailable';
                                userAccess.sites = [];
                                break;
                            case 'code':
                                userAccess.status = nodeName === 'TestSecure' ? 'readonly' : 'unauthorized';
                                break;
                            case 'pageExtension':
                                userAccess.status = 'unavailable';
                                userAccess.sites = [];
                                break;
                            default:
                                userAccess.status = 'unauthorized';
                                break;
                        }
                        break;
                    default:
                        break;
                }
                break;
            case 'LOOKUP':
                switch (userEmail) {
                    case '<EMAIL>':
                        switch (propertyOrOperation) {
                            case 'lookup':
                            case 'code':
                                break;
                            case 'noLookup':
                                userAccess.status = 'unauthorized';
                                break;
                            default:
                                userAccess.status = 'unauthorized';
                                break;
                        }
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }

        switch (userEmail) {
            case '<EMAIL>':
                userAccess.accessCodes = ['ACCESS1', 'ACCESS2'];
                break;
            case '<EMAIL>':
                userAccess.accessCodes = null;
                break;
            case '<EMAIL>':
                userAccess.accessCodes = [];
                break;
            default:
                userAccess.accessCodes = null;
        }

        return userAccess;
    },

    isAccessCodeAvailable(context: Context, accessCode: string): boolean {
        return accessCode !== 'RESTRICTED';
    },

    /* eslint-disable @typescript-eslint/no-unused-vars */
    getOperationSecurityFilter(
        context: Context,
        factory: NodeFactory,
        operation: string,
    ): Promise<AnyFilterObject | undefined> {
        return Promise.resolve(undefined);
    },
    /* eslint-enable @typescript-eslint/no-unused-vars */

    async createAdminUser(): Promise<void> {
        /* stub */
    },

    async createRequiredUsers(context: Context) {
        const userNode = this.getUserNode();
        const userFactory = context.application.getFactoryByName(userNode.name);
        if (userFactory.storage === 'external') return;

        await asyncArray(userData.filter(u => u.isRequired)).forEach(async u => {
            // shallow copy to avoid modifying the original object
            const user = { ...u };
            if (!(await context.tryRead(userNode, { email: user.email } as NodeKey<Node>))) {
                const userType = user.userType;

                // we cannot save or update a system user so we first create it as an 'application' user
                if (userType === 'system') {
                    user.userType = 'application';
                }
                const createUser = await context.create(userNode, _.omit(user as any, ['_id']));
                await createUser.$.save();
                // then update to force its _id to 1 and set its userType back to 'system'
                if (userType === 'system') {
                    const sql = `UPDATE ${context.schemaName}.${userFactory.tableName} SET _id = 1, _create_user = 1, _update_user = 1, user_type = $1 WHERE email = $2 AND _tenant_id = $3`;
                    await context.executeSql(sql, ['system', user.email, context.tenantId]);
                }
            }
        });
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ensureAdminPersonaCreated(context: Context): UserInfo {
        return adminDemoPersona;
    },

    async getUser(context: Context, email: string): Promise<UserInfo> {
        await checkCanWait();

        if (context.application.getFactoryByConstructor(this.getUserNode()).storage === 'external') {
            // For now we use in-memory data
            const user = userMap[email && email.toLowerCase()];
            if (!user) {
                throw new Error(`User '${email}' not found`);
            }
            return user;
        }

        const user = await context.tryRead(this.getUserNode(), { email } as NodeKey<Node>);

        if (!user) {
            throw new Error(`User '${email}' not found`);
        }
        // to review getUser should be in the sysManager like getUserNode
        return (user as any).toUserInfo();
    },

    async getCurrentUser(context: Context): Promise<UserInfo> {
        // For security, the default cannot be the root user, the context needs to be correctly initialized
        try {
            const user = await context.user;
            if (!user) return await context.rootUser;
            return { _id: String(user._id), email: user.email };
        } catch {
            return context.rootUser;
        }
    },

    getUserNode() {
        return CoreHooks.sysManager.getUserNode();
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getPermissions(context: Context, activity: string): string[] {
        return ['create', 'read', 'update', 'delete', 'lookup'];
    },

    createActivities(): void {
        return undefined;
    },

    updateActivities(): void {
        return undefined;
    },

    deleteActivities(): void {
        return undefined;
    },

    getActivityNode() {
        return TestActivity;
    },

    async getActivitiesInfo(context: Context): Promise<ActivityInfo[]> {
        const activitiesInDatabase = await context.query(TestActivity).toArray();

        const activityInfo = (await asyncArray(activitiesInDatabase)
            .map(async activity => {
                return { _id: activity._id, name: await activity.name };
            })
            .toArray()) as ActivityInfo[];

        return activityInfo;
    },

    supportsPersona(context: Context): AsyncResponse<boolean> {
        return context.isServiceOptionEnabled(serviceOptionsMock.isDemoTenant);
    },

    getPersonaUser(context: Context, email: string): UserInfo | null {
        const persona = userMap[email.toLowerCase()];
        return persona.isDemoPersona ? persona : null;
    },

    async getDemoPersonas(context: Context): Promise<UserInfo[]> {
        if (!(await this.supportsPersona(context))) return [];
        const loginUser = await context.loginUser;
        if (!loginUser) return [];
        const adminPersona = loginUser.isAdministrator
            ? await context.query(TestUser, { filter: { isDemoPersona: true, isAdministrator: true } }).toArray()
            : [];
        const personas = await context
            .query(TestUser, { filter: { isDemoPersona: true, isAdministrator: false } })
            .toArray();
        return asyncArray([...adminPersona, ...personas])
            .map(u => u.toUserInfo())
            .toArray();
    },

    invalidateAuthorizationCache(context: Context): Promise<void> {
        return context.invalidateCachedCategory('AUTHORIZATION', {});
    },

    /**
     * Returns whether the 'Authorization access control' service option is enabled
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isAuthorizationServiceOptionEnabled(_context: Context): AsyncResponse<boolean> {
        return false;
    },
};
