import { Diagnosis } from '@sage/xtrem-shared';
import { assert, config as chaiConfig } from 'chai';
import { Context } from '../runtime';

// default truncate threshold is 40, too small for our tests
chaiConfig.truncateThreshold = Math.max(chaiConfig.truncateThreshold, 200);

/**
 * Special assert function to compare actual and expected values in tests.
 *
 * It checks if `expected` is a subset of `actual` and throws an error if not.
 *
 * It does not check all the members of `actual`.
 * It only checks the ones that are present in `expected`.
 *
 * It supports some special syntaxes:
 * -    if `expected` is a string starting with a tilde (`~`), it is interpreted as a regular expression
 *      and the actual value is matched against it.
 * -    if `expected` is the string `<<undefined>>`, it checks if the actual value is `undefined`.
 *
 * Note: if you want strict equality with a string starting with a tilde use ~^your_string$.
 *
 * Examples:
 *  - `assertDeepPartialMatch({ a: 1, b: { c: 2, d: 3 }, e: 4  }, { a: 1, b: { d: 3 } })` passes
 *  - `assertDeepPartialMatch({ a: 1, b: { c: 2, d: 3 }, e: 4  }, { a: 1, b: { d: 3 }, f: 5 })` does not pass.
 *  - `assertDeepPartialMatch({ a: 'hello', b: 'world' }, { a: 'hello' , b: '~orl' })` passes
 *  - `assertDeepPartialMatch({ a: 'hello', b: 'world' }, { a: 'hello' , b: 'orl' })` does not pass
 *  - `assertDeepPartialMatch({ a: 'hello' }, { a: 'hello' , b: '<<undefined>>' })` passes
 */
export function assertDeepPartialMatch(actual: any, expected: any, message = ''): void {
    const errors = [] as string[];
    walkDeepPartialMatch(actual, expected, 'on expected', errors);
    if (errors.length > 0) {
        const prefix = message ? `${message}: ` : '';
        if (errors.length === 1) {
            assert.fail(`${prefix}mismatch ${errors[0]}`);
        }
        assert.fail(`${prefix}${errors.length} mismatches:${['', ...errors].join('\n    - ')}`);
    }
}

function walkDeepPartialMatch(actual: any, expected: any, path: string, errors: string[]): void {
    try {
        if (expected == null || typeof expected !== 'object') {
            if (expected === '<<undefined>>') {
                assert.isUndefined(actual, path);
            } else if (typeof expected === 'string' && expected.startsWith('~')) {
                assert.match(actual, new RegExp(expected.slice(1)), path);
                assert.match(actual, new RegExp(expected.slice(1)), path);
            } else {
                assert.equal(actual, expected, path);
            }
        } else if (Array.isArray(expected)) {
            assert.isArray(actual, path);
            assert.strictEqual(
                actual.length,
                expected.length,
                `${path}.length: expected length ${expected.length} [${expected}], got: length ${actual.length} [${actual}]`,
            );
            expected.forEach((item, index) => walkDeepPartialMatch(actual[index], item, `${path}[${index}]`, errors));
        } else {
            Object.keys(expected).forEach(key =>
                walkDeepPartialMatch(actual[key], expected[key], `${path}.${key}`, errors),
            );
        }
    } catch (e) {
        errors.push(e.message);
    }
}

export async function assertIsRejectedWithDiagnoses(
    promise: Promise<any>,
    {
        message,
        diagnoses,
    }: Partial<{ message: string | RegExp; diagnoses: Diagnosis[]; innerError: { message: string } }>,
): Promise<void> {
    try {
        await promise;
        assert.fail('Expected promise to be rejected');
    } catch (err) {
        assertDeepPartialMatch(err, { message, extensions: { diagnoses } });
    }
}

export async function assertIsRejectedWithContextDiagnoses(
    promise: Promise<any>,
    context: Context,
    expected: Partial<{ message: string | RegExp; diagnoses: Diagnosis[]; innerError: { message: string } }>,
): Promise<void> {
    try {
        await promise;
        assert.fail('Expected promise to be rejected');
    } catch (err) {
        assertDeepPartialMatch({ message: err.message, diagnoses: context.diagnoses }, expected);
    }
}

export async function assertIsRejectedWithContextDiagnose(
    promise: Promise<any>,
    context: Context,
    message: string | RegExp,
): Promise<void> {
    try {
        await promise;
        assert.fail('Expected promise to be rejected');
    } catch {
        assertDeepPartialMatch(context.diagnoses, [{ message }]);
    }
}
