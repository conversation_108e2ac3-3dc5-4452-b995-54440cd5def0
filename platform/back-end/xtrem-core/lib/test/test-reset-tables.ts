import { CoreHooks } from '..';
import { Application, ApplicationCreateSqlSchema } from '../application';
import { Context, ContextOptions, NodeFactory } from '../runtime';
import { loggers } from '../runtime/loggers';
import { DatabaseSqlContext } from '../sql';
import { Test } from './test';

export abstract class TestResetTables {
    /**
     * This method is called at the beginning of Test.withContext.
     * Its goal is to make sure the database is the exact mirror of the csv files contained in the layers
     * selected for the test.
     *
     * CAUTION: if needed, the schema will be dropped and re-created. Nothing must be written
     * to the database before calling this function otherwise it will be LOST.
     *
     * First we determine if the layers have changed since the previous test.
     *
     *  - If the current package, the layers or the now mock have changed, it is necessary to fully
     *    reload the entire database.
     *
     *  - If not, as all the applicative tests are supposed to be enclosed within a Test.withContext block,
     *    they are executed in uncommitted mode, so previous transactions have been rolled back.
     *    In that case, the only step is to reset the sequences of each table dirtied by a previous test.
     * @param contextOptions
     */
    static async resetTestTables(
        application: Application,
        contextOptions: ContextOptions,
        options?: { force?: boolean },
    ): Promise<void> {
        if (!options?.force && !(await this.resetNeeded(application, contextOptions))) return;

        // Either layers, the current package or the time mock have changed.
        // It is necessary to fully reload the entire database.
        await this._resetTables(application, contextOptions);
    }

    /**
     * Reset the sequences for all the tables
     * @param application
     * @param contextOptions
     */
    static async resetTestTableSequences(application: Application, contextOptions: ContextOptions): Promise<void> {
        const factories = await NodeFactory.fixSequences(application, Test.defaultTenantId);

        factories.forEach(factory => {
            factory.table.markAsLoadedForTests(application, contextOptions);
        });
    }

    private static resetNeeded(application: Application, contextOptions: ContextOptions): Promise<boolean> {
        return application.withReadonlyContext(
            null, // Only DDL queries will be run
            context => {
                // get current test state from static attribute in Context
                const currentTestState = Test.state;

                // Check whether DB needs to be reloaded
                return (
                    !currentTestState ||
                    application.name !== currentTestState.application ||
                    JSON.stringify(context.testLayers || []) !== JSON.stringify(currentTestState.layers) ||
                    (context.testNowMock || '') !== currentTestState.nowMock
                );
            },
            contextOptions,
        );
    }

    private static async _resetTables(application: Application, contextOptions: ContextOptions): Promise<void> {
        if (await application.createContextForDdl(context => context.managedExternal)) return;
        const dbContext = new DatabaseSqlContext();

        loggers.application.warn(`Drop schema ${application.schemaName}`);
        await dbContext.dropSchemaIfExists(application.schemaName);
        loggers.application.warn(`Create schema ${application.schemaName}`);
        await dbContext.createSchemaIfNotExists(application.schemaName);

        loggers.application.warn(`Create tables for schema ${application.schemaName}`);
        await application.createContextForDdl(context => ApplicationCreateSqlSchema.createTables(context, []), {
            description: () => 'Create test tables',
        });

        await application.asRoot.withCommittedContext(
            null,
            async context => {
                await application.serviceOptionManager.createOrUpdateServiceOptions(context);
                await application.serviceOptionManager.deleteObsoleteServiceOptions(context);
            },
            { description: () => 'resetTables.updateServiceOptions' },
        );

        await application.asRoot.withCommittedContext(
            Test.defaultTenantId,
            context => Test.ensureTestTenantExists(context),
            {
                ...contextOptions,
                withoutTransactionUser: true,
            },
        );

        await CoreHooks.testManager.initTestTenantActivePackages(application);

        await application.asRoot.withCommittedContext(
            Test.defaultTenantId,
            async context => {
                await Context.accessRightsManager.createActivities(context);
                await Context.accessRightsManager.updateActivities(context);
                await Context.accessRightsManager.deleteActivities(context);

                Test.state = {
                    application: application.name,
                    layers: context.testLayers || [],
                    nowMock: context.testNowMock || '',
                };
            },
            contextOptions,
        );

        await CoreHooks.metadataManager.upgradeMetadata(application);
        await CoreHooks.testManager.loadTestData(
            application,
            { testLayers: contextOptions.testLayers },
            Test.defaultTenantId,
        );

        const sortedFactories = application.getAllSortedFactories();
        sortedFactories.forEach(factory => {
            factory.table.markAsLoadedForTests(application, contextOptions);
        });
    }
}
