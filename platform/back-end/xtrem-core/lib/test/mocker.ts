/** @ignore */ /** */
import { AsyncResponse } from '@sage/xtrem-async-helper';
import { Datetime } from '@sage/xtrem-date-time';
import { AnyValue, Dict, LogicError, tryJsonParse } from '@sage/xtrem-shared';
import * as axios from 'axios';
import { assert } from 'chai';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as fsp from 'path';

export abstract class Plugin {
    constructor(
        public readonly name: string,
        public readonly path: string,
        public readonly scenario: string,
    ) {}

    // eslint-disable-next-line class-methods-use-this
    mock(): any {
        throw new Error('Must implement mock!');
    }
}

export class Mocker {
    /**
     * static attribute that stores a set of all the active mock plugins
     */
    private static plugins: Map<string, Plugin> = new Map();

    /**
     * Add plugin to static plugin set
     * @param plugin
     */
    private static addPlugin(plugin: Plugin): void {
        Mocker.plugins.set(plugin.name, plugin);
    }

    /**
     * Execute body with passed plugins in enabled
     * @param body
     * @param mockerPlugins
     */
    static async withMocks<T extends AnyValue | void>(
        body: () => AsyncResponse<T>,
        mockerPlugins: Plugin[] = [],
    ): Promise<T> {
        if (mockerPlugins.length === 0) {
            return body();
        }

        // enable mocks by adding them to the plugin set
        mockerPlugins.forEach(p => {
            Mocker.addPlugin(p);
        });

        try {
            return await body();
        } finally {
            // disable all mocks by clearing plugins set
            Mocker.plugins.clear();
        }
    }

    /**
     * return the mock for passed module or execute the standard require of the calling module (moduleRequire)
     * @param module
     * @param moduleRequire
     */
    static get(module: string, moduleRequire: any): any {
        return Mocker.plugins.get(module)?.mock() || moduleRequire(module);
    }
}

const locateFirstDiff = (s1: string, s2: string): { line: number; column: number } => {
    let line = 1;
    let column = 1;
    for (let i = 0; i < s1.length; i += 1) {
        if (s1[i] !== s2[i]) return { line, column };
        if (s1[i] === '\n') {
            line += 1;
            column = 1;
        } else {
            column += 1;
        }
    }
    return { line, column };
};

interface DiffElement {
    key?: string;
    actual?: any;
    expected?: any;
}

function deepDiff(a: any, b: any): DiffElement[] {
    if (typeof a !== 'object' || typeof b !== 'object') {
        return [];
    }
    return _.reduce(
        a,
        (result, actual, key) => {
            const expected = b[key];
            return _.isEqual(actual, expected)
                ? result
                : result.concat({ key, actual, expected }, deepDiff(actual, expected));
        },
        [] as DiffElement[],
    );
}

export class AxiosPlugin extends Plugin {
    private arrayCounter = 0;

    private entries: any[];

    /**
     * Return the mock for the axios module
     */
    override mock(): any {
        return (actual: axios.AxiosRequestConfig) => {
            if (actual.headers?.Authorization) delete actual.headers.Authorization;
            const fileName = `${fsp.join(this.path, this.scenario)}.json`;
            if (this.entries == null) {
                assert.isTrue(fs.existsSync(fileName), `Couldn't find mandatory '${fileName}' file.`);
                const file = fs.readFileSync(fileName, 'utf8');
                assert.isDefined(file, `Couldn't read '${fileName}'.`);
                const data = tryJsonParse(file);
                assert.exists(data, `'${fileName}' is not a valid JSON file.`);
                this.entries = !Array.isArray(data) ? [data] : data;
            }
            assert.isAtMost(
                this.arrayCounter,
                this.entries.length - 1,
                `'${fileName}' not all requests are a provided.`,
            );

            const fileContent = this.entries[this.arrayCounter];
            assert.exists(fileContent.request, `'${fileName}' must contain a 'request' key'.`);
            assert.exists(fileContent.response, `'${fileName}' must contain a 'response' key'.`);
            const actualObj = JSON.parse(JSON.stringify(actual));
            const expectedObj = fileContent.request;

            try {
                assert.deepEqual(actualObj, expectedObj);
            } catch {
                const tmpDir = fsp.join(this.path, 'tmp');
                // wrap the actual/expected objects into an axios payload so that the scenario file
                // can be fixed by copy/pasting the contents the tmp/*-actual.json file.
                const actualText = JSON.stringify({ ...fileContent, request: actualObj }, null, 4);
                const expectedText = JSON.stringify({ ...fileContent, request: expectedObj }, null, 4);
                const { line, column } = locateFirstDiff(actualText, expectedText);

                const now = Datetime.now(true).toString();
                const name = `${now}-${_.snakeCase(this.scenario)}`;
                fs.mkdirSync(tmpDir, { recursive: true });
                fs.writeFileSync(fsp.join(tmpDir, `${name}-actual.json`), actualText, 'utf8');
                fs.writeFileSync(fsp.join(tmpDir, `${name}-expected.json`), expectedText, 'utf8');
                const diff = deepDiff(actual, expectedObj);
                const lastDiff = diff.slice(-1)[0];
                const message = `AXIOS MOCKER FAILED
- Mock file: ${fileName}.
- Details in ${tmpDir}/${name}-{actual,expected}.json files.
- Diff starts at line ${line}, column ${column}
in ${diff.map(d => d.key).join('.')}`;

                fs.writeFileSync(
                    fsp.join(tmpDir, `${name}-actual-diff-value.txt`),
                    JSON.stringify(lastDiff.actual),
                    'utf8',
                );
                fs.writeFileSync(
                    fsp.join(tmpDir, `${name}-expected-diff-value.txt`),
                    JSON.stringify(lastDiff.expected),
                    'utf8',
                );
                throw new LogicError(message);
            }
            this.arrayCounter += 1;
            return fileContent.response;
        };
    }
}

export const mockPlugins: Dict<any> = {
    axios: AxiosPlugin,
};
