/** @ignore */ /** */
import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Dict } from '@sage/xtrem-shared';
import { safeSetInterval } from '../concurrency-utils';
import { monitoredFunnel } from '../runtime';
import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { MemoryCache, MemoryCacheCounters } from './memory-cache';

/** Global cache module
 * To use:
 *
 *      const cachedValue = application.globalCache.getValue<MyType>(
 *          context,
 *          {
 *              category: myCategory,
 *              key: myKey,
 *              getValue: getValueCallback,
 *              cacheInMemory: true,
 *              ttlInSeconds: 5 * 60,
 *           }
 *      );
 *
 * If the key is not in the cache, `getValueCallback` will be invoked to obtain the value.
 * The value will then be cached, according to the provided options
 * (here, the value will be cached for 5 minutes in memory and database).
 *
 *
 *  `getValueCallback` has the following signature: `() => { value: MyType, invalidatingTokens?: string[] }`
 *  where `value` is the value and invalidatingTokens is an optional list of tokens that may invalidate the cached result.
 *
 * `MyType` can usually be omitted. It will be inferred from `getValue`.
 */

export const logger = loggers.globalCache;

export type GetValueCallback<T extends AnyValue> = () => AsyncResponse<{ value: T; invalidatingTokens?: string[] }>;

export interface GlobalCacheOptions<T extends AnyValue> {
    category: string;
    key: string;
    getValue: GetValueCallback<T>;
    /** should the value be written to the context cache? */
    isolateInContext?: boolean;
    /** should the value be written to the in-memory cache? */
    cacheInMemory?: boolean;
    /** The TTL of the value (value will never expire if TTL is not set) */
    ttlInSeconds: number;
}

/** @internal */
class Funnel {
    private static readonly _funnelsByKey: { [key: string]: Funnel } = {};

    readonly #funnel: <T extends AnyValue>(fn: () => AsyncResponse<T>) => Promise<T>;

    #counter = 0;

    readonly #key: string;

    private constructor(key: string) {
        this.#funnel = monitoredFunnel('globalCache', 1);
        this.#key = key;
        Funnel._funnelsByKey[key] = this;
    }

    async invoke<T extends AnyValue>(body: () => AsyncResponse<T>): Promise<T> {
        this.#counter += 1;
        try {
            return await this.#funnel(body);
        } finally {
            this.#counter -= 1;
            if (this.#counter === 0) {
                // No more client is waiting for entering the critical section
                delete Funnel._funnelsByKey[this.#key];
            }
        }
    }

    static getFunnel(key: string): Funnel {
        return Funnel._funnelsByKey[key] || new Funnel(key);
    }
}

/** @internal */
export interface GlobalCacheCounters extends MemoryCacheCounters {
    queryCount: number;
    contextHitCount: number;
    memoryHitCount: number;
    missCount: number;
}

const DEFAULT_MAX_COST = 500_000;

const emptyCounters: GlobalCacheCounters = {
    queryCount: 0,
    contextHitCount: 0,
    memoryHitCount: 0,
    missCount: 0,
    memoryCost: 0,
    itemsCount: 0,
};

/** @internal */
export class GlobalCache {
    /**
     * The in-memory cache where we store the values.
     * Values are indexed by tenantId, then category, then key.
     * */
    private _memoryCachesByTenant: Dict<MemoryCache> = {};

    private _counters: GlobalCacheCounters = { ...emptyCounters };

    constructor() {
        safeSetInterval(this._purgeMemoryBuckets.bind(this), 30 * 1000); // every 30 seconds
        safeSetInterval(this._showCounters.bind(this), 30 * 60 * 1000); // every 30 minutes
    }

    getCounters(): GlobalCacheCounters {
        return Object.values(this._memoryCachesByTenant).reduce(
            (result, memoryCache) => {
                const counters = memoryCache.getCounters();
                result.itemsCount += counters.itemsCount;
                result.memoryCost += counters.memoryCost;
                return result;
            },
            { ...this._counters },
        );
    }

    /**
     * @internal
     * CAUTION : clears all the cached items
     * Note : clearAll() will clean the cached values from :
     * - the database
     * - the LOCAL memory, that means that if 2 node processes are running, the in-memory cached values won't be cleaned in the
     * other node process (except if this process also runs the clearAll() method)
     * */
    clearAll(): void {
        logger.warn('*** CACHE CLEAR ALL ***');

        this._memoryCachesByTenant = {};
        this._counters = { ...emptyCounters };
    }

    private _showCounters(): void {
        if (!logger.isActive('verbose')) return;
        Object.values(this._memoryCachesByTenant).forEach(memoryCache => memoryCache.showCounters());
    }

    /**
     * Purge the memory buckets of all tenants.
     * This method is called periodically to ensure that the memory cache does not grow indefinitely.
     * It calculates the maximum cost per tenant based on the global cache settings and purges each tenant's memory cache
     * accordingly.
     */
    private _purgeMemoryBuckets(): void {
        const now = Date.now();
        const memoryCaches = Object.values(this._memoryCachesByTenant);
        const maxCostPerTenant =
            (ConfigManager.current.settings?.globalCache?.maxCost || DEFAULT_MAX_COST) / (memoryCaches.length || 1);

        // The promise is caught here to avoid unhandled promise rejections
        // as this is called from a setInterval which is not awaited.
        try {
            memoryCaches.forEach(tenantCache => tenantCache.purge(now, maxCostPerTenant));
        } catch (err) {
            logger.error(`Error purging memory cache: ${err.message}`);
        }
    }

    /**
     * The map that gives a list of invalidation tokens for each category.
     * This map contains very static data. It is initialized by the NodeFactoryCache constructor when the factory is
     * constructed and it never changes afterwards.
     *
     * If the category is a cached node (see `factory.cache.cachedCategory`), the invalidation tokens are the names of
     * all the tables in the factory's inheritance tree.
     *
     * This map is used when a category is invalidated (see `#MemoryCache`).
     */
    readonly categoryTokens: Dict<string[]> = {};

    /**
     * Sets the cached category tokens in `this.#categoryTokens`.
     * This is called once for every factory (every '$NODE...' category), when the factory is constructed.
     */
    setCategoryTokens(category: string, tokens: string[]): void {
        this.categoryTokens[category] = tokens;
    }

    private static getTenantId(context: Context, category: string): string {
        return category.startsWith('$SHARED_NODE.') ? '*' : context.tenantId || '*';
    }

    private static getTenantIdList(context: Context, categories: string[]): string[] {
        return categories.map(category => this.getTenantId(context, category));
    }

    /**
     * @param category a string that will identify the category or a comma separated list of categories of the cached value.
     * @param options
     */
    async invalidateCategory(
        context: Context,
        category: string,
        options: { skipNotify?: boolean } = {},
    ): Promise<void> {
        const tenantIds = GlobalCache.getTenantIdList(context, category.split(','));
        // eslint-disable-next-line no-restricted-syntax
        for (const tenantId of tenantIds) {
            logger.debug(
                () =>
                    `*** invalidateCategory tenantId:${tenantId} category:${category} skipNotify:${options.skipNotify}`,
            );
            const memoryCache = this._memoryCachesByTenant[tenantId];
            if (memoryCache) {
                memoryCache.invalidateCategory(context, category);
            }
        }

        if (!context.managedExternal) {
            // notify once for the whole category, if requested
            if (!options.skipNotify) await context.notifyInvalidateCategory(category);
        }
    }

    /**
     *
     * @param context
     * @param options
     * @returns
     */
    invalidateAllCategories(
        context: Context,
        options: { includeShared?: boolean; skipNotify?: boolean } = {},
    ): Promise<void> {
        const categories = [];
        if (context.tenantId != null) {
            categories.push('*');
        }
        if (options.includeShared) {
            categories.push('$SHARED_NODE.*');
        }
        return this.invalidateCategory(context, categories.join(','), options);
    }

    private _storeInMemoryCache<T extends AnyValue>(
        context: Context,
        tenantId: string,
        category: string,
        key: string,
        item: { value: T; invalidatingTokens?: string[] },
        options: GlobalCacheOptions<T>,
    ): void {
        if (!options.cacheInMemory) {
            return;
        }
        let memoryCache = this._memoryCachesByTenant[tenantId];
        if (!memoryCache) {
            memoryCache = new MemoryCache(tenantId, 'global');
            this._memoryCachesByTenant[tenantId] = memoryCache;
        }
        memoryCache.storeValue(category, key, item, { ...options, vault: context.vault });
    }

    private getValueInMemory<T extends AnyValue>(tenantId: string, category: string, key: string): T | undefined {
        const tenantMemoryCache = this._memoryCachesByTenant[tenantId];
        if (!tenantMemoryCache) return undefined;
        return tenantMemoryCache.fetchValue<T>(category, key);
    }

    /**
     * Returns the cached value for a given key. If not found, the getCb callback will be invoked (if provided)
     * @param context
     * @param options
     */
    // eslint-disable-next-line require-await
    async getValue<T extends AnyValue>(context: Context, options: GlobalCacheOptions<T>): Promise<T> {
        const { category, key } = options;
        const tenantId = GlobalCache.getTenantId(context, category);

        // If isolateInContext is set, we do not store globally.
        // Instead we wait for the commit to invalidate the category in the global cache.
        const canStoreGlobally = !options.isolateInContext;

        this._counters.queryCount += 1;

        // Note: do not use fullKey in debug message because we don't want to allocate it before the funnel call.
        logger.debug(() => `Global cache read value for key = ${tenantId}.${category}.${key}`);

        const tryFromContext1 = context.cache.fetchValue<T>(category, key);
        if (tryFromContext1 != null) {
            this._counters.contextHitCount += 1;
            return tryFromContext1;
        }

        if (options.cacheInMemory) {
            const try1 = this.getValueInMemory<T>(tenantId, category, key);
            if (try1 !== undefined) {
                this._counters.memoryHitCount += 1;
                // store it in context for next time
                if (try1 != null) context.cache.storeValue(category, key, { value: try1 }, options);
                return try1;
            }
        }

        const fullKey = `${tenantId}.${category}.${key}`;

        return Funnel.getFunnel(fullKey).invoke(async () => {
            // Double check locking pattern (https://en.wikipedia.org/wiki/Double-checked_locking)
            const tryFromContext2 = context.cache.fetchValue<T>(category, key);
            if (tryFromContext2 != null) {
                this._counters.contextHitCount += 1;
                return tryFromContext2;
            }
            if (options.cacheInMemory) {
                const try2 = this.getValueInMemory<T>(tenantId, category, key);
                if (try2 !== undefined) {
                    this._counters.memoryHitCount += 1;
                    // store it in context for next time
                    if (try2 != null) context.cache.storeValue(category, key, { value: try2 }, options);
                    return try2;
                }
            }
            this._counters.missCount += 1;
            if (options.getValue) {
                // Invoke the callback provided by the user to get the value and cache it
                logger.debug(() => `\tCompute value for missing key (${fullKey})`);
                const item = await options.getValue();
                if (item.value !== null) {
                    // Don't store null values:
                    context.cache.storeValue(category, key, item, options);
                    if (canStoreGlobally) {
                        this._storeInMemoryCache<T>(context, tenantId, category, key, item, options);
                    }
                }
                return item.value;
            }
            throw new Error(`Could not compute value for key ${fullKey}`);
        });
    }
}
