/** @ignore */ /** */
import { AnyValue } from '@sage/xtrem-async-helper';
import { createDictionary, Dict, ProfilerCallback } from '@sage/xtrem-shared';
import { StringProperty } from '../properties';
import { Context } from '../runtime/context';
import { ContextVault } from '../runtime/context-vault';
import { loggers } from '../runtime/loggers';
import { GlobalCacheOptions } from './global-cache';

export const logger = loggers.globalCache;

/** @internal */
class MemoryCacheBucket {
    ttlInSeconds: number;

    expirationTime: number;

    #cost = 0;

    constructor(
        readonly value: AnyValue,
        options: {
            ttlInSeconds: number;
        },
    ) {
        this.ttlInSeconds = options.ttlInSeconds;
        this.recomputeExpirationTime(Date.now());
    }

    computeCost(): number {
        if (!this.#cost) this.#cost = JSON.stringify(this.value).length;
        return this.#cost;
    }

    isExpired(now: number): boolean {
        return now > this.expirationTime;
    }

    recomputeExpirationTime(now: number): void {
        const prevExpirationTime = this.expirationTime;
        const ttlInSeconds = this.ttlInSeconds;
        const newExpirationTime = now + ttlInSeconds * 1000;
        this.expirationTime = newExpirationTime;
        if (prevExpirationTime) {
            logger.debug(
                () =>
                    `Bucket - expiration date :  ${new Date(prevExpirationTime).toISOString()} -> ${new Date(
                        newExpirationTime,
                    ).toISOString()} / ${ttlInSeconds}`,
            );
        }
    }
}

/** @internal */
export interface MemoryCacheCounters {
    memoryCost: number;
    itemsCount: number;
}

enum MemoryCacheTypeEnum {
    context,
    global,
}

export type MemoryCacheType = keyof typeof MemoryCacheTypeEnum;

export class MemoryCache {
    #tenantId: string;

    #type: MemoryCacheType;

    #boundToVault = false;

    // SECURITY NOTE: Use createDictionary to prevent from prototype pollutions
    private _invalidatingTokenMap: Dict<{ category: string; key: string }[]> = createDictionary();

    private _values: Dict<Dict<MemoryCacheBucket>> = createDictionary();

    constructor(tenantId: string, type: MemoryCacheType) {
        this.#tenantId = tenantId;
        // While debugging cache issues #type makes it possible to make the difference between the context's cache
        // and the global cache
        this.#type = type;
    }

    getCounters(): MemoryCacheCounters {
        const counters = { memoryCost: 0, itemsCount: 0 };
        Object.values(this._values).forEach(categoryBucket => {
            Object.values(categoryBucket).forEach(bucket => {
                counters.memoryCost += bucket.computeCost();
                counters.itemsCount += 1;
            });
        });
        return counters;
    }

    fetchValue<T extends AnyValue>(category: string, key: string): T | undefined {
        const categoryBucket = this._values[category];
        if (!categoryBucket) return undefined;
        const bucket = categoryBucket[key];
        if (bucket) {
            const now = Date.now();
            if (bucket.isExpired(now)) {
                // Nothing to do : the bucket will be flushed by _purgeObsoleteDbBuckets
                logger.debug(() => `\t ${this.#type} memory cache ${category} EXPIRED: ${key}`);
            } else {
                logger.debug(() => `\t ${this.#type} memory cache ${category} HIT: ${key}`);
                // TTLs for memory buckets are based on the last READ access
                bucket.recomputeExpirationTime(now);
                return bucket.value as T;
            }
        } else {
            logger.debug(() => `\t ${this.#type} memory cache ${category} MISS: ${key}`);
        }
        return undefined;
    }

    storeValue<T extends AnyValue>(
        category: string,
        key: string,
        item: { value: T; invalidatingTokens?: string[]; storedEncryptedProperties?: StringProperty[] },
        options: GlobalCacheOptions<T> & { vault?: ContextVault },
    ): void {
        item.invalidatingTokens?.forEach(token => this.addInvalidatingToken(token, category, key));

        // TODO: we should freeze item.value before caching it
        // For this we'll have to change the way we lazy load stream properties
        const bucket = new MemoryCacheBucket(item.value, options);

        this._values[category] = this._values[category] || {};
        this._values[category][key] = bucket;

        if (options.vault && item.storedEncryptedProperties?.length) {
            this.#boundToVault = true;
            options.vault.cacheValues(category, item.value, item.storedEncryptedProperties);
        }
        logger.verbose(() => {
            let msg = `${this.#type} memory cache ${category} store ${key}`;
            if (options) {
                msg += `, will expire @ ${bucket.expirationTime} (ttl = ${bucket.ttlInSeconds} seconds)`;
            }
            return msg;
        });
    }

    /**
     * Add a token to `this.#MemoryCache`.
     *
     * This is called after a query has been run, to associate the query's `{ category, key }` pair with the token
     * (the name of a table which was joined by the query).
     */
    private addInvalidatingToken(token: string, category: string, key: string): void {
        logger.debug(
            () =>
                `${this.#type} memory cache add invalidating token: tenantId=${
                    this.#tenantId
                }, token=${token}, category=${category}, key=${key}`,
        );
        // Create the intermediate nodes of the map if necessary
        if (!this._invalidatingTokenMap[token]) this._invalidatingTokenMap[token] = [];

        // Push the entry into the array of entries
        this._invalidatingTokenMap[token].push({ category, key });
    }

    /**
     * Removes the cache entries for queries that joined one of the table names of a node factory (one of the tokens
     * of a category).
     *
     * This is called when we invalidate a node factory (a category) after records have been inserted/updated/deleted
     * in the factory's table (or in one of its tables if the factory is part of an inheritance tree).
     *
     * This is also called when we receive an `invalidateCategoryCache` broadcast event from another container.
     * In this case, the database change was done in a different container but we can use the same logic
     * to update our cache.
     */
    invalidateCategory(context: Context, category: string): void {
        const categories = category.split(',');
        if (categories.length > 1) {
            categories.forEach(cat => this.invalidateCategory(context, cat));
            return;
        }
        if (category.endsWith('*')) {
            const prefix = category.slice(0, -1);
            logger.debug(() => `*** invalidateCategory categories: ${Object.keys(this._values)}`);
            Object.keys(this._values)
                .filter(cat => cat.startsWith(prefix))
                .forEach(cat => this.invalidateCategory(context, cat));
            return;
        }
        if (this.#boundToVault) {
            ContextVault.invalidateCache(category);
        }
        // SECURITY NOTE: Use createDictionary to prevent from prototype pollutions
        this._values[category] = createDictionary();

        // Get the category's tokens, and return if none
        const tokens = context.application.globalCache.categoryTokens[category];
        if (!tokens) return;

        // Iterate on the tokens
        tokens.forEach(token => {
            // Find the token invalidation entries for this token.
            const entries = this._invalidatingTokenMap[token];
            logger.debug(
                () =>
                    `${this.#type} memory cache invalidating token entries:  tenantId=${
                        this.#tenantId
                    }, category=${category}, token=${token}, entries=${JSON.stringify(entries)}`,
            );
            if (!entries) return;

            // The keys of the entries that we found correspond to queries that may be impacted by the
            // change on category. We remove them from the cache.
            entries.forEach(dep => this.deleteKey(dep.category, dep.key));

            // The queries have been removed so we can delete their { category, key } pairs from
            // #MemoryCache.
            delete this._invalidatingTokenMap[token];
        });
    }

    /** @internal */
    deleteKey(category: string, key: string): void {
        const categoryCache = this._values[category];
        if (categoryCache && categoryCache[key]) {
            if (this.#boundToVault) {
                ContextVault.invalidateCache(category, key);
            }
            delete categoryCache[key];
        }
    }

    purge(now: number, maxCost: number): void {
        Object.keys(this._values).forEach(category => {
            const categoryCache = this._values[category];
            const obsoleteKeysToDelete = Object.keys(categoryCache).filter((key: string) =>
                categoryCache[key].isExpired(now),
            );
            if (obsoleteKeysToDelete.length) {
                logger.verbose(() => `${this.#type} memory cache purge ${obsoleteKeysToDelete.length} memory buckets`);
                obsoleteKeysToDelete.forEach((key: string) => {
                    const expirationTime = categoryCache[key].expirationTime;
                    logger.debug(
                        () =>
                            `\t-  ${this.#type} memory cache purge bucket ${key} / ${new Date(
                                expirationTime,
                            ).toISOString()}`,
                    );
                    this.deleteKey(category, key);
                });
            }
        });

        const keysToDelete: string[] = [];

        Object.keys(this._values).forEach(category => {
            const categoryCache = this._values[category];
            const sortedKeys = Object.keys(categoryCache).sort((key1, key2) => {
                const keyValue = (k: string): number => categoryCache[k].expirationTime;
                return keyValue(key1) - keyValue(key2);
            });
            let totalCost = 0;
            sortedKeys.forEach(key => {
                if (totalCost > maxCost) {
                    keysToDelete.push(key);
                } else {
                    totalCost += categoryCache[key].computeCost();
                }
            });
            keysToDelete.forEach((key: string) => {
                logger.debug(() => `\t-  ${this.#type} memory cache purge bucket ${key}`);
                this.deleteKey(category, key);
            });
        });

        if (keysToDelete.length) {
            logger.verbose(() => {
                const counters = this.getCounters();
                return `${this.#type} memory cache max cost exceeded (${counters.memoryCost}/${maxCost}), purge ${
                    keysToDelete.length
                } memory buckets`;
            });
        }
    }

    /** @internal */
    clear(): void {
        if (this.#boundToVault) {
            // eslint-disable-next-line no-restricted-syntax
            for (const category of Object.keys(this._values)) {
                ContextVault.invalidateCache(category);
            }
        }
        // SECURITY NOTE: Use createDictionary to prevent from prototype pollutions
        this._values = createDictionary();
    }

    showCounters(): ProfilerCallback {
        return logger.verbose(() => {
            const counters = this.getCounters();
            return `${this.#type} memory cache counters : tenant=${this.#tenantId}, memory=(${
                Object.keys(this._values).length
            }, items/cost=${counters.memoryCost})`;
        });
    }
}
