/** @ignore */ /** */
import { AsyncResponse, UnPromised, asyncArray } from '@sage/xtrem-async-helper';
import { ColumnTypeName, Dict } from '@sage/xtrem-shared';
import { snakeCase } from 'lodash';
import { InternalBooleanOrUndefined, Property } from '../properties/property';
import { PropertyDecorator } from '../runtime/property';
import { Node } from '../ts-api';
import { SubNodeDecorator } from './node-decorators/sub-node-decorator';
import { TypedPropertyDecorator } from './property-decorators';

export type StaticThis<T> = new () => T;
export { ColumnTypeName };

// TODO: remove instance
export type TypeName = ColumnTypeName | 'instance' | 'reference' | 'collection' | 'jsonReference';

/** @internal */
export const decoratorsSymbol = Symbol('decorators');

export class Decorated {
    /** @internal */
    public static [decoratorsSymbol]: Dict<any>;
}

export interface DecoratorArg {}

export type InferNonNullValT<PropType, ExpectedType> =
    PropType extends Promise<infer ValT> ? (ValT extends ExpectedType ? ValT : never) : never;

export type InferValT<PropType, ExpectedType> =
    UnPromised<PropType> extends ExpectedType
        ? UnPromised<PropType> extends null
            ? UnPromised<PropType> | null
            : UnPromised<PropType>
        : never;

export type NodeFilterTag = 'site';
export type FilterTag = NodeFilterTag | 'accessCode' | 'isActive';

/** When merging events from an extension, some events must be chained, meaning that the base event needs
 * to be executed before the extension event. Both events will be executed. */
export function chainEvents(baseEvent: any, extEvent: any, key: string): any {
    if (baseEvent && typeof baseEvent !== 'function') throw new Error(`Base event ${key}: event is not a function`);
    if (extEvent && typeof extEvent !== 'function') throw new Error(`Extension event ${key}: event is not a function`);
    return orderEvents(baseEvent, extEvent, key);
}

export function chainFilters(baseFilter: any, extFilter: any): any {
    if (!baseFilter) return extFilter;
    if (!extFilter) return baseFilter;
    return { _and: [baseFilter, extFilter] };
}

/** When merging isFrozen from an extension, if any of the base nodes has isFrozen evaluated to true, the property must be frozen
A subnode cannot override the isFrozen attribute with a false value*/
export function chainIsFrozens(property: Property, extension: TypedPropertyDecorator): InternalBooleanOrUndefined {
    // If only one decorator is set, return it
    if (extension.isFrozen === undefined) return property.isFrozen;
    if (property.isFrozen === undefined) return extension.isFrozen;
    // Do not allow overriding of baseIsFrozen: true but we allow overriding of other events
    if (property.isFrozen === true) {
        throw property.systemError('Cannot override isFrozen if already frozen in base class.');
    }
    const isFrozens = [property.isFrozen, extension.isFrozen];
    return function hasSomeFrozen(this: Node): Promise<boolean> {
        return asyncArray(isFrozens).some(isFrozen =>
            typeof isFrozen === 'function' ? isFrozen.call(this) : isFrozen,
        );
    };
}

function getOrderedFunctions(baseEvent: any, extEvent: any, key: string): any[] {
    const getFunction = (event: any): any =>
        !event || typeof event === 'function'
            ? event
            : // eslint-disable-next-line @typescript-eslint/no-unused-vars
              function f(this: Node, ..._args: any[]) {
                  return event;
              };
    const baseFunction = getFunction(baseEvent);
    const extFunction = getFunction(extEvent);

    let orderedFunctions: any[] = [];
    if (baseFunction && extFunction) {
        orderedFunctions = key.startsWith('delete') ? [extFunction, baseFunction] : [baseFunction, extFunction];
    } else if (extFunction) {
        orderedFunctions = [extFunction];
    } else if (baseFunction) {
        orderedFunctions = [baseFunction];
    }
    return orderedFunctions;
}

/** Called when merging an extension (node extension, property extension, superNode extension into subNode). */
function orderEvents(baseEvent: any, extEvent: any, key: string): any {
    return async function chained(this: Node, ...args: any[]): Promise<void> {
        if (!baseEvent && !extEvent) return undefined;

        const orderedFunctions = getOrderedFunctions(baseEvent, extEvent, key);

        // eslint-disable-next-line no-restricted-syntax
        for (const func of orderedFunctions) {
            await (func.apply(this, args) as AsyncResponse<void>);
        }

        return undefined;
    };
}

/** Called when an extension extends a list attribute from the base class by adding entries to the list. */
export function chainArrays(baseArray: any[] | undefined, extArray: any[] | undefined): any[] | undefined {
    if (baseArray && extArray) return [...baseArray, ...extArray];
    return baseArray || extArray;
}

/** @internal */
export function getDecorators(constructor: typeof Decorated): Dict<any> {
    if (!Object.prototype.hasOwnProperty.call(constructor, decoratorsSymbol)) {
        constructor[decoratorsSymbol] = { superDecorators: constructor[decoratorsSymbol] };
    }
    return constructor[decoratorsSymbol];
}

export type MemberDecoratorFunction<This extends Decorated, K extends keyof This> = (proto: This, name: K) => void;

/** @internal */
export function memberDecorator<This extends Decorated, K extends keyof This, ArgType extends DecoratorArg>(
    decoratorName: string,
): (arg: ArgType) => MemberDecoratorFunction<This, K> {
    return (arg: ArgType): MemberDecoratorFunction<This, K> => {
        return (proto: This, name: K): void => {
            const constructor = proto.constructor as typeof Decorated;
            // cast to any because we don't include name in DecoratorArg
            (arg as any).name = name;
            const decorators = getDecorators(constructor);
            decorators[decoratorName] = decorators[decoratorName] || [];
            decorators[decoratorName].push(arg);
        };
    };
}

/** @internal */
export function typedMemberDecorator<This extends Decorated, ArgType extends DecoratorArg>(
    decoratorName: string,
): (arg: ArgType) => MemberDecoratorFunction<This, keyof This> {
    return (arg: ArgType): MemberDecoratorFunction<This, keyof This> => {
        return (proto: This, name: keyof This): void => {
            const constructor = proto.constructor as typeof Decorated;
            // cast to any because we don't include name in DecoratorArg
            (arg as any).name = name;
            const decorators = getDecorators(constructor);
            decorators[decoratorName] = decorators[decoratorName] || [];
            if (decorators[decoratorName].find((deco: DecoratorArg) => (deco as any).name === name)) {
                throw new Error(`${constructor.name}.${name as string}: multiple decorators are not allowed`);
            }
            decorators[decoratorName].push(arg);
        };
    };
}

export type OperationKind =
    | 'query'
    | 'mutation'
    | 'asyncMutation'
    | 'asyncMutationListener'
    | 'asyncTrackerQuery'
    | 'notificationListener'
    | 'messageListener'
    | 'bulkMutation';

export type StaticMemberDecoratorFunction<ConstructorType extends typeof Decorated> = (
    constructor: ConstructorType,
    name: string,
) => void;

export function staticMemberDecorator<ConstructorType extends typeof Decorated, ArgType extends DecoratorArg>(
    decoratorName: string,
    kind: OperationKind,
): (arg: ArgType) => StaticMemberDecoratorFunction<ConstructorType> {
    return (arg: ArgType): StaticMemberDecoratorFunction<ConstructorType> => {
        return (constructor: ConstructorType, name: string): void => {
            // cast to any because we don't include name and className in DecoratorArg
            (arg as any).name = name;
            (arg as any).nodeConstructor = constructor;
            (arg as any).operationKind = kind;
            // TODO: remove className and use nodeConstructor instead
            (arg as any).className = constructor.name;

            const decorators = getDecorators(constructor);
            decorators[decoratorName] = decorators[decoratorName] || [];
            decorators[decoratorName].push(arg);
        };
    };
}

export type ClassDecoratorFunction<ConstructorType extends typeof Decorated> = (constructor: ConstructorType) => void;

/** @internal */
export function classDecorator<ConstructorType extends typeof Decorated, ArgType extends DecoratorArg>(
    decoratorName: string,
): (arg: ArgType) => ClassDecoratorFunction<ConstructorType> {
    return (arg: ArgType): ClassDecoratorFunction<ConstructorType> => {
        return (constructor: ConstructorType): void => {
            // cast to any because we don't include name in DecoratorArg
            (arg as any).name = constructor.name;
            const decorators = getDecorators(constructor);
            decorators[decoratorName] = decorators[decoratorName] || {};
            Object.assign(decorators[decoratorName], arg);
        };
    };
}

/** @internal */
export function classSubNodeDecorator<ConstructorType extends typeof Decorated, This extends Node>(
    decoratorName: string,
): (arg: SubNodeDecorator<This>) => ClassDecoratorFunction<ConstructorType> {
    return (arg: SubNodeDecorator<This>): ClassDecoratorFunction<ConstructorType> => {
        return (constructor: ConstructorType): void => {
            // cast to any because we don't include name in DecoratorArg
            (arg as any).name = constructor.name;
            const decorators = getDecorators(constructor);
            decorators[decoratorName] = decorators[decoratorName] || {};
            Object.assign(decorators[decoratorName], arg);

            const baseDecorators = getDecorators(arg.extends());
            if (!baseDecorators.node.isAbstract) {
                throw new Error(
                    `Subclass definition error: '${constructor.name}' can't inherit from '${baseDecorators.node.name}' ('${baseDecorators.node.name}' is not marked as abstract)`,
                );
            }
        };
    };
}

/** @internal */
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export function createTypedProperty<This extends Node, K extends keyof This>(type: TypeName, arg: any) {
    arg.type = type;
    return memberDecorator<This, K, PropertyDecorator>('properties')(arg);
}

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export function extendProperty<This extends Node, K extends keyof This>(type: TypeName, arg: any) {
    arg.isOverride = true;
    return createTypedProperty<This, K>(type, arg);
}

/** @internal */
export function snakeUpperCase(s: string): string {
    return snakeCase(s).toUpperCase();
}
