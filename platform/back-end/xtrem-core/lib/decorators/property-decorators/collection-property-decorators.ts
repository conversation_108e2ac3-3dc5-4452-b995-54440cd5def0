/** @packageDocumentation @module decorators */
import { AsyncResponse } from '@sage/xtrem-shared';
import { Collection, Extend, Node, NodeCreateData, NodeQueryFilter, OrderBy, Validator } from '../../ts-api';
import { PropertyJoin } from '../../types';
import { MemberDecoratorFunction, extendProperty, typedMemberDecorator } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface CollectionPropertyEvents<This extends Node = Node> {
    /** event before individual prepare on nodes */
    prepareBegin?: Validator<Extend<This>>;
    /** event after individual prepare on nodes */
    prepareEnd?: Validator<Extend<This>>;
    /** event before individual control on nodes */
    controlBegin?: Validator<Extend<This>>;
    /** event after individual control on nodes */
    controlEnd?: Validator<Extend<This>>;
    /** event before saving nodes */
    saveBegin?: (this: Extend<This>) => Promise<void>;
    /** event after saving nodes */
    saveEnd?: (this: Extend<This>) => void;
}

export type CollectionElement<T> = T extends Collection<infer U extends Node> ? U : never;

/** Intermediate type for collection property decorator */
export interface BaseCollectionPropertyDecorator<
    This extends Node = Node,
    ElementT extends Node = Node,
    DefaultVal extends NodeCreateData<ElementT>[] = NodeCreateData<ElementT>[],
> extends TypedPropertyDecorator<This, ElementT[], never, DefaultVal>,
        CollectionPropertyEvents<This> {
    /** Target class */
    node: () => { new (): ElementT };
    /** orderBy criteria (collection property only) */
    orderBy?: OrderBy<ElementT>;

    /** Name of the property of the target node that references back to this node */
    reverseReference?: keyof ElementT;

    /**
     * Filter on the target node, that expresses the join condition
     * Only necessary if `reverseReference` is absent.
     */
    getFilter?: (this: Extend<This>) => AsyncResponse<NodeQueryFilter<ElementT>>;

    /** ensure the collection contains at least one element */
    isRequired?: boolean;

    /** Is it a vital link ? */
    isVital?: boolean;

    /** Flags the collection as an additional vital relationship */
    isAssociation?: boolean;

    /** Is it a mutable collection? - Only relevant for references and collections on external storage nodes */
    isMutable?: boolean;

    /** Explicit join */
    join?: PropertyJoin<ElementT, Extend<This>>;

    prefetch?: (record: any) => any;

    /** Always save all the collection elements, instead of saving only the modified ones */
    forceFullSave?: boolean;
}

/** Intermediate type for non vital collection property decorator */
export interface NonVitalCollectionPropertyDecorator<This extends Node = Node, ElementT extends Node = Node>
    extends BaseCollectionPropertyDecorator<This, ElementT> {
    isVital?: false;
}

/** Intermediate type for vital collection property decorator */
export interface VitalCollectionPropertyDecorator<This extends Node = Node, ElementT extends Node = Node>
    extends BaseCollectionPropertyDecorator<This, ElementT, NodeCreateData<ElementT>[]> {
    isVital: true;
}

/** Parameter type for &#064;decorators.collectionProperty decorator */
export type CollectionPropertyDecorator<This extends Node = Node, ElementT extends Node = Node> =
    | NonVitalCollectionPropertyDecorator<This, ElementT>
    | VitalCollectionPropertyDecorator<This, ElementT>;

export interface CollectionPropertyOverrideDecorator<This extends Node = Node, ValT extends Node = Node>
    extends PropertyOverrideDecorator<This, ValT, never, NodeCreateData<ValT>[]>,
        CollectionPropertyEvents<This> {
    node?: () => { new (): ValT };
}

/** &#064;decorators.collectionProperty(arg) property decorator */
export function collectionProperty<This extends Node, K extends keyof This = any>(
    arg: CollectionPropertyDecorator<This, CollectionElement<This[K]>>,
): MemberDecoratorFunction<This, K> {
    // cast to any because we don't include type in DecoratorArg
    (arg as any).type = 'collection';
    return typedMemberDecorator<This, TypedPropertyDecorator<This, CollectionElement<This[K]>[], never>>('properties')(
        arg as any,
    );
}

/** &#064;decorators.collectionPropertyOverride(arg) property decorator */
export function collectionPropertyOverride<This extends Node, K extends keyof This = any>(
    arg: CollectionPropertyOverrideDecorator<This, CollectionElement<This[K]>>,
): MemberDecoratorFunction<This, K> {
    return extendProperty('collection', arg);
}
