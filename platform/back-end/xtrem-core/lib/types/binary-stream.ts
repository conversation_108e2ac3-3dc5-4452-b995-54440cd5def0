import { Stream } from './stream';

/** @packageDocumentation @module types */
export class BinaryStream extends Stream {
    constructor(public value: Buffer) {
        super();
    }

    static fromBuffer(value: Buffer): BinaryStream {
        return new BinaryStream(value);
    }

    static isBinaryStream(obj: any): boolean {
        return obj instanceof BinaryStream;
    }

    override toString(): string {
        return this.value.toString('base64');
    }

    compareTo(arg: any): number {
        if (arg == null) return 1;

        if (!(arg instanceof BinaryStream)) throw new Error(`invalid arg passed to BinaryStream compare: ${arg}`);

        return Buffer.compare(this.value, arg.value);
    }
}
