import { fileTypeFromBuffer } from 'file-type';
import * as matcher from 'matcher';
import * as mimeTypes from 'mime-types';
import * as sass from 'sass';

// validated on https://devina.io/redos-checker to check for potential ReDoS
const xmlTagRegex = /<\/?[a-z][^><]*?>/;
const xmlDtdRegex = /<!(doctype|entity|element) /i;
const htmlHeaderRegex = /^(?:<!--[^><]*?-->[\s\n\r]*)*<(!DOCTYPE html|html|head|body)\b/i;
const cssRegex = /[^{;}\s][^{;}]*(\{[^{}]*\})?/g;

/** @internal */
export abstract class MimeTypeHelper {
    private static async getMimeType(buf: Buffer): Promise<string | undefined> {
        const mimeType = await fileTypeFromBuffer(buf);
        return mimeType?.mime;
    }

    static isValidMimeType(mimeType: string): boolean {
        // 'application/graphql' is not an official mime type, but it's used internally. We need to allow it.
        return mimeType === 'application/graphql' || mimeTypes.extension(mimeType) !== false;
    }

    static isLikelyJsonBuffer(buffer: Buffer): boolean {
        const firstChar = buffer.subarray(0, 1).toString('utf8');
        const lastChar = buffer.subarray(-1).toString('utf8');
        if (firstChar === '{' && lastChar === '}') {
            return true;
        }
        return false;
    }

    static countFields(row: string, delimiter: string): number {
        // remove all quoted fields and the delimiters that they may contain
        const stripped = row.replace(/"[^"]*"/g, '');
        // count the fields
        return stripped.split(delimiter).length;
    }

    static isLikelyCsvBuffer(buf: string): boolean {
        // check only one row matching with the header
        const lines = buf.split(/\r?\n/).filter(Boolean);
        const headerCount = MimeTypeHelper.countFields(lines[0], ',');
        return !!(lines[1] && MimeTypeHelper.countFields(lines[1], ',') === headerCount);
    }

    static async guessFromString(str: string, originalContentType?: string): Promise<string> {
        if (!str) return 'text/plain';
        try {
            // Magic doesn't detect JSON
            JSON.parse(str);
            return 'application/json';
            // eslint-disable-next-line no-empty
        } catch {}
        const mime = await MimeTypeHelper.getMimeType(Buffer.from(str));
        if (!mime || mime === 'text/plain') {
            // try to guess a better one
            if (htmlHeaderRegex.test(str)) return 'text/html';
            if (xmlDtdRegex.test(str)) return 'application/xml';
            if (xmlTagRegex.test(str)) {
                if (originalContentType && ['text/xml', 'application/xml'].includes(originalContentType)) {
                    return 'application/xml';
                }
                if (originalContentType === 'text/csv') {
                    return 'text/csv';
                }
                if ((!originalContentType || originalContentType === 'text/css') && cssRegex.test(str)) {
                    try {
                        sass.compileString(str);
                        return 'text/css';
                    } catch {
                        // invalid css content
                    }
                }
                return 'text/html';
            }
        }
        return mime || 'text/plain';
    }

    static async guessFromBuffer(buf: Buffer): Promise<string> {
        const mime = await MimeTypeHelper.getMimeType(buf);
        if (mime) return mime;

        const subArrayStr = buf.subarray(0, 1000).toString('utf8');
        if (subArrayStr.includes('<svg ')) {
            return 'image/svg+xml';
        }
        if (MimeTypeHelper.isLikelyJsonBuffer(buf)) {
            return 'application/json';
        }
        if (MimeTypeHelper.isLikelyCsvBuffer(subArrayStr)) {
            return 'application/csv';
        }
        return 'application/octet-stream';
    }

    static isValid(contentType: string, validTypes?: string[]): boolean {
        if (!validTypes) return true;
        return validTypes.some(validType => !!contentType.match(validType));
    }

    static matchPatterns(contentType: string[] | string, patterns: string[] | string): boolean {
        return matcher(contentType, patterns).length > 0;
    }
}
