import * as json5 from 'json5';

// Handle circular reference
const getCircularReplacer = () => {
    const seen = new WeakSet();
    return (key: string, value: any) => {
        if (typeof value === 'object' && value !== null) {
            if (seen.has(value)) {
                return '[Circular]';
            }
            seen.add(value);
        }
        return value;
    };
};

export function json5Stringify(obj: any): string {
    try {
        return json5.stringify(obj);
    } catch {
        try {
            return json5.stringify(obj, getCircularReplacer());
        } catch {
            // Return a string representation of the object
            if (typeof obj === 'object' && obj !== null) {
                return `[Object: ${Object.keys(obj).length} keys]`;
            }
            return String(obj);
        }
    }
}

export function jsonStringify(obj: any): string {
    try {
        return JSON.stringify(obj);
    } catch {
        try {
            return JSON.stringify(obj, getCircularReplacer());
        } catch {
            // Return a string representation of the object
            if (typeof obj === 'object' && obj !== null) {
                return `[Object: ${Object.keys(obj).length} keys]`;
            }
            return String(obj);
        }
    }
}
