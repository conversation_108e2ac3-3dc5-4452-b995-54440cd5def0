import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import { datatypesData, fixDatetimes, initTables, restoreTables, setup } from '../fixtures/index';

describe('paging', () => {
    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
    });

    it('can limit page size', () =>
        Test.readonly(async context => {
            const data = datatypesData;
            const nodes = await context.query(fixtures.nodes.TestDatatypes, { first: 2 }).toArray();
            assert.equal(nodes.length, 2);
            await asyncArray(nodes).forEach(async (node, i) => {
                const d = data[i];
                fixDatetimes(
                    await node.datetimeVal,
                    d.datetimeVal,
                    (await node.datetimeRangeVal)?.start,
                    d.datetimeRangeVal?.start,
                    (await node.datetimeRangeVal)?.end,
                    d.datetimeRangeVal?.end,
                    (await node.datetimeRangeVal)?.includedStart,
                    d.datetimeRangeVal?.includedStart,
                    (await node.datetimeRangeVal)?.excludedEnd,
                    d.datetimeRangeVal?.excludedEnd,
                );
                assert.deepEqual(
                    await node.$.payload({ withIds: true }),
                    { ...d, computed: d.id * d.integerVal, computedCached: d.id * d.integerVal, complexComputed: 10 },
                    `record ${i} matches`,
                );
            });
        }));
    it('can filter on _id', () =>
        Test.readonly(async context => {
            const id = 5;
            const nodes = await context.query(fixtures.nodes.TestDatatypes, { filter: { _id: id } }).toArray();
            assert.equal(nodes.length, 1);
            assert.equal(nodes[0].$.id, id);
        }));
    after(() => restoreTables());
});
