import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import { Context, date, Dict, NodeQueryFilter, notNull, Test } from '../../index';
import * as fixtures from '../fixtures';
import { allIds, allIdsExcept, DatatypesData, initTables, setup } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

const datatypesData: DatatypesData[] = [...fixtures.datatypesData];

async function query(context: Context, predicate: NodeQueryFilter<TestDatatypes>): Promise<any[]> {
    return asyncArray(await context.query(TestDatatypes, { filter: predicate }).toArray())
        .map(obj =>
            asyncArray(Object.keys(datatypesData[1])).reduce(async (r, k) => {
                const val = (obj as Dict<any>)[k];
                if (val !== undefined) r[k] = await val;
                return r;
            }, {} as Dict<any>),
        )
        .toArray();
}

function test(predicate: NodeQueryFilter<TestDatatypes>, expectedIds: number[]) {
    return () =>
        Test.readonly(async context => {
            const records = await query(context, predicate);
            assert.deepEqual(
                records.map(record => record.id),
                expectedIds,
            );
        });
}

function testFilter(predicate: NodeQueryFilter<TestDatatypes>, expectedIds: number[]) {
    return test(predicate, expectedIds)();
}

describe('function filters', () => {
    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    describe('boolean', () => {
        it(
            'this.booleanVal == null',
            test(
                async function filter() {
                    return (await this.booleanVal) == null;
                },
                [0],
            ),
        );

        it(
            'this.booleanVal != null',
            test(async function filter() {
                return (await this.booleanVal) != null;
            }, allIdsExcept(0)),
        );

        it(
            'this.booleanVal === true',
            test(
                async function filter() {
                    return (await this.booleanVal) === true;
                },
                allIds.filter(id => id % 2 === 1),
            ),
        );

        it(
            'this.booleanVal === false',
            test(
                async function filter() {
                    return (await this.booleanVal) === false;
                },
                allIdsExcept(0).filter(id => id % 2 === 0),
            ),
        );

        it(
            'this.booleanVal !== true',
            test(
                async function filter() {
                    return (await this.booleanVal) !== true;
                },
                allIds.filter(id => id % 2 === 0),
            ),
        );
        it(
            'this.booleanVal !== false',
            test(
                async function filter() {
                    return (await this.booleanVal) !== false;
                },
                [0, ...allIds.filter(id => id % 2 === 1)],
            ),
        );
    });

    // use shortVal rather than integerVal because shortVal is nullable and integerVal is not.
    describe('integer', () => {
        it(
            'this.shortVal == null',
            test(
                async function filter() {
                    return (await this.shortVal) == null;
                },
                [0],
            ),
        );

        it(
            'this.shortVal != null',
            test(async function filter() {
                return (await this.shortVal) != null;
            }, allIdsExcept(0)),
        );

        it(
            'this.shortVal === 995',
            test(
                async function filter() {
                    return (await this.shortVal) === 995;
                },
                [5],
            ),
        );

        it(
            'this.shortVal !== 995',
            test(async function filter() {
                return (await this.shortVal) !== 995;
            }, allIdsExcept(5)),
        );

        it(
            'this.shortVal <= 995',
            test(
                async function filter() {
                    return (await this.shortVal)! <= 995;
                },
                // shortVals are 1000 - id
                allIdsExcept(0).filter(id => id >= 5),
            ),
        );

        it(
            'this.shortVal < 995',
            test(
                async function filter() {
                    return notNull(await this.shortVal) < 995;
                },
                allIdsExcept(0).filter(id => id > 5),
            ),
        );

        it(
            'this.shortVal >= 995',
            test(
                async function filter() {
                    return notNull(await this.shortVal) >= 995;
                },
                // shortVals are 1000 - id
                allIdsExcept(0).filter(id => id <= 5),
            ),
        );

        it(
            'this.shortVal > 995',
            test(
                async function filter() {
                    return (await this.shortVal)! > 995;
                },
                // shortVals are 1000 - id
                allIdsExcept(0).filter(id => id < 5),
            ),
        );
    });

    // strings are not nullable so we don't need special handling of id 0
    describe('string', () => {
        it(
            "this.stringVal === 'string%_5'",
            test(
                async function filter() {
                    return (await this.stringVal) === 'string%_$5';
                },
                [5],
            ),
        );

        it(
            "this.stringVal !== 'string%_5'",
            test(async function filter() {
                return (await this.stringVal) !== 'string%_$5';
            }, allIdsExcept(5)),
        );

        it(
            "this.stringVal <= 'string%_5'",
            test(
                async function filter() {
                    return (await this.stringVal) <= 'string%_$5';
                },
                allIds.filter(id => String(id) <= '5'),
            ),
        );

        it(
            "this.stringVal >= 'string%_5'",
            test(
                async function filter() {
                    return (await this.stringVal) >= 'string%_$5';
                },
                allIds.filter(id => String(id) >= '5'),
            ),
        );

        it(
            'can use str.trim() and str.length in filter',
            test(
                async function filter() {
                    return `  ${await this.stringVal}    `.trim().length > 'string%_$1'.length;
                },
                allIds.filter(id => id >= 10),
            ),
        );
    });

    describe('enum', () => {
        it(
            'this.enumVal == null',
            test(
                async function filter() {
                    return (await this.enumVal) == null;
                },
                [0],
            ),
        );

        it(
            'this.enumVal != null',
            test(async function filter() {
                return (await this.enumVal) != null;
            }, allIdsExcept(0)),
        );

        it(
            "this.enumVal === 'value2'",
            test(
                async function filter() {
                    return (await this.enumVal) === 'value2';
                },
                allIds.filter(id => id % 3 === 1),
            ),
        );

        it(
            "this.enumVal !== 'value2'",
            test(
                async function filter() {
                    return (await this.enumVal) !== 'value2';
                },
                allIds.filter(id => id % 3 !== 1),
            ),
        );

        it(
            "this.enumVal <= 'value2'",
            test(
                async function filter() {
                    return notNull(await this.enumVal) <= 'value2';
                },
                allIdsExcept(0).filter(id => id % 3 === 0 || id % 3 === 1),
            ),
        );

        it(
            "this.enumVal >= 'value2'",
            test(
                async function filter() {
                    return notNull(await this.enumVal) >= 'value2';
                },
                allIdsExcept(0).filter(id => id % 3 === 1 || id % 3 === 2),
            ),
        );
    });

    describe('date', () => {
        it(
            'this.dateVal == null',
            test(
                async function filter() {
                    return (await this.dateVal) == null;
                },
                [0],
            ),
        );

        it(
            'this.dateVal != null',
            test(async function filter() {
                return (await this.dateVal) != null;
            }, allIdsExcept(0)),
        );
        it(
            "this.dateVal === date.parse('2017-05-15')",
            test(
                async function filter() {
                    return (await this.dateVal) === date.parse('2017-05-15');
                },
                [4],
            ),
        );

        it(
            "this.dateVal !== date.parse('2017-05-15')",
            test(async function filter() {
                return (await this.dateVal) !== date.parse('2017-05-15');
            }, allIdsExcept(4)),
        );

        it(
            "this.dateVal <= date.parse('2017-05-15')",
            test(
                async function filter() {
                    return notNull(await this.dateVal) <= date.parse('2017-05-15');
                },
                allIdsExcept(0).filter(id => id <= 4),
            ),
        );

        it(
            "this.dateVal >= date.parse('2017-05-15')",
            test(
                async function filter() {
                    return notNull(await this.dateVal) >= date.parse('2017-05-15');
                },
                allIdsExcept(0).filter(id => id >= 4),
            ),
        );

        it('can filter dates with year, month, day, ... fields', async () => {
            await testFilter({ dateVal: { year: 2018 } }, [12, 13, 14, 15]);
            await testFilter({ dateVal: { month: 4 } }, [3, 15]);
            await testFilter({ dateVal: { day: 15 } }, allIdsExcept(0));
            // 2017-06-15 is in the 24th week of the year
            await testFilter({ dateVal: { week: 24 } }, [5]);
            // 2017-05-15 and 2018-01-15 are Monday
            await testFilter({ dateVal: { weekDay: 1 } }, [4, 12]);
            // 2017-02-15 and 2018-02-15 are both 46th day of the year
            await testFilter({ dateVal: { yearDay: 46 } }, [1, 13]);
            await testFilter({ dateVal: { value: 20180215 } }, [13]);
            await testFilter({ dateVal: { epoch: 1518652800 } }, [13]);
        });

        it('can filter dates with _fn', async () => {
            await testFilter({ _fn: 'this.dateVal.year === 2018' }, [12, 13, 14, 15]);
            await testFilter({ _fn: 'this.dateVal.month === 4' }, [3, 15]);
            await testFilter({ _fn: 'this.dateVal.day === 15' }, allIdsExcept(0));
            await testFilter({ _fn: 'this.dateVal.week === 24' }, [5]);
            await testFilter({ _fn: 'this.dateVal.weekDay === 1' }, [4, 12]);
            await testFilter({ _fn: 'this.dateVal.yearDay === 46' }, [1, 13]);
            await testFilter({ _fn: 'this.dateVal.value === 20180215' }, [13]);
            await testFilter({ _fn: 'this.dateVal.epoch === 1518652800' }, [13]);
        });

        it('can combine date fields in filter with _fn', async () => {
            // Test is silly but selects entries 2017-3-15 and 2018-4-15
            await testFilter({ _fn: 'this.dateVal.year === this.dateVal.month + 2014' }, [2, 15]);
        });
    });

    describe('functions with multiple statements', () => {
        it('can use const in filter', () =>
            test(
                async function filter() {
                    const testDate = date.parse('2017-05-15');
                    return notNull(await this.dateVal) >= testDate;
                },
                allIdsExcept(0).filter(id => id >= 4),
            ));

        it('can use if in filter', () =>
            test(
                async function filter() {
                    const testDate = date.parse('2017-05-15');
                    if (notNull(await this.dateVal) < testDate) return false;
                    return true;
                },
                allIdsExcept(0).filter(id => id >= 4),
            ));

        it('thows if test contains invalid statements', () =>
            assert.isRejected(
                test(async function filter() {
                    const testDate = date.parse('2017-05-15');
                    if (notNull(await this.dateVal) < testDate) return false;
                    notNull(await this.dateVal);
                    return false;
                }, [])(),
                'expected a console.xyz(...) call, got CallExpression',
            ));
    });
});
