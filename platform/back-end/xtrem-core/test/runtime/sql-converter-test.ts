import { asyncArray } from '@sage/xtrem-async-helper';
import { Datetime, DateValue } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import { Context, Dict, NodeQueryFilter, Test } from '../../index';
import * as fixtures from '../fixtures';
import { DatatypesData, fixDatetimes, initTables, restoreTables, setup, stripSysColumns } from '../fixtures/index';
import { TestDatatypes, TestReferring } from '../fixtures/nodes';

const datatypesData: DatatypesData[] = [...fixtures.datatypesData];

async function typingTest(context: Context): Promise<void> {
    await context
        .query(TestReferring, {
            filter: {
                reference: { details: { _gt: 'abc' } },
            },
        })
        .toArray();
    await context
        .query(TestReferring, {
            filter: {
                reference: 'def',
            },
        })
        .toArray();
}

async function query(context: Context, predicate: NodeQueryFilter<TestDatatypes>): Promise<any[]> {
    // hack to get typing tests on more complex configs
    // eslint-disable-next-line no-constant-condition
    if (!1) await typingTest(context);

    return asyncArray(await context.query(TestDatatypes, { filter: predicate }).toArray())
        .map(obj =>
            asyncArray(Object.keys(datatypesData[1])).reduce(async (r, k) => {
                const val = (obj as Dict<any>)[k];
                if (val !== undefined) r[k] = await val;
                return r;
            }, {} as Dict<any>),
        )
        .toArray();
}

function test(name: string, predicate: NodeQueryFilter<TestDatatypes>, results: DatatypesData[]): void {
    it(name, () =>
        Test.readonly(async context => {
            const records = await query(context, predicate);
            assert.equal(records.length, results.length, 'result length mismatches');
            records.map(stripSysColumns).forEach((rec, i) => {
                const d = stripSysColumns(results[i]);
                fixDatetimes(
                    rec.datetimeVal,
                    d.datetimeVal,
                    rec.datetimeRangeVal?.start,
                    d.datetimeRangeVal?.start,
                    rec.datetimeRangeVal?.end,
                    d.datetimeRangeVal?.end,
                    rec.datetimeRangeVal?.includedStart,
                    d.datetimeRangeVal?.includedStart,
                    rec.datetimeRangeVal?.excludedEnd,
                    d.datetimeRangeVal?.excludedEnd,
                    rec.datetimeRangeVal?.includedEnd,
                    d.datetimeRangeVal?.includedEnd,
                );
                assert.deepEqual(rec, d, `record ${i} mismatches`);
            });
        }),
    );
}

describe('sql converter tests', () => {
    describe('sql predicate dateRange tests', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });
        test(
            '_contains a dateRange with null',
            {
                dateRangeVal: {
                    _contains: null,
                },
            },
            [],
        );
        test(
            '_contains a dateRange with DateRange type',
            {
                dateRangeVal: {
                    _containsRange: datatypesData[5].dateRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a dateRange with String type',
            {
                dateRangeVal: {
                    _containsRange: datatypesData[5].dateRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a date',
            {
                dateRangeVal: {
                    _contains: datatypesData[5].dateRangeVal?.start as DateValue,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a date with string type',
            {
                dateRangeVal: {
                    _contains: datatypesData[5].dateRangeVal?.start?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_containedBy null dateRange',
            {
                dateRangeVal: {
                    _containedBy: null,
                },
            },
            [],
        );
        test(
            '_containedBy dateRange with DateRange type',
            {
                dateRangeVal: {
                    _containedBy: datatypesData[5].dateRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_containedBy dateRange with DateRange string',
            {
                dateRangeVal: {
                    _containedBy: datatypesData[5].dateRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );

        test(
            '_eq a null dateRange',
            {
                dateRangeVal: {
                    _eq: null,
                },
            },
            [datatypesData[0]],
        );
        test(
            '_eq a dateRange with DateRange type',
            {
                dateRangeVal: {
                    _eq: datatypesData[5].dateRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq a dateRange with DateRange string',
            {
                dateRangeVal: {
                    _eq: datatypesData[5].dateRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );

        test(
            '_ne a null dateRange',
            {
                dateRangeVal: {
                    _ne: null,
                },
            },
            datatypesData.slice(0, 0).concat(datatypesData.slice(1)),
        );

        test(
            '_ne a dateRange with DateRange type',
            {
                dateRangeVal: {
                    _ne: datatypesData[5].dateRangeVal,
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_ne a dateRange with DateRange string',
            {
                dateRangeVal: {
                    _ne: datatypesData[5].dateRangeVal?.toString(),
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_start date with DateValue type',
            {
                dateRangeVal: {
                    start: { _eq: datatypesData[14].dateRangeVal?.start as DateValue },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_start date with DateValue string',
            {
                dateRangeVal: {
                    start: { _eq: datatypesData[14].dateRangeVal?.start?.toString() },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end date with DateValue type',
            {
                dateRangeVal: {
                    end: { _eq: datatypesData[14].dateRangeVal?.end as DateValue },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end date with DateValue string',
            {
                dateRangeVal: {
                    end: { _eq: datatypesData[14].dateRangeVal?.end?.toString() },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_includedEnd date with DateValue type',
            {
                dateRangeVal: {
                    includedEnd: { _eq: datatypesData[14].dateRangeVal?.includedEnd as DateValue },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_includedEnd date with DateValue string',
            {
                dateRangeVal: {
                    includedEnd: { _eq: datatypesData[14].dateRangeVal?.includedEnd?.toString() },
                },
            },
            [datatypesData[14]],
        );
        after(() => restoreTables());
    });

    describe('sql predicate datetimeRange tests', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });
        test(
            '_contains a datetimeRange with null',
            {
                datetimeRangeVal: {
                    _contains: null,
                },
            },
            [],
        );

        test(
            '_contains a datetimeRange with DatetimeRange type',
            {
                datetimeRangeVal: {
                    _containsRange: datatypesData[5].datetimeRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a datetimeRange with String type',
            {
                datetimeRangeVal: {
                    _containsRange: datatypesData[5].datetimeRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a date',
            {
                datetimeRangeVal: {
                    _contains: datatypesData[5].datetimeRangeVal?.start as Datetime,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a date with string type',
            {
                datetimeRangeVal: {
                    _contains: datatypesData[5].datetimeRangeVal?.start?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_containedBy null datetimeRange',
            {
                datetimeRangeVal: {
                    _containedBy: null,
                },
            },
            [],
        );
        test(
            '_containedBy datetimeRange with DatetimeRange type',
            {
                datetimeRangeVal: {
                    _containedBy: datatypesData[5].datetimeRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_containedBy datetimeRange with DatetimeRange string',
            {
                datetimeRangeVal: {
                    _containedBy: datatypesData[5].datetimeRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq a null datetimeRange',
            {
                datetimeRangeVal: {
                    _eq: null,
                },
            },
            [datatypesData[0]],
        );
        test(
            '_eq a datetimeRange with DatetimeRange type',
            {
                datetimeRangeVal: {
                    _eq: datatypesData[5].datetimeRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq a datetimeRange with DatetimeRange string',
            {
                datetimeRangeVal: {
                    _eq: datatypesData[5].datetimeRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_ne a null datetimeRange',
            {
                datetimeRangeVal: {
                    _ne: null,
                },
            },
            datatypesData.slice(0, 0).concat(datatypesData.slice(1)),
        );
        test(
            '_ne a datetimeRange with DatetimeRange type',
            {
                datetimeRangeVal: {
                    _ne: datatypesData[5].datetimeRangeVal,
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_ne a datetimeRange with DatetimeRange string',
            {
                datetimeRangeVal: {
                    _ne: datatypesData[5].datetimeRangeVal?.toString(),
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_start date with DatetimeValue type',
            {
                datetimeRangeVal: {
                    start: { _eq: datatypesData[14].datetimeRangeVal?.start as Datetime },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_start date with DatetimeValue string',
            {
                datetimeRangeVal: {
                    start: { _eq: datatypesData[14].datetimeRangeVal?.start?.toString() },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end date with DatetimeValue type',
            {
                datetimeRangeVal: {
                    end: { _eq: datatypesData[14].datetimeRangeVal?.end as Datetime },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end date with DatetimeValue string',
            {
                datetimeRangeVal: {
                    end: { _eq: datatypesData[14].datetimeRangeVal?.end?.toString() },
                },
            },
            [datatypesData[14]],
        );
        after(() => restoreTables());
    });

    describe('sql predicate integerRange tests', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });
        test(
            '_contains an integerRange with null',
            {
                integerRangeVal: {
                    _contains: null,
                },
            },
            [],
        );
        test(
            '_contains an integerRange with DateRange type',
            {
                integerRangeVal: {
                    _containsRange: datatypesData[5].integerRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains an integerRange with String type',
            {
                integerRangeVal: {
                    _containsRange: datatypesData[5].integerRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a number',
            {
                integerRangeVal: {
                    _contains: datatypesData[5].integerRangeVal?.start,
                },
            },
            [datatypesData[4], datatypesData[5]],
        );
        test(
            '_contains a date with string type',
            {
                integerRangeVal: {
                    _contains: datatypesData[5].integerRangeVal?.start?.toString(),
                },
            },
            [datatypesData[4], datatypesData[5]],
        );
        test(
            '_containedBy null',
            {
                integerRangeVal: {
                    _containedBy: null,
                },
            },
            [],
        );
        test(
            '_containedBy integerRange with IntegerRange type',
            {
                integerRangeVal: {
                    _containedBy: datatypesData[5].integerRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_containedBy integerRange with DateRange string',
            {
                integerRangeVal: {
                    _containedBy: datatypesData[5].integerRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq a null integerRangeVal',
            {
                integerRangeVal: {
                    _eq: null,
                },
            },
            [datatypesData[0]],
        );
        test(
            '_eq an integerRangeVal with IntegerRangeVal type',
            {
                integerRangeVal: {
                    _eq: datatypesData[5].integerRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq an integerRangeVal with IntegerRangeVal string',
            {
                integerRangeVal: {
                    _eq: datatypesData[5].integerRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );

        test(
            '_ne a null integerRangeVal',
            {
                integerRangeVal: {
                    _ne: null,
                },
            },
            datatypesData.slice(0, 0).concat(datatypesData.slice(1)),
        );

        test(
            '_ne an integerRangeVal with IntegerRangeVal type',
            {
                integerRangeVal: {
                    _ne: datatypesData[5].integerRangeVal,
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_ne an integerRangeVal with IntegerRangeVal string',
            {
                integerRangeVal: {
                    _ne: datatypesData[5].integerRangeVal?.toString(),
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_start integer with IntegerRangeVal type',
            {
                integerRangeVal: {
                    start: { _eq: datatypesData[14].integerRangeVal?.start },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_start integer with integer string',
            {
                integerRangeVal: {
                    start: { _eq: datatypesData[14].integerRangeVal?.start?.toString() },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end integer with integer type',
            {
                integerRangeVal: {
                    end: { _eq: datatypesData[14].integerRangeVal?.end as number },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end integer with integer string',
            {
                integerRangeVal: {
                    end: { _eq: datatypesData[14].integerRangeVal?.end?.toString() },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_includedEnd integer with integer type',
            {
                integerRangeVal: {
                    includedEnd: { _eq: datatypesData[14].integerRangeVal?.includedEnd as number },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_includedEnd integer with integer string',
            {
                integerRangeVal: {
                    includedEnd: { _eq: datatypesData[14].integerRangeVal?.includedEnd?.toString() },
                },
            },
            [datatypesData[14]],
        );
        after(() => restoreTables());
    });

    describe('sql predicate decimalRange tests', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });
        test(
            '_contains a decimalRange with null',
            {
                decimalRangeVal: {
                    _contains: null,
                },
            },
            [],
        );
        test(
            '_contains a decimalRange with DateRange type',
            {
                decimalRangeVal: {
                    _containsRange: datatypesData[5].decimalRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a decimalRange with String type',
            {
                decimalRangeVal: {
                    _containsRange: datatypesData[5].decimalRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_contains a decimal',
            {
                decimalRangeVal: {
                    _contains: datatypesData[5].decimalRangeVal?.start,
                },
            },
            [datatypesData[4], datatypesData[5]],
        );
        test(
            '_contains a decimal with string type',
            {
                decimalRangeVal: {
                    _contains: datatypesData[5].decimalRangeVal?.start?.toString(),
                },
            },
            [datatypesData[4], datatypesData[5]],
        );
        test(
            '_containedBy null',
            {
                decimalRangeVal: {
                    _containedBy: null,
                },
            },
            [],
        );
        test(
            '_containedBy decimalRange with DecimalRange type',
            {
                decimalRangeVal: {
                    _containedBy: datatypesData[5].decimalRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_containedBy decimalRange with DecimalRange string',
            {
                decimalRangeVal: {
                    _containedBy: datatypesData[5].decimalRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq a null',
            {
                decimalRangeVal: {
                    _eq: null,
                },
            },
            [datatypesData[0]],
        );
        test(
            '_eq a decimalRange with DecimalRange type',
            {
                decimalRangeVal: {
                    _eq: datatypesData[5].decimalRangeVal,
                },
            },
            [datatypesData[5]],
        );
        test(
            '_eq a decimalRangeVal with DecimalRange string',
            {
                decimalRangeVal: {
                    _eq: datatypesData[5].decimalRangeVal?.toString(),
                },
            },
            [datatypesData[5]],
        );

        test(
            '_ne a null',
            {
                decimalRangeVal: {
                    _ne: null,
                },
            },
            datatypesData.slice(0, 0).concat(datatypesData.slice(1)),
        );

        test(
            '_ne a decimalRangeVal with DecimalRangeVal type',
            {
                decimalRangeVal: {
                    _ne: datatypesData[5].decimalRangeVal,
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_ne a decimalRangeVal with DecimalRangeVal string',
            {
                decimalRangeVal: {
                    _ne: datatypesData[5].decimalRangeVal?.toString(),
                },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_start decimal with DecimalRangeVal type',
            {
                decimalRangeVal: {
                    start: { _eq: datatypesData[14].decimalRangeVal?.start },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_start decimal with decimal string',
            {
                decimalRangeVal: {
                    start: { _eq: datatypesData[14].decimalRangeVal?.start?.toString() },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end decimal with Decimal type',
            {
                decimalRangeVal: {
                    end: { _eq: datatypesData[14].decimalRangeVal?.end },
                },
            },
            [datatypesData[14]],
        );
        test(
            '_end decimal with decimal string',
            {
                decimalRangeVal: {
                    end: { _eq: datatypesData[14].decimalRangeVal?.end?.toString() },
                },
            },
            [datatypesData[14]],
        );

        after(() => restoreTables());
    });

    describe('sql predicate array tests', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });

        test(
            '_contains an integer with integerArray type',
            {
                integerArrayVal: {
                    _contains: datatypesData[5].integerArrayVal[1],
                },
            },
            [datatypesData[4], datatypesData[5], datatypesData[6]],
        );
        test(
            '_contains an enum with enumArray type',
            {
                enumArrayVal: {
                    _contains: datatypesData[5].enumArrayVal[1],
                },
            },
            [datatypesData[2], datatypesData[5], datatypesData[8], datatypesData[11], datatypesData[14]],
        );

        test(
            '_contains a string with stringArray type',
            {
                stringArrayVal: {
                    _contains: datatypesData[5].stringArrayVal[1],
                },
            },
            [datatypesData[4], datatypesData[5], datatypesData[6]],
        );

        after(() => restoreTables());
    });

    describe('sql predicate tests', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });
        test('empty condition', {}, datatypesData);
        test(
            'direct match',
            {
                integerVal: datatypesData[5].integerVal,
                dateVal: datatypesData[5].dateVal,
                dateRangeVal: datatypesData[5].dateRangeVal,
                decimalVal: datatypesData[5].decimalVal.toString(),
            },
            [datatypesData[5]],
        );
        test(
            'direct no match',
            {
                integerVal: datatypesData[5].integerVal,
                dateVal: datatypesData[6].dateVal,
                dateRangeVal: datatypesData[6].dateRangeVal,
            },
            [],
        );
        test(
            '_eq',
            {
                dateVal: { _eq: datatypesData[5].dateVal },
            },
            [datatypesData[5]],
        );
        test(
            '_eq (null date)',
            {
                dateVal: { _eq: datatypesData[0].dateVal },
            },
            [datatypesData[0]],
        );
        test(
            '_ne',
            {
                dateVal: { _ne: datatypesData[5].dateVal },
            },
            datatypesData.slice(0, 5).concat(datatypesData.slice(6)),
        );
        test(
            '_ne (null date)',
            {
                dateVal: { _ne: datatypesData[0].dateVal },
            },
            datatypesData.slice(1),
        );
        test(
            '_lt',
            {
                dateVal: { _lt: datatypesData[5].dateVal },
            },
            datatypesData.slice(0, 5),
        );
        test(
            '_lte',
            {
                dateVal: { _lte: datatypesData[5].dateVal },
            },
            datatypesData.slice(0, 6),
        );
        test(
            '_gt',
            {
                dateVal: { _gt: datatypesData[5].dateVal },
            },
            datatypesData.slice(6),
        );
        test(
            '_gte',
            {
                dateVal: { _gte: datatypesData[5].dateVal },
            },
            datatypesData.slice(5),
        );
        test(
            '_in',
            {
                dateVal: { _in: [datatypesData[3].dateVal, datatypesData[5].dateVal] },
            },
            [datatypesData[3], datatypesData[5]],
        );
        test(
            '_nin',
            {
                dateVal: { _nin: [datatypesData[3].dateVal, datatypesData[5].dateVal] },
            },
            datatypesData.slice(0, 3).concat([datatypesData[4]]).concat(datatypesData.slice(6)),
        );
        test(
            '_and',
            {
                _and: [
                    {
                        dateVal: { _gt: datatypesData[3].dateVal },
                    },
                    {
                        dateVal: { _lt: datatypesData[6].dateVal },
                    },
                ],
            },
            datatypesData.slice(4, 6),
        );
        test(
            '_or',
            {
                _or: [
                    {
                        dateVal: { _eq: datatypesData[3].dateVal },
                    },
                    {
                        dateVal: { _eq: datatypesData[5].dateVal },
                    },
                ],
            },
            [datatypesData[3], datatypesData[5]],
        );
        test(
            '_nor',
            {
                _nor: [
                    {
                        dateVal: { _eq: datatypesData[3].dateVal },
                    },
                    {
                        dateVal: { _eq: datatypesData[5].dateVal },
                    },
                ],
            },
            datatypesData.slice(0, 3).concat([datatypesData[4]]).concat(datatypesData.slice(6)),
        );
        test(
            '_not',
            {
                _not: {
                    dateVal: { _gt: datatypesData[3].dateVal },
                    dateRangeVal: { _gt: datatypesData[3].dateRangeVal },
                },
            },
            datatypesData.slice(0, 4),
        );
        test(
            '_mod',
            {
                id: { _mod: [4, 1] },
            },
            [1, 5, 9, 13].map(i => datatypesData[i]),
        );
        after(() => restoreTables());
    });

    describe('sql predicate regex', () => {
        before(async () => {
            await setup({ application: await testDatatypesApplication.application });
            await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
        });
        test(
            'regex (not anchored)',
            {
                stringVal: /ring/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex with i option',
            {
                stringVal: {
                    _regex: 'Ring',
                    _options: 'i',
                },
            },
            datatypesData.slice(1),
        );
        test(
            'regex with i option and invalid regex on _id',
            {
                _id: {
                    _regex: NaN,
                    _options: NaN,
                } as any,
                stringVal: {
                    _regex: 'Ring',
                    _options: 'i',
                },
            },
            datatypesData.slice(1),
        );
        test(
            'regex without i option',
            {
                stringVal: {
                    _regex: 'Ring',
                },
            },
            [],
        );
        test(
            'regex without i option and invalid regex on _id',
            {
                _id: {
                    _regex: NaN,
                    _options: NaN,
                } as any,
                stringVal: {
                    _regex: 'Ring',
                },
            },
            [],
        );
        test(
            'regex (anchored at start, matching)',
            {
                stringVal: /^string/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex (anchored at start, not matching)',
            {
                stringVal: /^ring/,
            },
            [],
        );
        test(
            'regex (anchored at end, not matching)',
            {
                stringVal: /1$/,
            },
            [1, 11].map(i => datatypesData[i]),
        );
        test(
            'regex . pattern (matching)',
            {
                stringVal: /tr..g/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex . pattern (not matching)',
            {
                stringVal: /t..g/,
            },
            [],
        );
        test(
            'regex .* pattern (matching multiple)',
            {
                // eslint-disable-next-line @sage/redos/no-vulnerable
                stringVal: /t.*g/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex .* pattern (matching 1)',
            {
                // eslint-disable-next-line @sage/redos/no-vulnerable
                stringVal: /tri.*g/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex .* pattern (matching empty)',
            {
                // eslint-disable-next-line @sage/redos/no-vulnerable
                stringVal: /trin.*g/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex .* pattern (anchored and matching at start)',
            {
                stringVal: /^.*trin/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex .* pattern (not anchored and matching at start)',
            {
                // eslint-disable-next-line @sage/redos/no-vulnerable
                stringVal: /.*trin/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex .* pattern (anchored and matching at end)',
            {
                // eslint-disable-next-line @sage/redos/no-vulnerable
                stringVal: /tring...1.*$/,
            },
            [1, 10, 11, 12, 13, 14, 15].map(i => datatypesData[i]),
        );
        test(
            'regex .* pattern (not anchored and matching at end)',
            {
                stringVal: /tring...1.*/,
            },
            [1, 10, 11, 12, 13, 14, 15].map(i => datatypesData[i]),
        );
        test(
            'regex containing % (matching)',
            {
                stringVal: /tring%/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex containing % (not matching)',
            {
                stringVal: /t%g/,
            },
            [],
        );
        test(
            'regex containing _ (matching)',
            {
                stringVal: /tring._/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex containing _ (not matching)',
            {
                stringVal: /tr_ng/,
            },
            [],
        );
        test(
            'regex containing $ (matching)',
            {
                stringVal: /tring..\$/,
            },
            datatypesData.slice(1),
        );
        test(
            'regex containing $ (not matching)',
            {
                stringVal: /tr\$ng/,
            },
            [],
        );
        after(() => restoreTables());
    });
});
