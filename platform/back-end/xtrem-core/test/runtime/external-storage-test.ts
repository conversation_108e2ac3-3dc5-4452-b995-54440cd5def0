import { AnyR<PERSON><PERSON>, asyncArray, As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AsyncReader } from '@sage/xtrem-async-helper';
import { Datetime, DateValue } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import * as fsp from 'path';
import { fixtures } from '..';
import { decorators, Node, Reference, Test } from '../../index';
import {
    AnyRecordWithId,
    Collection,
    Context,
    Dict,
    EnumDataType,
    Extend,
    ExternalStorageManager,
    NodeExternalQueryOptions,
    NodeFactory,
    OrderBy,
    OrderByClause,
    PropertyAndValue,
    ValidationContext,
    ValidationSeverity,
} from '../../lib';
import { isScalar } from '../../lib/runtime';
import { integer } from '../../lib/ts-api';
import { InternalPropertyJoin, parseCursor } from '../../lib/types';
import { ConfigManager, createApplicationWithApi, restoreTables, setup } from '../fixtures/index';

let lineData = [] as AnyRecord[];

let headerData = [] as AnyRecord[];

function resetData(): void {
    lineData = [];
    for (let i = 1; i <= 10; i += 1) {
        const headerCode = `${(i % 2) + 1}`;
        lineData.push({
            COD_0: `${i}`,
            DES_0: `Description ${i}`,
            NUM_0: i,
            HEAD_0: headerCode,
            _createStamp: Datetime.now(),
            _updateStamp: Datetime.now(),
            _createUser: 'UT',
            _updateUser: 'UT',
        });
    }
    headerData = [];
    for (let i = 1; i <= 2; i += 1) {
        headerData.push({ COD_0: `${i}`, DEFERRED: `${i}`, DES_0: `Header ${i}`, DAT_0: DateValue.make(2020, 3, 1) });
    }
}
class TestExternalStorageManager<T extends Node> implements ExternalStorageManager<T> {
    factory: NodeFactory;

    additionalHeaderInfo = {
        COD_0: 'headerCode',
        DES_0: 'description',
        DAT_0: 'date',
        DEFERRED_0: 'deferred',
        _createStamp: '_createStamp',
        _updateStamp: '_updateStamp',
        _createUser: '_createUser',
        _updateUser: '_updateUser',
    };

    additionalLineInfo = {
        COD_0: 'code',
        DES_0: 'description',
        NUM_0: 'num',
        HEAD_0: 'header',
        _createStamp: '_createStamp',
        _updateStamp: '_updateStamp',
        _createUser: '_createUser',
        _updateUser: '_updateUser',
    };

    async insert(node: Extend<T>, cx: ValidationContext): Promise<AnyRecordWithId> {
        // this.factory.name === 'TestExternalLineNode' is not required but testing if this.factory is in context
        if (node instanceof TestExternalLineNode && this.factory.name === 'TestExternalLineNode') {
            lineData.push({
                COD_0: await node.code,
                DES_0: await node.description,
                NUM_0: await node.num,
                HEAD_0: await (await node.header).headerCode,
                _createStamp: Datetime.now(),
                _updateStamp: Datetime.now(),
                _createUser: 'UT',
                _updateUser: 'UT',
            });
            cx.addDiagnose(ValidationSeverity.test, `Insert detail code=${await node.code}`);

            return {
                _id: this.getId(node.$.context, node.$.state.values),
                _createStamp: Datetime.now(),
                _updateStamp: Datetime.now(),
                _createUser: 'UT',
                _updateUser: 'UT',
            };
        }

        if (node instanceof TestExternalHeaderNode && this.factory.name === 'TestExternalHeaderNode') {
            const headerCode = (await node.headerCode) || '99';
            headerData.push({
                COD_0: headerCode,
                DES_0: await node.description,
                DAT_0: await node.date,
                DEFERRED_0: 99,
                _createStamp: Datetime.now(),
                _updateStamp: Datetime.now(),
                _createUser: 'UT',
                _updateUser: 'UT',
            });
            cx.addDiagnose(ValidationSeverity.test, `Insert header code=${headerCode}`);

            return {
                _id: this.getId(node.$.context, { ...node.$.state.values, headerCode }),
                headerCode,
                deferred: 99,
                _createStamp: Datetime.now(),
                _updateStamp: Datetime.now(),
                _createUser: 'UT',
                _updateUser: 'UT',
            };
        }

        throw new Error(`${this.factory.name}: insert not managed`);
    }

    async update(node: Extend<T>, cx: ValidationContext): Promise<AnyRecord[]> {
        const data = node.$.state.values;
        if (node instanceof TestExternalLineNode && this.factory.name === 'TestExternalLineNode') {
            const line = (await asyncArray(lineData).find(async l => l.COD_0 === (await node.code)))!;

            Object.keys(data).forEach(dkey => {
                const mapKey = Object.keys(this.additionalLineInfo).find(
                    info => (this.additionalLineInfo as any)[info] === dkey,
                );
                line[mapKey!] = data[dkey];
            });
            cx.addDiagnose(ValidationSeverity.test, `Update detail code=${data.code}`);
        } else if (node instanceof TestExternalHeaderNode && this.factory.name === 'TestExternalHeaderNode') {
            const header = (await asyncArray(headerData).find(async head => head.COD_0 === (await node.headerCode)))!;

            Object.keys(data).forEach(dkey => {
                const mapKey = Object.keys(this.additionalHeaderInfo).find(
                    info => (this.additionalHeaderInfo as any)[info] === dkey,
                );
                header[mapKey!] = data[dkey];
            });
            cx.addDiagnose(ValidationSeverity.test, `Update header code=${data.headerCode}`);
        }
        return [{ _id: this.getId(node.$.context, node.$.state.values) }];
    }

    async delete(node: Extend<T>, cx: ValidationContext): Promise<number> {
        if (node instanceof TestExternalLineNode && this.factory.name === 'TestExternalLineNode') {
            const ind = lineData.map(line => line.COD_0).indexOf(await node.code);
            lineData.splice(ind, 1);

            cx.addDiagnose(ValidationSeverity.test, `Delete detail code=${await node.code}`);
        } else if (node instanceof TestExternalHeaderNode && this.factory.name === 'TestExternalHeaderNode') {
            const ind = headerData.map(head => head.COD_0).indexOf(await node.headerCode);
            headerData.splice(ind, 1);

            cx.addDiagnose(ValidationSeverity.test, `Delete header code=${await node.headerCode}`);
        }
        return 1;
    }

    query(context: Context, options: NodeExternalQueryOptions<T>): AsyncReader<any> {
        let counter = 0;
        const read = (): AnyRecord | undefined => {
            let val: AnyRecord | undefined;
            if (
                this.factory.name === 'TestExternalLineNode' &&
                options.filters &&
                options.filters.length &&
                (options.filters[0] as any).code
            ) {
                val = counter === 0 ? lineData.find(d => d.COD_0 === (options.filters[0] as any).code) : undefined;
            } else if (
                this.factory.name === 'TestExternalLineNode' &&
                options.filters &&
                options.filters.length &&
                (options.filters[0] as any).header
            ) {
                val = lineData.filter(d => d.HEAD_0 === (options.filters[0] as any).header)[counter];
            } else if (
                this.factory.name === 'TestExternalHeaderNode' &&
                options.filters &&
                options.filters.length &&
                (options.filters[0] as any).headerCode
            ) {
                val =
                    counter === 0
                        ? headerData.find(d => d.COD_0 === (options.filters[0] as any).headerCode)
                        : undefined;
            } else if (this.factory.name === 'TestExternalLineNode') {
                val = lineData[counter];
            } else if (this.factory.name === 'TestExternalHeaderNode') {
                val = headerData[counter];
            } else if (this.factory.name === 'TestExternalUser' && counter === 0) {
                // counter === 0 check is to break reader after the first read on the AsyncReader
                // Without this check we will have an infinite loop as we will neveer return undefined
                val = { code: 'UT', name: 'Unit test' };
            }

            if (val) {
                val._createStamp = Datetime.now();
                val._updateStamp = Datetime.now();
                val._createUser = 'UT';
                val._updateUser = 'UT';
            }

            counter += 1;
            return val;
        };

        return new AsyncGenericReader<any>({ read, stop() {} });
    }

    mapRecordIn(record: any): any {
        const result = {} as any;
        if (this.factory.name === 'TestExternalLineNode') {
            Object.keys(record).forEach(selectedColumn => {
                result[(this.additionalLineInfo as any)[selectedColumn]] = record[selectedColumn];
            });
        } else if (this.factory.name === 'TestExternalHeaderNode') {
            Object.keys(record).forEach(selectedColumn => {
                result[(this.additionalHeaderInfo as any)[selectedColumn]] = record[selectedColumn];
            });
        }

        return result;
    }

    // eslint-disable-next-line class-methods-use-this
    mapAggregateRecordIn(record: any): any {
        return record;
    }

    getReferenceJoin(propertyName: string): InternalPropertyJoin<T> {
        if (propertyName === 'header' && this.factory.name === 'TestExternalLineNode') {
            return { headerCode: 'header' } as Dict<any>;
        }

        if (propertyName === '_createUser' || propertyName === '_updateUser') {
            return { code: propertyName };
        }
        throw new Error(`${this.factory.name}.${propertyName}: invalid property name`);
    }

    getCollectionJoin(propertyName: string): InternalPropertyJoin<T> {
        if (
            (propertyName === 'lines' || propertyName === 'mutableLines') &&
            this.factory.name === 'TestExternalHeaderNode'
        ) {
            return { header: 'headerCode' };
        }
        throw new Error(`${this.factory.name}.${propertyName}: invalid property name`);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getJoinValues(node: Extend<T>, data: any, propertyName: string, _index: number): Dict<any> {
        if (propertyName === 'header' && this.factory.name === 'TestExternalLineNode') {
            return { headerCode: data.header };
        }
        if (
            (propertyName === 'lines' || propertyName === 'mutableLines') &&
            this.factory.name === 'TestExternalHeaderNode'
        ) {
            return { header: node._id };
        }

        if (propertyName === '_createUser' || propertyName === '_updateUser') {
            return { code: 'UT' };
        }
        throw new Error(`${this.factory.name}.${propertyName}: invalid property name`);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    parseOrderBy(context: Context, orderBy: OrderBy<Node> | undefined): OrderByClause[] {
        return this.factory.keyProperties.map(keyProp => {
            return {
                path: [keyProp.name],
                property: keyProp,
                direction: 1,
                sql: '',
            };
        });
    }

    parseCursor(orderByClauses: OrderByClause[], value: string): PropertyAndValue[] {
        const internalFactory = Test.application.getFactoryByConstructor(this.factory.nodeConstructor);
        const internalProperties = orderByClauses.map(clause => internalFactory.findProperty(clause.property.name));
        return parseCursor(internalProperties, value).map(propVal => {
            const property = this.factory.properties.find(prop => prop.name === propVal.property.name);
            return { property, value: propVal.value } as PropertyAndValue;
        });
    }

    // Offset transient _id so that it does not interfere with generated ids from payload
    private _lastTransientId = -1000000000;

    allocateTransientId(): string {
        this._lastTransientId -= 1;
        return String(this._lastTransientId);
    }

    getKeyValues(context: Context, values: any, options?: { allocateTransient: boolean }): Dict<any> {
        if (isScalar(values)) {
            if (this.factory.keyProperties.length > 1)
                throw new Error(`${this.factory.name} incomplete key passed for read.`);
            const result = { [this.factory.keyProperties[0].name]: values };
            result._id = this.getId(context, result);

            return result;
        }
        const result = this.factory.keyProperties.reduce((r, k) => {
            r[k.name] = values[k.name];
            return r;
        }, {} as AnyRecord);
        result._id = this.getId(context, result);
        if (!result._id && options?.allocateTransient) result._id = this.allocateTransientId();
        return result;
    }

    // eslint-disable-next-line class-methods-use-this
    isKeyPropertyTransient(_propertyName: string, value: any): boolean {
        const numberValue = Number(value);
        if (Number.isFinite(numberValue) && numberValue < 0) return true;
        return false;
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    isReverseReferenceProperty(_propertyName: string): boolean {
        return false;
    }

    get defaultOrderBy(): OrderBy<Node> {
        return this.factory.keyProperties.reduce((r, k) => {
            r[k.name] = 1;
            return r;
        }, {} as Dict<any>);
    }

    /**
     * Gets the formatted key values a string concatenated by `~`
     * This will be used for the _id value
     *
     * @param context
     * @param values
     * @returns
     */
    getId(context: Context, values: Dict<any>): string {
        let allKeysFound = true;
        const result = this.factory.keyProperties.reduce((r, k, i) => {
            if (values[k.name] == null) allKeysFound = false;
            let currVal = values[k.name];
            if (k.type === 'enum' && !Number.isFinite(Number(currVal))) {
                const enumDataType = k.dataType as EnumDataType;
                currVal = enumDataType.numberValue(currVal);
            }
            currVal = String(currVal);
            if (this.factory.keyProperties.length === 1 || i === 0) return currVal;
            return `${r}~${currVal}`;
        }, '');

        if (!allKeysFound) return String(context.allocateTransientId());

        return result;
    }

    // eslint-disable-next-line class-methods-use-this
    canCreate(canCreate: boolean): boolean {
        return canCreate;
    }

    // eslint-disable-next-line class-methods-use-this
    canUpdate(canUpdate: boolean): boolean {
        return canUpdate;
    }

    // eslint-disable-next-line class-methods-use-this
    canDelete(canDelete: boolean): boolean {
        return canDelete;
    }

    // eslint-disable-next-line class-methods-use-this
    canDeleteMany(canDeleteMany: boolean): boolean {
        return canDeleteMany;
    }
}

@decorators.node<TestExternalLineNode>({
    isPublished: true,
    storage: 'external',
    keyPropertyNames: ['code'],
    canCreate: true,
    canUpdate: true,
    canRead: true,
    canDelete: true,
    canDeleteMany: true,
    externalStorageManager: new TestExternalStorageManager(),
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isVitalCollectionChild: true,
})
export class TestExternalLineNode extends Node {
    @decorators.stringProperty<TestExternalLineNode, 'code'>({
        isStored: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.integerProperty<TestExternalLineNode, 'num'>({
        isStored: true,
    })
    readonly num: Promise<integer>;

    @decorators.stringProperty<TestExternalLineNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => fixtures.dataTypes.descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestExternalLineNode, 'header'>({
        isPublished: true,
        isStored: true,
        node: () => TestExternalHeaderNode,
        isVitalParent: true,
    })
    readonly header: Reference<TestExternalHeaderNode>;
}

@decorators.node<TestExternalHeaderNode>({
    isPublished: true,
    storage: 'external',
    keyPropertyNames: ['headerCode'],
    canCreate: true,
    canUpdate: true,
    canRead: true,
    canDelete: true,
    canDeleteMany: true,
    externalStorageManager: new TestExternalStorageManager(),
    indexes: [{ orderBy: { headerCode: 1 }, isUnique: true }],
})
export class TestExternalHeaderNode extends Node {
    @decorators.stringProperty<TestExternalHeaderNode, 'headerCode'>({
        isStored: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly headerCode: Promise<string>;

    @decorators.integerProperty<TestExternalHeaderNode, 'deferred'>({
        isStored: true,
    })
    readonly deferred: Promise<integer>;

    @decorators.stringProperty<TestExternalHeaderNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => fixtures.dataTypes.descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.dateProperty<TestExternalHeaderNode, 'date'>({
        isPublished: true,
        isStored: true,
    })
    readonly date: Promise<DateValue>;

    @decorators.collectionProperty<TestExternalHeaderNode, 'lines'>({
        node: () => TestExternalLineNode,
        isVital: true,
        reverseReference: 'header',
    })
    readonly lines: Collection<TestExternalLineNode>;

    @decorators.collectionProperty<TestExternalHeaderNode, 'mutableLines'>({
        node: () => TestExternalLineNode,
        isMutable: true,
    })
    readonly mutableLines: Collection<TestExternalLineNode>;
}

@decorators.node<TestExternalUser>({
    isPublished: true,
    storage: 'external',
    keyPropertyNames: ['code'],
    canCreate: true,
    canUpdate: true,
    canRead: true,
    canDelete: true,
    canDeleteMany: true,
    externalStorageManager: new TestExternalStorageManager(),
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestExternalUser extends Node {
    @decorators.stringProperty<TestExternalUser, 'code'>({
        isStored: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestExternalUser, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => fixtures.dataTypes.descriptionDataType,
    })
    readonly name: Promise<string>;
}

ConfigManager.load(fsp.join(__dirname, '..'), 'test');
let getUserNode: any;
describe('External Storage', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestExternalUser, TestExternalLineNode, TestExternalHeaderNode },
            }),
        });
        // Backup the mock getUserNode
        getUserNode = Context.accessRightsManager.getUserNode;
        // Override the getUserNode method to return a Node that does not exist in the application
        // This is so the at the _createUser and _updateUser properties are defined as integer properties.
        Context.accessRightsManager.getUserNode = () => TestExternalUser;
    });

    after(async () => {
        await restoreTables();
        // Restore the getUserNode method
        Context.accessRightsManager.getUserNode = getUserNode;
    });

    [
        { config: { storage: { ...ConfigManager.current.storage, managedExternal: false } } },
        { config: { storage: { managedExternal: true } } },
    ].forEach(config => {
        describe('External Storage - readonly context', () => {
            before(() => {
                resetData();
            });
            it('should allow read', () =>
                Test.withReadonlyContext(async context => {
                    const details = await context.read(TestExternalLineNode, { code: '1' });
                    assert.instanceOf(details, TestExternalLineNode);
                    assert.equal(await details.code, '1');
                }, config));

            it('should allow query', () =>
                Test.withReadonlyContext(async context => {
                    const details = await context.query(TestExternalLineNode).toArray();
                    assert.equal(details.length, 10);
                    await asyncArray(details).forEach(async (detail, i) => {
                        assert.instanceOf(detail, TestExternalLineNode);
                        assert.equal(await detail.code, `${i + 1}`);
                    });
                }, config));

            it('should allow traversal and load of references', () =>
                Test.withReadonlyContext(async context => {
                    const details = await context.read(TestExternalLineNode, { code: '1' });
                    const header = await details.header;
                    assert.instanceOf(header, TestExternalHeaderNode);
                    assert.equal(await header.headerCode, '2');
                }, config));

            it('should allow traversal and load of collection', () =>
                Test.withReadonlyContext(async context => {
                    const header = await context.read(TestExternalHeaderNode, { headerCode: '1' });
                    assert.instanceOf(header, TestExternalHeaderNode);
                    assert.equal(await header.headerCode, '1');
                    const lines = await header.lines.toArray();
                    assert.equal(lines.length, 5);

                    await asyncArray(lines).forEach(async line => {
                        assert.instanceOf(line, TestExternalLineNode);
                        assert.equal(`${(Number.parseInt(await line.code, 10) % 2) + 1}`, await header.headerCode);
                    });

                    const mutableCollectionFactory = context.application.getFactoryByConstructor(TestExternalLineNode);

                    assert.isDefined(mutableCollectionFactory.findProperty('_action'));

                    const mutableLines = await header.mutableLines.toArray();
                    assert.equal(mutableLines.length, 5);

                    await asyncArray(mutableLines).forEach(async line => {
                        assert.instanceOf(line, TestExternalLineNode);
                        assert.equal(`${(Number.parseInt(await line.code, 10) % 2) + 1}`, await header.headerCode);
                    });
                }, config));
            it('should throw on creating a node', () =>
                Test.withReadonlyContext(async context => {
                    await assert.isRejected(
                        context.create(TestExternalHeaderNode, {}),
                        'TestExternalHeaderNode: cannot create: context is readonly',
                    );
                }, config));
            it('should throw on reading for update', () =>
                Test.withReadonlyContext(async context => {
                    await assert.isRejected(
                        context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true }),
                        /TestExternalLineNode: cannot read for update: context is readonly/,
                    );
                }, config));
            it("should throw because the key doesn't match the defined table index", () =>
                Test.withReadonlyContext(async context => {
                    await assert.isRejected(
                        context.read(TestExternalLineNode, {
                            description: 'Description 1',
                        }),
                        "Keys don't match any unique index",
                    );
                }, config));
            it("tryRead - should throw because the key doesn't match the defined table index", () =>
                Test.withReadonlyContext(async context => {
                    await assert.isRejected(
                        context.tryRead(TestExternalLineNode, {
                            description: 'Description 1',
                        }),
                        "Keys don't match any unique index",
                    );
                }, config));
            after(() => restoreTables());
        });

        describe('External Storage - Writable context', () => {
            before(() => {
                resetData();
            });
            it('should allow read', () =>
                Test.withUncommittedContext(async context => {
                    const details = await context.read(TestExternalLineNode, { code: '1' });
                    assert.instanceOf(details, TestExternalLineNode);
                    assert.equal(await details.code, '1');
                }, config));

            it('should allow query', () =>
                Test.withUncommittedContext(async context => {
                    const details = await context.query(TestExternalLineNode).toArray();
                    assert.equal(details.length, 10);
                    await asyncArray(details).forEach(async (detail, i) => {
                        assert.instanceOf(detail, TestExternalLineNode);
                        assert.equal(await detail.code, `${i + 1}`);
                    });
                }, config));

            it('should allow traversal and load of references', () =>
                Test.withUncommittedContext(async context => {
                    const details = await context.read(TestExternalLineNode, { code: '1' });
                    const header = await details.header;
                    assert.instanceOf(header, TestExternalHeaderNode);
                    assert.equal(await header.headerCode, '2');
                }, config));

            it('should allow traversal and load of collection', () =>
                Test.withUncommittedContext(async context => {
                    const header = await context.read(TestExternalHeaderNode, { headerCode: '1' });
                    assert.instanceOf(header, TestExternalHeaderNode);
                    assert.equal(await header.headerCode, '1');
                    const lines = await header.lines.toArray();
                    assert.equal(lines.length, 5);

                    await asyncArray(lines).forEach(async line => {
                        assert.instanceOf(line, TestExternalLineNode);
                        assert.equal(`${(Number.parseInt(await line.code, 10) % 2) + 1}`, await header.headerCode);
                    });

                    const mutableLines = await header.mutableLines.toArray();
                    assert.equal(mutableLines.length, 5);

                    await asyncArray(mutableLines).forEach(async line => {
                        assert.instanceOf(line, TestExternalLineNode);
                        assert.equal(`${(Number.parseInt(await line.code, 10) % 2) + 1}`, await header.headerCode);
                    });
                }, config));

            it('should allow read for update', () =>
                Test.uncommitted(async context => {
                    const details = await context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true });
                    assert.instanceOf(details, TestExternalLineNode);
                    assert.equal(await details.code, '1');
                }, config));

            it('should be readonly after reference traversal', () =>
                Test.uncommitted(async context => {
                    const details = await context.read(TestExternalLineNode, { code: '1' });
                    const header = await details.header;
                    assert.instanceOf(header, TestExternalHeaderNode);
                    assert.equal(await header.headerCode, '2');
                    await assert.isRejected(
                        header.$.set({ description: 'def' }),
                        'TestExternalHeaderNode: node is readonly',
                    );
                }, config));
            it('should be readonly in collection', () =>
                Test.uncommitted(async context => {
                    const header = await context.read(TestExternalHeaderNode, { headerCode: '1' });
                    assert.instanceOf(header, TestExternalHeaderNode);
                    assert.equal(await header.headerCode, '1');
                    const lines = await header.lines.toArray();
                    assert.equal(lines.length, 5);
                    const line = lines[0];

                    await assert.isRejected(
                        line.$.set({ description: 'ghi' }),
                        'TestExternalLineNode: node is readonly',
                    );

                    const mutableLines = await header.mutableLines.toArray();
                    assert.equal(mutableLines.length, 5);
                    const mutableLine = mutableLines[0];

                    await assert.isRejected(
                        mutableLine.$.set({ description: 'ghi' }),
                        'TestExternalLineNode: node is readonly',
                    );
                }, config));

            after(() => {});
        });

        describe('External Storage - Mutations', () => {
            beforeEach(() => {
                resetData();
            });
            it('should be able to create and save', () =>
                Test.withUncommittedContext(async context => {
                    const line = await context.create(TestExternalLineNode, {
                        code: '99',
                        description: 'Description 99',
                        header: '1',
                        num: 99,
                    });

                    assert.equal(await line.description, 'Description 99');
                    // resolve reference
                    assert.instanceOf(await line.header, TestExternalHeaderNode);
                    assert.equal(await (await line.header).description, 'Header 1');

                    // can save
                    await line.$.save();

                    assert.deepEqual(line.$.context.diagnoses, [
                        { severity: 0, path: [], message: 'Insert detail code=99' },
                    ]);
                }, config));

            it('should be able to create and save with collection', async () => {
                const linesData = [
                    { code: '99.1', description: '99.1', num: 1 },
                    { code: '99.2', description: '99.2', num: 2 },
                ];

                const mutableLinesData = [
                    { code: '101.1', description: '101.1', num: 1 },
                    { code: '101.2', description: '101.2', num: 2 },
                ];

                const headerCreateData = {
                    description: 'Description 99',
                    lines: linesData,
                    mutableLines: mutableLinesData,
                    date: DateValue.make(2020, 3, 1),
                };
                await Test.withCommittedContext(async context => {
                    const header = await context.create(TestExternalHeaderNode, headerCreateData);

                    assert.equal(await header.description, 'Description 99');

                    // can save
                    await header.$.save();

                    assert.deepEqual(header.$.context.diagnoses, [
                        { severity: 0, path: [], message: 'Insert header code=99' },
                        {
                            message: 'Insert detail code=99.1',
                            path: ['lines', '99.1'],
                            severity: 0,
                        },
                        {
                            message: 'Insert detail code=99.2',
                            path: ['lines', '99.2'],
                            severity: 0,
                        },
                        {
                            message: 'Insert detail code=101.1',
                            path: ['mutableLines', '101.1'],
                            severity: 0,
                        },
                        {
                            message: 'Insert detail code=101.2',
                            path: ['mutableLines', '101.2'],
                            severity: 0,
                        },
                    ]);
                }, config);

                await Test.withReadonlyContext(async context => {
                    const readNode = await context.read(TestExternalHeaderNode, {
                        headerCode: '99',
                    });

                    assert.equal(await readNode.headerCode, '99');
                    assert.equal(await readNode.deferred, 99);
                    assert.equal(await readNode.date, headerCreateData.date);
                    assert.equal(await readNode.description, headerCreateData.description);
                    assert.equal(await readNode.description, headerCreateData.description);
                    const allLineData = [...linesData, ...mutableLinesData];

                    assert.equal(await readNode.lines.length, allLineData.length);
                    await readNode.lines.forEach(async (line, i) => {
                        assert.equal(allLineData[i].code, await line.code);
                        assert.equal(allLineData[i].description, await line.description);
                        assert.equal(allLineData[i].num, await line.num);
                    });

                    await readNode.mutableLines.forEach(async (line, i) => {
                        assert.equal(allLineData[i].code, await line.code);
                        assert.equal(allLineData[i].description, await line.description);
                        assert.equal(allLineData[i].num, await line.num);
                    });

                    assert.equal(await readNode.mutableLines.length, allLineData.length);

                    // payload should have both collections
                    const payload = await readNode.$.payload({
                        returnEnumsAsNumber: true,
                        convertScalarValue: val => {
                            if (DateValue.isDate(val)) return val.toString();
                            return val;
                        },
                    });

                    assert.isString(payload.date);
                    assert.equal(payload.date as unknown as string, '2020-03-01');
                    assert.equal(payload?.lines?.length, allLineData.length);

                    payload?.lines?.forEach((line, i) => {
                        assert.equal(allLineData[i].code, line.code);
                        assert.equal(allLineData[i].description, line.description);
                        assert.equal(allLineData[i].num, line.num);
                    });

                    assert.equal(payload?.mutableLines?.length, allLineData.length);

                    payload?.mutableLines?.forEach((line, i) => {
                        assert.equal(allLineData[i].code, line.code);
                        assert.equal(allLineData[i].description, line.description);
                        assert.equal(allLineData[i].num, line.num);
                    });
                }, config);
            });

            it('should be able to update, set (function) and save', () =>
                Test.withUncommittedContext(async context => {
                    const line = await context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true });
                    await line.$.set({ description: 'New One', num: 11 });
                    await line.$.save();
                    const lineAfter = await context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true });
                    assert.equal(await lineAfter.description, 'New One');
                    assert.equal(await lineAfter.num, 11);
                }, config));

            it('should be able to update, set (property) and save', () =>
                Test.withUncommittedContext(async context => {
                    const line = await context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true });
                    await line.$.set({ description: 'New One' });
                    await line.$.set({ num: 11 });
                    await line.$.save();
                    const lineAfter = await context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true });
                    assert.equal(await lineAfter.description, 'New One');
                    assert.equal(await lineAfter.num, 11);
                }, config));

            it('should be able to delete', () =>
                Test.withUncommittedContext(async context => {
                    const line = await context.read(TestExternalLineNode, { code: '1' }, { forUpdate: true });
                    await line.$.delete();
                    await assert.isRejected(
                        context.read(TestExternalLineNode, { code: '1' }),
                        'TestExternalLineNode: record not found: {"code":"1"}',
                    );
                }, config));

            it('should be able to bulk delete', () =>
                Test.withUncommittedContext(async context => {
                    const linesBefore = await context.query(TestExternalLineNode).toArray();
                    assert.equal(linesBefore.length, lineData.length);
                    const deleted = await context.deleteMany(TestExternalLineNode, { header: '1' });
                    const linesAfter = await context.query(TestExternalLineNode).toArray();

                    resetData();
                    assert.equal(linesAfter.length, lineData.length - deleted);
                }, config));

            after(() => {});
        });
    });
});
