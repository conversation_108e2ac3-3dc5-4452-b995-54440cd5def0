import { assert } from 'chai';
import { promisify } from 'util';
import { Context, Test } from '../../index';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

const { TestSignal } = fixtures.nodes;

async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}

describe('abort signal', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestSignal } }) });
        // Manually create the table.
        await initTables([{ nodeConstructor: TestSignal, data: [] }]);
    });

    it('can cancel promise context before commit', async () => {
        const delay = 200;
        const create = async (context: Context, id: number) => {
            const code = `code${id}`;
            const node = await context.create(TestSignal, {
                code,
                description: `desc ${code}`,
            });
            await node.$.save();
            // wait for a while before commit
            for (let i = 0; i < 5; i += 1) {
                await sleepMillis(delay);
            }
        };

        const ac = new AbortController();
        const { signal } = ac;

        const promises = [
            assert.isRejected(
                Test.withCommittedContext(testContext => create(testContext, 1), {
                    signal,
                }),
                'context has been aborted',
            ),
            assert.isRejected(
                Test.withCommittedContext(testContext => create(testContext, 2), {
                    signal,
                }),
                'context has been aborted',
            ),
            new Promise(resolve => {
                setTimeout(() => {
                    ac.abort(new Error('abort'));
                    resolve(undefined);
                }, 3 * delay);
            }),
        ];
        await Promise.race(promises);
        for (let i = 0; i < 5; i += 1) {
            await sleepMillis(delay);
        }
        await Test.withReadonlyContext(async testContext => {
            const results = await testContext
                .query(TestSignal)
                .map(r => r.code)
                .toArray();

            assert.strictEqual(results.length, 0);
        });
    });

    after(() => restoreTables());
});
