import { Dict, ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { testExtensionApplication, testVitalChildExtensionApplication } from '..';
import { NodeCreateData } from '../../lib';
import { Test } from '../../lib/test';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestBaseVitalChildExtension } from '../fixtures/node-extensions';
import { BaseExtensionDuplicateMutation } from '../fixtures/node-extensions/base-extension-duplicate-mutation';
import { TestExtensionReference } from '../fixtures/nodes';

describe('Invalid node extensions', () => {
    after(() => restoreTables());

    it('Cannot define a static method in more than one extension', async () => {
        const nodeNames = [
            'TestBase',
            'TestBaseJson',
            'TestExtensionReference',
            'TestBaseReference',
            'TestBaseCollectionElement',
        ];
        const nodeExtensionNames = [
            'BaseExtension1',
            'TestBaseExtension2',
            'BaseJsonExtension1',
            'TestBaseJsonExtension2',
        ];
        const nodes = nodeNames.reduce((r, k) => {
            r[k] = (fixtures.nodes as any)[k];
            return r;
        }, {} as Dict<any>);
        const nodeExtensions = {
            ...nodeExtensionNames.reduce((r, k) => {
                r[k] = (fixtures.nodeExtensions as any)[k];
                return r;
            }, {} as Dict<any>),
            BaseExtensionDuplicateMutation,
        };

        await assert.isRejected(
            createApplicationWithApi({
                nodes,
                nodeExtensions,
            }),
            'testCreate is already defined on a parent node of TestBase',
        );
    });
});

describe('Node extension', () => {
    before(async () => {
        await setup({ application: await testVitalChildExtensionApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestVitalReferenceExtensionParent, data: [] }]);
    });

    after(() => restoreTables());

    it('can add isVitalReferenceChild in extension', () =>
        Test.withUncommittedContext(async context => {
            const child = {
                code: 'CHILD',
            } as NodeCreateData<TestBaseVitalChildExtension>;

            const node = await context.create(fixtures.nodes.TestVitalReferenceExtensionParent, {
                code: 'abc',
                child,
            });
            assert.equal(await node.code, 'abc');
            assert.equal(await (await node.child).code, 'CHILD');
        }));
});

describe('Node extension', () => {
    before(async () => {
        await setup({ application: await testExtensionApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestExtensionReference, data: [] }]);
    });

    after(() => restoreTables());

    it('can extend a node', () =>
        Test.withUncommittedContext(async context => {
            const node = await context.create(fixtures.nodes.TestBase, {
                code: 'abc',
                testBaseEnum: 'value4',
            });

            assert.equal(await node.code, 'abc');
            assert.equal(await node.extensionCode1, 'abcExt1');
            assert.equal(await node.extensionCode2, 'abcExt2');
            assert.equal(await node.testBaseEnum, 'value4');
            assert.equal(await node.extensionMethod1(5), 'extensionMethod1 code=abc, extensionCode1=abcExt1, param=5');
            assert.equal(await node.extensionMethod2(8), 'extensionMethod2 code=abc, extensionCode2=abcExt2, param=8');
        }));
    it('can override property events in extension', () =>
        Test.withUncommittedContext(async context => {
            const node = await context.create(fixtures.nodes.TestBase, {
                code: 'abc',
                extensionCode1: 'abcExt1',
            });
            assert.equal(await node.code, 'abc');
            assert.equal(await node.controlled, 'GOOD2');
            await node.$.set({ controlled: 'BAD1' });
            await node.$.control();
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['controlled'],
                    message: "value must not be equal to 'BAD1'",
                },
            ]);
            await node.$.set({ controlled: 'BAD2' });
            await node.$.control();
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['controlled'],
                    message: "value must not be equal to 'BAD2'",
                },
            ]);
        }));
    it('can override node events in extension', () =>
        Test.withUncommittedContext(async context => {
            const node = await context.create(fixtures.nodes.TestBase, {
                code: 'abc',
                extensionCode1: 'abcExt1',
            });
            assert.equal(await node.code, 'abc');
            assert.equal(await node.controlled, 'GOOD2');
            await node.$.set({ controlled: 'BAD3' });
            await node.$.control();
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: [],
                    message: "value must not be equal to 'BAD3'",
                },
            ]);
        }));
    it('can override dependsOn in extension', () =>
        Test.withUncommittedContext(async context => {
            const node = await context.create(fixtures.nodes.TestBase, {
                code: 'abc',
                extensionCode1: 'abcExt1',
            });
            assert.equal(await node.code, 'abc');
            assert.equal(await node.dependsOn, 'INITIAL1');
            await node.$.set({ controlled: 'A' });
            assert.equal(await node.dependsOn, 'UPDATED2: DEP1/A');
            await node.$.set({ dependedUpon: 'DEP2' });
            assert.equal(await node.dependsOn, 'UPDATED2: DEP2/A');
        }));
    it('If value same dont update dependents', () =>
        Test.withUncommittedContext(async context => {
            const node = await context.create(fixtures.nodes.TestBase, {
                code: 'abc',
                extensionCode1: 'abcExt1',
            });
            assert.equal(await node.code, 'abc');
            assert.equal(await node.dependsOn2, 'DEP2: DEP1/GOOD2');
            await node.$.set({ controlled: 'A' });
            await node.$.set({ dependedUpon: 'DEPX' });
            assert.equal(await node.dependsOn2, 'DEP2: DEPX/A');

            await node.$.set({ controlled: 'B' });
            await node.$.set({ dependedUpon: 'DEPX' });
            assert.equal(await node.dependsOn2, 'DEP2: DEPX/A');
        }));

    it('can do a lookup on extension reference', () =>
        Test.withUncommittedContext(async context => {
            for (let i = 0; i < 10; i += 1) {
                const refNode = await context.create(TestExtensionReference, {
                    refCode: String(i),
                    refExtensionCode: String(i),
                });
                await refNode.$.save();
            }
            const lookupParameters = await context.makeLookupQueryParameters(
                fixtures.nodes.TestBase,
                'extensionReference1',
                {
                    data: { code: '5' },
                },
            );
            const candidates = (await context.lookup(lookupParameters).toArray()) as TestExtensionReference[];

            assert.equal(candidates.length, 1);
            const candidate = candidates[0];
            assert(candidate instanceof TestExtensionReference);
            assert.equal(await candidate.refCode, '5');
            assert.equal(await candidate.refExtensionCode, '5');
        }));
});
