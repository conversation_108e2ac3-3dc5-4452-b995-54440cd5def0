import { Logger } from '@sage/xtrem-log';
import { assert } from 'chai';
import { initTables, Test } from '../../index';
import { ConfigManager, createApplicationWithApi, datatypesData, restoreTables, setup } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

function unwrapError(err: Error): Error & { code?: string } {
    const innerError = (err as any).innerError;
    return innerError ? unwrapError(innerError) : err;
}

describe('Context configuration API', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestDatatypes } }) });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    after(() => restoreTables());

    it('can get deployment mode', async () => {
        Test.patchConfig({
            deploymentMode: 'production',
        });
        await Test.readonly(context => {
            assert.equal(context.configuration.deploymentMode, 'production');
        });
        Test.patchConfig({
            deploymentMode: 'development',
        });
        await Test.readonly(context => {
            assert.equal(context.configuration.deploymentMode, 'development');
        });
    });
    it('can get package configuration', async () => {
        Test.patchConfig({
            packages: {
                '@sage/package1': 'config1',
            },
        });
        await Test.readonly(context => {
            assert.equal(context.configuration.getPackageConfig('@sage/package1', 'default1'), 'config1');
            assert.equal(context.configuration.getPackageConfig('@sage/package2', 'default2'), 'default2');
        });
    });
    it('can get logger', async () => {
        Logger.reloadConfig({
            ...ConfigManager.current,
            ...{
                logs: {
                    domains: {
                        'sage/xtrem-core/context': {
                            level: 'verbose',
                        },
                    },
                },
            },
        });
        await Test.withContext(context => {
            assert.equal(context.logger.logLevel, 'verbose');
            assert.isFalse(context.logger.isActive('debug'));
            assert.isTrue(context.logger.isActive('info'));
        });
        Logger.reloadConfig(ConfigManager.current);
    });

    it('cannot run in a new writable context when calling from a source that is not "listener"', () =>
        assert.isRejected(
            Test.withReadonlyContext(context => context.runInWritableContext(() => Promise.resolve('foo'))),
            'Cannot create a writable context outside of a listener, custom mutation or web-socket. Got internal',
        ));

    it('cannot run in a new writable context when calling from a writable context', () =>
        assert.isRejected(
            Test.withCommittedContext(context => context.runInWritableContext(() => Promise.resolve('foo')), {
                source: 'listener',
            }),
            'Cannot create a writable context, from an existing writable context.',
        ));

    it('can run in a new writable context when calling from a readonly context and "listener" is the context source', () =>
        Test.withReadonlyContext(
            async context => {
                assert.isTrue(await context.runInWritableContext(innerContext => innerContext.isWritable));
            },
            { source: 'listener' },
        ));

    it('can run in a new writable context when calling from a readonly context and setting the context source to "customMutation"', () =>
        Test.withReadonlyContext(
            async context => {
                context.source = 'customMutation';
                assert.isTrue(await context.runInWritableContext(innerContext => innerContext.isWritable));
            },
            { source: 'graphql' },
        ));

    // TODO: I need more time to find out how to make this one work in async/await mode
    it.skip('cannot use a node in a context that it was not created in the sub context', () =>
        assert.isRejected(
            Test.withReadonlyContext(
                async context => {
                    const node = await context.read(TestDatatypes, { _id: 1 });
                    context
                        .runInWritableContext(() => node.$.state.context)
                        .catch(err => {
                            throw unwrapError(err);
                        });
                },
                { source: 'listener' },
            ),
            'TestDatatypes: cannot use a node in a context other than the one it was created',
        ));
});
