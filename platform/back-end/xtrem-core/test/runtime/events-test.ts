import { asyncArray } from '@sage/xtrem-async-helper';
import { ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Collection, decorators, Node, NodeDecorator, Reference, Test } from '../../index';
import { NodeExtension } from '../../lib';
import { StateStatus } from '../../lib/node-state';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

interface Event {
    key: string;
    event: string;
    path: string;
    isCascading?: boolean;
}

class Recorder {
    events = [] as Event[];

    add(parent: Node, event: string, path = '', isCascading?: boolean): void {
        const record = { key: parent.$.keyToken, event, path } as Event;
        if (isCascading != null) record.isCascading = isCascading;
        this.events.push(record);
    }
}

function testClassDecorator<T extends Node>(): NodeDecorator<T> {
    return {
        storage: 'sql',
        prepareBegin(): void {
            this.$.context.emit('record', this, 'prepareBegin');
        },
        prepareEnd(): void {
            this.$.context.emit('record', this, 'prepareEnd');
        },
        controlBegin(): void {
            this.$.context.emit('record', this, 'controlBegin');
        },
        controlEnd(): void {
            this.$.context.emit('record', this, 'controlEnd');
        },
        saveBegin(): void {
            this.$.context.emit('record', this, 'saveBegin');
        },
        saveEnd(): void {
            this.$.context.emit('record', this, 'saveEnd');
        },
        controlDelete(cx, isCascading): void {
            assert.notEqual(this.$.state.status, StateStatus.deleted);
            this.$.context.emit('record', this, 'controlDelete', cx.path.join('/'), isCascading);
        },
        deleteBegin(): void {
            assert.equal(this.$.state.status, StateStatus.updatable);
            this.$.context.emit('record', this, 'deleteBegin');
        },
        deleteEnd(): void {
            assert.equal(this.$.state.status, StateStatus.deleted);
            this.$.context.emit('record', this, 'deleteEnd');
        },
    };
}
@decorators.node<TestEventGrandParent>({
    ...testClassDecorator<TestEventGrandParent>(),
    tableName: 'TestEventGrandParent',
    canDeleteMany: true,
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
    controlDelete(cx, isCascading): void {
        this.$.context.emit('record', this, 'controlDelete', cx.path.join('/'), isCascading);
    },
    deleteEnd(): void {
        this.$.context.emit('record', this, 'deleteEnd');
    },
    async saveEnd(): Promise<void> {
        this.$.context.emit('record', this, 'saveEnd');
        await this.parents.forEach(p => p.$.set({ grandParent: this }));
    },
})
class TestEventGrandParent extends Node {
    @decorators.stringProperty<TestEventGrandParent, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
        control(): void {
            this.$.context.emit('record', this, 'control', 'name');
        },
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestEventGrandParent, 'parents'>({
        isVital: true,
        node: () => TestEventParent,
        reverseReference: 'grandParent',
        prepareBegin(): void {
            this.$.context.emit('record', this, 'prepareBegin', 'parent');
        },
        prepareEnd(): void {
            this.$.context.emit('record', this, 'prepareEnd', 'parent');
        },
        controlBegin(): void {
            this.$.context.emit('record', this, 'controlBegin', 'parent');
        },
        controlEnd(): void {
            this.$.context.emit('record', this, 'controlEnd', 'parent');
        },
        saveBegin(): Promise<void> {
            this.$.context.emit('record', this, 'saveBegin', 'parent');
            return Promise.resolve();
        },
        saveEnd(): void {
            this.$.context.emit('record', this, 'saveEnd', 'parent');
        },
    })
    readonly parents: Collection<TestEventParent>;
}

@decorators.node<TestEventParent>({
    ...testClassDecorator<TestEventParent>(),
    tableName: 'TestEventParent',
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
    isVitalCollectionChild: true,
    async controlDelete(cx, isCascading): Promise<void> {
        if ((await this.name) === 'DONOTDELETE1') {
            cx.addDiagnose(ValidationSeverity.error, 'MUST NOT BE DELETED 1');
            return;
        }
        if ((await this.name) === 'DONOTDELETE2') {
            throw new Error('MUST NOT BE DELETED 2');
        }
        assert.notEqual(this.$.state.status, StateStatus.stale);
        assert.notEqual(this.$.state.status, StateStatus.deleted);
        this.$.context.emit('record', this, 'controlDelete', cx.path.join('/'), isCascading);
    },
    async deleteEnd(): Promise<void> {
        // deleteEnd is called before delete so raising an error will prevent the deletion.
        if ((await this.name) === 'PARENT4-1') {
            throw new Error('PARENT4-1 MUST NOT BE DELETED');
        }
        assert.equal(this.$.state.status, StateStatus.updatable);
        this.$.context.emit('record', this, 'deleteEnd');
    },
    async saveEnd(): Promise<void> {
        this.$.context.emit('record', this, 'saveEnd');
        await this.children.forEach(c => c.$.set({ parent: this }));
    },
})
class TestEventParent extends Node {
    @decorators.stringProperty<TestEventParent, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
        control(): void {
            this.$.context.emit('record', this, 'control', 'name');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestEventParent, 'value'>({
        isStored: true,
        dataType: () => codeDataType,
        defaultValue(): string {
            this.$.context.emit('record', this, 'defaultValue', 'value');
            return 'AAA';
        },
        async prepare(): Promise<void> {
            this.$.context.emit('record', this, 'prepare', 'value');
            await this.$.set({ value: `${await this.value}BBB` });
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'value');
        },
    })
    readonly value: Promise<string>;

    @decorators.collectionProperty<TestEventParent, 'children'>({
        isVital: true,
        node: () => TestEventChild,
        reverseReference: 'parent',
        prepareBegin(): void {
            this.$.context.emit('record', this, 'prepareBegin', 'children');
        },
        prepareEnd(): void {
            this.$.context.emit('record', this, 'prepareEnd', 'children');
        },
        controlBegin(): void {
            this.$.context.emit('record', this, 'controlBegin', 'children');
        },
        controlEnd(): void {
            this.$.context.emit('record', this, 'controlEnd', 'children');
        },
        saveBegin(): Promise<void> {
            this.$.context.emit('record', this, 'saveBegin', 'children');
            return Promise.resolve();
        },
        saveEnd(): void {
            this.$.context.emit('record', this, 'saveEnd', 'children');
        },
    })
    readonly children: Collection<TestEventChild>;

    @decorators.referenceProperty<TestEventParent, 'ref'>({
        isStored: true,
        isNullable: true,
        node: () => TestEventReference,
        prepare(): void {
            this.$.context.emit('record', this, 'prepare', 'reference');
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'reference');
        },
    })
    readonly ref: Reference<TestEventReference>;

    @decorators.referenceProperty<TestEventParent, 'grandParent'>({
        isStored: true,
        node: () => TestEventGrandParent,
        isVitalParent: true,
    })
    readonly grandParent: Reference<TestEventGrandParent>;
}

@decorators.node<TestEventChild>({
    ...testClassDecorator<TestEventChild>(),
    tableName: 'TestEventChild',
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
    async controlDelete(cx, isCascading): Promise<void> {
        if ((await this.name) === 'DONOTDELETE3_1') {
            cx.addDiagnose(ValidationSeverity.error, 'DONOTDELETE3_1 MUST NOT BE DELETED');
        }
        this.$.context.emit('record', this, 'controlDelete', cx.path.join('/'), isCascading);
    },

    async deleteEnd(): Promise<void> {
        assert.equal(this.$.state.status, StateStatus.updatable);
        if ((await this.name) === 'FORDELETE1' || (await this.name) === 'FORDELETE2') {
            if ((await this.parent).$.state.status !== StateStatus.updatable) {
                throw new Error('Invalid parent state');
            }
        }
        this.$.context.emit('record', this, 'deleteEnd');
    },
})
class TestEventChild extends Node {
    @decorators.stringProperty<TestEventChild, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
        control(): void {
            this.$.context.emit('record', this, 'control', 'name');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestEventChild, 'value'>({
        isStored: true,
        dataType: () => codeDataType,
        defaultValue(): string {
            this.$.context.emit('record', this, 'defaultValue', 'value');
            return 'CCC';
        },
        async prepare(): Promise<void> {
            this.$.context.emit('record', this, 'prepare', 'value');
            await this.$.set({ value: `${await this.value}DDD` });
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'value');
        },
    })
    readonly value: Promise<string>;

    @decorators.referenceProperty<TestEventChild, 'parent'>({
        isStored: true,
        node: () => TestEventParent,
        isVitalParent: true,
    })
    readonly parent: Reference<TestEventParent>;
}

@decorators.node<TestEventReference>({
    ...testClassDecorator<TestEventReference>(),
    tableName: 'TestEventReference',
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
})
class TestEventReference extends Node {
    @decorators.stringProperty<TestEventReference, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
        control(): void {
            this.$.context.emit('record', this, 'control', 'name');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestEventReference, 'value'>({
        isStored: true,
        dataType: () => codeDataType,
        defaultValue(): string {
            this.$.context.emit('record', this, 'defaultValue', 'value');
            return 'EEE';
        },
        async prepare(): Promise<void> {
            this.$.context.emit('record', this, 'prepare', 'value');
            await this.$.set({ value: `${await this.value}FFF` });
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'value');
        },
    })
    readonly value: Promise<string>;
}

@decorators.nodeExtension<TestEventExtendedChild>({
    extends: () => TestEventChild,

    deleteBegin(): void {
        this.$.context.emit('record', this, 'deleteBeginExtended');
    },

    async controlDelete(cx, isCascading): Promise<void> {
        if ((await (this as TestEventChild).name).startsWith('DONOTDELETE_')) {
            cx.addDiagnose(ValidationSeverity.error, `${await (this as TestEventChild).name} MUST NOT BE DELETED`);
        }
        this.$.context.emit('record', this, 'controlDeleteExtended', cx.path.join('/'), isCascading);
    },

    deleteEnd(): void {
        this.$.context.emit('record', this, 'deleteEndExtended');
    },
})
export class TestEventExtendedChild extends NodeExtension<TestEventChild> {}

@decorators.nodeExtension<TestEventExtendedParent>({
    extends: () => TestEventParent,

    deleteBegin(): void {
        this.$.context.emit('record', this, 'deleteBeginExtended');
    },

    deleteEnd(): void {
        this.$.context.emit('record', this, 'deleteEndExtended');
    },
})
export class TestEventExtendedParent extends NodeExtension<TestEventParent> {}

describe('Events', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestEventGrandParent, TestEventParent, TestEventChild, TestEventReference },
                nodeExtensions: { TestEventExtendedChild, TestEventExtendedParent },
            }),
        });
        await initTables([
            { nodeConstructor: TestEventReference, data: [{ name: 'R1', value: 'EEE' }] },
            {
                nodeConstructor: TestEventGrandParent,
                data: [
                    { _id: 1, name: 'GRANDPARENT1' },
                    { _id: 2, name: 'GRANDPARENT2' },
                    { _id: 3, name: 'GRANDPARENT3' },
                    { _id: 4, name: 'GRANDPARENT4' },
                    { _id: 5, name: 'GRANDPARENT5' },
                ],
            },
            {
                nodeConstructor: TestEventParent,
                data: [
                    { _id: 1, name: 'FORDELETE', grandParent: 1, value: 'AAA' },
                    { _id: 2, name: 'DONOTDELETE1', grandParent: 1, value: 'AAA' },
                    { _id: 3, name: 'DONOTDELETE2', grandParent: 1, value: 'AAA' },
                    { _id: 4, name: 'DONOTDELETE3', grandParent: 2, value: 'AAA' },
                    { _id: 5, name: 'PARENT5', grandParent: 3, value: 'AAA' },
                    { _id: 6, name: 'PARENT6', grandParent: 3, value: 'AAA' },
                    { _id: 7, name: 'PARENT4-1', grandParent: 4, value: 'AAA' },
                    { _id: 8, name: 'PARENT4-2', grandParent: 4, value: 'AAA' },
                    { _id: 9, name: 'PARENT5-1', grandParent: 5, value: 'AAA' },
                    { _id: 10, name: 'PARENT5-2', grandParent: 5, value: 'AAA' },
                ],
            },
            {
                nodeConstructor: TestEventChild,
                data: [
                    { name: 'FORDELETE1', parent: 1, value: 'CCC' },
                    { name: 'FORDELETE2', parent: 1, value: 'CCC' },
                    { name: 'DONOTDELETE3_1', parent: 4, value: 'CCC' },
                    { name: 'CHILD4', parent: 5, value: 'CCC' },
                    { name: 'CHILD5', parent: 5, value: 'CCC' },
                    { name: 'CHILD6', parent: 6, value: 'CCC' },
                    { name: 'CHILD7', parent: 6, value: 'CCC' },
                    { name: 'CHILD4-1-1', parent: 7, value: 'CCC' },
                    { name: 'CHILD4-2-1', parent: 8, value: 'CCC' },
                    { name: 'CHILD5-1-1', parent: 9, value: 'CCC' },
                    { name: 'CHILD5-2-1', parent: 10, value: 'CCC' },
                ],
            },
        ]);
    });

    after(() => restoreTables());

    it('should be sent in the right order in create sequence', () =>
        Test.uncommitted(async context => {
            const recorder = new Recorder();
            context.on('record', recorder.add.bind(recorder));

            const parent = await context.create(TestEventParent, {
                name: 'P1',
                grandParent: 1,
                children: [
                    {
                        name: 'C1',
                    },
                    {
                        name: 'C2',
                    },
                ],
                ref: 1,
            });
            // Note : 'parent' has not been saved : all the '_ids' are transient (<0)
            const p1Key = '-1000000001';
            const c1Key = '-1000000002';
            const c2Key = '-1000000003';

            await parent.$.save();
            const p1SavedKey = parent.$.keyToken;
            const c1SavedKey = (await parent.children.elementAt(0)).$.keyToken;
            const c2SavedKey = (await parent.children.elementAt(1)).$.keyToken;

            assert.deepEqual(recorder.events, [
                { event: 'defaultValue', key: p1Key, path: 'value' },
                { event: 'defaultValue', key: c1Key, path: 'value' },
                { event: 'defaultValue', key: c2Key, path: 'value' },
                { event: 'prepareBegin', key: p1Key, path: '' },
                { event: 'prepare', key: p1Key, path: 'value' },
                { event: 'prepareBegin', key: p1Key, path: 'children' },

                { event: 'prepareBegin', key: c1Key, path: '' },
                { event: 'prepare', key: c1Key, path: 'value' },
                { event: 'prepareEnd', key: c1Key, path: '' },

                { event: 'prepareBegin', key: c2Key, path: '' },
                { event: 'prepare', key: c2Key, path: 'value' },
                { event: 'prepareEnd', key: c2Key, path: '' },

                { event: 'prepareEnd', key: p1Key, path: 'children' },
                { event: 'prepare', key: p1Key, path: 'reference' },
                { event: 'prepareEnd', key: p1Key, path: '' },

                { event: 'controlBegin', key: p1Key, path: '' },
                { event: 'control', key: p1Key, path: 'name' },
                { event: 'control', key: p1Key, path: 'value' },
                { event: 'controlBegin', key: p1Key, path: 'children' },

                { event: 'controlBegin', key: c1Key, path: '' },
                { event: 'control', key: c1Key, path: 'name' },
                { event: 'control', key: c1Key, path: 'value' },
                { event: 'controlEnd', key: c1Key, path: '' },

                { event: 'controlBegin', key: c2Key, path: '' },
                { event: 'control', key: c2Key, path: 'name' },
                { event: 'control', key: c2Key, path: 'value' },
                { event: 'controlEnd', key: c2Key, path: '' },

                { event: 'controlEnd', key: p1Key, path: 'children' },
                { event: 'control', key: p1Key, path: 'reference' },
                { event: 'controlEnd', key: p1Key, path: '' },

                { event: 'saveBegin', key: p1Key, path: '' },
                { event: 'saveBegin', key: p1SavedKey, path: 'children' },

                { event: 'saveBegin', key: c1Key, path: '' },
                { event: 'saveEnd', key: c1SavedKey, path: '' },

                { event: 'saveBegin', key: c2Key, path: '' },
                { event: 'saveEnd', key: c2SavedKey, path: '' },

                { event: 'saveEnd', key: p1SavedKey, path: 'children' },
                { event: 'saveEnd', key: p1SavedKey, path: '' },
            ]);
        }));
    it("should respect 'controlDelete' - from diagnoses", () =>
        Test.uncommitted(async context => {
            const recorder = new Recorder();
            context.on('record', recorder.add.bind(recorder));

            const parent = await context.read(TestEventParent, { name: 'DONOTDELETE1' }, { forUpdate: true });
            const expectedDiagnoses = [
                {
                    message: 'MUST NOT BE DELETED 1',
                    severity: ValidationSeverity.error,
                    path: [],
                },
            ];
            assert.isFalse(await parent.$.tryDelete());
            assert.equal(context.severity, ValidationSeverity.error);
            let errors = context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.deepEqual(errors, expectedDiagnoses);

            await assert.isRejected(parent.$.delete(), 'The record was not deleted.');
            errors = context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.deepEqual(errors, expectedDiagnoses);
        }));

    it("should respect 'controlDelete' - from thrown error", () =>
        Test.uncommitted(async context => {
            const recorder = new Recorder();
            context.on('record', recorder.add.bind(recorder));

            const parent = await context.read(TestEventParent, { name: 'DONOTDELETE2' }, { forUpdate: true });
            const expectedDiagnoses = [
                {
                    message: 'MUST NOT BE DELETED 2',
                    severity: ValidationSeverity.error,
                    path: [],
                },
            ];

            assert.isFalse(await parent.$.tryDelete());
            assert.equal(context.severity, ValidationSeverity.error);
            let errors = context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.deepEqual(errors, expectedDiagnoses);

            await assert.isRejected(parent.$.delete(), 'The record was not deleted.');
            errors = context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.deepEqual(errors, expectedDiagnoses);
        }));

    it("should respect 'controlDelete' in child - from thrown error", () =>
        Test.uncommitted(async context => {
            const recorder = new Recorder();
            context.on('record', recorder.add.bind(recorder));

            const grandParent = await context.read(TestEventGrandParent, { name: 'GRANDPARENT2' }, { forUpdate: true });
            const expectedDiagnoses = [
                {
                    message: 'DONOTDELETE3_1 MUST NOT BE DELETED',
                    severity: ValidationSeverity.error,
                    path: ['parents', 'children', '3'],
                },
            ];

            assert.isFalse(await grandParent.$.tryDelete());
            assert.equal(context.severity, ValidationSeverity.error);
            let errors = context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.deepEqual(errors, expectedDiagnoses);
            await assert.isRejected(grandParent.$.delete(), 'The record was not deleted.');
            errors = context.diagnoses.filter(d => d.severity === ValidationSeverity.error);
            assert.deepEqual(errors, expectedDiagnoses);
        }));

    it("delete should respect 'controlDelete' in child - from thrown error", () =>
        Test.uncommitted(async context => {
            const expectedDiagnoses = [
                {
                    message: 'DONOTDELETE3_1 MUST NOT BE DELETED',
                    severity: ValidationSeverity.error,
                    path: ['parents', 'children', '3'],
                },
            ];
            await assert.isRejected(
                context.delete(TestEventGrandParent, { name: 'GRANDPARENT2' }),
                'The record was not deleted.',
            );
            assert.deepEqual(context.diagnoses, expectedDiagnoses);
        }));

    it('deleteMany skips controls', () =>
        Test.uncommitted(async context => {
            await context.deleteMany(
                TestEventGrandParent,
                {
                    name: { _in: ['GRANDPARENT1', 'GRANDPARENT2'] },
                },
                { skipControls: true },
            );

            const grandParents = await context
                .query(TestEventGrandParent, {
                    filter: {
                        name: { _in: ['GRANDPARENT1', 'GRANDPARENT2'] },
                    },
                })
                .toArray();
            assert.equal(grandParents.length, 0);
        }));

    it("deleteMany should respect 'controlDelete' in child - from thrown error", () =>
        Test.uncommitted(async context => {
            const expectedDiagnoses = [
                {
                    message: 'MUST NOT BE DELETED 1',
                    severity: ValidationSeverity.error,
                    path: ['parents', '2'],
                },
                {
                    message: 'MUST NOT BE DELETED 2',
                    severity: ValidationSeverity.error,
                    path: ['parents', '3'],
                },
            ];

            await assert.isRejected(
                context.deleteMany(TestEventGrandParent, {
                    name: { _in: ['GRANDPARENT1', 'GRANDPARENT2'] },
                }),
                'The record was not deleted.',
            );
            assert.deepEqual(context.diagnoses, expectedDiagnoses);
        }));

    it('deleteMany should respect exception in child', () =>
        Test.uncommitted(async context => {
            await assert.isRejected(
                context.deleteMany(TestEventGrandParent, {
                    name: { _in: ['GRANDPARENT4', 'GRANDPARENT5'] },
                }),
                'PARENT4-1 MUST NOT BE DELETED',
            );
        }));

    it('are sent in the right order in delete sequence', () =>
        Test.uncommitted(async context => {
            const recorder = new Recorder();
            context.on('record', recorder.add.bind(recorder));

            const parent = await context.read(TestEventParent, { name: 'FORDELETE' }, { forUpdate: true });
            const children = await parent.children.toArray();
            const keyParent = parent.$.keyToken;
            const keyChild1 = children[0].$.keyToken;
            const keyChild2 = children[1].$.keyToken;
            await parent.$.delete();
            assert.equal(parent.$.state.status, StateStatus.stale);
            children.forEach(child => {
                assert.equal(child.$.state.status, StateStatus.stale);
            });
            assert.deepEqual(recorder.events, [
                { event: 'controlDelete', key: keyParent, path: '', isCascading: false },
                { event: 'controlDelete', key: keyChild1, path: 'children/1', isCascading: true },
                { event: 'controlDeleteExtended', key: keyChild1, path: 'children/1', isCascading: true },
                { event: 'controlDelete', key: keyChild2, path: 'children/2', isCascading: true },
                { event: 'controlDeleteExtended', key: keyChild2, path: 'children/2', isCascading: true },

                { event: 'deleteBeginExtended', key: keyParent, path: '' },
                { event: 'deleteBegin', key: keyParent, path: '' },
                { event: 'deleteBeginExtended', key: keyChild1, path: '' },
                { event: 'deleteBegin', key: keyChild1, path: '' },
                { event: 'deleteEndExtended', key: keyChild1, path: '' },
                { event: 'deleteEnd', key: keyChild1, path: '' },
                { event: 'deleteBeginExtended', key: keyChild2, path: '' },
                { event: 'deleteBegin', key: keyChild2, path: '' },
                { event: 'deleteEndExtended', key: keyChild2, path: '' },
                { event: 'deleteEnd', key: keyChild2, path: '' },
                { event: 'deleteEndExtended', key: keyParent, path: '' },
                { event: 'deleteEnd', key: keyParent, path: '' },
            ]);
        }));

    it('Should respect delete grandparent: be sent in the right order in delete sequence', () =>
        Test.uncommitted(async context => {
            const recorder = new Recorder();
            context.on('record', recorder.add.bind(recorder));

            const grandParent = await context.read(TestEventGrandParent, { name: 'GRANDPARENT3' }, { forUpdate: true });
            const parents = await grandParent.parents.toArray();
            const keyGrandParent = grandParent.$.keyToken;
            const parentKeys = parents.map(p => p.$.keyToken);
            const childKeys = await asyncArray(parents)
                .map(p => p.children.map(c => c.$.keyToken).toArray())
                .toArray();
            await grandParent.$.delete();
            assert.equal(grandParent.$.state.status, StateStatus.stale);
            parents.forEach(child => {
                assert.equal(child.$.state.status, StateStatus.stale);
            });
            assert.deepEqual(recorder.events, [
                { event: 'controlDelete', key: keyGrandParent, path: '', isCascading: false },
                {
                    event: 'controlDelete',
                    key: parentKeys[0],
                    path: `parents/${parentKeys[0]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDelete',
                    key: childKeys[0][0],
                    path: `parents/children/${childKeys[0][0]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDeleteExtended',
                    key: childKeys[0][0],
                    path: `parents/children/${childKeys[0][0]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDelete',
                    key: childKeys[0][1],
                    path: `parents/children/${childKeys[0][1]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDeleteExtended',
                    key: childKeys[0][1],
                    path: `parents/children/${childKeys[0][1]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDelete',
                    key: parentKeys[1],
                    path: `parents/${parentKeys[1]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDelete',
                    key: childKeys[1][0],
                    path: `parents/children/${childKeys[1][0]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDeleteExtended',
                    key: childKeys[1][0],
                    path: `parents/children/${childKeys[1][0]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDelete',
                    key: childKeys[1][1],
                    path: `parents/children/${childKeys[1][1]}`,
                    isCascading: true,
                },
                {
                    event: 'controlDeleteExtended',
                    key: childKeys[1][1],
                    path: `parents/children/${childKeys[1][1]}`,
                    isCascading: true,
                },

                { event: 'deleteBegin', key: keyGrandParent, path: '' },

                { event: 'deleteBeginExtended', key: parentKeys[0], path: '' },
                { event: 'deleteBegin', key: parentKeys[0], path: '' },

                { event: 'deleteBeginExtended', key: childKeys[0][0], path: '' },
                { event: 'deleteBegin', key: childKeys[0][0], path: '' },
                { event: 'deleteEndExtended', key: childKeys[0][0], path: '' },
                { event: 'deleteEnd', key: childKeys[0][0], path: '' },

                { event: 'deleteBeginExtended', key: childKeys[0][1], path: '' },
                { event: 'deleteBegin', key: childKeys[0][1], path: '' },
                { event: 'deleteEndExtended', key: childKeys[0][1], path: '' },
                { event: 'deleteEnd', key: childKeys[0][1], path: '' },

                { event: 'deleteEndExtended', key: parentKeys[0], path: '' },
                { event: 'deleteEnd', key: parentKeys[0], path: '' },

                { event: 'deleteBeginExtended', key: parentKeys[1], path: '' },
                { event: 'deleteBegin', key: parentKeys[1], path: '' },

                { event: 'deleteBeginExtended', key: childKeys[1][0], path: '' },
                { event: 'deleteBegin', key: childKeys[1][0], path: '' },
                { event: 'deleteEndExtended', key: childKeys[1][0], path: '' },
                { event: 'deleteEnd', key: childKeys[1][0], path: '' },

                { event: 'deleteBeginExtended', key: childKeys[1][1], path: '' },
                { event: 'deleteBegin', key: childKeys[1][1], path: '' },
                { event: 'deleteEndExtended', key: childKeys[1][1], path: '' },
                { event: 'deleteEnd', key: childKeys[1][1], path: '' },

                { event: 'deleteEndExtended', key: parentKeys[1], path: '' },
                { event: 'deleteEnd', key: parentKeys[1], path: '' },

                { event: 'deleteEnd', key: keyGrandParent, path: '' },
            ]);
        }));
});
