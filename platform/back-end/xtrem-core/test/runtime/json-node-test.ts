import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { decorators, Node, Reference } from '../../index';
import { integer, StringDataType, Test } from '../../lib';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestExtensionReference } from '../fixtures/nodes';

@decorators.node<TestPerson>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestPerson',
})
export class TestPerson extends Node {
    @decorators.stringProperty<TestPerson, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestPerson, 'address'>({
        isVital: true,
        isPublished: true,
        isStored: true,
        node: () => TestJsonAddress,
        isNullable: true,
    })
    readonly address: Reference<TestJsonAddress | null>;
}

@decorators.node<TestJsonAddress>({
    isPublished: true,
    storage: 'json',
    canCreate: true,
    canUpdate: true,
})
export class TestJsonAddress extends Node {
    @decorators.stringProperty<TestJsonAddress, 'streetAddress'>({
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly streetAddress: Promise<string>;

    @decorators.integerProperty<TestJsonAddress, 'postalCode'>({
        isPublished: true,
    })
    readonly postalCode: Promise<integer>;

    @decorators.stringProperty<TestJsonAddress, 'city'>({
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly city: Promise<string>;
}

@decorators.node<TestPersonMissingVital>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestPersonMissingVital',
})
export class TestPersonMissingVital extends Node {
    @decorators.referenceProperty<TestPersonMissingVital, 'address'>({
        isPublished: true,
        isStored: true,
        node: () => TestJsonAddress,
        isNullable: true,
    })
    readonly address: Reference<TestJsonAddress | null>;
}

@decorators.node<TestPersonIllegalReference>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestPersonIllegalReference',
})
export class TestPersonIllegalReference extends Node {
    @decorators.referenceProperty<TestPersonIllegalReference, 'address'>({
        isPublished: true,
        isStored: true,
        isVital: true,
        node: () => TestJsonAddressIllegalReference,
        isNullable: true,
    })
    readonly address: Reference<TestJsonAddressIllegalReference | null>;
}

@decorators.node<TestJsonAddressIllegalReference>({
    isPublished: true,
    storage: 'json',
    canCreate: true,
    canUpdate: true,
})
export class TestJsonAddressIllegalReference extends Node {
    @decorators.referenceProperty<TestJsonAddressIllegalReference, 'illegalReference'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        node: () => TestExtensionReference,
    })
    readonly illegalReference: Reference<TestExtensionReference>;
}

const data = [
    {
        name: 'Hercule',
        address: { streetAddress: '3 rue Oberkampf', postalCode: 75011, city: 'Paris' },
    },
    {
        name: 'Agatha',
        address: { streetAddress: '4 rue Voltaire', postalCode: 75011, city: 'Paris' },
    },
    {
        name: 'Tuppence',
        address: { streetAddress: '2 boulevard Soult', postalCode: 75012, city: 'Paris' },
    },
    {
        name: 'Tommy',
        address: { streetAddress: '1 Cussonia Street', postalCode: 10184, city: 'Pretoria' },
    },
];

describe('JSON nodes', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestPerson, TestJsonAddress } }) });
        await initTables([{ nodeConstructor: TestPerson, data: [] }]);
    });

    after(() => restoreTables());

    it("references to json nodes persisted as json in the table's column can be retrieved", () =>
        Test.uncommitted(async context => {
            const newRecord = await context.create(TestPerson, data[1]);
            await newRecord.$.save();
            const addressDb = await (
                await context.query(TestPerson, { filter: { name: 'Agatha' } }).toArray()
            )[0].address;
            assert.isNotNull(addressDb);
            assert.deepEqual(
                {
                    streetAddress: await addressDb.streetAddress,
                    postalCode: await addressDb.postalCode,
                    city: await addressDb.city,
                },
                data[1].address,
            );
        }));

    it('a json node cannot be queried or mutated directly', () =>
        Test.readonly(async context => {
            await assert.isRejected(
                context.read(TestJsonAddress, { _id: 1 }),
                'TestJsonAddress: cannot load: bad class storage: json',
            );

            await assert.isRejected(
                context.query(TestJsonAddress, { filter: { postalCode: 75011 } }).toArray(),
                /cannot query: bad class storage: json$/,
            );
        }));

    it('filter is fully supported on JSON references', () =>
        Test.uncommitted(async context => {
            await asyncArray(data).forEach(async record => {
                const newRecord = await context.create(TestPerson, record);
                await newRecord.$.save();
            });

            const addressLt20000 = await context
                .query(TestPerson, { filter: { address: { postalCode: { _lt: 20000 } } } })
                .map(r => r.name)
                .toArray();
            assert(addressLt20000.length === 1);
            assert.deepEqual(addressLt20000, ['Tommy']);

            const address75011 = await context
                .query(TestPerson, { filter: { address: { postalCode: 75011 } } })
                .map(r => r.name)
                .toArray();
            assert(address75011.length === 2);
            assert.deepEqual(address75011, ['Hercule', 'Agatha']);

            const addressParis = await context
                .query(TestPerson, { filter: { address: { city: 'Paris' } } })
                .map(r => r.name)
                .toArray();
            assert(addressParis.length === 3);
            assert.deepEqual(addressParis, ['Hercule', 'Agatha', 'Tuppence']);
        }));

    it('orderBy is fully supported on JSON references', () =>
        Test.uncommitted(async context => {
            await asyncArray(data).forEach(async record => {
                const newRecord = await context.create(TestPerson, record);
                await newRecord.$.save();
            });
            const addressSorted = await context
                .query(TestPerson, { orderBy: { address: { streetAddress: 1 } } })
                .map(async r => (await r.address)!.streetAddress)
                .toArray();
            assert.deepEqual(addressSorted, [
                '1 Cussonia Street',
                '2 boulevard Soult',
                '3 rue Oberkampf',
                '4 rue Voltaire',
            ]);
        }));

    it('properties of a json node can only be scalar', async () => {
        const apiNodes = {
            TestPerson,
            TestJsonAddress,
            TestPersonIllegalReference,
            TestJsonAddressIllegalReference,
            TestExtensionReference,
        };
        await assert.isRejected(
            createApplicationWithApi({ nodes: apiNodes }),
            'TestPersonIllegalReference.address: illegalReference: json nodes can only have scalar properties',
        );
    });

    it('reference properties that point to a json node must be "isVital"', async () => {
        const apiNodes = {
            TestPerson,
            TestJsonAddress,
            TestPersonMissingVital,
        };
        await assert.isRejected(
            createApplicationWithApi({ nodes: apiNodes }),
            'TestPersonMissingVital.address: A reference property to a json node must be vital',
        );
    });
});
