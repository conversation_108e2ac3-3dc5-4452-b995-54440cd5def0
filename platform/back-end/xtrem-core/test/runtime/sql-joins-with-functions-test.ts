import { assert } from 'chai';
import { datetime, decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/** DECLARE SOME TYPES */
@decorators.node<TestCustomerFn>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCustomerFn',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCustomerFn extends Node {
    @decorators.stringProperty<TestCustomerFn, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestCustomerFn, 'address'>({
        isPublished: true,
        isStored: true,
        node: () => TestAddressFn,
    })
    readonly address: Reference<TestAddressFn>;
}

@decorators.node<TestAddressFn>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestAddressFn',
    indexes: [{ orderBy: { code: 1, addressType: 1 }, isUnique: true }],
})
class TestAddressFn extends Node {
    @decorators.stringProperty<TestAddressFn, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAddressFn, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestAddressFn, 'postalCode'>({
        isPublished: true,
        isStored: true,
        node: () => TestPostalCodeFn,
    })
    readonly postalCode: Reference<TestPostalCodeFn>;

    @decorators.integerProperty<TestAddressFn, 'addressType'>({ isStored: true })
    readonly addressType: Promise<number>;

    @decorators.datetimeProperty<TestAddressFn, 'dateTime'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
    })
    readonly dateTime: Promise<datetime | null>;
}

@decorators.node<TestPostalCodeFn>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestPostalCodeFn',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestPostalCodeFn extends Node {
    @decorators.stringProperty<TestPostalCodeFn, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestPostalCodeFn, 'city'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly city: Promise<string>;
}

/** DECLARE SOME DATA */
const customerData = [
    {
        _id: 1,
        code: 'CUST1',
        address: 1,
    },
    {
        _id: 2,
        code: 'CUST2',
        address: 2,
    },
];

const addressData = [
    {
        _id: 1,
        code: 'ADDR1',
        text: 'Address #1',
        postalCode: 1,
        addressType: 1,
    },
    {
        _id: 2,
        code: 'ADDR2',
        text: 'Address #2',
        postalCode: 2,
        addressType: 2,
        dateTime: datetime.now(),
    },
    {
        _id: 3,
        code: 'ADDR3',
        text: 'Address #3',
        postalCode: 3,
        addressType: 3,
    },
];

const postalCode = [
    {
        _id: 1,
        code: '33160',
        city: 'Saint Medard en Jalles',
    },
    {
        _id: 2,
        code: '33680',
        city: 'Lacanau',
    },
    {
        _id: 3,
        code: '33700',
        city: 'Merignac',
    },
];

describe('sql joins tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestPostalCodeFn, TestAddressFn, TestCustomerFn } }),
        });
        await initTables([
            { nodeConstructor: TestPostalCodeFn, data: postalCode },
            { nodeConstructor: TestAddressFn, data: addressData },
            { nodeConstructor: TestCustomerFn, data: customerData },
        ]);
    });
    it('simple join : hit (raw value filter)', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestAddressFn, {
                    async filter() {
                        return (await this.postalCode)._id === 2;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'ADDR2');
        }));
    it('simple join : hit (object filter)', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestAddressFn, {
                    async filter() {
                        return (await (await this.postalCode).city) === 'Lacanau';
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'ADDR2');
        }));
    it('simple join : miss', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestAddressFn, {
                    async filter() {
                        return (await (await this.postalCode).city) === 'Le porg';
                    },
                })
                .toArray();
            assert.equal(result.length, 0);
        }));
    it('simple join : with direct join value', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (await this.address)._id === 2;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('compound join : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (await (await this.address).text) === 'Address #2';
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('nested joins : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (await (await (await this.address).postalCode).city) === 'Lacanau';
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('nested joins (2) : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (
                            (await this.code) === 'CUST2' &&
                            (await (await (await this.address).postalCode).city) === 'Lacanau'
                        );
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('nested joins : miss', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (
                            (await this.code) === 'CUST1' &&
                            (await (await (await this.address).postalCode).city) === 'Lacanau'
                        );
                    },
                })
                .toArray();
            assert.equal(result.length, 0);
        }));
    it('nested joins (2) : miss', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (await (await (await this.address).postalCode).city) === 'Le porge';
                    },
                })
                .toArray();
            assert.equal(result.length, 0);
        }));
    it('implicit and : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return [0, 2, 3].includes((await (await this.address).postalCode)._id);
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('Can set a valid reference', () =>
        Test.uncommitted(async context => {
            const address = await context.read(TestAddressFn, { code: 'ADDR2', addressType: 2 });
            const customer = await context.read(TestCustomerFn, { code: 'CUST1' }, { forUpdate: true });
            await customer.$.set({ address });
            assert.strictEqual(await (await customer.address).code, 'ADDR2');
        }));
    it('Join test on null date : _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (await (await this.address).dateTime) == null;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST1');
        }));
    it('Join test on null date : _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        return (await (await this.address).dateTime) != null;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    // Now, interesting things that we could not do with filter objects
    it('RegExp and string template', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerFn, {
                    async filter() {
                        // eslint-disable-next-line @sage/redos/no-vulnerable
                        return /AD.*2-33680/.test(
                            `${await (await this.address).code}-${await (await (await this.address).postalCode).code}`,
                        );
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    after(() => restoreTables());
});
