import { assert } from 'chai';
import { decorators, Node, Test } from '../../index';
import { ValidationSeverity } from '../../lib';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestSqlNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
})
export class TestSqlNode extends Node {
    @decorators.stringProperty<TestSqlNode, 'field'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly field: Promise<string>;
}

@decorators.node<TestJsonNode>({
    isPublished: true,
    storage: 'json',
    canCreate: true,
    canUpdate: true,
})
export class TestJsonNode extends Node {
    @decorators.stringProperty<TestJsonNode, 'field'>({
        isPublished: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly field: Promise<string>;
}

describe('Transaction scope', () => {
    before(async () =>
        setup({ application: await createApplicationWithApi({ nodes: { TestSqlNode, TestJsonNode } }) }),
    );
    beforeEach(() => initTables([{ nodeConstructor: TestSqlNode, data: [{ _id: 1, field: 'REF1' }] }]));
    it('query and read work on sql nodes', () =>
        Test.readonly(async context => {
            const document = await context.read(TestSqlNode, { _id: 1 });
            assert.deepEqual(await document.$.payload(), {
                field: 'REF1',
                _customData: {},
            });
            const queryResult = await context.query(TestSqlNode, { filter: { field: 'REF1' } }).toArray();
            assert.deepEqual(await queryResult[0].field, 'REF1');
        }));

    it('create works both on sql and json nodes', () =>
        Test.uncommitted(async context => {
            const newSqlInstance = await context.create(TestSqlNode, { field: 'REF2' });
            assert.deepEqual(await newSqlInstance.field, 'REF2');

            const newJsonInstance = await context.create(TestJsonNode, { field: 'REF2' });
            assert.deepEqual(await newJsonInstance.field, 'REF2');
        }));

    it('save works on sql nodes', () =>
        Test.uncommitted(async context => {
            const newSqlInstance = await context.create(TestSqlNode, { field: 'REF2' });
            await newSqlInstance.$.save();
            assert.deepEqual(await newSqlInstance.field, 'REF2');
        }));

    it('save throws if validation fails', () =>
        Test.uncommitted(async context => {
            const newSqlInstance = await context.create(TestSqlNode, {});
            await assert.isRejected(newSqlInstance.$.save(), /was not created/);
        }));

    it('trySave returns false if validation fails', () =>
        Test.uncommitted(async context => {
            const newSqlInstance = await context.create(TestSqlNode, {});

            let ok = await newSqlInstance.$.trySave();
            assert.isFalse(ok);
            assert.deepEqual(context.diagnoses, [
                { message: 'string cannot be empty', path: ['field'], severity: ValidationSeverity.error },
            ]);

            await newSqlInstance.$.set({ field: 'REF2' });
            ok = await newSqlInstance.$.trySave();
            assert.isTrue(ok);
            assert.deepEqual(await newSqlInstance.field, 'REF2');
        }));

    it('save fails on json nodes', () =>
        Test.uncommitted(async context => {
            const newJsonInstance = await context.create(TestJsonNode, { field: 'REF2' });
            let thrown = false;
            try {
                await newJsonInstance.$.save();
            } catch (e) {
                thrown = true;
                assert(e.message, 'Save TestJsonNode is a forbidden operation : storage is not sql.');
            }
            assert(thrown);
        }));

    after(() => restoreTables());
});
