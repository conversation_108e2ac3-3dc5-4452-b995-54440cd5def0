import { assert } from 'chai';
import { nanoid } from 'nanoid';
import { decorators, Node, Test } from '../../index';
import * as fixtures from '../fixtures';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestInsertWithRetry>({
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [
        { orderBy: { setupId: 1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { code1: 1 }, isUnique: true },
    ],
    isSetupNode: true,
})
export class TestInsertWithRetry extends Node {
    @decorators.stringProperty<TestInsertWithRetry, 'setupId'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        isFrozen: true,
        defaultValue() {
            return nanoid();
        },
    })
    readonly setupId: Promise<string>;

    @decorators.stringProperty<TestInsertWithRetry, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestInsertWithRetry, 'code1'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code1: Promise<string>;
}

describe('upsert with retry tests', () => {
    before(async () => setup({ application: await createApplicationWithApi({ nodes: { TestInsertWithRetry } }) }));

    /** Reset tables before each to start autoIncrement over for each test */
    beforeEach(() => fixtures.initTables([{ nodeConstructor: TestInsertWithRetry, data: [] }]));

    afterEach(() => restoreTables());

    it('upsert from natural key', () =>
        Test.withContext(async context => {
            await (
                await context.create(TestInsertWithRetry, {
                    setupId: 'A',
                    code1: 'A',
                    description: 'desc 1',
                })
            ).$.save();

            const node2 = await context.create(TestInsertWithRetry, {
                setupId: 'A',
                code1: 'B',
                description: 'desc 2',
            });

            // This upsert should pass as there is no conflict on 2nd unique index (on code1)
            await node2.$.save({ useUpsert: true });

            assert.deepEqual(await node2.$.payload({ withIds: true }), {
                _id: 1,
                _sourceId: '',
                _vendor: null,
                setupId: 'A',
                code1: 'B',
                description: 'desc 2',
                _customData: {},
            });

            assert.deepEqual((await context.query(TestInsertWithRetry).toArray()).length, 1);
        }));

    it('fails when conflict on second unique index', () =>
        Test.withContext(async context => {
            await (
                await context.create(TestInsertWithRetry, {
                    setupId: 'A',
                    code1: 'A',
                    description: 'desc 1',
                })
            ).$.save();

            await (
                await context.create(TestInsertWithRetry, {
                    setupId: 'B',
                    code1: 'B',
                    description: 'desc 2',
                })
            ).$.save();

            const node2 = await context.create(TestInsertWithRetry, {
                setupId: 'A',
                code1: 'B',
                description: 'desc 3',
            });
            // This upsert should have failed because on a conflict on 2nd unique index (on code1)
            try {
                await node2.$.save({ useUpsert: true });
                throw new Error('Upsert should have failed');
            } catch {
                // OK : upsert failed
            }

            assert.deepEqual((await context.query(TestInsertWithRetry).toArray()).length, 2);
        }));
});
