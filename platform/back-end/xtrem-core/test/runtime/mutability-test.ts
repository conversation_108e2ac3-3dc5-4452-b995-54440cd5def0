import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testBasicDocumentApplication } from '..';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import {
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    restoreTables,
    setup,
} from '../fixtures/index';

const { TestReferring, TestReferred, TestDocument, TestDocumentLine } = fixtures.nodes;

async function createTables(): Promise<void> {
    await setup({ application: await testBasicDocumentApplication.application });
    await initTables([
        { nodeConstructor: TestReferred, data: referredData },
        { nodeConstructor: TestReferring, data: referringData },
        { nodeConstructor: TestDocument, data: documentData },
        { nodeConstructor: TestDocumentLine, data: documentLineData },
    ]);
}

describe('Readonly context', () => {
    before(() => createTables());
    it('should allow read', () =>
        Test.readonly(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
        }));
    it('should allow traversal and load of references', () =>
        Test.readonly(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
            const reference = await referring.reference;
            assert.instanceOf(reference, TestReferred);
            assert.equal(await reference.code, referredData[0].code);
        }));
    it('should allow traversal and load of collection', () =>
        Test.readonly(async context => {
            const document = await context.read(TestDocument, { code: documentData[0].code });
            assert.instanceOf(document, TestDocument);
            assert.equal(await document.code, documentData[0].code);
            const lines = await document.lines.toArray();
            const lineData = documentLineData.filter(line => line.document === document._id);
            await asyncArray(lines).forEach(async (line, j) => {
                assert.instanceOf(line, TestDocumentLine);
                assert.equal(await line.lineNumber, lineData[j].lineNumber);
                assert.equal(await line.description, lineData[j].description);
            });
        }));
    it('should throw on creating a node', () =>
        Test.readonly(async context => {
            await assert.isRejected(
                context.create(TestReferring, {}),
                'TestReferring: cannot create: context is readonly',
            );
        }));
    it('should throw on reading for update', () =>
        Test.readonly(async context => {
            await assert.isRejected(
                context.read(TestReferring, { code: referringData[0].code }, { forUpdate: true }),
                /TestReferring: cannot read for update: context is readonly/,
            );
        }));
    it("should throw because the key doesn't match the defined table index", () =>
        Test.readonly(async context => {
            await assert.isRejected(
                context.read(TestReferring, { description: referringData[0].description }),
                "Keys don't match any unique index",
            );
        }));
    it("tryRead - should throw because the key doesn't match the defined table index", () =>
        Test.readonly(async context => {
            await assert.isRejected(
                context.tryRead(TestReferring, {
                    description: referringData[0].description,
                }),
                "Keys don't match any unique index",
            );
        }));
    after(() => restoreTables());
});

describe('Writable context', () => {
    before(() => createTables());
    it('should allow read', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
        }));
    it('should allow traversal and load of references', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
            const reference = await referring.reference;
            assert.instanceOf(reference, TestReferred);
            assert.equal(await reference.code, referredData[0].code);
        }));
    it('should allow traversal and load of collection', () =>
        Test.uncommitted(async context => {
            const document = await context.read(TestDocument, { code: documentData[0].code });
            assert.instanceOf(document, TestDocument);
            assert.equal(await document.code, documentData[0].code);
            const lines = await document.lines.toArray();
            const lineData = documentLineData.filter(line => line.document === document._id);
            await asyncArray(lines).forEach(async (line, j) => {
                assert.instanceOf(line, TestDocumentLine);
                assert.equal(await line.lineNumber, lineData[j].lineNumber);
                assert.equal(await line.description, lineData[j].description);
            });
        }));
    it('should allow read for update', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code }, { forUpdate: true });
            assert.instanceOf(referring, TestReferring);
        }));
    it('should allow read with a matching table index', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
        }));
    it("should throw because the key doesn't match the defined table index", () =>
        Test.uncommitted(async context => {
            await assert.isRejected(
                context.read(TestReferring, { description: referringData[0].description }),
                "Keys don't match any unique index",
            );
        }));
    it("tryRead- should throw because the key doesn't match the defined table index", () =>
        Test.uncommitted(async context => {
            await assert.isRejected(
                context.tryRead(TestReferring, {
                    description: referringData[0].description,
                }),
                "Keys don't match any unique index",
            );
        }));
    after(() => restoreTables());
});

describe('Node in writable context', () => {
    before(() => createTables());
    it('should be readonly after read', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            await assert.isRejected(referring.$.set({ description: 'abc' }), 'TestReferring: node is readonly');
        }));
    it('should be readonly after reference traversal', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            const reference = await referring.reference;
            assert.instanceOf(reference, TestReferred);
            await assert.isRejected(reference.$.set({ details: 'def' }), 'TestReferred: node is readonly');
        }));
    it('should be readonly in collection', () =>
        Test.uncommitted(async context => {
            const document = await context.read(TestDocument, { code: documentData[0].code });
            const line = await document.lines.elementAt(0);
            await assert.isRejected(line.$.set({ description: 'ghi' }), 'TestDocumentLine: node is readonly');
        }));
});
describe('Writable node', () => {
    before(() => createTables());
    it('should be different from original readonly node', () =>
        Test.uncommitted(async context => {
            const document = await context.read(TestDocument, { code: documentData[0].code });
            const writable = await context.read(TestDocument, { code: documentData[0].code }, { forUpdate: true });
            assert.notEqual(writable, document);
            await writable.$.set({ description: 'abc' });
            assert.equal(await writable.description, 'abc');
            assert.equal(await document.description, documentData[0].description);
        }));
    it('should be unique', () =>
        Test.uncommitted(async context => {
            const writable1 = await context.read(TestDocument, { code: documentData[0].code }, { forUpdate: true });
            const writable2 = await context.read(TestDocument, { code: documentData[0].code }, { forUpdate: true });
            assert.equal(writable1, writable2);
        }));
    after(() => restoreTables());
});
