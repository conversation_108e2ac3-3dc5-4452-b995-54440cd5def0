/* eslint-disable @typescript-eslint/no-unused-vars */
import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import {
    createApplicationWithApi,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    referredData,
    referringData,
    referringWithJoinData,
} from '../fixtures/index';
import { TestMandatoryReferring } from '../fixtures/nodes';

const {
    TestReferring,
    TestReferringWithJoin,
    TestReferred,
    TestNodeWithReferenceArrays,
    TestReferenceForReferenceArrays,
} = fixtures.nodes;

let graphqlHelper: GraphQlHelper;

describe('reference tests', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: {
                    TestReferring,
                    TestReferred,
                    TestMandatoryReferring,
                    TestReferringWithJoin,
                    TestNodeWithReferenceArrays,
                    TestReferenceForReferenceArrays,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestReferred, data: referredData },
            { nodeConstructor: TestReferring, data: referringData },
            { nodeConstructor: TestReferringWithJoin, data: referringWithJoinData },
            { nodeConstructor: TestNodeWithReferenceArrays, data: [] },
            { nodeConstructor: TestReferenceForReferenceArrays, data: [] },
        ]);
    });
    it('read parent and reference', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
            const reference = (await referring.reference)!;
            assert.instanceOf(reference, TestReferred);
            assert.equal(await reference.code, referredData[0].code);
            assert.equal(await reference.details, referredData[0].details);
        }));

    it('read parent and reference array', () =>
        Test.uncommitted(async context => {
            const referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.instanceOf(referring, TestReferring);
            assert.equal(await referring.code, referringData[0].code);
            const referenceArray = (await referring.referenceArray)!;
            assert.equal(referenceArray.length, 2);
            await asyncArray(referenceArray).forEach(async (reference, i) => {
                assert.instanceOf(reference, TestReferred);
                assert.equal(await reference.code, referredData[0].code);
                assert.equal(await reference.details, referredData[0].details);
            });
        }));

    it('read invalid parent', () =>
        Test.uncommitted(async context => {
            let referring = await context.tryRead(TestReferring, { code: `${referringData[0].code}BAD` });
            assert.isNull(referring, 'tryRead returns null with invalid key');
            await assert.isRejected(
                context.read(TestReferring, { code: `${referringData[0].code}BAD` }),
                /record not found/,
            );
            referring = await context.read(TestReferring, { code: referringData[0].code });
            assert.ok(referring != null, 'valid key ok');
        }));
    it('fails create if mandatory reference code is empty', () =>
        Test.uncommitted(async context => {
            const referring = await context.create(TestMandatoryReferring, {
                code: 'MAND1',
            });

            await assert.isRejected(referring.$.save(), /was not created/);

            assert.deepEqual(referring?.$.context.diagnoses[0], {
                message: 'TestMandatoryReferring.reference: property is required',
                path: ['reference'],
                severity: 4,
            });
        }));
    it('fails create if mandatory reference code does not exist', () =>
        Test.uncommitted(async context => {
            await assert.isRejected(
                context.create(TestMandatoryReferring, {
                    code: 'MAND1',
                    reference: {
                        code: 'BADREF',
                    },
                }),
                'TestReferred: record not found: {"code":"BADREF"}',
            );
        }));
    it('can re-assign a reference', () =>
        Test.uncommitted(async context => {
            const referring = await context.create(TestMandatoryReferring, {
                code: 'MAND1',
                reference: {
                    code: referredData[0].code,
                },
            });
            assert.ok(await referring.$.control());
            await referring.$.set({
                reference: await context.read(TestReferred, { code: referredData[1].code }),
            });
            assert.ok(await referring.$.control());
            // need any cast because we are violating type checks here.
            await referring.$.set({ reference: null as any });
            await assert.isRejected(referring.$.save(), /was not created/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 4,
                    path: ['reference'],
                    message: 'TestMandatoryReferring.reference: property is required',
                },
            ]);
        }));

    it('can save a reference', () =>
        Test.uncommitted(async context => {
            const initialData = {
                code: 'RING1',
                reference: {
                    _id: referredData[1]._id,
                },
            };
            let referring = await context.create(TestReferring, initialData);
            await referring.$.save();
            const newId = referring._id;

            const checkResult = async (code: string, data: any) => {
                const node = await context.read(TestReferring, { code });
                assert.deepEqual(
                    await node.$.payload({ omitDefaultValues: true, withIds: true, withoutCustomData: true }),
                    data,
                );
            };
            await checkResult('RING1', { _id: newId, ...initialData });

            // reset reference, save and check again
            referring = await context.read(TestReferring, { code: 'RING1' }, { forUpdate: true });
            await referring.$.set({ reference: null });
            await referring.$.save();

            await checkResult('RING1', { _id: newId, code: 'RING1' });
        }));

    it('can save a reference array', async () => {
        const testArraySave = (referenceArray: number[] | { _id: number }[]) =>
            Test.uncommitted(async context => {
                const initialData = {
                    code: 'RING1',
                    referenceArray,
                };
                let referring = await context.create(TestReferring, initialData);
                await referring.$.save();
                const newId = referring._id;

                const checkResult = async (code: string, data: any) => {
                    const node = await context.read(TestReferring, { code });
                    assert.deepEqual(
                        await node.$.payload({ omitDefaultValues: true, withIds: true, withoutCustomData: true }),
                        data,
                    );
                };
                await checkResult('RING1', {
                    _id: newId,
                    ...initialData,
                    referenceArray:
                        typeof referenceArray[0] === 'number'
                            ? (referenceArray as number[]).map(r => ({
                                  _id: r,
                              }))
                            : referenceArray,
                });

                // reset reference, save and check again
                referring = await context.read(TestReferring, { code: 'RING1' }, { forUpdate: true });
                await referring.$.set({ referenceArray: null });
                await referring.$.save();
                await checkResult('RING1', { _id: newId, code: 'RING1' });
            });

        await testArraySave([2, 2]);
        await testArraySave([{ _id: 2 }, { _id: 2 }]);
    });

    it('cannot save a reference array where reference record does not exists=', () =>
        Test.uncommitted(async context => {
            const initialData = {
                code: 'RING1',
                referenceArray: [{ _id: -1 }],
            };
            await assert.isRejected(
                context.create(TestReferring, initialData),
                'TestReferring.referenceArray: invalid value supplied. Record in array does not exist',
            );
        }));

    it('can filter with a reference', () =>
        Test.uncommitted(async context => {
            const referred = await context.read(TestReferred, { code: referredData[0].code });
            const referrings = await context
                .query(TestReferring, {
                    filter: {
                        _and: [
                            // both forms should work
                            { reference: referred },
                            { reference: referred._id },
                        ],
                    },
                })
                .toArray();
            assert.equal(referrings.length, 2);
            assert.equal(await referrings[0].reference, referred);
            assert.equal(await referrings[1].reference, referred);
        }));

    it('can filter with a reference array', () =>
        Test.uncommitted(async context => {
            const referred = await context.read(TestReferred, { code: referredData[0].code });
            const referrings = await context
                .query(TestReferring, {
                    filter: {
                        _and: [
                            // both forms should work
                            { referenceArray: [`${referred._id}`, `${referred._id}`] },
                            { referenceArray: [referred._id, referred._id] },
                            { referenceArray: [referred, referred] },
                        ],
                    },
                })
                .toArray();
            assert.equal(referrings.length, 2);
            assert.equal(await referrings[0].reference, referred);
            assert.equal(await referrings[1].reference, referred);
        }));

    it('can filter with a reference array with _contains', () =>
        Test.uncommitted(async context => {
            const referred1 = await context.read(TestReferred, { code: referredData[0].code });
            const referred2 = await context.read(TestReferred, { code: referredData[1].code });
            const referrings = await context
                .query(TestReferring, {
                    filter: { referenceArray: { _contains: referred1._id } },
                })
                .toArray();
            assert.equal(referrings.length, 3);

            assert.equal(JSON.stringify(await referrings[0].referenceArray), JSON.stringify([referred1, referred1]));
            assert.equal(JSON.stringify(await referrings[1].referenceArray), JSON.stringify([referred1, referred2]));
            assert.equal(JSON.stringify(await referrings[2].referenceArray), JSON.stringify([referred1, referred1]));
        }));

    it('can save a reference using value "_id:xxx"', async () => {
        // Create with REF2 as reference
        const createResult = await graphqlHelper.mutation<{
            testReferring: { create: { code: string; reference: { code: string } } };
        }>(
            `{ testReferring { create(data: {
                    code: "RING2",
                    reference: "_id:${referredData[1]._id}"
                })  { code, reference {code}  } } }`,
        );

        assert.deepEqual(createResult, {
            testReferring: {
                create: {
                    code: 'RING2',
                    reference: {
                        code: 'REF2',
                    },
                },
            },
        });

        const updateResult = await graphqlHelper.mutation<{
            testReferring: { updateById: { code: string; description: string; reference: { code: string } } };
        }>(
            `{ testReferring { updateById(_id:"3", data: {
                    code: "RING3",
                    description : "Ring reference",
                    reference: "_id:${referredData[0]._id}"
                })  { code, description,reference {code}  } } }`,
        );

        assert.deepEqual(updateResult, {
            testReferring: {
                updateById: {
                    code: 'RING3',
                    description: 'Ring reference',
                    reference: {
                        code: 'REF1',
                    },
                },
            },
        });
    });

    it('can save a reference using value {_id:"xxx"}', () =>
        Test.withContext(async context => {
            const createData = {
                code: 'OBJREF1',
                reference: { _id: `${referredData[1]._id}` },
            } as any;
            const createResult = await context.create(TestReferring, createData);
            await createResult.$.save();

            assert.deepEqual(await (await createResult.reference)?.code, 'REF2');
        }));

    [
        {
            filter: {},
            expected: [
                {
                    code: 'PAR1',
                    referenceCode: 'REF1',
                    reference: { code: 'REF1' },
                    propertyJoinReference: { code: 'REF1' },
                    joinLiteralReference: { code: 'REF1' },
                },
                {
                    code: 'PAR2',
                    referenceCode: '',
                    reference: null,
                    propertyJoinReference: null,
                    joinLiteralReference: { code: 'REF1' },
                },
            ],
        },
        {
            filter: { reference: { code: 'REF1' } },
            expected: [
                {
                    code: 'PAR1',
                    referenceCode: 'REF1',
                    reference: { code: 'REF1' },
                    propertyJoinReference: { code: 'REF1' },
                    joinLiteralReference: { code: 'REF1' },
                },
            ],
        },
        {
            filter: { propertyJoinReference: { code: 'REF1' } },
            expected: [
                {
                    code: 'PAR1',
                    referenceCode: 'REF1',
                    reference: { code: 'REF1' },
                    propertyJoinReference: { code: 'REF1' },
                    joinLiteralReference: { code: 'REF1' },
                },
            ],
        },
        {
            filter: { joinLiteralReference: { code: 'REF1' } },
            expected: [
                {
                    code: 'PAR1',
                    referenceCode: 'REF1',
                    reference: { code: 'REF1' },
                    propertyJoinReference: { code: 'REF1' },
                    joinLiteralReference: { code: 'REF1' },
                },
                {
                    code: 'PAR2',
                    referenceCode: '',
                    reference: null,
                    propertyJoinReference: null,
                    joinLiteralReference: { code: 'REF1' },
                },
            ],
        },
    ].forEach(({ filter, expected }) =>
        it(`can read reference with join (filter = ${JSON.stringify(filter)})`, () =>
            Test.withContext(async context => {
                const data = await context.select(
                    TestReferringWithJoin,
                    {
                        code: true,
                        referenceCode: true,
                        reference: { code: true },
                        propertyJoinReference: { code: true },
                        joinLiteralReference: { code: true },
                    },
                    { filter },
                );
                assert.deepEqual(data, expected);
            })),
    );

    it('test onDelete behavoir on referenceArrays', async () => {
        const parentCodes = ['parent1', 'parent2', 'parent3', 'parent4', 'parent5', 'parent6', 'parent7', 'parent8'];
        const { parentIds, ref1Id, ref2Id } = await Test.withCommittedContext(async context => {
            const ref1 = await context.create(TestReferenceForReferenceArrays, { code: 'forRemove' });
            await ref1.$.save();

            const ref2 = await context.create(TestReferenceForReferenceArrays, { code: 'forRestrict' });
            await ref2.$.save();

            const ids = await asyncArray(parentCodes)
                .map(async code => {
                    const parent = await context.create(TestNodeWithReferenceArrays, {
                        code,
                        refsWithRemove: [ref1],
                        refsWithRestrict: [ref2],
                    });
                    await parent.$.save();
                    return parent._id;
                })
                .toArray();

            return { parentIds: ids, ref1Id: ref1._id, ref2Id: ref2._id };
        });

        // Try to delete ref1 and check that parent.refsWithRemove was updated
        await Test.withCommittedContext(async context => {
            await context.delete(TestReferenceForReferenceArrays, { _id: ref1Id });
            // Check that all the parents were updated by the trigger
            await asyncArray(parentIds).forEach(async parentId => {
                const parent = await context.read(TestNodeWithReferenceArrays, { _id: parentId });
                assert.deepEqual((await parent.refsWithRemove).length, 0, `parent ${parentId} was not updated`);
            });
        });

        // Try to delete ref2 and check that parent.refsWithRemove was updated
        await Test.withCommittedContext(async context => {
            try {
                await context.delete(TestReferenceForReferenceArrays, { _id: ref2Id });
            } catch (e) {
                assert.equal(
                    e.message,
                    // Note: only the 5 first referencing _ids will be in the exception message
                    `Error: test_node_with_reference_arrays.refs_with_restrict({${parentIds.slice(0, 5)}}) are referencing test_reference_for_reference_arrays(${ref2Id})`,
                );
            }
        });
    });

    after(() => {
        // restoreTables();
    });
});
