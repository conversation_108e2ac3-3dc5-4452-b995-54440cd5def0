import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testSubclassingApplication } from '..';
import { Context, NodeCreateData, Test } from '../../index';
import { initTables, restoreTables, setup, TestInitData } from '../fixtures/index';
import {
    TestAnimal,
    TestBird,
    TestCat,
    TestDog,
    TestFish,
    TestFlyBehavior,
    TestMammal,
    TestPetOwner,
    TestRefPropNoDataType,
    TestRefPropWithDataType,
    TestSleepBehavior,
} from '../fixtures/nodes';

const dataFishes: TestInitData<TestFish>[] = [];
for (let i = 0; i < 5; i += 1) {
    dataFishes.push({
        strFromAnimal: `animal/mammal/fish/${i}`,
        strFromFish: `animal/mammal/fish/${i}`,
        flyBehavior: i === 0 ? 2 : null,
        sleepBehavior: 4,
    });
}

const dataDogs: TestInitData<TestDog>[] = [];
for (let i = 0; i < 5; i += 1) {
    dataDogs.push({
        strFromAnimal: `animal/mammal/dog/${i}`,
        strFromMammal: `animal/mammal/dog/${i}`,
        strFromDog: `animal/mammal/dog/${i}`,
        flyBehavior: i === 0 ? 2 : null,
        sleepBehavior: 1,
    });
}

const dataCats: TestInitData<TestCat>[] = [];
for (let i = 0; i < 5; i += 1) {
    dataCats.push({
        strFromAnimal: `animal/mammal/cat/${i}`,
        strFromMammal: `animal/mammal/cat/${i}`,
        strFromCat: `animal/mammal/cat/${i}`,
        flyBehavior: i === 0 ? 2 : null,
        sleepBehavior: 2,
    });
}

const dataBirds: TestInitData<TestBird>[] = [];
for (let i = 0; i < 5; i += 1) {
    dataBirds.push({
        strFromAnimal: `animal/mammal/dog/${i}`,
        strFromBird: `animal/mammal/dog/${i}`,
        flyBehavior: i === 0 ? 1 : null,
        sleepBehavior: 3,
    });
}

const dataOwners = [
    {
        _id: 1,
        favoritePet: () => dataDogs[0]._id,
        pets: [dataDogs[0], dataCats[0], dataFishes[0]],
    },
    {
        _id: 2,
        favoritePet: () => dataCats[1]._id,
        pets: [dataDogs[1], dataCats[1], dataFishes[1]],
    },
    {
        _id: 3,
        favoritePet: () => dataFishes[2]._id,
        pets: [dataDogs[2], dataCats[2], dataFishes[2]],
    },
    {
        _id: 4,
        favoritePet: () => dataDogs[3]._id,
        pets: [dataDogs[3], dataCats[3], dataFishes[3]],
    },
];

const dataAnimalFlyBehavior: TestInitData<TestFlyBehavior>[] = [
    {
        _id: 1,
        flyBehavior: 'flyWithWings',
    },
    {
        _id: 2,
        flyBehavior: 'cannotFly',
    },
];

const dataAnimalSleepBehavior: TestInitData<TestSleepBehavior>[] = [
    {
        _id: 1,
        behavior: 'sleepOnTheGround',
    },
    {
        _id: 2,
        behavior: 'sleepInATree',
    },
    {
        _id: 3,
        behavior: 'sleepInTheAir',
    },
    {
        _id: 4,
        behavior: 'neverSleep',
    },
];

const dataTestRefPropWithDataType: TestInitData<TestRefPropWithDataType>[] = [
    {
        _id: 1,
        id: 1,
    },
    {
        _id: 2,
        id: 2,
    },
];

const dataTestRefPropNoDataType: TestInitData<TestRefPropNoDataType>[] = [
    {
        _id: 1,
        id: 1,
    },
    {
        _id: 2,
        id: 2,
    },
];

function sortBy<T>(propertyName: string) {
    return (data1: T, data2: T) => {
        const val1 = (data1 as any)[propertyName];
        const val2 = (data2 as any)[propertyName];
        if (val1 === val2) return 0;
        if (val1 == null) return +1;
        if (val2 == null) return -1;
        return val1 > val2 ? +1 : -1;
    };
}

describe('Subclasses', () => {
    before(async () => {
        await setup({ application: await testSubclassingApplication.application });
        await initTables([
            { nodeConstructor: TestFlyBehavior, data: dataAnimalFlyBehavior },
            { nodeConstructor: TestSleepBehavior, data: dataAnimalSleepBehavior },
            { nodeConstructor: TestFish, data: dataFishes },
            { nodeConstructor: TestDog, data: dataDogs },
            { nodeConstructor: TestCat, data: dataCats },
            { nodeConstructor: TestBird, data: dataBirds },
            { nodeConstructor: TestPetOwner, data: dataOwners },
            { nodeConstructor: TestRefPropWithDataType, data: dataTestRefPropWithDataType },
            { nodeConstructor: TestRefPropNoDataType, data: dataTestRefPropNoDataType },
        ]);
        // Note: initTables has enhanced data with the autoIds set by the database engine
        await Test.committed(context =>
            asyncArray(dataOwners).forEach(dataOwner =>
                asyncArray(dataOwner.pets)?.forEach(async pet => {
                    const animal = await context.read(TestAnimal, { _id: pet._id as number }, { forUpdate: true });
                    await animal.$.set({ owner: dataOwner._id as any });
                    await animal.$.save();
                }),
            ),
        );
    });

    describe('References and collections', () => {
        it('Check references', () =>
            Test.readonly(context =>
                asyncArray(dataOwners).forEach(async dataOwner => {
                    const owner = await context.read(TestPetOwner, { _id: dataOwner._id });
                    const favoritePet = await owner.favoritePet;
                    assert.isNotNull(favoritePet);
                    assert.equal(favoritePet?._id, dataOwner.favoritePet as any as number);
                }),
            ));
        it('Check lazy-loading of references', () =>
            Test.readonly(context =>
                asyncArray(dataOwners).forEach(async dataOwner => {
                    const owner = await context.read(TestPetOwner, { _id: dataOwner._id });
                    const favoritePet = await owner.favoritePet;
                    assert.isNotNull(favoritePet);
                    assert.equal(
                        await favoritePet?.strFromAnimal,
                        [...dataDogs, ...dataCats, ...dataFishes].find(animal => animal._id === favoritePet?._id)
                            ?.strFromAnimal,
                    );
                }),
            ));
        it('Check collections', () =>
            Test.readonly(context =>
                asyncArray(dataOwners).forEach(async dataOwner => {
                    const owner = await context.read(TestPetOwner, { _id: dataOwner._id });
                    const gotPetIds = (await owner.pets.map(p => p._id).toArray()).sort();
                    const expectedPetIds = dataOwner.pets.map(pet => pet._id as number).sort();
                    assert.deepEqual(gotPetIds, expectedPetIds);
                }),
            ));
        it('Check null/not null references', () =>
            Test.readonly(async context => {
                for (let i = 0; i < 5; i += 1) {
                    const id = dataDogs[i]._id;
                    const dog = await context.read(TestAnimal, { _id: id });
                    if (i === 4) assert.isNull(await dog.owner, `Dog(id=${id}).owner should be null`);
                    else assert.isNotNull(await dog.owner, `Dog(id=${id}).owner should not be null`);
                }
            }));
    });

    it("Can't create an abstract node", () =>
        Test.readonly(context =>
            assert.isRejected(
                context.create(TestAnimal, {
                    strFromAnimal: 'foo',
                }),
                "Could not create an instance of 'TestAnimal', this node is declared as abstract",
            ),
        ));

    it("Can read concrete node from 'animal' abstract factory", () =>
        Test.readonly(async context => {
            const node = await context.tryRead(TestAnimal, { _id: dataDogs[0]._id });
            assert.isNotNull(node);
            assert.isTrue(node instanceof TestDog, 'Node is not instanceof TestDog');
            // The node has been loaded from the 'animal' factory. It should have values for all the properties defined
            // by the 'animal' factory (_id, strFromAnimal) but not for the other properties (strFromMammal, strFromDog)
            assert.isDefined(node?.$.getRawPropertyValue('_id'), "'_id' property is not set");
            assert.equal(node?.$.getRawPropertyValue('strFromAnimal'), dataDogs[0].strFromAnimal);
            assert.isUndefined(node?.$.getRawPropertyValue('strFromMammal'), "'strFromMammal' property is set");
            assert.isUndefined(node?.$.getRawPropertyValue('strFromDog'), "'strFromDog' property is set");
        }));

    it("Can read concrete node from 'mammal' abstract factory", () =>
        Test.readonly(async context => {
            const node = await context.tryRead(TestMammal, { _id: dataDogs[0]._id });
            assert.isNotNull(node);
            assert.isTrue(node instanceof TestDog, 'Node is not instanceof TestDog');
            // The node has been loaded from the 'mammal' factory. It should have values for all the properties defined
            // by the 'animal' and the 'mammal' factories (_id, strFromAnimal, strFromMammal) but not for the
            // other properties (strFromDog)
            assert.isDefined(node?.$.getRawPropertyValue('_id'), "'_id' property is not set");
            assert.equal(node?.$.getRawPropertyValue('strFromAnimal'), dataDogs[0].strFromAnimal);
            assert.equal(node?.$.getRawPropertyValue('strFromMammal'), dataDogs[0].strFromMammal);
            assert.isUndefined(node?.$.getRawPropertyValue('strFromDog'), "'strFromDog' property is set");
        }));

    it("Can read concrete node from 'animal' abstract factory (cat factory)", () =>
        Test.readonly(async context => {
            const node = await context.tryRead(TestAnimal, { _id: dataCats[0]._id });
            assert.isNotNull(node);
            assert.isTrue(node instanceof TestCat, 'Node is not instanceof TestCat');
            assert.equal(node?.$.getRawPropertyValue('strFromAnimal'), dataCats[0].strFromAnimal);
        }));

    it('Lazy load of missing properties', () =>
        Test.readonly(async context => {
            const node = (await context.read(TestAnimal, { _id: dataDogs[0]._id })) as TestDog;
            assert.isDefined(node?.$.getRawPropertyValue('_id'), "'_id' property is not set");
            assert.equal(node?.$.getRawPropertyValue('strFromAnimal'), dataDogs[0].strFromAnimal);
            // For now, strFromMammal/strFromDog should not be defined (the node was loaded from the 'animal' factory)
            assert.isUndefined(node?.$.getRawPropertyValue('strFromMammal'), "'strFromMammal' property is set");
            assert.isUndefined(node?.$.getRawPropertyValue('strFromDog'), "'strFromDog' property is set");
            // Force the lazy loading of the missing properties
            assert.equal(await node?.strFromMammal, dataDogs[0].strFromMammal);
            // properties from all the missing factories (here, mammal/dog) should have been loaded
            assert.equal(node?.$.getRawPropertyValue('strFromDog'), dataDogs[0].strFromDog);
            assert.equal(await node?.strFromDog, dataDogs[0].strFromDog);
        }));

    it('Check events', () =>
        Test.uncommitted(async context => {
            // here, we don't check all the events : we assume that if the 'controlBegin' event was successfully managed
            // all the other events will be too.
            // Some other unit tests are dedicated to events.
            const node = await context.create(TestDog, {
                strFromDog: 'animal/mammal/dog/foo',
                strFromMammal: 'animal/mammal/dog/foo',
                strFromAnimal: 'animal/mammal/dog/foo',
            });
            await node.$.control();
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 1,
                    path: [],
                    message: 'TestAnimal.controlBegin',
                },
                {
                    severity: 1,
                    path: [],
                    message: 'TestMammal.controlBegin',
                },
                {
                    severity: 1,
                    path: [],
                    message: 'TestDog.controlBegin',
                },
            ]);
        }));

    describe('Can delete/deleteMany', () => {
        const ids: number[] = [];
        before(() =>
            Test.committed(async context => {
                for (let i = 0; i < 9; i += 1) {
                    const node = await context.create(TestDog, {
                        strFromDog: `animal/mammal/dog/new${i}`,
                        strFromMammal: `animal/mammal/dog/new${i}`,
                        strFromAnimal: 'toDelete',
                    });
                    await node.$.save();
                    ids.push(node._id);
                }
            }),
        );
        it("Can delete at 'animal' level", () =>
            Test.committed(async context => {
                const idToDelete = ids[0];
                const node = await context.read(
                    TestAnimal,
                    { _id: idToDelete },
                    {
                        forUpdate: true,
                    },
                );
                await node.$.delete();
                assert.isNull(await context.tryRead(TestDog, { _id: idToDelete }), "'dog' was not deleted");
                assert.isNull(await context.tryRead(TestMammal, { _id: idToDelete }), "'mammal' was not deleted");
                assert.isNull(await context.tryRead(TestAnimal, { _id: idToDelete }), "'animal' was not deleted");
            }));
        it("Can delete at 'mammal' level", () =>
            Test.committed(async context => {
                const idToDelete = ids[1];
                const node = await context.read(
                    TestMammal,
                    { _id: idToDelete },
                    {
                        forUpdate: true,
                    },
                );
                await node.$.delete();
                assert.isNull(await context.tryRead(TestDog, { _id: idToDelete }), "'dog' was not deleted");
                assert.isNull(await context.tryRead(TestMammal, { _id: idToDelete }), "'mammal' was not deleted");
                assert.isNull(await context.tryRead(TestAnimal, { _id: idToDelete }), "'animal' was not deleted");
            }));
        it("Can delete at 'dog' level", () =>
            Test.committed(async context => {
                const idToDelete = ids[2];
                const node = await context.read(
                    TestDog,
                    { _id: idToDelete },
                    {
                        forUpdate: true,
                    },
                );
                await node.$.delete();
                assert.isNull(await context.tryRead(TestDog, { _id: idToDelete }), "'dog' was not deleted");
                assert.isNull(await context.tryRead(TestMammal, { _id: idToDelete }), "'mammal' was not deleted");
                assert.isNull(await context.tryRead(TestAnimal, { _id: idToDelete }), "'animal' was not deleted");
            }));
        it("Can't deleteMany at 'animal' level (TestAnimal.canDeleteMany is not set)", () =>
            Test.committed(async context => {
                const idsToDelete = [ids[3], ids[4]];
                await assert.isRejected(
                    context.deleteMany(TestAnimal, {
                        _id: { _in: idsToDelete },
                    }),
                    'TestAnimal: deleteMany operation is not allowed',
                );
            }));
        it("Can't deleteMany at 'mammal' level (abstract factory)", () =>
            Test.committed(async context => {
                const idsToDelete = [ids[5], ids[6]];
                await assert.isRejected(
                    context.deleteMany(TestMammal, {
                        _id: { _in: idsToDelete },
                    }),
                    "test_mammal : Deletions can't be done on abstract factories",
                );
            }));
        it("Can deleteMany at 'dog' level with filter", () =>
            Test.committed(async context => {
                const idsToDelete = [ids[7], ids[8]];
                const deletedCount = await context.deleteMany(TestDog, {
                    _id: { _in: idsToDelete },
                });
                assert.equal(deletedCount, 2);
                await asyncArray(idsToDelete).forEach(async id => {
                    assert.isNull(await context.tryRead(TestDog, { _id: id }), "'dog' was not deleted");
                    assert.isNull(await context.tryRead(TestMammal, { _id: id }), "'mammal' was not deleted");
                    assert.isNull(await context.tryRead(TestAnimal, { _id: id }), "'animal' was not deleted");
                });
            }));
        it("Can deleteMany at 'dog' level without filter", () =>
            Test.uncommitted(async context => {
                await asyncArray(dataOwners).forEach(dataOwner =>
                    asyncArray(dataOwner.pets)?.forEach(async pet => {
                        const animal = await context.read(TestAnimal, { _id: pet._id as number }, { forUpdate: true });
                        await animal.$.set({ owner: null });
                        await animal.$.save();
                    }),
                );
                const dogs = await context.query(TestDog, {}).toArray();
                await context.deleteMany(TestPetOwner, {});
                const deletedCount = await context.deleteMany(TestDog, {});
                assert.strictEqual(deletedCount, dogs.length);
                assert.strictEqual((await context.query(TestDog, {}).toArray()).length, 0);
                await asyncArray(dogs).forEach(async dog => {
                    assert.isNull(await context.tryRead(TestDog, { _id: dog._id }), "'dog' was not deleted");
                    assert.isNull(await context.tryRead(TestMammal, { _id: dog._id }), "'mammal' was not deleted");
                    assert.isNull(await context.tryRead(TestAnimal, { _id: dog._id }), "'animal' was not deleted");
                });
            }));
        after(() =>
            Test.committed(context =>
                context.deleteMany(TestDog, {
                    strFromAnimal: 'toDelete',
                }),
            ),
        );
    });

    ['create', 'update'].forEach(mode => {
        describe(`Concrete node - ${mode}`, () => {
            let nodeId = 0;
            const valuesToCheck = {
                strFromDog: 'animal/mammal/dog/new1',
                strFromMammal: 'animal/mammal/dog/new1',
                strFromAnimal: 'animal/mammal/dog/new1',
            };
            before(() =>
                Test.committed(async context => {
                    if (mode === 'create') {
                        // Creation test
                        const newNode = await context.create(TestDog, valuesToCheck);
                        await newNode.$.save();
                        nodeId = newNode._id;
                    } else {
                        // Update test
                        const newNode = await context.create(TestDog, {
                            strFromDog: 'animal/mammal/dog/foo',
                            strFromMammal: 'animal/mammal/dog/foo',
                            strFromAnimal: 'animal/mammal/dog/foo',
                        });
                        await newNode.$.save();
                        nodeId = newNode._id;

                        const nodeToUpdate = await context.read(
                            TestDog,
                            { _id: nodeId },
                            {
                                forUpdate: true,
                            },
                        );

                        await nodeToUpdate.$.set(valuesToCheck);
                        await nodeToUpdate.$.save();
                    }
                }),
            );
            it("Can read at 'animal' level", () =>
                Test.uncommitted(async context => {
                    // Try to read the new node at the 'animal' level
                    const animal = await context.tryRead(TestAnimal, { _id: nodeId });
                    assert.isNotNull(animal, "Could not read the node at the 'animal' level");
                    assert.equal(await animal?.strFromAnimal, valuesToCheck.strFromAnimal);
                    assert.isTrue(animal instanceof TestMammal, 'Node is not instanceof TestMammal');
                    assert.isTrue(animal instanceof TestDog, 'Node is not instanceof TestDog');
                    // The node has been loaded from the 'animal' factory. It should only have values for all the
                    // properties defined by the 'animal' factory (_id, strFromAnimal) but not for the
                    // other properties (strFromMammal, strFromDog)
                    assert.isDefined(animal?.$.getRawPropertyValue('_id'), "'_id' property is not set");
                    // For now, we don't test the getProperty() function but directly the values from node.$.state.data
                    assert.equal(animal?.$.getRawPropertyValue('strFromAnimal'), valuesToCheck.strFromAnimal);
                    assert.isUndefined(
                        animal?.$.getRawPropertyValue('strFromMammal'),
                        "'strFromMammal' property is set",
                    );
                    assert.isUndefined(animal?.$.getRawPropertyValue('strFromDog'), "'strFromDog' property is set");
                    // Now, we can test the getProperty() function
                    assert.equal(await animal?.strFromAnimal, valuesToCheck.strFromAnimal);
                }));
            it("Can read at 'mammal' level", () =>
                Test.uncommitted(async context => {
                    // Try to read the new node at the 'mammal' level
                    const mammal = await context.tryRead(TestMammal, { _id: nodeId });
                    assert.isNotNull(mammal, "Could not read the node at the 'mammal' level");
                    assert.isTrue(mammal instanceof TestDog, 'Node is not instanceof TestDog');
                    // When loading from the 'mammal' factory, a left join should be created betwwen the 'mammal'
                    // and the 'animal' tables. So, the node should only have the properties defined by 'mammal' / 'animal'
                    // factories (_id, strFromAnimal, strFromMammal) but not the props from 'dog' factory
                    // For now, we don't test the getProperty() function but directly the values from node.$.state.data
                    assert.isDefined(mammal?.$.getRawPropertyValue('_id'), "'_id' property is not set");
                    assert.equal(mammal?.$.getRawPropertyValue('strFromAnimal'), valuesToCheck.strFromAnimal);
                    assert.equal(mammal?.$.getRawPropertyValue('strFromMammal'), valuesToCheck.strFromMammal);
                    assert.isUndefined(mammal?.$.getRawPropertyValue('strFromDog'), "'strFromDog' property is set");
                    // Now, we can test the getProperty() function
                    assert.equal(await mammal?.strFromAnimal, valuesToCheck.strFromAnimal);
                    assert.equal(await mammal?.strFromMammal, valuesToCheck.strFromMammal);
                }));
            it("Can read at 'dog' level", () =>
                Test.uncommitted(async context => {
                    // Try to read the new node at the 'dog' level
                    const dog = await context.tryRead(TestDog, { _id: nodeId });
                    assert.isNotNull(dog, "Could not read the node at the 'dog' level");
                    // For now, we don't test the getProperty() function but directly the values from node.$.state.data
                    assert.equal(dog?.$.getRawPropertyValue('strFromAnimal'), valuesToCheck.strFromAnimal);
                    assert.equal(dog?.$.getRawPropertyValue('strFromMammal'), valuesToCheck.strFromMammal);
                    assert.equal(dog?.$.getRawPropertyValue('strFromDog'), valuesToCheck.strFromDog);
                    // Now, we can test the getProperty() function
                    assert.equal(await dog?.strFromAnimal, valuesToCheck.strFromAnimal);
                    assert.equal(await dog?.strFromMammal, valuesToCheck.strFromMammal);
                    assert.equal(await dog?.strFromDog, valuesToCheck.strFromDog);
                }));
            it("Can't read at 'cat' level", () =>
                Test.uncommitted(async context => {
                    // Try to read the new node at the 'cat' level
                    // nodeId is the id of a 'dog' and should not be used to get a 'cat'
                    const cat = await context.tryRead(TestCat, { _id: nodeId });
                    assert.isNull(cat, "Was able to read the node at the 'cat' level");
                }));
            after(() =>
                Test.committed(async context =>
                    (await context.read(TestDog, { _id: nodeId }, { forUpdate: true })).$.delete(),
                ),
            );
        });
    });

    describe('Can query with filters', () => {
        it("At 'dog' level - property from dog", () =>
            Test.uncommitted(async context => {
                const dogs = await context
                    .query(TestDog, {
                        filter: {
                            strFromDog: {
                                _or: [dataDogs[1].strFromDog!, dataCats[1].strFromCat!],
                            },
                        },
                    })
                    .toArray();
                // Only one of the 2 strings matches a dog (the other one is a cat)
                assert.equal(dogs.length, 1);
                assert.equal(dogs[0]._id, dataDogs[1]._id);
            }));
        it("At 'dog' level - property from animal", () =>
            Test.uncommitted(async context => {
                const dogs = await context
                    .query(TestDog, {
                        filter: {
                            strFromAnimal: {
                                _or: [dataDogs[1].strFromAnimal!, dataCats[1].strFromAnimal!],
                            },
                        },
                    })
                    .toArray();
                // Only one of the 2 strings matches a dog (the other one is a cat)
                assert.equal(dogs.length, 1);
            }));
        it("At 'mammal' level - property from mammal", () =>
            Test.uncommitted(async context => {
                const mammals = await context
                    .query(TestMammal, {
                        filter: {
                            strFromMammal: {
                                _or: [dataDogs[1].strFromMammal!, dataCats[1].strFromMammal!],
                            },
                        },
                    })
                    .toArray();

                // The 2 strings match a mammal (1 dog, 1 cat)
                assert.equal(mammals.length, 2);
                assert.equal(mammals[0]._id, dataDogs[1]._id);
                assert.equal(mammals[1]._id, dataCats[1]._id);
            }));
        it("At 'mammal' level - property from animal", () =>
            Test.uncommitted(async context => {
                const dogs = await context
                    .query(TestMammal, {
                        filter: {
                            strFromAnimal: {
                                _or: [dataDogs[1].strFromAnimal!, dataCats[1].strFromAnimal!],
                            },
                        },
                    })
                    .toArray();
                // The 2 strings match a mammal (1 dog, 1 cat)
                assert.equal(dogs.length, 2);
                assert.equal(dogs[0]._id, dataDogs[1]._id);
                assert.equal(dogs[1]._id, dataCats[1]._id);
            }));
        it("Filter on _constructor at 'Animal' level", () =>
            Test.uncommitted(async context => {
                const animals = await context
                    .query(TestAnimal, {
                        filter: { _constructor: { _in: ['TestDog', 'TestFish'] } } as any,
                    })
                    .toArray();
                // animals should only contains dogs and fiches (TestFish and TestDog inherit from TestAnimal)
                const gotIds = animals
                    .map(animal => animal._id)
                    .sort()
                    .join('/');
                const expectedIds = [...dataDogs, ...dataFishes]
                    .map(d => d._id)
                    .sort()
                    .join('/');
                assert.equal(gotIds, expectedIds);
            }));
        it("Filter on _constructor at 'Mammal' level", () =>
            Test.uncommitted(async context => {
                const mammals = await context
                    .query(TestMammal, {
                        filter: { _constructor: { _in: ['TestDog', 'TestFish'] } } as any,
                    })
                    .toArray();
                // mammals should only contain dogs (TestFish does not inherit from TestMammal)
                const gotIds = mammals
                    .map(mammal => mammal._id)
                    .sort()
                    .join('/');
                const expectedIds = dataDogs
                    .map(d => d._id)
                    .sort()
                    .join('/');
                assert.equal(gotIds, expectedIds);
            }));
    });
    describe('Check sorting', () => {
        it("At 'dog' level - property from dog", () =>
            Test.uncommitted(async context => {
                const dogs = await context
                    .query(TestDog, {
                        orderBy: {
                            strFromDog: -1,
                        },
                    })
                    .toArray();
                const gotIds = dogs.map(dog => dog._id).join('/');
                const expectedIds = [...dataDogs]
                    .reverse()
                    .map(d => d._id)
                    .join('/');
                assert.equal(gotIds, expectedIds);
            }));
        it("At 'dog' level - property from 'animal'", () =>
            Test.uncommitted(async context => {
                const dogs = await context
                    .query(TestDog, {
                        orderBy: {
                            strFromAnimal: -1,
                        },
                    })
                    .toArray();
                const gotIds = dogs.map(dog => dog._id).join('/');
                const expectedIds = [...dataDogs]
                    .reverse()
                    .map(d => d._id)
                    .join('/');
                assert.equal(gotIds, expectedIds);
            }));
        it("At 'mammal' level - property from 'animal'", () =>
            Test.uncommitted(async context => {
                const mammals = await context
                    .query(TestMammal, {
                        orderBy: {
                            strFromAnimal: -1,
                        },
                    })
                    .toArray();
                assert.isTrue(mammals[0] instanceof TestDog, 'instance #0 should be a dog');
                assert.isTrue(mammals[6] instanceof TestCat, 'instance #6 should be a cat');
                const got = await asyncArray(mammals)
                    .map(async mammal => ({
                        id: mammal._id,
                        strFromAnimal: await mammal.strFromAnimal,
                        isDog: mammal instanceof TestDog,
                        isCat: mammal instanceof TestCat,
                    }))
                    .toArray();
                const expected = [
                    ...(await asyncArray(dataDogs)
                        .map(dog => ({
                            id: dog._id,
                            strFromAnimal: dog.strFromAnimal,
                            isDog: true,
                            isCat: false,
                        }))
                        .toArray()),
                    ...(await asyncArray(dataCats)
                        .map(cat => ({
                            id: cat._id,
                            strFromAnimal: cat.strFromAnimal,
                            isDog: false,
                            isCat: true,
                        }))
                        .toArray()),
                ]
                    .sort(sortBy('strFromAnimal'))
                    .reverse();
                assert.deepEqual(got, expected);
            }));
    });

    describe('Checks property overriding filter decorator', () => {
        const dogCreatePayload: TestInitData<TestDog> = {
            strFromAnimal: 'animal/mammal/dog',
            strFromMammal: 'animal/mammal/dog',
            flyBehavior: 2, // 'cannotFly',
        };
        const birdCreatePayload: TestInitData<TestBird> = {
            strFromAnimal: 'animal/bird',
            strFromBird: 'animal/bird',
            flyBehavior: 1, // 'flyWithWings'
            sleepBehavior: 3,
        };
        const createAndSaveDog = async (context: Context, payload: NodeCreateData<TestDog>) => {
            const dog = await context.create(TestDog, payload);
            assert.equal((await dog.sleepBehavior)?._id, payload.sleepBehavior);
            assert.equal((await dog.flyBehavior)?._id, payload.flyBehavior);

            await dog.$.save();
            assert.deepEqual(
                dog.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                [],
            );
            return dog;
        };
        const createAndSaveBird = async (context: Context, payload: NodeCreateData<TestBird>) => {
            const bird = await context.create(TestBird, payload);
            assert.equal((await bird.sleepBehavior)?._id, payload.sleepBehavior);
            assert.equal((await bird.flyBehavior)?._id, payload.flyBehavior);
            await bird.$.save();
            assert.deepEqual(
                bird.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                [],
            );
            return bird;
        };
        before(async () => {
            await setup({ application: await testSubclassingApplication.application });
            await initTables([
                { nodeConstructor: TestFlyBehavior, data: dataAnimalFlyBehavior },
                { nodeConstructor: TestSleepBehavior, data: dataAnimalSleepBehavior },
            ]);
        });
        it("At 'dog' level - create with right flyBehavior 'cannotFly'", () =>
            Test.withContext(async context => {
                await createAndSaveDog(context, dogCreatePayload);
            }));
        it("At 'dog' level - create with wrong flyBehavior 'flyWithWings' should fail", () =>
            Test.withContext(async context => {
                const newNode = await context.create(TestDog, { ...dogCreatePayload, flyBehavior: 1 });
                assert.equal(await (await newNode.flyBehavior)?.flyBehavior, 'flyWithWings');

                await assert.isRejected(newNode.$.save(), 'The record was not created.');
                assert.deepEqual(
                    newNode.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [
                        {
                            message: 'The record is not valid. You need to select a different record.',
                            path: ['flyBehavior'],
                            severity: 3,
                        },
                    ],
                );
            }));
        it("At 'dog' level - update with right sleepBehavior 'sleepOnTheGround'", () =>
            Test.withContext(async context => {
                const newNode = await createAndSaveDog(context, dogCreatePayload);

                const nodeToUpdate = await context.read(TestDog, { _id: newNode._id }, { forUpdate: true });
                await nodeToUpdate.$.set({ sleepBehavior: 1 });
                assert.equal(await (await nodeToUpdate.sleepBehavior)?.behavior, 'sleepOnTheGround');

                assert.isTrue(await nodeToUpdate.$.trySave());
                assert.deepEqual(
                    context.diagnoses.filter(message => (message.severity as number) > 1),
                    [],
                );
            }));
        it("At 'dog' level - update with wrong sleepBehavior 'neverSleep' should fail", () =>
            Test.withContext(async context => {
                const newNode = await createAndSaveDog(context, dogCreatePayload);

                const nodeToUpdate = await context.read(TestDog, { _id: newNode._id }, { forUpdate: true });
                await nodeToUpdate.$.set({ sleepBehavior: 4 });
                assert.equal(await (await nodeToUpdate.sleepBehavior)?.behavior, 'neverSleep');
                await assert.isRejected(nodeToUpdate.$.save(), 'The record was not updated.');
                assert.deepEqual(
                    nodeToUpdate.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [
                        {
                            message: 'Dog animal/mammal/dog does not sleep on the ground.',
                            path: ['sleepBehavior'],
                            severity: 3,
                        },
                    ],
                );
            }));
        it("At 'dog' level - update with wrong flyBehavior 'flyWithWings' should fail", () =>
            Test.withContext(async context => {
                const newNode = await createAndSaveDog(context, dogCreatePayload);

                const nodeToUpdate = await context.read(TestDog, { _id: newNode._id }, { forUpdate: true });
                await nodeToUpdate.$.set({ flyBehavior: 1 });
                assert.equal(await (await nodeToUpdate.flyBehavior)?.flyBehavior, 'flyWithWings');

                await assert.isRejected(nodeToUpdate.$.save(), 'The record was not updated.');
                assert.deepEqual(
                    nodeToUpdate.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [
                        {
                            message: 'The record is not valid. You need to select a different record.',
                            path: ['flyBehavior'],
                            severity: 3,
                        },
                    ],
                );
            }));
        it("At 'bird' level - create with right flyBehavior 'flyWithWings'", () =>
            Test.withContext(async context => {
                await createAndSaveBird(context, birdCreatePayload);
            }));
        it("At 'bird' level - create with wrong flyBehavior 'cannotFly' should fail", () =>
            Test.withContext(async context => {
                const newNode = await context.create(TestBird, { ...birdCreatePayload, flyBehavior: 2 });
                assert.equal(await (await newNode.flyBehavior)?.flyBehavior, 'cannotFly');
                await assert.isRejected(newNode.$.save());
                assert.deepEqual(
                    newNode.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [
                        {
                            message: 'The record is not valid. You need to select a different record.',
                            path: ['flyBehavior'],
                            severity: 3,
                        },
                    ],
                );
            }));
        it("At 'bird' level - create with right sleepBehavior 'sleepInATree'", () =>
            Test.withContext(async context => {
                await createAndSaveBird(context, { ...birdCreatePayload, sleepBehavior: 2 });
            }));
        it("At 'bird' level - create with wrong sleepBehavior 'neverSleep' should fail", () =>
            Test.withContext(async context => {
                const newNode = await context.create(TestBird, { ...birdCreatePayload, sleepBehavior: 4 });
                assert.equal(await (await newNode.sleepBehavior)?.behavior, 'neverSleep');

                await assert.isRejected(newNode.$.save());
                assert.deepEqual(
                    newNode.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [
                        {
                            message: 'Birds sleep.',
                            path: ['sleepBehavior'],
                            severity: 3,
                        },
                    ],
                );
            }));
        it("At 'bird' level - update with right sleepBehavior 'sleepInATree'", () =>
            Test.withContext(async context => {
                const newNode = await createAndSaveBird(context, birdCreatePayload);

                const nodeToUpdate = await context.read(TestBird, { _id: newNode._id }, { forUpdate: true });
                await nodeToUpdate.$.set({ sleepBehavior: 2 });
                assert.isTrue(await nodeToUpdate.$.trySave());
                assert.deepEqual(
                    nodeToUpdate.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [],
                );
            }));
        it("At 'bird' level - update with wrong sleepBehavior 'neverSleep' should fail", () =>
            Test.withContext(async context => {
                const newNode = await createAndSaveBird(context, birdCreatePayload);

                const nodeToUpdate = await context.read(TestBird, { _id: newNode._id }, { forUpdate: true });
                await nodeToUpdate.$.set({ sleepBehavior: 4 });
                assert.equal(await (await nodeToUpdate.sleepBehavior).behavior, 'neverSleep');
                await assert.isRejected(nodeToUpdate.$.save());
                assert.deepEqual(
                    nodeToUpdate.$.context.diagnoses.filter(message => (message.severity as number) > 1),
                    [
                        {
                            message: 'Birds sleep.',
                            path: ['sleepBehavior'],
                            severity: 3,
                        },
                    ],
                );
            }));
    });
    describe('Fix XT-87013 : Create "Dog" instances with different values for references', () => {
        it("Create Dog using ref as 'refProp: 1", () =>
            Test.withContext(async context => {
                const dog = await context.create(TestDog, {
                    strFromDog: 'animal/mammal/dog/foo',
                    strFromMammal: 'animal/mammal/dog/foo',
                    strFromAnimal: 'animal/mammal/dog/foo',
                    baseRefPropNoDataType: 1,
                    baseRefPropWithDataType: 1,
                    refPropNoDataType: 1,
                    refPropWithDataType: 1,
                    fromDogRefPropNoDataType: 1,
                    fromDogRefPropWithDataType: 1,
                });
                assert.isTrue(await dog.$.trySave());
            }));
        it("Create Dog using ref as 'refProp: {_id: 1}", () =>
            Test.withContext(async context => {
                const dog = await context.create(TestDog, {
                    strFromDog: 'animal/mammal/dog/foo',
                    strFromMammal: 'animal/mammal/dog/foo',
                    strFromAnimal: 'animal/mammal/dog/foo',
                    baseRefPropNoDataType: { _id: 1 },
                    baseRefPropWithDataType: { _id: 1 },
                    refPropNoDataType: { _id: 1 },
                    refPropWithDataType: { _id: 1 },
                    fromDogRefPropNoDataType: { _id: 1 },
                    fromDogRefPropWithDataType: { _id: 1 },
                });
                assert.isTrue(await dog.$.trySave());
            }));
        it("Create Dog using ref as 'refProp: {_id: myProp._id}", () =>
            Test.withContext(async context => {
                const refPropNoDataType = await context.read(TestRefPropNoDataType, { _id: 1 });
                const refPropWithDataType = await context.read(TestRefPropWithDataType, { _id: 1 });

                const dog = await context.create(TestDog, {
                    strFromDog: 'animal/mammal/dog/foo',
                    strFromMammal: 'animal/mammal/dog/foo',
                    strFromAnimal: 'animal/mammal/dog/foo',
                    baseRefPropNoDataType: { _id: refPropNoDataType._id },
                    baseRefPropWithDataType: { _id: refPropWithDataType._id },
                    refPropNoDataType: { _id: refPropNoDataType._id },
                    refPropWithDataType: { _id: refPropWithDataType._id },
                    fromDogRefPropNoDataType: { _id: refPropNoDataType._id },
                    fromDogRefPropWithDataType: { _id: refPropWithDataType._id },
                });
                assert.isTrue(await dog.$.trySave());
            }));
        it("Create Dog using ref as 'refProp: {id: 1}", () =>
            Test.withContext(async context => {
                const dog = await context.create(TestDog, {
                    strFromDog: 'animal/mammal/dog/foo',
                    strFromMammal: 'animal/mammal/dog/foo',
                    strFromAnimal: 'animal/mammal/dog/foo',
                    baseRefPropNoDataType: { id: 1 },
                    baseRefPropWithDataType: { id: 1 },
                    refPropNoDataType: { id: 1 },
                    refPropWithDataType: { id: 1 },
                    fromDogRefPropNoDataType: { id: 1 },
                    fromDogRefPropWithDataType: { id: 1 },
                });
                assert.isTrue(await dog.$.trySave());
            }));
        it("Create Dog using ref as 'refProp: '#1''", () =>
            Test.withContext(async context => {
                const dog = await context.create(TestDog, {
                    strFromDog: 'animal/mammal/dog/foo',
                    strFromMammal: 'animal/mammal/dog/foo',
                    strFromAnimal: 'animal/mammal/dog/foo',
                    baseRefPropNoDataType: '#1',
                    baseRefPropWithDataType: '#1',
                    refPropNoDataType: '#1',
                    refPropWithDataType: '#1',
                    fromDogRefPropNoDataType: '#1',
                    fromDogRefPropWithDataType: '#1',
                });
                assert.isTrue(await dog.$.trySave());
            }));
    });
    after(() => restoreTables());
});
