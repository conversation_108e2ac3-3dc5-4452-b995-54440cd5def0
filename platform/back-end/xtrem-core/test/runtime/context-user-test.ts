import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { CookieOptions } from 'express';
import { omit } from 'lodash';
import { Cookie, Test, UserTestOptions, initTables } from '../../index';
import { Context } from '../../lib';
import { createApplicationWithApi, datatypesData, restoreTables, setup } from '../fixtures/index';
import { TestDatatypes, TestUser } from '../fixtures/nodes';
import * as serviceOptions from '../fixtures/service-options/index';

const defaultTenantCookieName = `xtrem_${Test.defaultTenantId}_persona`;

const createId = datatypesData.length + 100;

function createUser(context: Context): Promise<TestDatatypes> {
    return context.create(TestDatatypes, { id: createId, integerVal: 1 });
}

function withDemoUserContext<T extends AnyValue>(
    body: (context: Context) => AsyncResponse<T>,
    options: UserTestOptions,
): Promise<T> {
    return Test.withUserContext(body, options, [serviceOptions.isDemoTenant]);
}

describe('Context user API', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestDatatypes }, serviceOptions }) });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    after(() => restoreTables());

    describe('user initialization', () => {
        it('config user is resolved in context', () =>
            withDemoUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(context.response.constructor.name, 'Object');
                    assert.equal(user?.email, '<EMAIL>');
                    assert.equal(user?.userName, '<EMAIL>');
                },
                { user: '<EMAIL>' },
            ));

        it('config user is resolved in request context', async () => {
            const cookies = {} as Dict<Cookie>;
            await withDemoUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, '<EMAIL>');
                    assert.equal(user?.userName, '<EMAIL>');
                },
                {
                    user: '<EMAIL>',
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.isUndefined(cookies[defaultTenantCookieName]);
        });

        it('persona and login user are resolved in a request context', async () => {
            const cookies = {} as Dict<Cookie>;
            const persona = '<EMAIL>';
            await withDemoUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, persona);
                    assert.equal(user?.userName, persona);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, '<EMAIL>');
                    assert.equal(loginUser?.userName, '<EMAIL>');
                },
                {
                    user: '<EMAIL>',
                    auth: { login: '<EMAIL>', persona },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.deepEqual(cookies[defaultTenantCookieName], {
                value: persona,
                options: { httpOnly: true, sameSite: 'lax', secure: false },
            });
        });

        it('admin persona for non admin user login should not be resolved in a request context', async () => {
            const cookies = {} as Dict<Cookie>;
            const login = '<EMAIL>';
            const persona = '<EMAIL>';
            await assert.isRejected(
                withDemoUserContext(
                    async context => {
                        assert.isFalse((await context.user)!.isAdministrator);
                    },
                    {
                        user: login,
                        auth: { login, persona },
                        cookie: (name: string, val: string, options: CookieOptions) => {
                            cookies[name] = { value: val, options };
                        },
                    },
                ),
                "The login id '<EMAIL>' does not have the '<EMAIL>' persona, and no 'administrator' persona is available for this user",
            );
        });

        it('non admin user should not be able to change to an admin persona', async () => {
            const cookies = {} as Dict<Cookie>;
            const login = '<EMAIL>';
            const personas = ['<EMAIL>', '<EMAIL>'];
            await withDemoUserContext(
                async context => {
                    assert.isFalse((await context.user)!.isAdministrator);
                    const user = await context.user;
                    assert.equal(user?.email, personas[0]);
                    assert.equal(user?.userName, personas[0]);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                    await assert.isRejected(
                        context.setDemoPersona(personas[1]),
                        "The login id '<EMAIL>' does not have the '<EMAIL>' persona, and no 'administrator' persona is available for this user.",
                    );
                    const userAfterSetPersona = await context.user;
                    assert.equal(userAfterSetPersona?.email, personas[0]);
                    assert.equal(userAfterSetPersona?.userName, personas[0]);
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                },
                {
                    auth: { login },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.deepEqual(cookies[defaultTenantCookieName], {
                value: personas[0],
                options: { httpOnly: true, sameSite: 'lax', secure: false },
            });
        });

        it('set a persona for login user without cookie', async () => {
            const cookies = {} as Dict<Cookie>;
            const login = '<EMAIL>';
            const persona = '<EMAIL>';
            await withDemoUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, persona);
                    assert.equal(user?.userName, persona);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                },
                {
                    auth: { login },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.deepEqual(cookies[defaultTenantCookieName], {
                value: '<EMAIL>',
                options: { httpOnly: true, sameSite: 'lax', secure: false },
            });
        });

        it('set a persona for login user via context options without persona but with login', () =>
            Test.withUserContext(
                async context => {
                    const user = await context.user;
                    // persona is not passed is therefore defaulted to the first demo persona of the users allowed persona's
                    assert.equal(user?.email, '<EMAIL>');
                    assert.equal(user?.userName, '<EMAIL>');
                    const loginUser = await context.loginUser;
                    assert.equal(loginUser?.email, '<EMAIL>');
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.userName, '<EMAIL>');
                },
                {
                    auth: { login: '<EMAIL>' },
                },
                [serviceOptions.isDemoTenant],
            ));

        it('set a persona for login user via context options with persona but without login', () =>
            Test.withUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, '<EMAIL>');
                    assert.equal(user?.userName, '<EMAIL>');
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, '<EMAIL>');
                    assert.equal(loginUser?.userName, '<EMAIL>');
                },
                {
                    auth: { login: '<EMAIL>', persona: '<EMAIL>' },
                },
                [serviceOptions.isDemoTenant],
            ));

        it('set an admin persona for admin login user without cookie', async () => {
            const cookies = {} as Dict<Cookie>;
            await withDemoUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, '<EMAIL>');
                    assert.equal(user?.userName, '<EMAIL>');
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, '<EMAIL>');
                    assert.equal(loginUser?.userName, '<EMAIL>');
                },
                {
                    auth: { login: '<EMAIL>' },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.deepEqual(cookies[defaultTenantCookieName], {
                value: '<EMAIL>',
                options: { httpOnly: true, sameSite: 'lax', secure: false },
            });
        });

        it('can change to a new persona', async () => {
            const cookies = {} as Dict<Cookie>;
            const login = '<EMAIL>';
            const personas = ['<EMAIL>', '<EMAIL>'];
            await withDemoUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, personas[0]);
                    assert.equal(user?.userName, personas[0]);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                    await assert.isFulfilled(context.setDemoPersona(personas[1]));
                    const userAfterSetPersona = await context.user;
                    assert.equal(userAfterSetPersona?.email, personas[1]);
                    assert.equal(userAfterSetPersona?.userName, personas[1]);
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                },
                {
                    auth: { login },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.deepEqual(cookies[defaultTenantCookieName], {
                value: personas[1],
                options: { httpOnly: true, sameSite: 'lax', secure: false },
            });
        });

        it('cannot use a persona without a login user in context', async () => {
            await assert.isRejected(
                withDemoUserContext(context => context.user, { user: '<EMAIL>' }),
                "cannot use the persona '<EMAIL>' without a login user",
            );
        });

        it('cannot use a persona as a login user in context', async () => {
            await assert.isRejected(
                withDemoUserContext(context => context.user, {
                    user: '<EMAIL>',
                    auth: { login: '<EMAIL>' },
                    cookie: true,
                }),
                "cannot use the persona '<EMAIL>' as login user",
            );
        });

        it('can use an api login with an api token', async () => {
            const login = '<EMAIL>';
            await Test.withUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, login);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                    assert.isTrue(loginUser?.isApiUser);
                },
                {
                    auth: { login, auth0: 'api|0000000000' },
                    cookie: true,
                },
            );
        });

        it('can use an api login with an copilot token', async () => {
            const login = '<EMAIL>';
            await Test.withUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, login);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                    assert.isFalse(loginUser?.isApiUser);
                },
                {
                    auth: { login, auth0: `copilot|${login}` },
                    cookie: true,
                },
            );
        });

        it('can use an interactive login with an auth0 token', async () => {
            const login = '<EMAIL>';
            await Test.withUserContext(
                async context => {
                    const user = await context.user;
                    assert.equal(user?.email, login);
                    const loginUser = await context.loginUser;
                    assert.isNotNull(loginUser);
                    assert.equal(loginUser?.email, login);
                    assert.equal(loginUser?.userName, login);
                },
                {
                    auth: { login, auth0: 'auth0|1c771052e65940afb10b3986b263c3d6' },
                    cookie: true,
                },
            );
        });

        it('cannot use an api login with an auth0 token', async () => {
            const login = '<EMAIL>';
            await assert.isRejected(
                Test.withUserContext(
                    async context => {
                        const user = await context.user;
                        assert.equal(user?.email, login);
                        const loginUser = await context.loginUser;
                        assert.isNotNull(loginUser);
                        assert.equal(loginUser?.email, login);
                        assert.equal(loginUser?.userName, login);
                    },
                    {
                        auth: { login, auth0: 'auth0|1c771052e65940afb10b3986b263c3d6' },
                        cookie: true,
                    },
                ),
                "login user '<EMAIL>' requires an api token",
            );
        });
    });

    describe('demo persona list', () => {
        it('can get all personas of a admin login user', () =>
            withDemoUserContext(
                async context => {
                    const loginUser = await context.loginUser;
                    assert.isTrue(loginUser?.isAdministrator);
                    const personas = (await Context.accessRightsManager.getDemoPersonas(context)).map(p =>
                        omit(p, ['_id']),
                    );
                    assert.deepEqual(personas, [
                        {
                            email: '<EMAIL>',
                            firstName: 'Admin',
                            lastName: 'Persona',
                            isActive: true,
                            userType: 'application',
                            isAdministrator: true,
                            isApiUser: false,
                            isDemoPersona: true,
                            userName: '<EMAIL>',
                        },
                        {
                            email: '<EMAIL>',
                            firstName: 'Demo',
                            lastName: 'Persona',
                            isActive: true,
                            userType: 'application',
                            isDemoPersona: true,
                            isAdministrator: false,
                            isApiUser: false,
                            userName: '<EMAIL>',
                        },
                        {
                            email: '<EMAIL>',
                            firstName: 'Demo2',
                            lastName: 'Persona',
                            isActive: true,
                            userType: 'application',
                            isDemoPersona: true,
                            isAdministrator: false,
                            isApiUser: false,
                            userName: '<EMAIL>',
                        },
                    ]);
                },
                { user: '<EMAIL>', auth: { login: '<EMAIL>' }, cookie: true },
            ));

        it('can get all personas of a normal login user', () =>
            withDemoUserContext(
                async context => {
                    const loginUser = await context.loginUser;
                    assert.isNotNull(!loginUser);
                    assert.isTrue(!loginUser?.isAdministrator);
                    const personas = (await Context.accessRightsManager.getDemoPersonas(context)).map(p =>
                        omit(p, ['_id']),
                    );
                    assert.deepEqual(personas, [
                        {
                            email: '<EMAIL>',
                            firstName: 'Demo',
                            lastName: 'Persona',
                            isActive: true,
                            userType: 'application',
                            isDemoPersona: true,
                            isAdministrator: false,
                            isApiUser: false,
                            userName: '<EMAIL>',
                        },
                        {
                            email: '<EMAIL>',
                            firstName: 'Demo2',
                            lastName: 'Persona',
                            isActive: true,
                            userType: 'application',
                            isDemoPersona: true,
                            isAdministrator: false,
                            isApiUser: false,
                            userName: '<EMAIL>',
                        },
                    ]);
                },
                { user: '<EMAIL>', auth: { login: '<EMAIL>' }, cookie: true },
            ));
    });

    describe('transaction user', () => {
        it('can write and have createdBy set with a basic user', () =>
            withDemoUserContext(
                async context => {
                    const created = await createUser(context);
                    await created.$.save();
                    assert.ok(await context.exists(TestDatatypes, { id: createId }));
                    const createdBy = (await created.$.createdBy) as TestUser;
                    assert.equal(await createdBy.email, '<EMAIL>');
                },
                { user: '<EMAIL>' },
            ));

        it('can write with a persona and have createdBy set with login user', async () => {
            const cookies = {} as Dict<Cookie>;
            await withDemoUserContext(
                async context => {
                    const created = await createUser(context);
                    await created.$.save();
                    assert.ok(await context.exists(TestDatatypes, { id: createId }));
                    const createdBy = (await created.$.createdBy) as TestUser;
                    assert.equal(await createdBy.email, '<EMAIL>');
                    // the transactionUser no longer trigger the computation of the login user and persona
                    // so we need to explicitly request the context user
                    const user = await context.user;
                    assert.equal(user?.email, '<EMAIL>');
                },
                {
                    user: '<EMAIL>',
                    auth: { login: '<EMAIL>', persona: '<EMAIL>' },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            );
            assert.deepEqual(cookies[defaultTenantCookieName], {
                value: '<EMAIL>',
                options: { httpOnly: true, sameSite: 'lax', secure: false },
            });
        });

        it('cannot write with a persona but no login user in context', async () => {
            await assert.isRejected(
                withDemoUserContext(
                    async context => {
                        const created = await createUser(context);
                        await created.$.save();
                    },
                    { user: '<EMAIL>' },
                ),
                "cannot use the persona '<EMAIL>' without a login user",
            );
        });
    });
});
