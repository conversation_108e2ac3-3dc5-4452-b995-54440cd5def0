import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import * as lodash from 'lodash';
import * as sinon from 'sinon';
import { testDatatypesApplication } from '..';
import { Test } from '../../index';
import { loggers } from '../../lib/runtime/loggers';
import {
    createApplicationWithApi,
    datatypesData,
    fixDatetimes,
    initTables,
    restoreTables,
    setup,
} from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';
import { TestAnonymousDataType } from '../fixtures/nodes/anonymous-data-type';

describe('testDatatypes tests (runtime)', () => {
    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    const testPayload = async (node: TestDatatypes, d: any, i: number) => {
        const payload = await node.$.payload({ withIds: true });
        fixDatetimes(
            payload.datetimeVal,
            d.datetimeVal,
            payload.datetimeRangeVal?.start,
            d.datetimeRangeVal?.start,
            payload.datetimeRangeVal?.end,
            d.datetimeRangeVal?.end,
            payload.datetimeRangeVal?.includedStart,
            d.datetimeRangeVal?.includedStart,
            payload.datetimeRangeVal?.excludedEnd,
            d.datetimeRangeVal?.excludedEnd,
        );
        assert.deepEqual(payload, { ...d, computed: d.id * d.integerVal, complexComputed: 10 }, `record ${i} matches`);
    };

    it('can read all data types', () =>
        Test.readonly(async context => {
            const data = datatypesData;
            const nodes = await context.query(TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            await asyncArray(nodes).forEach(async (node, i) => {
                const d = data[i];
                await testPayload(node, { ...d, computedCached: d.id * d.integerVal }, i);
            });
        }));

    it('can update arrays', () =>
        Test.committed(async context => {
            const node = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });

            await node.$.set(datatypesData[3]);
            await node.$.set({ integerArrayVal: [1, 2, 3] });
            await node.$.set({ enumArrayVal: ['arrayVal1', 'arrayVal3'] });
            await node.$.set({ stringArrayVal: ['four', 'words', 'i', 'know'] });

            await testPayload(
                node,
                {
                    ...datatypesData[3],
                    integerArrayVal: [1, 2, 3],
                    enumArrayVal: ['arrayVal1', 'arrayVal3'],
                    stringArrayVal: ['four', 'words', 'i', 'know'],
                    computedCached: datatypesData[3].id * datatypesData[3].integerVal,
                },
                1,
            );
            await node.$.save();
        }));

    it('can not push in arrays after setting their values through the node set flow', () =>
        Test.committed(async context => {
            const node = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });
            await node.$.set(datatypesData[3]);

            const integerArrayVal = (await node.integerArrayVal)!;
            assert.throws(() => integerArrayVal.push(4), 'Cannot add property 3, object is not extensible');
            await node.$.save();

            const enumArrayVal = (await node.enumArrayVal)!;
            assert.throws(() => enumArrayVal.push('arrayVal1'), 'Cannot add property 3, object is not extensible');
            await node.$.save();

            const stringArrayVal = (await node.stringArrayVal)!;
            assert.throws(() => stringArrayVal.push('string-value'), 'Cannot add property 3, object is not extensible');
            await node.$.save();
        }));

    it('can not push in arrays that are directly read from sql', () =>
        Test.committed(async context => {
            const node = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });

            const integerArrayVal = (await node.integerArrayVal)!;
            assert.throws(() => integerArrayVal.push(4), 'Cannot add property 3, object is not extensible');
            await node.$.save();

            const enumArrayVal = (await node.enumArrayVal)!;
            assert.throws(() => enumArrayVal.push('arrayVal1'), 'Cannot add property 3, object is not extensible');
            await node.$.save();

            const stringArrayVal = (await node.stringArrayVal)!;
            assert.throws(() => stringArrayVal.push('string-value'), 'Cannot add property 3, object is not extensible');
            await node.$.save();
        }));

    it('can update all data types', async () => {
        const data = datatypesData;
        const newData = { ...data[5], id: 2 };
        // The _id won't be changed
        newData._id = data[2]._id;
        let computedCached = 0;
        await Test.committed(async context => {
            const node = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });
            computedCached = data[2].id * data[2].integerVal;
            await testPayload(node, { ...data[2], computedCached }, 2);
            const newDataForUpdate = { ...newData };
            // Do not try to update the _id
            delete (newDataForUpdate as any)._id;
            await node.$.set(newDataForUpdate);
            // cache was invalidated and the value is recomputed
            computedCached = newData.id * newData.integerVal;
            await testPayload(node, { ...newData, computedCached }, 2);
            await node.$.save();
        });
        await Test.readonly(async context => {
            // read it again to check that db was correctly updated
            const updatedNode = await context.read(TestDatatypes, { id: 2 });
            computedCached = (await updatedNode.id) * (await updatedNode.integerVal);
            await testPayload(updatedNode, { ...newData, computedCached }, 2);
        });
    });

    it('can bulk update all data types', async () => {
        const data = lodash.omit(datatypesData[6], '_id', 'id');
        const ids = [3, 5];
        await Test.committed(async context => {
            const updatedCount = await context.bulkUpdate(TestDatatypes, {
                set: data,
                where: { id: { _in: ids } },
            });
            assert.equal(updatedCount, ids.length);
        });
        // read it again to check that db was correctly updated
        await Test.readonly(context =>
            asyncArray(ids).forEach(async id => {
                const updatedNode = await context.read(TestDatatypes, { id });
                await testPayload(updatedNode, { ...data, _id: id + 1, id, computedCached: id * data.integerVal }, id);
            }),
        );
    });

    it('can cache computed value and invalidate when dependent property is ', async () => {
        const data = datatypesData;
        const newData = { ...data[5], id: 2 };
        // The _id won't be changed
        newData._id = data[2]._id;
        await Test.withContext(async context => {
            const node = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });
            const newDataForUpdate = { ...newData };
            const oldVal = (await node.id) * (await node.integerVal);
            assert.equal(await node.computedCached, oldVal);
            // Second assert is to fetch using cache
            assert.equal(await node.computedCached, oldVal);
            // Do not try to update the _id
            delete (newDataForUpdate as any)._id;
            await node.$.set({ integerVal: 10 });

            const newVal = (await node.id) * (await node.integerVal);
            assert.equal(await node.computedCached, newVal);
            // Second assert is to fetch using cache
            assert.equal(await node.computedCached, newVal);
            assert.notEqual(oldVal, newVal);
            await node.$.save();
        });
    });
    after(() => restoreTables());
});

describe('Anonymous DataTypes', () => {
    let consoleWarnStub: sinon.SinonStub;

    beforeEach(() => {
        consoleWarnStub = sinon.stub(loggers.property, 'warn');
    });

    afterEach(() => {
        consoleWarnStub.restore();
    });

    it('Should throw a warning if an anonymous data types is used in a property', async () => {
        const apiNodes = {
            TestAnonymousDataType,
        };
        await createApplicationWithApi({ nodes: apiNodes });

        const expectedWarning = 'TestAnonymousDataType.type: anonymous DataTypes are not allowed';

        const hasMatchingWarning = consoleWarnStub.getCalls().some(call => call.args[0].includes(expectedWarning));

        assert.isTrue(hasMatchingWarning);
    });
});
