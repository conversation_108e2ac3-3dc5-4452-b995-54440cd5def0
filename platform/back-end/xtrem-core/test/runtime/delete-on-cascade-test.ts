import { assert } from 'chai';
import { assertIsRejectedWithContextDiagnoses, NodeCreateData, Test, ValidationSeverity } from '../../index';
import {
    createApplicationWithApi,
    documentData,
    documentLineData,
    initTables,
    referredData,
    restoreTables,
    setup,
} from '../fixtures/index';
import {
    TestBaseCertificate,
    TestDocument,
    TestDocumentLine,
    TestReferred,
    TestRefToDoc,
    TestSupplier,
    TestSupplierCertificate,
} from '../fixtures/nodes';

/** DECLARE SOME TYPES */

const refToDocData = [
    { _id: 1, code: 'REFDOCB1', vitalDoc: 2 },
    { _id: 2, code: 'REFDOCC1', vitalDoc: 3 },
    { _id: 3, code: 'REFDOCC2', vitalDoc: 3 },
];

const dataSupplierCertificates: NodeCreateData<TestSupplierCertificate>[] = [];
for (let i = 1; i <= 5; i += 1) {
    dataSupplierCertificates.push({
        _id: i,
        code: `SUPPLIER_CERT_${i}`,
        supplier: ((i - 1) % 2) + 1,
    });
}

const dataSuppliers: NodeCreateData<TestSupplier>[] = [];
for (let i = 1; i <= 2; i += 1) {
    dataSuppliers.push({
        _id: i,
        code: `SUPPLIER_${i}`,
    });
}

describe('Delete on cascade', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestReferred,
                    TestDocument,
                    TestDocumentLine,
                    TestRefToDoc,
                    TestSupplier,
                    TestSupplierCertificate,
                    TestBaseCertificate,
                },
            }),
        }),
    );

    beforeEach(() =>
        initTables([
            { nodeConstructor: TestReferred, data: referredData },
            { nodeConstructor: TestDocument, data: documentData },
            { nodeConstructor: TestDocumentLine, data: documentLineData },
            { nodeConstructor: TestRefToDoc, data: refToDocData },
            { nodeConstructor: TestSupplier, data: dataSuppliers },
            { nodeConstructor: TestSupplierCertificate, data: dataSupplierCertificates },
        ]),
    );

    // Seen with Eric => still skipped until new PR about deletions coming shortly.
    it.skip('Can delete', () =>
        Test.uncommitted(async context => {
            assert.equal((await context.query(TestRefToDoc, { filter: { vitalDoc: 3 } }).toArray()).length, 3);
            assert.equal((await context.query(TestDocumentLine, { filter: { document: 3 } }).toArray()).length, 5);

            const doc1 = await context.read(TestDocument, { code: 'DOCC' }, { forUpdate: true });
            await doc1.$.delete();

            assert.isNull(await context.tryRead(TestDocument, { code: 'DOCC' }));
            assert.equal((await context.query(TestRefToDoc, { filter: { vitalDoc: 3 } }).toArray()).length, 0);
            assert.equal((await context.query(TestDocumentLine, { filter: { document: 3 } }).toArray()).length, 0);
        }));

    it('Can reset reference to sub nodes', () =>
        Test.uncommitted(async context => {
            const supplier = await context.tryRead(TestSupplier, { _id: 1 }, { forUpdate: true });
            assert.isNotNull(supplier);
            assert.strictEqual(await supplier.certificates.length, 3);
            await supplier.$.delete();

            assert.isNull(await context.tryRead(TestSupplier, { _id: 1 }));
            assert.strictEqual((await context.query(TestSupplierCertificate).toArray()).length, 2);
            assert.strictEqual((await context.query(TestBaseCertificate).toArray()).length, 2);
            assert.isNull(await context.tryRead(TestSupplierCertificate, { _id: 1 }));
            assert.isNull(await context.tryRead(TestBaseCertificate, { _id: 1 }));
            assert.isNull(await context.tryRead(TestSupplierCertificate, { _id: 3 }));
            assert.isNull(await context.tryRead(TestSupplierCertificate, { _id: 5 }));
        }));

    it('Can delete parent', () =>
        Test.uncommitted(async context => {
            const referred = await context.read(TestReferred, {
                code: 'REF1',
            });

            const doc = await context.create(TestDocument, {
                code: 'DOC3',
                mandatoryReference: referred,
            });
            await doc.$.save();
            await context.delete(TestDocument, { code: 'DOC3' });

            await assert.isRejected(
                context.read(TestDocument, {
                    code: 'DOC3',
                }),
                'TestDocument: record not found: {"code":"DOC3"}',
            );
        }));

    it("Can delete parent with 'forbid'", () =>
        Test.uncommitted(async context => {
            const referred = await context.read(TestReferred, {
                code: 'REF1',
            });
            const doc = await context.create(TestDocument, {
                code: 'DOC1',
                mandatoryReference: referred,
            });
            await doc.$.save();
            const refToDoc = await context.create(TestRefToDoc, {
                code: 'REF1',
                forbidDoc: doc,
                vitalDoc: 3,
            });
            await refToDoc.$.save();

            await refToDoc.$.delete();
        }));

    it("Can't delete child with 'forbid'", () =>
        Test.uncommitted(async context => {
            const referred = await context.read(TestReferred, {
                code: 'REF1',
            });
            let doc = await context.create(TestDocument, {
                code: 'DOC1',
                mandatoryReference: referred,
            });
            await doc.$.save();
            doc = await context.read(TestDocument, { code: 'DOC1' }, { forUpdate: true });
            const refToDoc = await context.create(TestRefToDoc, {
                code: 'REF1',
                forbidDoc: doc,
                vitalDoc: 3,
            });
            await refToDoc.$.save();

            await assert.isRejected(doc.$.delete(), 'Could not delete: blocked by property TestRefToDoc.forbidDoc');
        }));

    it('Delete TestRefToDoc forbidden by control', () =>
        Test.uncommitted(async context => {
            const code = 'DONOTDELETE_REF';

            const refToDoc1 = await context.create(TestRefToDoc, {
                code,
                vitalDoc: 3,
            });
            await refToDoc1.$.save();

            const refToDoc2 = await context.read(TestRefToDoc, { code }, { forUpdate: true });
            await assertIsRejectedWithContextDiagnoses(refToDoc2.$.delete(), context, {
                message: 'The record was not deleted.',
                diagnoses: [
                    {
                        severity: ValidationSeverity.error,
                        message: `TestRefToDoc(${await refToDoc2.code}) MUST NOT BE DELETED`,
                        path: [],
                    },
                ],
            });
        }));

    after(() => restoreTables());
});
