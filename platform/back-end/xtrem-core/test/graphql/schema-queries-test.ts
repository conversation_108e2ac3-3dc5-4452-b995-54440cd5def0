import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import { GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';

let graphqlHelper: GraphQlHelper;
describe('schema queries', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDatatypesApplication.application });
        await initTables([]);
    });
    after(() => restoreTables());
    it('can query enum values', async () => {
        const result = await graphqlHelper.execute<{ __type: { name: string } }>(
            '{ __type(name: "TestEnum") { name } }',
        );
        assert.deepEqual(result, {
            data: {
                __type: {
                    name: 'TestEnum',
                },
            },
        });
    });

    it('can query a stringArrayProperty type', async () => {
        const metadata = await graphqlHelper.execute<{
            TestDatatypes: { fields: unknown[] };
            TestDatatypes_Input: { inputFields: unknown[] };
        }>(`query {
            TestDatatypes: __type (name: "TestDatatypes") {
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        kind
                        enumValues {
                            name
                        }
                    }
                }
            }
            TestDatatypes_Input: __type (name: "TestDatatypes_Input") {
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        kind
                        enumValues {
                            name
                        }
                    }
                }
            }
        }`);

        const checkFields = (fields: any) => {
            assert.isArray(fields);
            fields.forEach((field: any) => {
                assert.exists(field.name);
                assert.exists(field.type);
                if (field.name === 'stringVal') {
                    assert.deepEqual(field.type, {
                        ofType: null,
                        name: 'String',
                        kind: 'SCALAR',
                        enumValues: null,
                    });
                } else if (field.name === 'stringArrayVal') {
                    assert.deepEqual(field.type, {
                        ofType: {
                            name: 'String',
                        },
                        kind: 'LIST',
                        name: null,
                        enumValues: null,
                    });
                }
            });
        };

        const data = metadata.data!;
        assert.exists(data);
        assert.exists(data.TestDatatypes);
        assert.exists(data.TestDatatypes);
        assert.exists(data.TestDatatypes.fields);
        checkFields(data.TestDatatypes.fields);

        assert.exists(data.TestDatatypes_Input);
        assert.exists(data.TestDatatypes_Input);
        assert.exists(data.TestDatatypes_Input.inputFields);
        checkFields(data.TestDatatypes_Input.inputFields);
    });
});
