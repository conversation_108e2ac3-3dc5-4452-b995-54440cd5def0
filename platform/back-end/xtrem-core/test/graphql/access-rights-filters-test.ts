import { edgesSelector, Graph } from '@sage/xtrem-client';
import { assert } from 'chai';
import * as fixtures from '../fixtures';
import { GraphApi } from '../fixtures/client-nodes/index';
import {
    clientSetup,
    createApplicationWithApi,
    initTables,
    restoreTables,
    secureData,
    siteData,
} from '../fixtures/index';
import {
    TestAnimal,
    TestAnimalLine,
    TestCat,
    TestDog,
    TestFish,
    TestFlyBehavior,
    TestMammal,
    TestPetOwner,
    TestRefPropNoDataType,
    TestRefPropWithDataType,
    TestSecure,
    TestSite,
    TestSleepBehavior,
} from '../fixtures/nodes';
import {
    dataAnimalFlyBehavior,
    dataAnimalSleepBehavior,
    dataCats,
    dataDogs,
    dataFishes,
    dataOwners,
} from './subclassing-test';

let graph: Graph<GraphApi>;
let secureNode: GraphApi['@sage/xtrem-core/testSecure'];
let siteNode: GraphApi['@sage/xtrem-core/testSite'];
let animalNode: GraphApi['@sage/xtrem-core/testAnimal'];

describe('apply site security filter', () => {
    before(async () => {
        graph = await clientSetup({
            application: await createApplicationWithApi({
                nodes: { TestSite, TestSecure },
            }),
        });
        siteNode = graph.node('@sage/xtrem-core/testSite');
        secureNode = graph.node('@sage/xtrem-core/testSecure');
        await initTables([
            { nodeConstructor: fixtures.nodes.TestSite, data: siteData },
            { nodeConstructor: fixtures.nodes.TestSecure, data: secureData },
        ]);
    });
    after(() => restoreTables());
    const test = (title: string, user: string, expected: string[], expectedSites: string[]) => {
        it(title, async () => {
            (graph as any).testUser = user;
            const result = await secureNode.query({ ...edgesSelector({ code: true }, {}), totalCount: true }).execute();
            assert.deepEqual(
                result.edges.map((edge: any) => edge.node.code),
                expected,
            );
            assert.deepEqual(result.totalCount, expected.length);

            const siteResult = await siteNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();

            assert.deepEqual(
                siteResult.edges.map((edge: any) => edge.node.code),
                expectedSites,
            );
            assert.deepEqual(siteResult.totalCount, expectedSites.length);

            // note : _id = 3 => code = 'SITE2R1'
            const node = await secureNode.read({ code: true }, '3').execute();
            assert.equal(!!node, expected.includes('SITE2R1'));
        });
    };
    test(
        'authorization filter by site (all sites)',
        '<EMAIL>',
        ['SITE1R1', 'SITE1R2A1', 'SITE2R1', 'SITE3R1', 'SITE4R1A2'],
        ['SITE1', 'SITE2', 'SITE3', 'SITE4', 'SITE5'],
    );
    test(
        'authorization filter by site (sites 1 and 3 only)',
        '<EMAIL>',
        ['SITE1R1', 'SITE1R2A1', 'SITE3R1'],
        ['SITE1', 'SITE3'],
    );
    test('authorization filter by site (no sites - no access)', '<EMAIL>', [], []);
});

describe('apply access code security filter', () => {
    before(async () => {
        graph = await clientSetup({ application: await createApplicationWithApi({ nodes: { TestSite, TestSecure } }) });
        secureNode = graph.node('@sage/xtrem-core/testSecure');
        await initTables([
            { nodeConstructor: fixtures.nodes.TestSite, data: siteData },
            { nodeConstructor: fixtures.nodes.TestSecure, data: secureData },
        ]);
    });
    after(() => restoreTables());
    const test = (title: string, user: string, expected: string[]) => {
        it(title, async () => {
            (graph as any).testUser = user;
            const result = await secureNode.query({ ...edgesSelector({ code: true }, {}), totalCount: true }).execute();
            assert.deepEqual(
                result.edges.map((edge: any) => edge.node.code),
                expected,
            );
            assert.deepEqual(result.totalCount, expected.length);

            // note : _id = 3 => code = 'SITE2R1'
            const node = await secureNode.read({ code: true }, '3').execute();
            assert.equal(!!node, expected.includes('SITE2R1'));
        });
    };
    test('authorization filter by access code (all access codes)', '<EMAIL>', [
        'SITE1R1',
        'SITE1R2A1',
        'SITE2R1',
        'SITE3R1',
        'SITE4R1A2',
    ]);
    test('authorization filter by access code (1 and 2 only)', '<EMAIL>', ['SITE1R2A1', 'SITE4R1A2']);
    test('authorization filter by access (no access codes)', '<EMAIL>', []);
});

describe('apply factory security filter', () => {
    before(async () => {
        graph = await clientSetup({
            application: await createApplicationWithApi({
                nodes: {
                    TestSite,
                    TestSecure,
                    TestAnimal,
                    TestAnimalLine,
                    TestMammal,
                    TestFish,
                    TestDog,
                    TestCat,
                    TestPetOwner,
                    TestFlyBehavior,
                    TestSleepBehavior,
                    TestRefPropNoDataType,
                    TestRefPropWithDataType,
                },
            }),
        });
        secureNode = graph.node('@sage/xtrem-core/testSecure');

        animalNode = graph.node('@sage/xtrem-core/testAnimal');

        await initTables([
            { nodeConstructor: fixtures.nodes.TestSite, data: siteData },
            { nodeConstructor: fixtures.nodes.TestSecure, data: secureData },
            { nodeConstructor: TestFlyBehavior, data: dataAnimalFlyBehavior },
            { nodeConstructor: TestSleepBehavior, data: dataAnimalSleepBehavior },
            { nodeConstructor: TestFish, data: dataFishes },
            { nodeConstructor: TestDog, data: dataDogs },
            { nodeConstructor: TestCat, data: dataCats },
            { nodeConstructor: TestPetOwner, data: dataOwners },
        ]);
    });
    after(() => restoreTables());
    const test = (title: string, user: string, expected: { _constructor: string; strFromAnimal: string }[]) => {
        it(title, async () => {
            (graph as any).testUser = user;
            const result = await animalNode
                .query({ ...edgesSelector({ _constructor: true, strFromAnimal: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result.edges.map((edge: any) => edge.node),
                expected,
            );
            assert.deepEqual(result.totalCount, expected.length);
        });
    };
    test('authorization abstract node _factory filter - user has access to all subnodes', '<EMAIL>', [
        {
            _constructor: 'TestFish',
            strFromAnimal: 'fish(1)/animal',
        },
        {
            _constructor: 'TestFish',
            strFromAnimal: 'fish(2)/animal',
        },
        {
            _constructor: 'TestFish',
            strFromAnimal: 'fish(3)/animal',
        },
        {
            _constructor: 'TestFish',
            strFromAnimal: 'fish(4)/animal',
        },
        {
            _constructor: 'TestFish',
            strFromAnimal: 'fish(5)/animal',
        },
        {
            _constructor: 'TestDog',
            strFromAnimal: 'dog(6)/animal',
        },
        {
            _constructor: 'TestDog',
            strFromAnimal: 'dog(7)/animal',
        },
        {
            _constructor: 'TestDog',
            strFromAnimal: 'dog(8)/animal',
        },
        {
            _constructor: 'TestDog',
            strFromAnimal: 'dog(9)/animal',
        },
        {
            _constructor: 'TestDog',
            strFromAnimal: 'dog(10)/animal',
        },
        {
            _constructor: 'TestCat',
            strFromAnimal: 'cat(11)/animal',
        },
        {
            _constructor: 'TestCat',
            strFromAnimal: 'cat(12)/animal',
        },
        {
            _constructor: 'TestCat',
            strFromAnimal: 'cat(13)/animal',
        },
        {
            _constructor: 'TestCat',
            strFromAnimal: 'cat(14)/animal',
        },
        {
            _constructor: 'TestCat',
            strFromAnimal: 'cat(15)/animal',
        },
    ]);
    test(
        'authorization abstract node _factory filter - user does not have access to TestFish subnode',
        '<EMAIL>',
        [
            {
                _constructor: 'TestDog',
                strFromAnimal: 'dog(6)/animal',
            },
            {
                _constructor: 'TestDog',
                strFromAnimal: 'dog(7)/animal',
            },
            {
                _constructor: 'TestDog',
                strFromAnimal: 'dog(8)/animal',
            },
            {
                _constructor: 'TestDog',
                strFromAnimal: 'dog(9)/animal',
            },
            {
                _constructor: 'TestDog',
                strFromAnimal: 'dog(10)/animal',
            },
            {
                _constructor: 'TestCat',
                strFromAnimal: 'cat(11)/animal',
            },
            {
                _constructor: 'TestCat',
                strFromAnimal: 'cat(12)/animal',
            },
            {
                _constructor: 'TestCat',
                strFromAnimal: 'cat(13)/animal',
            },
            {
                _constructor: 'TestCat',
                strFromAnimal: 'cat(14)/animal',
            },
            {
                _constructor: 'TestCat',
                strFromAnimal: 'cat(15)/animal',
            },
        ],
    );
});
