import { edgesSelector, Graph, PagingOptions, querySelector, withoutEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import * as fixtures from '../fixtures';
import { ClientDocumentLine, DocumentInput, GraphApi } from '../fixtures/client-nodes/index';
import {
    clientSetup,
    createApplicationWithApi,
    documentData,
    documentLineData,
    initTables,
    referredData,
    restoreTables,
} from '../fixtures/index';
import { ComputedDocumentLine, TestDocument, TestDocumentLine, TestReferred } from '../fixtures/nodes';

let graph: Graph<GraphApi>;
let documentNode: GraphApi['@sage/xtrem-core/testDocument'];

const api = {
    nodes: {
        TestReferred,
        TestDocument,
        TestDocumentLine,
        ComputedDocumentLine,
    },
};

describe('graphql collection tests', () => {
    before(async () => {
        const application = await createApplicationWithApi(api);
        graph = await clientSetup({ application });
        documentNode = graph.node('@sage/xtrem-core/testDocument');

        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });
    after(() => restoreTables());

    it('query with collection', async () => {
        const result = await documentNode
            .query(
                edgesSelector({
                    _id: true,
                    description: true,
                    lines: querySelector({ lineNumber: true, description: true }),
                }),
            )
            .execute();
        const documents = withoutEdges(result);
        documents.forEach((t: any, i: any) => {
            assert.isObject(t);
            assert.equal(t._id, `${documentData[i]._id}`);
            assert.equal(t.description, documentData[i].description);
            const lines = t.lines;
            const linesData = documentLineData.filter(line => line.document === parseInt(t._id, 10));
            assert.equal(lines.query.length, linesData.length);
            lines.query.forEach((l: any, j: any) => {
                assert.equal(l.lineNumber, linesData[j].lineNumber);
                assert.equal(l.description, linesData[j].description);
            });
        });
    });

    const lineFilters: PagingOptions<ClientDocumentLine>[] = [{}, { first: 2 }];
    lineFilters.forEach(lineFilter => {
        it('query with collection and filter', async () => {
            const result = await documentNode
                .query(
                    edgesSelector({
                        _id: true,
                        code: true,
                        description: true,
                        lines: querySelector({ lineNumber: true, description: true }, lineFilter),
                    }),
                )
                .execute();
            const documents = withoutEdges(result);
            documents.forEach((t: any, i: any) => {
                assert.isObject(t);
                assert.equal(t.code, documentData[i].code);
                assert.equal(t.description, documentData[i].description);
                const lines = t.lines;
                const linesData = documentLineData.filter(line => line.document === parseInt(t._id, 10));
                if (lineFilter.first) {
                    assert.equal(lines.query.length, Math.min(linesData.length, lineFilter.first));
                } else {
                    assert.equal(lines.query.length, linesData.length);
                }
                lines.query.forEach((l: any, j: any) => {
                    assert.equal(l.lineNumber, linesData[j].lineNumber);
                    assert.equal(l.description, linesData[j].description);
                });
            });
        });
    });

    it('query with back reference', async () => {
        const result = await documentNode
            .query(
                edgesSelector({
                    _id: true,
                    lines: querySelector({ lineNumber: true, document: { _id: true } }),
                }),
            )
            .execute();
        const documents = withoutEdges(result);
        assert.equal(documents.length, documentData.length);
        documents.forEach((t: any, i: any) => {
            assert.isObject(t);
            assert.equal(t._id, `${documentData[i]._id}`);
            const lines = t.lines;
            const linesData = documentLineData.filter(line => line.document === parseInt(t._id, 10));
            assert.equal(lines.query.length, linesData.length);
            lines.query.forEach((l: any, j: any) => {
                assert.equal(l.lineNumber, linesData[j].lineNumber);
                assert.isObject(l.document);
                assert.equal(l.document._id, `${linesData[j].document}`);
            });
        });
    });

    async function testLinesPaging(
        linesProperty: 'lines' | 'filteredLines',
        options: PagingOptions<ClientDocumentLine>,
        lineNumbers: number[],
    ): Promise<void> {
        const result = await documentNode
            .query(
                edgesSelector(
                    {
                        code: true,
                        description: true,
                        [linesProperty]: querySelector({ lineNumber: true, description: true }, options),
                    },
                    {
                        filter: { code: { _eq: 'DOCC' } },
                    },
                ),
            )
            .execute();
        const documents = withoutEdges(result) as any;
        assert.equal(documents.length, 1);
        assert.isObject(documents[0]);
        const lines = documents[0][linesProperty];
        assert.deepEqual(
            lines.query.map((line: any) => line.lineNumber),
            lineNumbers,
        );
    }

    it('query with page limit on lines', async () => {
        await testLinesPaging('lines', { first: 2 }, [1, 2]);
        await testLinesPaging('lines', { filter: { description: { _eq: 'y' } } }, [2, 4, 5]);
        await testLinesPaging('lines', { orderBy: { description: -1, lineNumber: 1 } }, [3, 2, 4, 5, 1]);
        await testLinesPaging('lines', { orderBy: { description: -1, lineNumber: -1 } }, [3, 5, 4, 2, 1]);
    });

    it('query with page limit on filtered lines', async () => {
        await testLinesPaging('filteredLines', { first: 2 }, [1, 2]);
        await testLinesPaging('filteredLines', { filter: { description: { _eq: 'y' } } }, [2, 4, 5]);
        await testLinesPaging('filteredLines', { orderBy: { description: -1, lineNumber: 1 } }, [3, 2, 4, 5, 1]);
        await testLinesPaging('filteredLines', { orderBy: { description: -1, lineNumber: -1 } }, [3, 5, 4, 2, 1]);
    });

    it('can create with collection', async () => {
        const inputData: DocumentInput = {
            code: 'DOCZ',
            description: 'document Z',
            mandatoryReference: referredData[0]._id,
            lines: [
                {
                    lineNumber: 1,
                    description: 'line Z1',
                },
                {
                    lineNumber: 2,
                    description: 'line Z2',
                },
            ],
        };
        const document = await documentNode
            .create(
                {
                    code: true,
                    description: true,
                    lines: querySelector({ lineNumber: true, description: true }),
                },
                {
                    data: inputData,
                },
            )
            .execute();
        assert.deepEqual(
            {
                ...document,
                lines: document.lines.query.edges.map((edge: any) => edge.node),
            },
            {
                code: inputData.code,
                description: inputData.description,
                lines: inputData.lines,
            },
        );
    });

    it('can update with collection', async () => {
        const inputData = {
            code: 'DOCC',
            description: 'document C updated',
            lines: [
                {
                    _id: '5',
                    lineNumber: 2,
                },
                {
                    lineNumber: 6,
                    description: 'created by update',
                },
            ],
        };
        const document = await documentNode
            .updateById(
                {
                    code: true,
                    description: true,
                    lines: querySelector({ lineNumber: true, description: true }),
                },
                {
                    _id: '3',
                    data: inputData,
                },
            )
            .execute();
        assert.deepEqual(
            {
                ...document,
                lines: document.lines.query.edges.map((edge: any) => edge.node),
            },
            {
                code: 'DOCC',
                description: 'document C updated',
                lines: [
                    {
                        lineNumber: 2,
                        description: 'y',
                    },
                    {
                        lineNumber: 6,
                        description: 'created by update',
                    },
                ],
            },
        );
    });

    after(() => restoreTables());
});
