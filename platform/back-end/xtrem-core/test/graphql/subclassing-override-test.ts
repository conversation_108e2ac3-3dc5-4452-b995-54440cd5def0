import { assert } from 'chai';
import { testSubclassingOverrideApplication } from '..';
import { asyncArray, decorators, Node, Reference, Test } from '../../lib';
import {
    createApplicationWithApi,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    restoreTables,
    TestInitData,
} from '../fixtures/index';
import {
    TestOverrideAnimal,
    TestOverrideAnimalOwner,
    TestOverrideBaseDocument,
    TestOverrideBaseDocumentLine,
    TestOverrideCow,
    TestOverrideCowOwner,
    TestOverrideHorse,
    TestOverrideHorseOwner,
    TestOverrideInvoice,
    TestOverrideInvoiceLine,
} from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const horsesData: TestInitData<TestOverrideHorse>[] = [];
const cowsData: TestInitData<TestOverrideCow>[] = [];

const horseOwnersData: TestInitData<TestOverrideHorseOwner>[] = [{ _id: 1, racesWon: 10 }];
const cowOwnersData: TestInitData<TestOverrideCowOwner>[] = [{ _id: 2 }];

for (let i = 0; i < 5; i += 1) {
    if (i < 3) horsesData.push({ _id: i + 1, name: `horse ${i + 1}`, owner: 1, bestRaceResult: 3 * i + 1 });
    else cowsData.push({ _id: i + 1, name: `cow ${i + 1}`, owner: 2 });
}

const favoritesData = [
    { ownerId: 1, animalId: 2 },
    { ownerId: 2, animalId: 4 },
];

// Classes to test invalid override type
@decorators.subNode<TestOverrideBad>({
    extends: () => TestOverrideAnimal,
    isPublished: true,
})
class TestOverrideBad extends TestOverrideAnimal {
    @decorators.referencePropertyOverride<TestOverrideBad, 'owner'>({
        node: () => TestOverrideBadOwner,
    })
    // Type as any because compiler will detect that TestOverrideBadOwner is invalid here
    override readonly owner: Reference<any>;
}

// This one is not a subclass of TestOverrideAnimalOwner
@decorators.node<TestOverrideBadOwner>({
    isPublished: true,
    canDeleteMany: true,
})
class TestOverrideBadOwner extends Node {}

describe('GraphQL subclassing - reference and collection overrides', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testSubclassingOverrideApplication.application });
        await initTables([
            { nodeConstructor: TestOverrideHorseOwner, data: horseOwnersData },
            { nodeConstructor: TestOverrideCowOwner, data: cowOwnersData },
            { nodeConstructor: TestOverrideHorse, data: horsesData },
            { nodeConstructor: TestOverrideCow, data: cowsData },
            { nodeConstructor: TestOverrideBaseDocument, data: [] },
            { nodeConstructor: TestOverrideBaseDocumentLine, data: [] },
            { nodeConstructor: TestOverrideInvoice, data: [] },
            { nodeConstructor: TestOverrideInvoiceLine, data: [] },
        ]);
        await Test.withCommittedContext(async context => {
            await asyncArray(favoritesData).forEach(async favorite => {
                const owner = await context.read(
                    TestOverrideAnimalOwner,
                    { _id: favorite.ownerId },
                    { forUpdate: true },
                );
                await owner.$.set({ favoriteAnimal: favorite.animalId });
                await owner.$.save();
            });
        });
    });

    describe('Reference override', () => {
        it('Can query all animals', async () => {
            const result = (await graphqlHelper.query(
                `
            {
                testOverrideAnimal {
                    query {
                        totalCount
                        edges {
                            node {
                                name
                                owner {
                                    favoriteAnimal {
                                        name
                                    }
                                    ...on TestOverrideHorseOwner {
                                        racesWon
                                    }
                               }
                            }
                        }
                    }
                }
            }`,
            )) as any;
            assert.deepEqual(result.testOverrideAnimal.query, {
                totalCount: 5,
                edges: [
                    { node: { name: 'horse 1', owner: { favoriteAnimal: { name: 'horse 2' }, racesWon: 10 } } },
                    { node: { name: 'horse 2', owner: { favoriteAnimal: { name: 'horse 2' }, racesWon: 10 } } },
                    { node: { name: 'horse 3', owner: { favoriteAnimal: { name: 'horse 2' }, racesWon: 10 } } },
                    { node: { name: 'cow 4', owner: { favoriteAnimal: { name: 'cow 4' } } } },
                    { node: { name: 'cow 5', owner: { favoriteAnimal: { name: 'cow 4' } } } },
                ],
            });
        });

        it('Can query horses only', async () => {
            const result = (await graphqlHelper.query(
                `
            {
                testOverrideHorse {
                    query {
                        totalCount
                        edges {
                            node {
                                name
                                owner {
                                    favoriteAnimal {
                                        name
                                    }
                                    racesWon
                               }
                            }
                        }
                    }
                }
            }`,
            )) as any;
            assert.deepEqual(result.testOverrideHorse.query, {
                totalCount: 3,
                edges: [
                    { node: { name: 'horse 1', owner: { favoriteAnimal: { name: 'horse 2' }, racesWon: 10 } } },
                    { node: { name: 'horse 2', owner: { favoriteAnimal: { name: 'horse 2' }, racesWon: 10 } } },
                    { node: { name: 'horse 3', owner: { favoriteAnimal: { name: 'horse 2' }, racesWon: 10 } } },
                ],
            });
        });
    });

    describe('Collection override', () => {
        it('Can query animals of all owners', async () => {
            const result = (await graphqlHelper.query(
                `
            {
                testOverrideAnimalOwner {
                    query {
                        totalCount
                        edges {
                            node {
                                favoriteAnimal {
                                    name
                                }
                                ...on TestOverrideHorseOwner {
                                    racesWon
                                }
                                animals: anyAnimals {
                                    query {
                                        edges {
                                            node {
                                                name
                                                ...on TestOverrideHorse {
                                                    bestRaceResult
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
            )) as any;
            assert.deepEqual(result.testOverrideAnimalOwner.query, {
                totalCount: 2,
                edges: [
                    {
                        node: {
                            favoriteAnimal: { name: 'horse 2' },
                            racesWon: 10,
                            animals: {
                                query: {
                                    edges: [
                                        { node: { name: 'horse 1', bestRaceResult: 1 } },
                                        { node: { name: 'horse 2', bestRaceResult: 4 } },
                                        { node: { name: 'horse 3', bestRaceResult: 7 } },
                                    ],
                                },
                            },
                        },
                    },
                    {
                        node: {
                            favoriteAnimal: { name: 'cow 4' },
                            animals: { query: { edges: [{ node: { name: 'cow 4' } }, { node: { name: 'cow 5' } }] } },
                        },
                    },
                ],
            });
        });

        it('Can query horses of all horse owners', async () => {
            const result = (await graphqlHelper.query(
                `
            {
                testOverrideHorseOwner {
                    query {
                        totalCount
                        edges {
                            node {
                                favoriteAnimal {
                                    name
                                }
                                racesWon
                                animals {
                                    query {
                                        edges {
                                            node {
                                                name
                                                bestRaceResult
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
            )) as any;
            assert.deepEqual(result.testOverrideHorseOwner.query, {
                totalCount: 1,
                edges: [
                    {
                        node: {
                            favoriteAnimal: { name: 'horse 2' },
                            racesWon: 10,
                            animals: {
                                query: {
                                    edges: [
                                        { node: { name: 'horse 1', bestRaceResult: 1 } },
                                        { node: { name: 'horse 2', bestRaceResult: 4 } },
                                        { node: { name: 'horse 3', bestRaceResult: 7 } },
                                    ],
                                },
                            },
                        },
                    },
                ],
            });
        });
    });

    it('Can filter on a overridden collection', async () => {
        const result = (await graphqlHelper.query(
            `
        {
            testOverrideHorseOwner {
                query(filter: "{ animals: { _atLeast: 2, name: { _regex: 'horse' }, bestRaceResult: { _gt: 3 } } }") {
                    totalCount
                    edges { node { racesWon } }
                }
            }
        }`,
        )) as any;

        assert.deepEqual(result.testOverrideHorseOwner.query, {
            totalCount: 1,
            edges: [{ node: { racesWon: 10 } }],
        });
    });

    describe('Mutations', () => {
        it('Can create an invoice', async () => {
            const result = (await graphqlHelper.mutation(
                `
            {
                testOverrideInvoice {
                    create(data: {
                        id: "INVOICE-1",
                        isClosed: true,
                        lines: [
                            { quantity: 2, date: "2022-11-15" },
                            { quantity: 5, date: "2022-11-10" }
                        ]
                    }) { id, isClosed, lines {query { edges { node { quantity, date } } } } }
                }
            }`,
            )) as any;
            assert.deepEqual(result.testOverrideInvoice.create, {
                id: 'INVOICE-1',
                isClosed: true,
                lines: {
                    query: {
                        edges: [
                            { node: { quantity: 2, date: '2022-11-15' } },
                            { node: { quantity: 5, date: '2022-11-10' } },
                        ],
                    },
                },
            });
        });

        it('Can update an invoice', async () => {
            const result = (await graphqlHelper.mutation(
                `
            {
                testOverrideInvoice {
                    update(data: {
                        _id: "1",
                        isClosed: false,
                        lines: [
                            { _action: "create", quantity: 8, date: "2022-11-12" },
                        ]
                    }) { id, isClosed, lines {query { edges { node { quantity, date } } } } }
                }
            }`,
            )) as any;
            assert.deepEqual(result.testOverrideInvoice.update, {
                id: 'INVOICE-1',
                isClosed: false,
                lines: {
                    query: {
                        edges: [
                            { node: { quantity: 2, date: '2022-11-15' } },
                            { node: { quantity: 5, date: '2022-11-10' } },
                            { node: { quantity: 8, date: '2022-11-12' } },
                        ],
                    },
                },
            });
        });

        it('Can delete an invoice', async () => {
            const result = (await graphqlHelper.mutation('{ testOverrideInvoice { delete(_id: "1") } }')) as any;
            assert.deepEqual(result.testOverrideInvoice.delete, 1);
        });
    });

    describe('Invalid reference override', () => {
        it('throws if reference override is not a subclass', async () => {
            await assert.isRejected(
                createApplicationWithApi({
                    nodes: { TestOverrideAnimal, TestOverrideAnimalOwner, TestOverrideBad, TestOverrideBadOwner },
                }),
                "TestOverrideBad.owner: invalid override: 'TestOverrideBadOwner' is not a subclass of 'TestOverrideAnimalOwner'",
            );
        });
    });

    after(() => restoreTables());
});
