import { edgesSelector, Graph, withoutEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import { merge } from 'lodash';
import { testDatatypesApplication } from '..';
import {
    date,
    dateRange,
    datetime,
    datetimeRange,
    decimalRange,
    integerRange,
    MAX_INT_32,
    MIN_INT_32,
    Uuid,
} from '../../lib/index';
import * as fixtures from '../fixtures';
import { GraphApi } from '../fixtures/client-nodes';
import { clientSetup, datatypesData, initTables, restoreTables } from '../fixtures/index';

let graph: Graph<GraphApi>;
let datatypesNode: GraphApi['@sage/xtrem-core/testDatatypes'];

const nullify = (x: any) => (x === undefined ? null : x);
let lastId = datatypesData.length - 1;

describe('testDatatypes tests (graphql)', () => {
    before(async () => {
        graph = await clientSetup({ application: await testDatatypesApplication.application });
        datatypesNode = graph.node('@sage/xtrem-core/testDatatypes');
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
    });
    it('basic query on testDatatypes', async () => {
        const result = await datatypesNode
            .query(
                edgesSelector({
                    id: true,
                    booleanVal: true,
                    enumVal: true,
                    shortVal: true,
                    integerVal: true,
                    decimalVal: true,
                    floatVal: true,
                    doubleVal: true,
                    dateVal: true,
                    datetimeVal: true,
                    stringVal: true,
                    uuidVal: true,
                    textStream: { value: true },
                    binaryStream: { value: true },
                    jsonVal: true,
                    integerRangeVal: true,
                    decimalRangeVal: true,
                    dateRangeVal: true,
                    datetimeRangeVal: true,
                    integerArrayVal: true,
                    enumArrayVal: true,
                    stringArrayVal: true,
                }),
            )
            .execute();
        const testDatatypes = withoutEdges(result);
        assert.equal(testDatatypes.length, datatypesData.length);
        testDatatypes.forEach((t: any, i: any) => {
            const data = datatypesData[i];
            assert.deepEqual(
                { ...t, jsonVal: JSON.parse(t.jsonVal) },
                {
                    id: i,
                    booleanVal: nullify(data.booleanVal),
                    // TODO: why do we need any ??
                    enumVal: data.enumVal || null,
                    shortVal: data.shortVal,
                    integerVal: data.integerVal,
                    decimalVal: data.decimalVal.toString(),
                    floatVal: data.floatVal,
                    doubleVal: data.doubleVal,
                    dateVal: data.dateVal && data.dateVal.toString(),
                    datetimeVal: data.datetimeVal && data.datetimeVal.toString(),
                    stringVal: data.stringVal,
                    uuidVal: data.uuidVal.toString(),
                    textStream: data.textStream ? { value: data.textStream.value } : null,
                    binaryStream: data.binaryStream ? { value: data.binaryStream.value.toString('base64') } : null,
                    jsonVal: data.jsonVal,
                    integerRangeVal: data.integerRangeVal == null ? null : data.integerRangeVal.toString(),
                    decimalRangeVal: data.decimalRangeVal == null ? null : data.decimalRangeVal.toString(),
                    dateRangeVal: data.dateRangeVal == null ? null : data.dateRangeVal.toString(),
                    datetimeRangeVal: data.datetimeRangeVal == null ? null : data.datetimeRangeVal.toString(),
                    integerArrayVal: data.integerArrayVal,
                    stringArrayVal: data.stringArrayVal,
                    enumArrayVal: data.enumArrayVal || [],
                },
            );
        });
    });

    it('returns computed value', async () => {
        const result = await datatypesNode
            .query(
                edgesSelector({
                    id: true,
                    integerVal: true,
                    computed: true,
                    complexComputed: true,
                }),
            )
            .execute();
        const testDatatypes = withoutEdges(result);
        assert.equal(testDatatypes.length, datatypesData.length);
        testDatatypes.forEach((t: any, i: any) => {
            assert.equal(t.id, i);
            assert.equal(t.integerVal, datatypesData[i].integerVal);
            assert.equal(t.computed, t.id * t.integerVal);
            assert.equal(t.complexComputed, 10);
        });
    });

    async function testCreate(forceStringValues: boolean): Promise<void> {
        const uuid = Uuid.generate().toString();
        const base64Value = Buffer.from('bye').toString('base64');
        lastId += 1;
        const data = {
            id: lastId,
            booleanVal: true,
            enumVal: 'value2',
            shortVal: forceStringValues ? '5' : 5,
            integerVal: forceStringValues ? '100000' : 100000,
            decimalVal: forceStringValues ? '123.45' : 123.45,
            floatVal: forceStringValues ? '123.5' : 123.5,
            doubleVal: forceStringValues ? ' 1234567890.25' : 1234567890.25,
            dateVal: date.make(2010, 3, 1).toString(),
            datetimeVal: datetime.make(2010, 3, 1, 14, 5, 30).toString(),
            stringVal: `created_${lastId}`,
            uuidVal: uuid,
            textStream: { value: 'hello world' },
            binaryStream: { value: base64Value },
            jsonVal: { id: lastId, isValidated: true, text: { fr: `chaîne%_$${lastId}`, en: `string%_$${lastId}` } },
            integerRangeVal: integerRange.make(99, 101).toString(),
            decimalRangeVal: decimalRange.make('99.001', '101.0004').toString(),
            dateRangeVal: dateRange.make(date.make(2010, 3, 1), date.make(2010, 3, 4)).toString(),
            datetimeRangeVal: datetimeRange
                .make(datetime.make(2010, 3, 1, 0, 0, 0), datetime.make(2010, 3, 4, 23, 59, 59))
                .toString(),
            integerArrayVal: forceStringValues ? ['1', '2', '3'] : [1, 2, 3],
            stringArrayVal: [`created_${lastId}_1`, `created_${lastId}_'2`, `created_${lastId}_"3`],
            enumArrayVal: ['arrayVal1', 'arrayVal2', 'arrayVal1'],
        };

        const result = await datatypesNode
            .create(
                {
                    id: true,
                    booleanVal: true,
                    enumVal: true,
                    shortVal: true,
                    integerVal: true,
                    decimalVal: true,
                    floatVal: true,
                    doubleVal: true,
                    dateVal: true,
                    datetimeVal: true,
                    stringVal: true,
                    uuidVal: true,
                    textStream: { value: true },
                    binaryStream: { value: true },
                    jsonVal: true,
                    integerRangeVal: true,
                    decimalRangeVal: true,
                    dateRangeVal: true,
                    datetimeRangeVal: true,
                    integerArrayVal: true,
                    stringArrayVal: true,
                    enumArrayVal: true,
                },
                {
                    data: {
                        ...data,
                        jsonVal: JSON.stringify(data.jsonVal || null),
                    } as any, // cast needed because of enum
                },
            )
            .execute();
        assert.deepEqual(
            { ...result, jsonVal: JSON.parse(result.jsonVal) },
            {
                id: lastId,
                booleanVal: true,
                enumVal: 'value2',
                shortVal: 5,
                integerVal: 100000,
                decimalVal: '123.45',
                floatVal: 123.5,
                doubleVal: 1234567890.25,
                dateVal: date.make(2010, 3, 1).toString(),
                datetimeVal: datetime.make(2010, 3, 1, 14, 5, 30).toString(),
                stringVal: `created_${lastId}`,
                uuidVal: uuid,
                textStream: { value: 'hello world' },
                binaryStream: { value: base64Value },
                jsonVal: {
                    id: lastId,
                    isValidated: true,
                    text: { fr: `chaîne%_$${lastId}`, en: `string%_$${lastId}` },
                },
                integerRangeVal: integerRange.make(99, 101).toString(),
                decimalRangeVal: decimalRange.make('99.001', '101.0004').toString(),
                dateRangeVal: dateRange.make(date.make(2010, 3, 1), date.make(2010, 3, 4)).toString(),
                datetimeRangeVal: datetimeRange
                    .make(datetime.make(2010, 3, 1, 0, 0, 0), datetime.make(2010, 3, 4, 23, 59, 59))
                    .toString(),
                integerArrayVal: [1, 2, 3],
                stringArrayVal: [`created_${lastId}_1`, `created_${lastId}_'2`, `created_${lastId}_"3`],
                enumArrayVal: ['arrayVal1', 'arrayVal2', 'arrayVal1'],
            },
        );
    }

    it('basic create on testDatatypes table', async () => {
        await testCreate(true);
        await testCreate(false);
    });

    it('can update all testDatatypes', async () => {
        const beforeUpdate = await datatypesNode
            .read(
                {
                    _id: true,
                    id: true,
                    booleanVal: true,
                    enumVal: true,
                    shortVal: true,
                    integerVal: true,
                    decimalVal: true,
                    floatVal: true,
                    doubleVal: true,
                    dateVal: true,
                    datetimeVal: true,
                    stringVal: true,
                    uuidVal: true,
                    textStream: { value: true },
                    binaryStream: { value: true },
                    jsonVal: true,
                    integerRangeVal: true,
                    decimalRangeVal: true,
                    dateRangeVal: true,
                    datetimeRangeVal: true,
                    integerArrayVal: true,
                    stringArrayVal: true,
                    enumArrayVal: true,
                    computed: true,
                    computedCached: true,
                },
                '3',
            )
            .execute();
        assert.equal(beforeUpdate.id, 2);
        assert.equal(beforeUpdate.enumVal, 'value3');

        const beforeUpdateJsonVal = JSON.parse(beforeUpdate.jsonVal);
        const afterUpdateJsonVal = merge(beforeUpdateJsonVal, { text: { es: `chain%_$${beforeUpdate.id}` } });
        const beforeUpdateDateRangeVal = beforeUpdate.dateRangeVal && dateRange.parse(beforeUpdate.dateRangeVal);
        const beforeUpdateDatetimeRangeVal =
            beforeUpdate.datetimeRangeVal && datetimeRange.parse(beforeUpdate.datetimeRangeVal);

        assert.equal(beforeUpdate.computed, beforeUpdate.computedCached);

        const updateData = {
            id: beforeUpdate.id,
            booleanVal: !beforeUpdate.booleanVal,
            enumVal: 'value1',
            shortVal: beforeUpdate.shortVal + 1,
            integerVal: -beforeUpdate.integerVal + 2,
            decimalVal: `${parseFloat(beforeUpdate.decimalVal) + 3}`,
            floatVal: beforeUpdate.floatVal + 4,
            doubleVal: beforeUpdate.doubleVal + 5,
            dateVal: date.parse(beforeUpdate.dateVal!).addDays(6).toString(),
            datetimeVal: datetime.parse(beforeUpdate.datetimeVal!).addMinutes(7).toString(),
            stringVal: `updated_${beforeUpdate.id}`,
            uuidVal: Uuid.generate().toString(),
            textStream: { value: 'bonjour monde' },
            binaryStream: { value: Buffer.from('au revoir').toString('base64') },
            jsonVal: JSON.stringify(afterUpdateJsonVal),
            integerRangeVal: integerRange.make(99, 101).toString(),
            decimalRangeVal: decimalRange.make('99.001', '101.0004').toString(),
            dateRangeVal:
                beforeUpdateDateRangeVal &&
                dateRange
                    .make(
                        beforeUpdateDateRangeVal.start?.addDays(6) || null,
                        beforeUpdateDateRangeVal.end?.addDays(6) || null,
                    )
                    .toString(),
            datetimeRangeVal:
                beforeUpdateDatetimeRangeVal &&
                datetimeRange
                    .make(
                        beforeUpdateDatetimeRangeVal.start?.addMinutes(7) || null,
                        beforeUpdateDatetimeRangeVal.end?.addMinutes(7) || null,
                    )
                    .toString(),
            integerArrayVal: beforeUpdate.integerArrayVal.map((n: number) => -n + 2),
            stringArrayVal: [
                `updated_${beforeUpdate.id}_1`,
                `updated_${beforeUpdate.id}_2`,
                `updated_${beforeUpdate.id}_3`,
            ],
            enumArrayVal: ['arrayVal2', 'arrayVal1', 'arrayVal2'],
        };

        const afterUpdate = await datatypesNode
            .updateById(
                {
                    id: true,
                    booleanVal: true,
                    enumVal: true,
                    shortVal: true,
                    integerVal: true,
                    decimalVal: true,
                    floatVal: true,
                    doubleVal: true,
                    dateVal: true,
                    datetimeVal: true,
                    stringVal: true,
                    uuidVal: true,
                    textStream: { value: true },
                    binaryStream: { value: true },
                    jsonVal: true,
                    integerRangeVal: true,
                    decimalRangeVal: true,
                    dateRangeVal: true,
                    datetimeRangeVal: true,
                    integerArrayVal: true,
                    stringArrayVal: true,
                    enumArrayVal: true,
                    computedCached: true,
                },
                {
                    _id: '3',
                    data: updateData as any,
                },
            )
            .execute();
        assert.deepEqual(
            {
                ...afterUpdate,
                jsonVal: JSON.parse(afterUpdate.jsonVal),
            },
            {
                ...updateData,
                computedCached: updateData.id * updateData.integerVal,
                jsonVal: JSON.parse(updateData.jsonVal),
            } as any,
        );
    });

    it('can set a stringArray to null', async () => {
        const beforeUpdate = await datatypesNode
            .read(
                {
                    _id: true,
                    id: true,
                    booleanVal: true,
                    enumVal: true,
                    shortVal: true,
                    integerVal: true,
                    decimalVal: true,
                    floatVal: true,
                    doubleVal: true,
                    dateVal: true,
                    datetimeVal: true,
                    stringVal: true,
                    uuidVal: true,
                    textStream: { value: true },
                    binaryStream: { value: true },
                    jsonVal: true,
                    integerRangeVal: true,
                    decimalRangeVal: true,
                    dateRangeVal: true,
                    datetimeRangeVal: true,
                    integerArrayVal: true,
                    stringArrayVal: true,
                    enumArrayVal: true,
                },
                '3',
            )
            .execute();
        assert.equal(beforeUpdate.id, 2);

        const updateData = {
            id: beforeUpdate.id,
            stringArrayVal: null,
        };

        const afterUpdate = await datatypesNode
            .updateById(
                {
                    id: true,
                    stringArrayVal: true,
                },
                {
                    _id: '3',
                    data: updateData as any,
                },
            )
            .execute();
        assert.isArray(afterUpdate.stringArrayVal);
        assert.equal(afterUpdate.stringArrayVal.length, 0);
    });

    it('can delete a datatype', async () => {
        let deleted: number;
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);

        deleted = await datatypesNode.deleteById('2').execute();
        assert.equal(deleted, 1);
        // try again with same _id
        deleted = await datatypesNode.deleteById('2').execute();
        assert.equal(deleted, 0);
    });

    it('can read by _id', async () => {
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
        const readData = await datatypesNode
            .read(
                {
                    _id: true,
                    id: true,
                    booleanVal: true,
                    enumVal: true,
                    shortVal: true,
                    integerVal: true,
                    decimalVal: true,
                    floatVal: true,
                    doubleVal: true,
                    dateVal: true,
                    datetimeVal: true,
                    stringVal: true,
                    uuidVal: true,
                    textStream: { value: true },
                    binaryStream: { value: true },
                    jsonVal: true,
                    integerRangeVal: true,
                    decimalRangeVal: true,
                    dateRangeVal: true,
                    datetimeRangeVal: true,
                    integerArrayVal: true,
                    stringArrayVal: true,
                    enumArrayVal: true,
                },
                '3',
            )
            .execute();

        assert.equal(readData._id, '3');
        assert.equal(readData.id, 2);
        assert.equal(readData.enumVal, 'value3');
        readData.integerArrayVal.forEach((val, i) => {
            assert.equal(val, [2, 3, 4][i]);
        });

        (readData.enumArrayVal as any[]).forEach((val, i) => {
            assert.equal(val, ['arrayVal1', 'arrayVal3', 'arrayVal1'][i]);
        });

        (readData.stringArrayVal as any[]).forEach((val, i) => {
            assert.equal(val, ['string%_$2', 'string%_$3', 'string%_$4'][i]);
        });

        assert.deepEqual(JSON.parse(readData.jsonVal), datatypesData[2].jsonVal);
    });

    async function testGrahQlInt(integerVal: number): Promise<{ id: number; integerVal: number }> {
        lastId += 1;
        const data = {
            id: lastId,
            integerVal,
        };

        const result = await datatypesNode
            .create(
                {
                    id: true,
                    integerVal: true,
                },
                {
                    data: {
                        ...data,
                    } as any, // cast needed because of enum
                },
            )
            .execute();
        return result;
    }

    it('can insert an integer lesser or equal to  MAX_INT_32 or greater or equal to MIN_INT_32', async () => {
        const created1 = await testGrahQlInt(MAX_INT_32);
        assert.equal(created1.integerVal, MAX_INT_32);

        const created2 = await testGrahQlInt(MIN_INT_32);
        assert.equal(created2.integerVal, MIN_INT_32);
    });

    it('cannot insert an integer greater than MAX_INT_32 or lesser than MIN_INT_32', async () => {
        await assert.isRejected(
            testGrahQlInt(MAX_INT_32 + 1),
            `Int cannot represent non 32-bit signed integer value: ${MAX_INT_32 + 1}`,
        );

        await assert.isRejected(
            testGrahQlInt(MIN_INT_32 - 1),
            `Int cannot represent non 32-bit signed integer value: ${MIN_INT_32 - 1}`,
        );
    });

    after(() => restoreTables());
});
