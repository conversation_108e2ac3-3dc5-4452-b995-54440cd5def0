import { Logger } from '@sage/xtrem-log';
import { expect } from 'chai';
import * as sinon from 'sinon';
import { testDataSensitivityLogApplication } from '..';
import { Test } from '../../lib';
import { GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestDataSensitivityLog, TestDelegatedTo, TestDelegatedToReference } from '../fixtures/nodes';

const testDelegatedToReference = [
    {
        _id: 1,
        _values_hash: '1',
        text: '001',
        bool: true,
        textSensitive: 'Sensitive collection testDelegatedToReference should not show 1',
        boolSensitive: false,
    },
];

const testDataSensitivityLog = [
    {
        _id: 1,
        code: '001',
        stringSensitive: 'Sensitive should not show 1',
        stringRegular: 'Show in clear 1',
        delegate: 1,
    },
];

const testDelegatedTo = [
    {
        testDataSensitivityLog: 1,
        testDelegatedToReference: 1,
    },
];

const sandbox = sinon.createSandbox();
let graphqlHelper: GraphQlHelper;

describe('Test mask data in log', () => {
    const loggerInternalsOld = {} as {
        _logAsJson?: boolean;
        isDisabled?: boolean;
    };
    function getLogTemplateSpy() {
        (Logger as any)._logAsJson = true;
        (Logger as any).isDisabled = false;
        return sandbox.spy((Logger as any).consoleFormatter, 'template');
    }

    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDataSensitivityLogApplication.application });
        await initTables([
            {
                nodeConstructor: TestDelegatedToReference,
                data: testDelegatedToReference,
            },
            {
                nodeConstructor: TestDataSensitivityLog,
                data: testDataSensitivityLog,
            },
            {
                nodeConstructor: TestDelegatedTo,
                data: testDelegatedTo,
            },
        ]);
        loggerInternalsOld._logAsJson = (Logger as any)._logAsJson;
        loggerInternalsOld.isDisabled = (Logger as any).isDisabled;
    });

    afterEach(() => {
        (Logger as any)._logAsJson = loggerInternalsOld._logAsJson;
        (Logger as any).isDisabled = loggerInternalsOld.isDisabled;
        sandbox.restore();
    });

    it('Can mask data for creation - 1', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    `mutation { xtremCore { testDataSensitivityLog { create(data: {
                        code: "004",
                        stringSensitive: "Sensitive should not show 4",

                        text: "002",
                        bool: true,
                        textSensitive: "Sensitive collection testDelegatedToReference should not show 2",
                        boolSensitive: false,

                        lines:[{
                            text: "004",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 4",
                            boolSensitive: false,
                        },{
                            text: "003",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 3",
                            boolSensitive: false,
                        }]
                    })  {code } } } }`,
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"data":{"code":"***","stringSensitive":"***","text":"***","bool":"***","textSensitive":"***","boolSensitive":"***","lines":[{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"},{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"}]}}',
                );
            }
        }));
    it('Can mask data for creation - 2', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    `mutation { xtremCore { testDataSensitivityLog { create(data: {
                        code: "004",
                        stringSensitive: "Sensitive should not show 4",
                        delegate:{
                            text: "002",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 2",
                            boolSensitive: false,
                        }
                        lines:[{
                            text: "004",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 4",
                            boolSensitive: false,
                        },{
                            text: "003",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 3",
                            boolSensitive: false,
                        }]
                    })  {code } } } }`,
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"data":{"code":"***","stringSensitive":"***","delegate":{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"},"lines":[{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"},{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"}]}}',
                );
            }
        }));
    it('Can mask data for creation - 3', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    `mutation { xtremCore { testDataSensitivityLog { create(data: {
                        code: "004",
                        stringSensitive: "Sensitive should not show 4",
                        delegate:{_id:"1"}
                    })  {code } } } }`,
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"data":{"code":"***","stringSensitive":"***","delegate":{"_id":"***"}}}',
                );
            }
        }));
    it('Can mask data for update', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    `mutation { xtremCore { testDataSensitivityLog { create(data: {
                        code: "002",
                        stringSensitive: "Sensitive should not show 2",
                        stringRegular: "Show in clear 2",
                        text: "002",
                        bool: true,
                        textSensitive: "Sensitive collection testDelegatedToReference should not show 2",
                        boolSensitive: false,

                        lines:[{
                            text: "004",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 4",
                            boolSensitive: false,
                        },{
                            text: "003",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 3",
                            boolSensitive: false,
                        }]
                    })  {code } } } }`,
                );

                await graphqlHelper.execute(
                    `mutation { xtremCore { testDataSensitivityLog { update(data: {
                        _id: "2",
                        code: "002 UPDATED",
                        stringSensitive: "Sensitive should not show 2 UPDATED",
                        stringRegular:"",
                        text: "002",
                        bool: true,
                        textSensitive: "Sensitive collection testDelegatedToReference should not show 2",
                        boolSensitive: false,

                        lines:[{
                            text: "004",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 4",
                            boolSensitive: false,
                        },{
                            text: "003",
                            bool: true,
                            textSensitive: "Sensitive collection testDelegatedToReference should not show 3",
                            boolSensitive: false,
                        }]
                    })  {code } } } }`,
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"data":{"_id":"***","code":"***","stringRegular":"***","stringSensitive":"***","text":"***","bool":"***","textSensitive":"***","boolSensitive":"***","lines":[{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"},{"text":"***","bool":"***","textSensitive":"***","boolSensitive":"***"}]}}',
                );
            }
        }));

    it('Can mask data for delete', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    'mutation { xtremCore { testDataSensitivityLog { deleteById(_id: "1") } } }',
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal('input data: {"_id":"***"}');
            }
        }));
    it('Can mask data for custom query', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    'query { xtremCore { testDataSensitivityLog { customQuery(searchCriteria: { item: 1, itemList: [{status: "5"},{status: "7"}] } )} } }',
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"searchCriteria":{"item":"***","itemList":[{"status":"***"},{"status":"***"}]}}',
                );
            }
        }));

    it('Can mask data for query', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    `{
                        xtremCore {
                            testDataSensitivityLog {
                                query(filter: "{_id: { _in: [1,2,3]}}", orderBy: "{_id: 3}") {
                                    edges {
                                        node {
                                            _id
                                        }
                                    }
                                }
                            }
                        }
                    }
                    `,
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"filter":{"_id":{"_in":["***","***","***"]}},"orderBy":"***"}',
                );
            }
        }));
    it('Can mask data for custom mutation', () =>
        Test.withContext(async () => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.execute(
                    'mutation { xtremCore { testDataSensitivityLog { customMutation (searchCriteria: { item: 1, itemList: [{status: "5"},{status: "7"}]}) } } }',
                );
            } catch {
                const jsonError = spy.returnValues
                    .map(r => JSON.parse(r))
                    .find(
                        r =>
                            r.domain === 'xtrem-core/graphql' &&
                            r.logLevel === 'ERROR' &&
                            r.message.includes('input data:'),
                    );
                expect(jsonError.message).to.be.equal(
                    'input data: {"searchCriteria":{"item":"***","itemList":[{"status":"***"},{"status":"***"}]}}',
                );
            }
        }));
    after(() => restoreTables());
});
