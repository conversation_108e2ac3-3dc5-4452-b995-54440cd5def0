import { edgesSelector, Filter, Graph, withoutEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import { testDocumentWithTransientApplication } from '..';
import { Application, datetime, DateValue, PagingFilter, registerSqlFunction, Test } from '../../lib/index';
import * as fixtures from '../fixtures';
import { GraphApi, TestDatatypesInterface, TestReferringInterface } from '../fixtures/client-nodes/index';
import {
    allIds,
    allIdsExcept,
    clientSetup,
    datatypesData,
    documentData,
    documentLineData,
    dropTestTable,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    restoreTables,
} from '../fixtures/index';
import { TestDocument } from '../fixtures/nodes';

let graph: Graph<GraphApi>;
let datatypesNode: GraphApi['@sage/xtrem-core/testDatatypes'];
let referringNode: GraphApi['@sage/xtrem-core/testReferring'];

let graphqlHelper: GraphQlHelper;

const referringData = [
    {
        _id: 1,
        code: 'PAR1',
        description: 'referring 1',
        reference: 1,
    },
    {
        _id: 2,
        code: 'PAR2',
        description: 'referring 2',
        reference: 2,
    },
    {
        _id: 3,
        code: 'PAR3',
        description: 'referring 3',
        reference: 3,
    },
    {
        _id: 4,
        code: 'PAR4',
        description: 'referring 4',
        restricted: 'restricted',
    },
];

const referredData = [
    {
        _id: 1,
        code: 'REF1',
        details: 'reference 1',
    },
    {
        _id: 2,
        code: 'REF2',
        details: 'reference 2',
    },
    {
        _id: 3,
        code: 'REF3',
        details: 'reference 3',
        restricted: 'restricted',
    },
];

registerSqlFunction('test.functions.begOfYearEquals', (date: DateValue, begOfYearStr: string): boolean => {
    return date.begOfYear() === DateValue.parse(begOfYearStr);
});

let application: Application;
describe('query filter', () => {
    before(async () => {
        application = await testDocumentWithTransientApplication.application;
        graph = await clientSetup({ application });
        datatypesNode = graph.node('@sage/xtrem-core/testDatatypes');
        referringNode = graph.node('@sage/xtrem-core/testReferring');
        graphqlHelper = await graphqlSetup({ application });

        await dropTestTable(fixtures.nodes.TestDocument); // HACK
        await dropTestTable(fixtures.nodes.TestDocumentLine); // HACK

        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });

    async function query(filter?: Filter<TestDatatypesInterface>): Promise<{ id: number; _id: string }[]> {
        const result = await datatypesNode
            .query(edgesSelector({ id: true, _id: true, decimalRangeVal: true }, { filter }))
            .execute();
        return withoutEdges(result);
    }
    async function queryReferring(
        filter: Filter<TestReferringInterface>,
    ): Promise<{ _id: string; description: string; reference: { _id: string } | null | undefined }[]> {
        const result = await referringNode
            .query(
                edgesSelector(
                    { _id: true, description: true, reference: { _id: true } },
                    {
                        filter,
                    },
                ),
            )
            .execute();
        return withoutEdges(result);
    }
    async function testFilter(filter: Filter<TestDatatypesInterface>, expected: number[]): Promise<void> {
        const result = await query(filter);
        assert.deepEqual(
            result.map(node => node.id),
            expected,
        );
    }
    it('with reference', async () => {
        const result = await queryReferring({ reference: { _in: [null] } });
        assert.equal(result.length, 1);
        assert.equal(result[0]._id, '4');
    });

    it('implicit _eq', async () => {
        await testFilter({ id: 3 }, [3]);
        await testFilter({ booleanVal: null }, [0]);
        await testFilter(
            { booleanVal: true },
            allIds.filter(x => x % 2 === 1),
        );
        await testFilter(
            { booleanVal: false },
            allIds.filter(x => x > 0 && x % 2 === 0),
        );
        await testFilter({ dateVal: '2017-04-15' }, [3]);
        await testFilter({ dateVal: null }, [0]);
        await testFilter(
            { enumVal: 'value2' },
            allIds.filter(x => x % 3 === 1),
        );
        await testFilter({ enumVal: null }, [0]);
    });
    it('_eq', () => testFilter({ id: { _eq: 3 } }, [3]));
    it('_ne', () => testFilter({ id: { _ne: 3 } }, allIdsExcept(3)));
    it('_lt', () => testFilter({ id: { _lt: 3 } }, [0, 1, 2]));
    it('_lte', () => testFilter({ id: { _lte: 3 } }, [0, 1, 2, 3]));
    it('_gt', () => testFilter({ id: { _gt: 13 } }, [14, 15]));
    it('_gte', () => testFilter({ id: { _gte: 13 } }, [13, 14, 15]));
    it('_in', async () => {
        await testFilter({ id: { _in: [2, 5] } }, [2, 5]);
        await testFilter({ dateVal: { _in: [null] } }, [0]);
        await testFilter({ dateVal: { _in: [] } }, []);
    });
    it('_nin', async () => {
        await testFilter({ id: { _nin: [2, 5] } }, allIdsExcept(2, 5));
        await testFilter({ dateVal: { _nin: [null] } }, allIdsExcept(0));
        await testFilter({ dateVal: { _nin: [] } }, allIds);
    });
    it('_regex', () => testFilter({ stringVal: { _regex: '3$' } }, [3, 13]));
    it('_regex on numbers', async () => {
        await testFilter({ integerVal: { _regex: '7' } }, [3, 13]);
        await testFilter({ shortVal: { _regex: '6' } }, [4, 14]);
        await testFilter({ decimalVal: { _regex: '2' } }, [2, 12]);
        await testFilter({ doubleVal: { _regex: '4' } }, [4, 14]);
        await testFilter({ floatVal: { _regex: '3' } }, [3, 13]);
        await testFilter({ decimalVal: { _regex: 'a' } }, []);
    });

    it('can filter with nested regex filter', async () => {
        let result = await queryReferring({ reference: { _id: { _regex: '(1|2)', _options: 'i' } } });
        assert.equal(result.map(r => r.reference?._id).join('/'), '1/2');
        result = await queryReferring({ reference: { _id: { _regex: 'a' } } });
        assert.equal(result.length, 0);
    });

    it('_regex with i option', () => testFilter({ stringVal: { _regex: 'Ring.*3$', _options: 'i' } }, [3, 13]));
    it('_regex without i option', () => testFilter({ stringVal: { _regex: 'Ring.*3$' } }, []));
    it('_mod', () => testFilter({ id: { _mod: [5, 2] } }, [2, 7, 12]));
    it('_and', () =>
        testFilter(
            {
                _and: [{ id: { _gte: 3 } }, { id: { _lt: 6 } }],
            },
            [3, 4, 5],
        ));
    it('_or', () =>
        testFilter(
            {
                _or: [{ id: { _eq: 3 } }, { id: { _eq: 6 } }],
            },
            [3, 6],
        ));
    it('_not', () =>
        testFilter(
            {
                _not: { id: { _gte: 3 } },
            },
            [0, 1, 2],
        ));
    it('_and / _or / _not ', () =>
        testFilter(
            {
                _and: [
                    {
                        _or: [{ id: { _lte: 2 } }, { _not: { id: { _lt: 14 } } }],
                    },
                    {
                        id: { _in: [1, 5, 14] },
                    },
                ],
            },
            [1, 14],
        ));

    it('by date', () =>
        testFilter(
            {
                _and: [{ dateVal: { _gte: '2017-04-10' } }, { dateVal: { _lt: '2017-08-10' } }],
            },
            [3, 4, 5, 6],
        ));

    it('can filter by _id', async () => {
        await testFilter({ _id: { _eq: '5' } }, [4]);
        await testFilter({ _id: { _ne: '5' } }, allIdsExcept(4));
        await testFilter({ _id: { _in: ['5', '8'] } }, [4, 7]);
        await testFilter({ _id: { _nin: ['5', '8'] } }, allIdsExcept(4, 7));
    });

    it('can filter by _createStamp and _updateStamp', async () => {
        const oneHourAgo = datetime.now().addHours(-1);
        await testFilter({ _createStamp: { _lt: oneHourAgo.toString() } }, []);
        await testFilter({ _updateStamp: { _lt: oneHourAgo.toString() } }, []);
        await testFilter({ _createStamp: { _gt: oneHourAgo.toString() } }, allIds);
        await testFilter({ _updateStamp: { _gt: oneHourAgo.toString() } }, allIds);
    });

    it('can filter by date', async () => {
        await testFilter({ dateVal: { _eq: '2017-04-15' } }, [3]);
        await testFilter({ dateVal: { _ne: '2017-04-15' } }, allIdsExcept(3));
        await testFilter({ dateVal: { _lt: '2017-04-15' } }, [0, 1, 2]);
        await testFilter({ dateVal: { _lte: '2017-04-15' } }, [0, 1, 2, 3]);
        await testFilter(
            { dateVal: { _gt: '2017-04-15' } },
            allIds.filter(x => x > 3),
        );
        await testFilter(
            { dateVal: { _gte: '2017-04-15' } },
            allIds.filter(x => x >= 3),
        );
        await testFilter({ dateVal: { _in: ['2017-04-15', '2017-08-15'] } }, [3, 7]);
        await testFilter({ dateVal: { _nin: ['2017-04-15', '2017-08-15'] } }, allIdsExcept(3, 7));
    });

    it('Can filter integer range', async () => {
        await testFilter({ integerRangeVal: { _eq: null } }, [0]);
        await testFilter({ integerRangeVal: { _ne: null } }, allIdsExcept(0, 0));
        await testFilter({ integerRangeVal: { _eq: '[5,7)' } }, [5]);
        await testFilter({ integerRangeVal: { _eq: '[5,6]' } }, [5]);
        await testFilter({ integerRangeVal: { _ne: '[7,8]' } }, allIdsExcept(7, 7));
        await testFilter({ integerRangeVal: { _contains: '13' } }, [12, 13]);
        await testFilter({ integerRangeVal: { _containsRange: '(5,7)' } }, [5, 6]);
        await testFilter({ integerRangeVal: { _containedBy: '[1, 4]' } }, [1, 2, 3]);
        await testFilter({ integerRangeVal: { _containedBy: '(0, 5)' } }, [1, 2, 3]);
        await testFilter({ integerRangeVal: { start: { _gte: '3' } } }, allIdsExcept(0, 1, 2));
        await testFilter({ integerRangeVal: { start: { _lt: '3' } } }, [0, 1, 2]);
        await testFilter({ integerRangeVal: { end: { _lte: '4' } } }, [0, 1, 2]);
    });

    it('Can filter integer array', async () => {
        await testFilter({ integerArrayVal: { _eq: null } }, []);
        await testFilter({ integerArrayVal: { _ne: null } }, allIds);
        await testFilter({ integerArrayVal: { _eq: [1, 2, 3] } }, [1]);
        await testFilter({ integerArrayVal: { _eq: [2, 3, 4] } }, [2]);
        await testFilter({ integerArrayVal: { _ne: [7, 8, 9] } }, allIdsExcept(7, 7));
        await testFilter({ integerArrayVal: { _contains: 9 } }, [7, 8, 9]);
    });

    it('Can filter string array', async () => {
        await testFilter({ stringArrayVal: { _eq: null } }, []);
        await testFilter({ stringArrayVal: { _ne: null } }, allIds);
        await testFilter({ stringArrayVal: { _eq: ['string%_$1', 'string%_$2', 'string%_$3'] } }, [1]);
        await testFilter({ stringArrayVal: { _eq: ['string%_$2', 'string%_$3', 'string%_$4'] } }, [2]);
        await testFilter({ stringArrayVal: { _ne: ['string%_$7', 'string%_$8', 'string%_$9'] } }, allIdsExcept(7, 7));
        await testFilter({ stringArrayVal: { _contains: 'string%_$9' } }, [7, 8, 9]);
    });

    it('Can filter decimal range', async () => {
        await testFilter({ decimalRangeVal: { _eq: null } }, [0]);
        await testFilter({ decimalRangeVal: { _ne: null } }, allIdsExcept(0, 0));
        await testFilter({ decimalRangeVal: { _eq: '[5.001,7.0004)' } }, [5]);
        await testFilter({ decimalRangeVal: { _ne: '[7.001,9.0004)' } }, allIdsExcept(7, 7));
        await testFilter({ decimalRangeVal: { _contains: '14.2' } }, [13, 14]);
        await testFilter({ decimalRangeVal: { _containsRange: '(8.501,9.004)' } }, [8]);
        await testFilter({ decimalRangeVal: { _containedBy: '[1.002, 6.09]' } }, [2, 3, 4]);
        await testFilter({ decimalRangeVal: { _containedBy: '(0, 5.009)' } }, [1, 2, 3]);
        await testFilter({ decimalRangeVal: { start: { _gte: '2.9' } } }, allIdsExcept(0, 1, 2));
        await testFilter({ decimalRangeVal: { start: { _lt: '5.08' } } }, [0, 1, 2, 3, 4, 5]);
        await testFilter({ decimalRangeVal: { end: { _lte: '5.1' } } }, [0, 1, 2, 3]);
    });

    it('Can filter date range', async () => {
        await testFilter({ dateRangeVal: { _eq: null } }, [0]);
        await testFilter({ dateRangeVal: { _ne: null } }, allIdsExcept(0, 0));
        await testFilter({ dateRangeVal: { _eq: '[2018-01-01,2018-01-15]' } }, [12]);
        await testFilter({ dateRangeVal: { _ne: '[2018-01-01,2018-01-15]' } }, allIdsExcept(12, 12));
        await testFilter({ dateRangeVal: { _contains: '2018-01-03' } }, [12]);
        await testFilter({ dateRangeVal: { _containsRange: '[2018-01-01,2018-01-15]' } }, [12]);
        await testFilter({ dateRangeVal: { _containedBy: '[2018-01-01,2018-01-15]' } }, [12]);
        await testFilter({ dateRangeVal: { start: { _gte: '2018-03-03' } } }, [15]);
        await testFilter({ dateRangeVal: { end: { _lte: '2017-03-03' } } }, [0, 1]);
    });

    it('Can filter datetime range', async () => {
        await testFilter({ datetimeRangeVal: { _eq: null } }, [0]);
        await testFilter({ datetimeRangeVal: { _ne: null } }, allIdsExcept(0, 0));
        await testFilter({ datetimeRangeVal: { _eq: datatypesData[12].datetimeRangeVal?.toString() } }, [12]);
        await testFilter(
            { datetimeRangeVal: { _ne: datatypesData[12].datetimeRangeVal?.toString() } },
            allIdsExcept(12, 12),
        );
        await testFilter({ datetimeRangeVal: { _contains: '2018-03-01 12:04' } }, [14]);
        await testFilter({ datetimeRangeVal: { _containsRange: '[2018-01-01,2018-01-15]' } }, [12]);
        await testFilter({ datetimeRangeVal: { _containedBy: '[2017-12-29,2018-01-17 01:12:01]' } }, [12]);
        await testFilter({ datetimeRangeVal: { start: { _gte: '2018-03-03 12:01' } } }, [15]);
        await testFilter({ datetimeRangeVal: { start: { _gte: '2018-03-03 12:01:01' } } }, [15]);
        await testFilter({ datetimeRangeVal: { start: { _gte: '2018-03-03 12:01:01.000' } } }, [15]);
        await testFilter({ datetimeRangeVal: { end: { _lte: '2017-03-03' } } }, [0, 1]);
    });

    it('can filter by enum value', async () => {
        await testFilter(
            { enumVal: { _eq: 'value2' } },
            allIds.filter(x => x % 3 === 1),
        );
        await testFilter(
            { enumVal: { _ne: 'value2' } },
            allIds.filter(x => x % 3 !== 1),
        );
        await testFilter(
            { enumVal: { _lt: 'value2' } },
            allIds.filter(x => x % 3 === 0),
        );
        await testFilter(
            { enumVal: { _lte: 'value2' } },
            allIds.filter(x => x % 3 !== 2),
        );
        await testFilter(
            { enumVal: { _gt: 'value2' } },
            allIds.filter(x => x % 3 === 2),
        );
        await testFilter(
            { enumVal: { _gte: 'value2' } },
            allIds.filter(x => x % 3 !== 0),
        );
        await testFilter(
            { enumVal: { _in: ['value1', 'value3'] } },
            allIds.filter(x => x !== 0 && x % 3 !== 1),
        );
        await testFilter(
            { enumVal: { _nin: ['value1', 'value3'] } },
            allIds.filter(x => x === 0 || x % 3 === 1),
        );
    });

    it('Can filter enum array', async () => {
        await testFilter(
            {
                enumArrayVal: {
                    _eq: ['arrayVal1', 'arrayVal2', 'arrayVal1'],
                },
            },
            allIds.filter(x => x % 3 === 1),
        );

        await testFilter(
            {
                enumArrayVal: {
                    _ne: ['arrayVal1', 'arrayVal2', 'arrayVal1'],
                },
            },
            allIds.filter(x => x % 3 !== 1),
        );

        await testFilter(
            {
                enumArrayVal: {
                    _contains: 'arrayVal3',
                },
            },
            [2, 5, 8, 11, 14],
        );
    });

    it('implicit "and"', async () => {
        const result = await queryReferring({ reference: { _nin: ['1'], _ne: '2' } });
        assert.equal(result.map(r => r._id).join('/'), '3/4');
    });

    it('cannot filter by restricted property', async () => {
        const filter = { restricted: { _ne: 'restricted1' } };
        let result: any = await referringNode
            .query(
                edgesSelector(
                    { _id: true, description: true, reference: { _id: true }, restricted: true },
                    {
                        filter,
                    },
                ),
            )
            .execute();
        result = withoutEdges(result);
        assert.equal(result.length, 4);
    });

    it('cannot filter by restricted property using _fn', async () => {
        const filter = { _fn: "this._id===1 && this.restricted!=='restricted1'  " };
        let result: any = await referringNode
            .query(
                edgesSelector(
                    { _id: true, description: true, reference: { _id: true }, restricted: true },
                    {
                        filter,
                    },
                ),
            )
            .execute();
        result = withoutEdges(result);
        assert.equal(result.length, 4);

        await Test.withContext(async context => {
            const factory = Test.application.getFactoryByConstructor(fixtures.nodes.TestReferring);

            const parsedFilter = await PagingFilter.parseFilter(
                context,
                factory,
                JSON.stringify({ ...filter, _id: '1' }),
            );

            // _fn is removed
            assert.deepEqual(parsedFilter, { _id: 1 });
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 2,
                    path: ['_fn'],
                    message: 'The property in the filter is unavailable or unauthorized.',
                },
            ]);
        });
    });

    it('Invalid filter', async () => {
        const filter = '\\"{code:\'DOCB\'}\\"';
        await assert.isRejected(
            graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                    testDocument {
                        query(filter:"${filter}") {
                            totalCount
                            edges {
                                node {
                                    description
                                }
                            }
                        }
                    }
                }`,
            ),
            'Invalid filter (parsed value is a string, expected an object): "{code:\'DOCB\'}"',
        );
    });

    it('Invalid filter on collection', async () => {
        const filter = '\\"{lineNumber:{_eq:2}}\\"';
        await assert.isRejected(
            graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                    testDocument {
                        query(filter:"{code:'DOCB'}") {
                            totalCount
                            edges {
                                node {
                                    description
                                    lines {
                                        query(filter: "${filter}") {
                                            totalCount
                                            edges {
                                                node {
                                                    lineNumber
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            ),
            'Invalid filter (parsed value is a string, expected an object): "{lineNumber:{_eq:2}}"',
        );
    });

    it('json filter _eq number', async () => {
        const result = await datatypesNode
            .query(edgesSelector({ id: true, _id: true, jsonVal: true }, { filter: { jsonVal: { id: { _eq: 3 } } } }))
            .execute();

        const nodes = withoutEdges(result) as any;
        assert.equal(nodes.length, 1);
        assert.equal(nodes[0].id, 3);
        assert.deepEqual(JSON.parse(nodes[0].jsonVal), datatypesData[3].jsonVal);
    });

    it('json filter _eq string', async () => {
        const result = await datatypesNode
            .query(edgesSelector({ id: true, jsonVal: true }, { filter: { jsonVal: { code: { _eq: 'string_3' } } } }))
            .execute();

        const nodes = withoutEdges(result) as any;
        assert.equal(nodes.length, 1);
        assert.equal(nodes[0].id, 3);
        assert.deepEqual(
            { ...nodes[0], jsonVal: JSON.parse(nodes[0].jsonVal) },
            {
                id: 3,
                jsonVal: datatypesData[3].jsonVal,
            },
        );
    });

    it('json in depth filter', async () => {
        const result = await datatypesNode
            .query(
                edgesSelector(
                    { id: true, jsonVal: true },
                    { filter: { jsonVal: { text: { en: { _eq: 'string%_$3' } } } } },
                ),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        assert.equal(nodes.length, 1);
        assert.equal(nodes[0].id, 3);
        assert.deepEqual(
            { ...nodes[0], jsonVal: JSON.parse(nodes[0].jsonVal) },
            {
                id: 3,
                jsonVal: datatypesData[3].jsonVal,
            },
        );
    });

    it('json filter _ne', async () => {
        const notEqualTo = 3;
        const result = await datatypesNode
            .query(
                edgesSelector(
                    { id: true, _id: true, jsonVal: true },
                    { filter: { jsonVal: { id: { _ne: notEqualTo } } } },
                ),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        assert.equal(nodes.length, datatypesData.length - 1);
        nodes.forEach((node: any) => {
            assert.isOk(node.id !== notEqualTo);
        });
    });

    it('json filter _gt', async () => {
        const minId = 5;
        const result = await datatypesNode
            .query(
                edgesSelector({ id: true, _id: true, jsonVal: true }, { filter: { jsonVal: { id: { _gt: minId } } } }),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        nodes.forEach((node: any) => {
            assert.isOk(node.id > minId);
        });
    });

    it('json filter _gte', async () => {
        const maxId = 5;
        const result = await datatypesNode
            .query(
                edgesSelector({ id: true, _id: true, jsonVal: true }, { filter: { jsonVal: { id: { _gte: maxId } } } }),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        nodes.forEach((node: any) => {
            assert.isOk(node.id >= maxId);
        });
    });

    it('json filter _lt', async () => {
        const maxId = 5;
        const result = await datatypesNode
            .query(
                edgesSelector({ id: true, _id: true, jsonVal: true }, { filter: { jsonVal: { id: { _lt: maxId } } } }),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        nodes.forEach((node: any) => {
            assert.isOk(node.id < maxId);
        });
    });

    it('json filter _lte', async () => {
        const minId = 5;
        const result = await datatypesNode
            .query(
                edgesSelector({ id: true, _id: true, jsonVal: true }, { filter: { jsonVal: { id: { _lte: minId } } } }),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        nodes.forEach((node: any) => {
            assert.isOk(node.id <= minId);
        });
    });

    it('json filter _or', async () => {
        const notEqualTo = 3;
        const result = await datatypesNode
            .query(
                edgesSelector(
                    { id: true, _id: true, jsonVal: true },
                    {
                        filter: {
                            jsonVal: { _or: [{ id: { _lt: notEqualTo } }, { id: { _gt: notEqualTo } }] },
                        },
                    },
                ),
            )
            .execute();

        const nodes = withoutEdges(result) as any;
        assert.equal(nodes.length, datatypesData.length - 1);
        nodes.forEach((node: any) => {
            assert.isOk(node.id !== notEqualTo);
        });
    });
    it('get meaningful error if id is empty', async () => {
        await assert.isRejected(testFilter({ id: { _eq: '' } }, []), /TestDatatypes\.id: invalid integer value: ''/);
        await assert.isRejected(testFilter({ id: '' }, []), /TestDatatypes\.id: invalid integer value: ''/);
    });
    it('can by computed property', () =>
        testFilter(
            {
                _or: [
                    { computed: datatypesData[1].id * datatypesData[1].integerVal },
                    { computed: datatypesData[3].id * datatypesData[3].integerVal },
                ],
            },
            [1, 3],
        ));
    it('cannot filter by complex computed property', async () => {
        await assert.isRejected(
            testFilter({ complexComputed: 1 }, []),
            /An error has occurred. Please contact your administrator/,
        );
    });

    it('can filter dates with year, month, day, ... fields', async () => {
        await testFilter({ dateVal: { year: 2018 } }, [12, 13, 14, 15]);
        await testFilter({ dateVal: { month: 4 } }, [3, 15]);
        await testFilter({ dateVal: { day: 15 } }, allIdsExcept(0));
        // 2017-06-15 is in the 24th week of the year
        await testFilter({ dateVal: { week: 24 } }, [5]);
        // 2017-05-15 and 2018-01-15 are Monday
        await testFilter({ dateVal: { weekDay: 1 } }, [4, 12]);
        // 2017-02-15 and 2018-02-15 are both 46th day of the year
        await testFilter({ dateVal: { yearDay: 46 } }, [1, 13]);
        await testFilter({ dateVal: { value: 20180215 } }, [13]);
        await testFilter({ dateVal: { epoch: 1518652800 } }, [13]);
    });

    it('can filter date fields with _fn', async () => {
        await testFilter({ _fn: 'this.dateVal.year === 2018' }, [12, 13, 14, 15]);
        await testFilter({ _fn: 'this.dateVal.month === 4' }, [3, 15]);
        await testFilter({ _fn: 'this.dateVal.day === 15' }, allIdsExcept(0));
        await testFilter({ _fn: 'this.dateVal.week === 24' }, [5]);
        await testFilter({ _fn: 'this.dateVal.weekDay === 1' }, [4, 12]);
        await testFilter({ _fn: 'this.dateVal.yearDay === 46' }, [1, 13]);
        await testFilter({ _fn: 'this.dateVal.value === 20180215' }, [13]);
        await testFilter({ _fn: 'this.dateVal.epoch === 1518652800' }, [13]);
    });

    it('can filter date methods with _fn', async () => {
        // compare
        await testFilter(
            { _fn: 'this.dateVal.compare(date.parse("2018-01-15")) < 0' },
            [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        );
        await testFilter({ _fn: 'this.dateVal.compare(date.parse("2018-01-15")) === 0' }, [12]);
        await testFilter({ _fn: 'this.dateVal.compare(date.parse("2018-01-15")) > 0' }, [13, 14, 15]);

        // equals
        await testFilter({ _fn: 'this.dateVal.equals(date.parse("2018-01-15"))' }, [12]);
        await testFilter(
            { _fn: 'this.dateVal.isBetween(date.parse("2018-01-15"), date.parse("2018-03-15"))' },
            [12, 13, 14],
        );

        // add years, months, days and weeks
        await testFilter({ _fn: 'this.dateVal.addYears(1) === date.parse("2018-05-15")' }, [4]);
        await testFilter({ _fn: 'this.dateVal.addYears(-1) === date.parse("2016-05-15")' }, [4]);
        await testFilter({ _fn: 'this.dateVal.addMonths(3) === date.parse("2018-05-15")' }, [13]);
        await testFilter({ _fn: 'this.dateVal.addMonths(-3) === date.parse("2017-11-15")' }, [13]);
        await testFilter({ _fn: 'this.dateVal.addDays(3) === date.parse("2018-01-18")' }, [12]);
        await testFilter({ _fn: 'this.dateVal.addDays(-3) === date.parse("2018-01-12")' }, [12]);
        // If we add 4 weeks to 2018-02-15 we get 2018-03-15
        await testFilter({ _fn: 'this.dateVal.addWeeks(4) === date.parse("2018-03-15")' }, [13]);
        // But we are off the next month, we need 3 more days
        await testFilter({ _fn: 'this.dateVal.addWeeks(4) === date.parse("2018-04-15")' }, []);
        await testFilter({ _fn: 'this.dateVal.addWeeks(4).addDays(3) === date.parse("2018-04-15")' }, [14]);

        await testFilter({ _fn: 'this.dateVal.daysDiff(date.parse("2018-02-12")) === 3' }, [13]);

        // isLeapYear
        await testFilter({ _fn: 'this.dateVal.isLeapYear()' }, []);
        await testFilter({ _fn: 'this.dateVal.addYears(2).isLeapYear()' }, [12, 13, 14, 15]);

        // isWorkDay
        await testFilter({ _fn: 'this.dateVal.weekDay === 0' }, [9, 15]); // Sundays
        await testFilter({ _fn: 'this.dateVal.weekDay === 6' }, [3, 6]); // Saturdays
        await testFilter({ _fn: 'this.dateVal.isWorkDay()' }, [1, 2, 4, 5, 7, 8, 10, 11, 12, 13, 14]);
        await testFilter({ _fn: '!this.dateVal.isWorkDay()' }, [3, 6, 9, 15]);

        // daysInMonth
        await testFilter({ _fn: 'this.dateVal.daysInMonth() === 30' }, [3, 5, 8, 10, 15]);
        await testFilter({ _fn: 'this.dateVal.daysInMonth() === 28' }, [1, 13]);
        await testFilter({ _fn: 'this.dateVal.daysInMonth() === 31' }, [2, 4, 6, 7, 9, 11, 12, 14]);

        // Beginning of year, quarter, month, week
        await testFilter({ _fn: 'this.dateVal.begOfYear() === date.parse("2018-01-01")' }, [12, 13, 14, 15]);
        await testFilter({ _fn: 'this.dateVal.begOfQuarter() === date.parse("2018-01-01")' }, [12, 13, 14]);
        await testFilter({ _fn: 'this.dateVal.begOfMonth() === date.parse("2018-01-01")' }, [12]);
        await testFilter({ _fn: 'this.dateVal.begOfWeek() === date.parse("2018-02-12")' }, [13]);

        // End of year, quarter, month, week
        await testFilter({ _fn: 'this.dateVal.endOfYear() === date.parse("2018-12-31")' }, [12, 13, 14, 15]);
        await testFilter({ _fn: 'this.dateVal.endOfQuarter() === date.parse("2018-03-31")' }, [12, 13, 14]);
        await testFilter({ _fn: 'this.dateVal.endOfMonth() === date.parse("2018-01-31")' }, [12]);
        await testFilter({ _fn: 'this.dateVal.endOfWeek() === date.parse("2018-02-18")' }, [13]);
    });

    it('can combine date fields in filter with _fn', async () => {
        // Test is silly but selects entries 2017-3-15 and 2018-4-15
        await testFilter({ _fn: 'this.dateVal.year === this.dateVal.month + 2014' }, [2, 15]);
    });

    it('can nest _fn and combine it with other filters', async () => {
        const filter = JSON.stringify({
            mandatoryReference: { _fn: "this.code !== 'REF2'" },
            code: { _ne: 'DOCD' },
        });
        const result = await graphqlHelper.query<{ testDocument: { query: { edges: { node: { code: string } }[] } } }>(
            `{
                testDocument {
                    query(filter: ${JSON.stringify(filter)}) {
                        edges { node { code } }
                    }
                }
            }`,
        );
        const codes = result.testDocument.query.edges.map(edge => edge.node.code);
        assert.deepEqual(codes, ['DOCA', 'DOCC', 'DOCE', 'TEST_DELETE_LINE']);
    });

    it('can filter with registered function', () =>
        testFilter({ _fn: 'test.functions.begOfYearEquals(this.dateVal, "2018-01-01")' }, [12, 13, 14, 15]));

    after(() => restoreTables());
});
