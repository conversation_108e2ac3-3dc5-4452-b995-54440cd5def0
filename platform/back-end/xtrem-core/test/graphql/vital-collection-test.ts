import { Graph, querySelector } from '@sage/xtrem-client';
import { assert } from 'chai';
import { pick } from 'lodash';
import { testVitalCollectionApplication } from '..';
import { integer, Test } from '../../lib';
import { GraphApi } from '../fixtures/client-nodes/index';
import { clientSetup, initTables, restoreTables } from '../fixtures/index';
import { TestVitalCollectionChild, TestVitalCollectionParent, TestVitalCollectionSubChild } from '../fixtures/nodes';

let graph: Graph<GraphApi>;

export interface QueryResult {
    _id: string;
    children: { _sortValue: number; code: string; _id: string }[];
}

describe('graphql collection tests', () => {
    before(async () => {
        graph = await clientSetup({ application: await testVitalCollectionApplication.application });
        await initTables([
            { nodeConstructor: TestVitalCollectionParent, data: [] },
            { nodeConstructor: TestVitalCollectionChild, data: [] },
            { nodeConstructor: TestVitalCollectionSubChild, data: [] },
        ]);
    });

    beforeEach(() => Test.committed(context => context.deleteMany(TestVitalCollectionParent, {})));

    async function createParent(len: integer): Promise<QueryResult> {
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalCollectionParent');
        const result = await vitalParentNode
            .create(
                {
                    code: true,
                    _id: true,
                    children: querySelector({ _id: true, _sortValue: true, code: true }),
                },
                {
                    data: {
                        code: 'PARENT-1',
                        children: [...Array(len).keys()].map(i => ({ code: `CHILD-${i + 1}` })),
                    },
                },
            )
            .execute();
        return {
            _id: result._id,
            children: result.children.query.edges.map(edge => edge.node),
        };
    }

    function checkChildren(result: QueryResult, expected: { _sortValue: number; code?: string; _id?: string }[]) {
        assert.equal(result.children.length, expected.length);
        assert.deepEqual(
            result.children.map((child, i) => pick(child, Object.keys(expected[i]))),
            expected,
        );
    }

    it('can create vital collection', async () => {
        const parent = await createParent(3);
        checkChildren(parent, [
            //
            { _sortValue: 10, code: 'CHILD-1' },
            { _sortValue: 20, code: 'CHILD-2' },
            { _sortValue: 30, code: 'CHILD-3' },
        ]);
    });

    it('can update vital collection', async () => {
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalCollectionParent');
        const parent = await createParent(8);
        const ids = parent.children.map(child => child._id);
        const lastId = +ids[7];
        const updated = await vitalParentNode
            .update(
                { code: true, _id: true, children: querySelector({ _id: true, _sortValue: true, code: true }) },
                {
                    _id: parent._id,
                    data: {
                        _id: parent._id,
                        children: [
                            { _id: ids[0] },
                            { _id: ids[3] },
                            { code: 'CHILD-A' },
                            { _id: ids[5] },
                            { _id: ids[2] },
                            { code: 'CHILD-B' },
                            { code: 'CHILD-C' },
                            { _id: ids[7] },
                        ],
                    },
                },
            )
            .execute();
        checkChildren(
            {
                _id: updated._id,
                children: updated.children.query.edges.map(edge => edge.node),
            },
            [
                { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
                { _sortValue: 40, code: 'CHILD-4', _id: ids[3] },
                { _sortValue: 50, code: 'CHILD-A', _id: `${lastId + 1}` },
                { _sortValue: 60, code: 'CHILD-6', _id: ids[5] },
                { _sortValue: 65, code: 'CHILD-3', _id: ids[2] },
                { _sortValue: 70, code: 'CHILD-B', _id: `${lastId + 2}` },
                { _sortValue: 75, code: 'CHILD-C', _id: `${lastId + 3}` },
                { _sortValue: 80, code: 'CHILD-8', _id: ids[7] },
            ],
        );
    });

    it('can update with partial payload', async () => {
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalCollectionParent');
        const parent = await createParent(8);
        const ids = parent.children.map(child => child._id);
        const lastId = +ids[7];
        const updated = await vitalParentNode
            .update(
                { code: true, _id: true, children: querySelector({ _id: true, _sortValue: true, code: true }) },
                {
                    _id: parent._id,
                    data: {
                        _id: parent._id,
                        children: [
                            { _action: 'delete', _id: ids[1] },
                            { _action: 'update', _id: ids[2], _sortValue: 65 },
                            { _action: 'delete', _id: ids[4] },
                            { _action: 'create', _sortValue: 50, code: 'CHILD-A' },
                            { _action: 'delete', _id: ids[6] },
                            { _action: 'create', _sortValue: 70, code: 'CHILD-B' },
                            { _action: 'create', _sortValue: 75, code: 'CHILD-C' },
                        ],
                    },
                },
            )
            .execute();
        checkChildren(
            {
                _id: updated._id,
                children: updated.children.query.edges.map(edge => edge.node),
            },
            [
                { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
                { _sortValue: 40, code: 'CHILD-4', _id: ids[3] },
                { _sortValue: 50, code: 'CHILD-A', _id: `${lastId + 1}` },
                { _sortValue: 60, code: 'CHILD-6', _id: ids[5] },
                { _sortValue: 65, code: 'CHILD-3', _id: ids[2] },
                { _sortValue: 70, code: 'CHILD-B', _id: `${lastId + 2}` },
                { _sortValue: 75, code: 'CHILD-C', _id: `${lastId + 3}` },
                { _sortValue: 80, code: 'CHILD-8', _id: ids[7] },
            ],
        );
    });

    after(() => restoreTables());
});
