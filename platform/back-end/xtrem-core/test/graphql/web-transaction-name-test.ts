import { assert } from 'chai';
import { Request } from 'express';
import { setGraphQlHint } from '../../index';

import * as mocha from 'mocha';

function getGraphQlRequestBuilder(method: string, baseUrl: string): (query: string) => Request {
    return (query: string) =>
        ({
            method,
            baseUrl,
            route: { path: '{/*path}' },
            body: { query },
        }) as unknown as Request;
}

const apiQueryBuilder = getGraphQlRequestBuilder('POST', '/api');
const metadataQueryBuilder = getGraphQlRequestBuilder('POST', '/metadata');
const getGraphQlApiHint = (query: string) => setGraphQlHint(apiQueryBuilder(query));
const getGraphQlMetadataHint = (query: string) => setGraphQlHint(metadataQueryBuilder(query));

describe('graphql request hint', () => {
    before(() => {});

    it('hint for GraphQL api queries with query', () => {
        const hint = getGraphQlApiHint(`query {
            xtremMasterData {
                currency {
                    query(filter: "{_and:[{isActive:true},{_or:[{name:{_regex:'Euro',_options:'i'}}]}]}", orderBy: "{name:1}") {
                        edges {
                            node {
                                _id
                                isActive
                                name
                                id
                                symbol
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
        `);

        assert.strictEqual(hint, 'POST /api{/*path} > query > xtremMasterData > currency > query');
    });

    it('hint for GraphQL api queries with query and aliases', () => {
        const hint = getGraphQlApiHint(`query {
            rootNode: xtremSystem {
                user {
                    getDefaults (data: {}) {
                        selectedDashboard {
                            _id
                            title
                            owner {
                                _id
                                lastName
                                firstName
                            }
                        }
                        photo {
                            value
                        }
                        authorizationGroup {
                            query (first: 20, orderBy: "{'_id':-1}") {
                                edges {
                                    node {
                                        group {
                                            _id
                                            groupSitesDisplay
                                            groupRolesDisplay
                                            id
                                            name
                                        }
                                        _id
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                        role: billingRole {
                            role {
                                _id
                                name
                                id
                            }
                        }
                        isAdministrator
                        isWelcomeMailSent: preferences {
                            isWelcomeMailSent
                        }
                        isApiUser
                        isDemoPersona
                        email
                        lastName
                        firstName
                        isActive
                    }
                }
            }
            navigationPanelItems: xtremSystem {
                user {
                    query (filter: "{'userType':'application'}", orderBy: "{'firstName':1,'email':1,'_id':1}") {
                        edges {
                            node {
                                _id
                                userType
                                selectedDashboard {
                                    _id
                                    title
                                }
                                isApiUser
                                email
                                lastName
                                firstName
                                photo {
                                    value
                                }
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }`);

        assert.strictEqual(hint, 'POST /api{/*path} > query > xtremSystem > user > getDefaults');
    });

    it('hint for GraphQL api introspection queries', () => {
        const hint = getGraphQlApiHint(`query {
            Role: __type (name: "Role") {
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                                fields {
                                    name
                                    type {
                                        ofType {
                                            name
                                        }
                                        name
                                        enumValues {
                                            name
                                        }
                                        kind
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Role_Input: __type (name: "Role_Input") {
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                                inputFields {
                                    name
                                    type {
                                        ofType {
                                            name
                                        }
                                        name
                                        enumValues {
                                            name
                                        }
                                        kind
                                    }
                                }
                            }
                        }
                    }
                }
            }
            GroupRoleSite: __type (name: "GroupRoleSite") {
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                    }
                }
            }
            GroupRoleSite_Input: __type (name: "GroupRoleSite_Input") {
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                    }
                }
            }
            UserGroup: __type (name: "UserGroup") {
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
            UserGroup_Input: __type (name: "UserGroup_Input") {
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
            Dashboard: __type (name: "Dashboard") {
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                                fields {
                                    name
                                    type {
                                        ofType {
                                            name
                                        }
                                        name
                                        enumValues {
                                            name
                                        }
                                        kind
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Dashboard_Input: __type (name: "Dashboard_Input") {
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                                inputFields {
                                    name
                                    type {
                                        ofType {
                                            name
                                        }
                                        name
                                        enumValues {
                                            name
                                        }
                                        kind
                                    }
                                }
                            }
                        }
                    }
                }
            }
            User: __type (name: "User") {
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                                fields {
                                    name
                                    type {
                                        ofType {
                                            name
                                        }
                                        name
                                        enumValues {
                                            name
                                        }
                                        kind
                                    }
                                }
                            }
                        }
                    }
                }
            }
            User_Input: __type (name: "User_Input") {
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                                inputFields {
                                    name
                                    type {
                                        ofType {
                                            name
                                        }
                                        name
                                        enumValues {
                                            name
                                        }
                                        kind
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }`);

        assert.strictEqual(hint, 'POST /api{/*path} > query > __type > fields > name');
    });

    it('hint for GraphQL api queries without query', () => {
        const hint = getGraphQlApiHint(
            `{
                xtremAuthorization {
                    activity {
                        query(filter: "{name: 'dummyTest'}") {
                            edges {
                                node {
                                    name
                                    description
                                    permissions
                                }
                            }
                        }
                    }
                }
            }
            `,
        );

        assert.strictEqual(hint, 'POST /api{/*path} > query > xtremAuthorization > activity > query');
    });

    it('hint for GraphQL api mutations', () => {
        let hint = getGraphQlApiHint(
            `mutation {
                xtremAuthorization {
                    activity {
                        delete(_id: "#dummyTest5")
                    }
                }
            }
            `,
        );
        assert.strictEqual(hint, 'POST /api{/*path} > mutation > xtremAuthorization > activity > delete');

        hint = getGraphQlApiHint(
            `mutation {
                xtremAuthorization {
                    activity {
                        update(data: { _id: "#dummyTest", description: "Dummy Updated" }) {
                            name
                            description
                        }
                    }
                }
            }
            `,
        );
        assert.strictEqual(hint, 'POST /api{/*path} > mutation > xtremAuthorization > activity > update');

        hint = getGraphQlApiHint(
            `mutation {
                xtremAuthorization {
                    roleActivity {
                        roleActivitiesCreateUpdate(
                            roleActivities: [
                                {
                                    activity: "#dummyTest"
                                    permissions: ["lookup", "read"]
                                    isActive: true
                                    hasAllPermissions: false
                                }
                                {
                                    activity: "#dummyTest2"
                                    permissions: ["lookup", "read", "create"]
                                    isActive: true
                                    hasAllPermissions: false
                                }
                                {
                                    activity: "#dummyTest3"
                                    permissions: ["lookup", "read", "create", "update"]
                                    isActive: true
                                    hasAllPermissions: false
                                }
                                {
                                    activity: "#dummyTest4"
                                    permissions: ["lookup", "read", "create", "update", "delete"]
                                    isActive: false
                                    hasAllPermissions: false
                                }
                            ]
                            roleSysId: "1"
                        )
                    }
                }
            }
            `,
        );
        assert.strictEqual(
            hint,
            'POST /api{/*path} > mutation > xtremAuthorization > roleActivity > roleActivitiesCreateUpdate',
        );
    });

    it('hint for GraphQL metadata queries without query', () => {
        let hint = getGraphQlMetadataHint(
            '{ pages { title key access { node bindings { name status } } pageNode pageAccess extensions } }',
        );
        assert.strictEqual(hint, 'POST /metadata{/*path} > query > pages > title');

        hint = getGraphQlMetadataHint('{ stickers {title, key} }');
        assert.strictEqual(hint, 'POST /metadata{/*path} > query > stickers > title');

        hint = getGraphQlMetadataHint('{ pages {title, key, content} }');
        assert.strictEqual(hint, 'POST /metadata{/*path} > query > pages > title');

        hint = getGraphQlMetadataHint('{ installedPackages {name, version} }');
        assert.strictEqual(hint, 'POST /metadata{/*path} > query > installedPackages > name');

        hint = getGraphQlMetadataHint(
            '{ pages(filter:{pageNode:"@sage/xtrem-core/TestDatatypes"}) { title key access { node bindings { name status } } pageNode pageAccess extensions } }',
        );
        assert.strictEqual(hint, 'POST /metadata{/*path} > query > pages > title');
    });

    after(() => {});
});

describe('PERF: GraphQL query hint parsing', function desc() {
    // just to keep the ref on mocha import and have the correct typing
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const suite: mocha.Suite = this;
    suite.timeout(1000);

    it('10000 request should not take more than 200ms', () => {
        for (let i = 0; i < 10000; i += 1) {
            getGraphQlApiHint(`query {
            xtremMasterData {
                currency {
                    query(filter: "{_and:[{isActive:true},{_or:[{name:{_regex:'Euro',_options:'i'}}]}]}", orderBy: "{name:1}") {
                        edges {
                            node {
                                _id
                                isActive
                                name
                                id
                                symbol
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
        `);
        }
        assert.ok(true);
    }).timeout(200);
});
