import { edgesSelector, Graph, PagingOptions, Selector, withoutEdges, WithoutSelectedEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import { testDocumentWithTransientApplication } from '..';
import * as fixtures from '../fixtures';
import {
    GraphApi,
    TestDatatypesInterface,
    TestDatatypesInterface$Operations,
    TestReferredInterface,
    TestReferringInterface,
    TestReferringInterface$Operations,
} from '../fixtures/client-nodes/index';
import { testEnumDataType } from '../fixtures/enums';
import {
    clientSetup,
    datatypesData,
    DatatypesData,
    initTables,
    ReferredData,
    referredData,
    referringData,
    ReferringData,
    restoreTables,
} from '../fixtures/index';

let graph: Graph<GraphApi>;
let datatypesNode: GraphApi['@sage/xtrem-core/testDatatypes'];
let referringNode: GraphApi['@sage/xtrem-core/testReferring'];

function compare(x: any, y: any): number {
    if (x == null) return y == null ? 0 : -1;
    if (y == null) return 1;
    return x < y ? -1 : x > y ? 1 : 0;
}

type AllowedTypes = TestDatatypesInterface | TestReferredInterface | TestReferringInterface;
type TestOperation<T> = T extends TestDatatypesInterface
    ? TestDatatypesInterface$Operations
    : T extends TestReferringInterface
      ? TestReferringInterface$Operations
      : never;
type SampleData<T> = T extends TestDatatypesInterface
    ? DatatypesData
    : T extends TestReferredInterface
      ? ReferredData
      : T extends TestReferringInterface
        ? ReferringData
        : never;

describe('query sorting', () => {
    before(async () => {
        graph = await clientSetup({ application: await testDocumentWithTransientApplication.application });
        datatypesNode = graph.node('@sage/xtrem-core/testDatatypes');
        referringNode = graph.node('@sage/xtrem-core/testReferring');

        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
        ]);
    });
    async function testOrderBy<T extends AllowedTypes>(
        options: PagingOptions<T>,
        selector: Selector<T>,
        compareFn: (t1: WithoutSelectedEdges<T, Selector<T>>, t2: WithoutSelectedEdges<T, Selector<T>>) => boolean,
        nodeType: TestOperation<T>,
        sampleData: SampleData<T>[],
    ): Promise<void> {
        const s = edgesSelector(selector, options);
        const result = await (nodeType as any).query(s).execute();
        const nodes = withoutEdges(result);
        assert.equal(nodes.length, sampleData.length);
        let prev = nodes[0];
        nodes.slice(1).forEach((t: SampleData<T>) => {
            assert.isOk(compareFn(prev as any, t as any));
            prev = t;
        });
    }
    it('returns correct order with simple key ascending', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { uuidVal: +1 } },
            { uuidVal: true },
            (t1, t2) => compare(t1.uuidVal, t2.uuidVal) < 0,
            datatypesNode,
            datatypesData,
        ));
    it('returns correct order with simple key descending', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { uuidVal: -1 } },
            { uuidVal: true },
            (t1, t2) => compare(t1.uuidVal, t2.uuidVal) > 0,
            datatypesNode,
            datatypesData,
        ));
    it('returns correct order with composite key ascending', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { enumVal: +1, uuidVal: +1 } },
            { enumVal: true, uuidVal: true },
            // compare enums based on integer values, not strings
            (t1, t2) =>
                testEnumDataType.compareValues(t1.enumVal, t2.enumVal) < 0 ||
                (t1.enumVal === t2.enumVal && compare(t1.uuidVal, t2.uuidVal) < 0),
            datatypesNode,
            datatypesData,
        ));
    it('returns correct order with composite key ascending then descending', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { enumVal: +1, uuidVal: -1 } },
            { enumVal: true, uuidVal: true },
            // compare enums based on integer values, not strings
            (t1, t2) =>
                testEnumDataType.compareValues(t1.enumVal, t2.enumVal) < 0 ||
                (t1.enumVal === t2.enumVal && compare(t1.uuidVal, t2.uuidVal) > 0),
            datatypesNode,
            datatypesData,
        ));
    it('returns correct order with simple key ascending on computed property', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { computed: 1 } },
            { computed: true },
            (t1, t2) => compare(t1.computed, t2.computed) < 0,
            datatypesNode,
            datatypesData,
        ));
    it('Throw an error when using an orderBy against a computed property which cannot be translated into sql', async () => {
        const s = edgesSelector({ complexComputed: true }, { orderBy: { complexComputed: 1 } });
        await assert.isRejected(
            datatypesNode.query(s).execute(),
            '2 error(s):\n  xtremCore.testDatatypes.query.edges: An error has occurred. Please contact your administrator.\n  xtremCore.testDatatypes.query.pageInfo: An error has occurred. Please contact your administrator.',
        );
    });

    it('returns correct order with simple key descending on computed property', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { computed: -1 } },
            { computed: true },
            (t1, t2) => compare(t1.computed, t2.computed) > 0,
            datatypesNode,
            datatypesData,
        ));
    it('discards restricted order by conditions', () =>
        testOrderBy<TestReferringInterface>(
            { orderBy: { restricted: -1 } },
            { code: true },
            (t1, t2) => compare(t1.code, t2.code) < 0,
            referringNode,
            referringData,
        ));
    it('returns correct order by ascending _updateStamp', () =>
        testOrderBy<TestDatatypesInterface>(
            { orderBy: { _updateStamp: +1 } },
            { _updateStamp: true },
            (t1, t2) => compare(t1._updateStamp, t2._updateStamp) <= 0,
            datatypesNode,
            datatypesData,
        ));

    it('returns correct order on json property', async () => {
        const getId = (node: any): string | null => {
            return node.jsonVal?.id || null;
        };

        await testOrderBy<TestDatatypesInterface>(
            { orderBy: { jsonVal: { id: -1 } } },
            { jsonVal: true },
            (t1, t2) => compare(getId(t1), getId(t2)) <= 0,
            datatypesNode,
            datatypesData,
        );

        await testOrderBy<TestDatatypesInterface>(
            { orderBy: { jsonVal: { id: +1 } } },
            { jsonVal: true },
            (t1, t2) => compare(getId(t1), getId(t2)) >= 0,
            datatypesNode,
            datatypesData,
        );

        const getEnText = (node: any): string | null => {
            return node.jsonVal?.text?.en || null;
        };

        await testOrderBy<TestDatatypesInterface>(
            { orderBy: { jsonVal: { text: { en: -1 } } } },
            { jsonVal: true },
            (t1, t2) => compare(getEnText(t1), getEnText(t2)) <= 0,
            datatypesNode,
            datatypesData,
        );

        await testOrderBy<TestDatatypesInterface>(
            { orderBy: { jsonVal: { text: { en: 1 } } } },
            { jsonVal: true },
            (t1, t2) => compare(getEnText(t1), getEnText(t2)) >= 0,
            datatypesNode,
            datatypesData,
        );
    });

    after(() => restoreTables());
});
