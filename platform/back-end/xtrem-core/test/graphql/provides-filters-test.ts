import { edgesSelector, Graph } from '@sage/xtrem-client';
import { assert } from 'chai';
import * as fixtures from '../fixtures';
import { GraphApi } from '../fixtures/client-nodes/index';
import {
    clientSetup,
    createApplicationWithApi,
    initTables,
    providesChildData,
    providesChildSiteData,
    providesGrandChildData,
    providesGrandChildSiteData,
    providesParentData,
    providesParentNoSiteData,
    providesSiteData,
    restoreTables,
} from '../fixtures/index';
import {
    TestProvidesChild,
    TestProvidesChildSite,
    TestProvidesGrandChild,
    TestProvidesGrandChildSite,
    TestProvidesParent,
    TestProvidesParentNoSite,
    TestProvidesSite,
} from '../fixtures/nodes';

let graph: Graph<GraphApi>;
let providesSiteNode: GraphApi['@sage/xtrem-core/testProvidesSite'];
let providesParentNode: GraphApi['@sage/xtrem-core/testProvidesParent'];
let providesChildNode: GraphApi['@sage/xtrem-core/testProvidesChild'];
let providesGrandChildNode: GraphApi['@sage/xtrem-core/testProvidesGrandChild'];
let providesParentNoSiteNode: GraphApi['@sage/xtrem-core/testProvidesParentNoSite'];
let providesChildSiteNode: GraphApi['@sage/xtrem-core/testProvidesChildSite'];
let providesGrandChildSiteNode: GraphApi['@sage/xtrem-core/testProvidesGrandChildSite'];

describe('Provides: apply site security filter', () => {
     
    before(async () => {
        graph = await clientSetup({
            application: await createApplicationWithApi({
                nodes: {
                    TestProvidesSite,
                    TestProvidesParent,
                    TestProvidesChild,
                    TestProvidesGrandChild,
                    TestProvidesParentNoSite,
                    TestProvidesChildSite,
                    TestProvidesGrandChildSite,
                },
            }),
        });
        providesSiteNode = graph.node('@sage/xtrem-core/testProvidesSite');
        providesParentNode = graph.node('@sage/xtrem-core/testProvidesParent');
        providesChildNode = graph.node('@sage/xtrem-core/testProvidesChild');
        providesGrandChildNode = graph.node('@sage/xtrem-core/testProvidesGrandChild');
        providesParentNoSiteNode = graph.node('@sage/xtrem-core/testProvidesParentNoSite');
        providesChildSiteNode = graph.node('@sage/xtrem-core/testProvidesChildSite');
        providesGrandChildSiteNode = graph.node('@sage/xtrem-core/testProvidesGrandChildSite');

        await initTables([
            { nodeConstructor: fixtures.nodes.TestProvidesSite, data: providesSiteData },
            { nodeConstructor: fixtures.nodes.TestProvidesParent, data: providesParentData },
            { nodeConstructor: fixtures.nodes.TestProvidesChild, data: providesChildData },
            { nodeConstructor: fixtures.nodes.TestProvidesGrandChild, data: providesGrandChildData },
            { nodeConstructor: fixtures.nodes.TestProvidesParentNoSite, data: providesParentNoSiteData },
            { nodeConstructor: fixtures.nodes.TestProvidesChildSite, data: providesChildSiteData },
            { nodeConstructor: fixtures.nodes.TestProvidesGrandChildSite, data: providesGrandChildSiteData },
        ]);
    });
    after(() => restoreTables());
    const test = (
        title: string,
        user: string,
        expectedSites: string[],
        expected1: string[],
        expected2: string[],
        expected3: string[],
        expected4: string[],
        expected5: string[],
        expected6: string[],
    ) => {
        it(title, async () => {
            (graph as any).testUser = user;
            const siteResult = await providesSiteNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();

            assert.deepEqual(
                siteResult.edges.map((edge: any) => edge.node.code),
                expectedSites,
                'providesSiteNode unexpected values',
            );
            assert.deepEqual(siteResult.totalCount, expectedSites.length);

            const result1 = await providesParentNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result1.edges.map((edge: any) => edge.node.code),
                expected1,
                'providesParentNode unexpected values',
            );
            assert.deepEqual(result1.totalCount, expected1.length);

            const result2 = await providesChildNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result2.edges.map((edge: any) => edge.node.code),
                expected2,
                'providesChildNode unexpected values',
            );
            assert.deepEqual(result2.totalCount, expected2.length);

            const result3 = await providesGrandChildNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result3.edges.map((edge: any) => edge.node.code),
                expected3,
                'providesGrandChildNode unexpected values',
            );
            assert.deepEqual(result3.totalCount, expected3.length);

            const result4 = await providesParentNoSiteNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result4.edges.map((edge: any) => edge.node.code),
                expected4,
                'providesParentNoSiteNode unexpected values',
            );
            assert.deepEqual(result4.totalCount, expected4.length);

            const result5 = await providesChildSiteNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result5.edges.map((edge: any) => edge.node.code),
                expected5,
                'providesChildSiteNode unexpected values',
            );
            assert.deepEqual(result5.totalCount, expected5.length);

            const result6 = await providesGrandChildSiteNode
                .query({ ...edgesSelector({ code: true }, {}), totalCount: true })
                .execute();
            assert.deepEqual(
                result6.edges.map((edge: any) => edge.node.code),
                expected6,
                'providesGrandChildSiteNode unexpected values',
            );
            assert.deepEqual(result6.totalCount, expected6.length);
        });
    };
    test(
        'Provides filter by site (all sites)',
        '<EMAIL>',
        ['SITE1', 'SITE2', 'SITE3', 'SITE4', 'SITE5'],
        ['PARENT1', 'PARENT2', 'PARENT3', 'PARENT4', 'PARENT5'],
        ['CHILD1of1', 'CHILD2of1', 'CHILD1of2', 'CHILD2of2', 'CHILD1of3'],
        ['GRANDCHILD1of1of1', 'GRANDCHILD1of2of1', 'GRANDCHILD1of1of2', 'GRANDCHILD1of2of2', 'GRANDCHILD1of1of3'],
        ['PARENT1', 'PARENT2', 'PARENT3', 'PARENT4', 'PARENT5'],
        ['CHILD1of1', 'CHILD2of1', 'CHILD1of2', 'CHILD2of2', 'CHILD1of3'],
        ['GRANDCHILD1of1of1', 'GRANDCHILD1of2of1', 'GRANDCHILD1of1of2', 'GRANDCHILD1of2of2', 'GRANDCHILD1of1of3'],
    );
    test(
        'Provides filter by site (sites 1 and 3 only)',
        '<EMAIL>',
        ['SITE1', 'SITE3'],
        ['PARENT1', 'PARENT3'],
        ['CHILD1of1', 'CHILD2of1', 'CHILD1of3'],
        ['GRANDCHILD1of1of1', 'GRANDCHILD1of2of1', 'GRANDCHILD1of1of3'],
        ['PARENT1', 'PARENT2', 'PARENT3', 'PARENT4', 'PARENT5'],
        ['CHILD1of1', 'CHILD2of1', 'CHILD1of3'],
        ['GRANDCHILD1of1of1', 'GRANDCHILD1of2of1', 'GRANDCHILD1of1of3'],
    );
    test(
        'Provides filter by site (no sites - no access)',
        '<EMAIL>',
        [],
        [],
        [],
        [],
        ['PARENT1', 'PARENT2', 'PARENT3', 'PARENT4', 'PARENT5'],
        [],
        [],
    );
});
