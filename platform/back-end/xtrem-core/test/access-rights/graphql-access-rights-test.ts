import { assert } from 'chai';
import { assertIsRejectedWithDiagnoses } from '../../lib';
import { createApplicationWithApi, GraphQlHelper, graphqlSetup, initTables } from '../fixtures/index';
import { TestGraphQlOperation, TestLookupAccess, TestSecure, TestSite } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;
const user1Email = '<EMAIL>';
const user2Email = '<EMAIL>';

describe('access rights (GraphQl) tests', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: { TestSite, TestSecure, TestGraphQlOperation, TestLookupAccess },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestLookupAccess,
                data: [
                    { code: '01', noLookup: 'No lookup access 1' },
                    { code: '02', noLookup: 'No lookup access 2' },
                    { code: '03', noLookup: 'No lookup access 3' },
                ],
            },
        ]);
    });

    it('can invoke create operation', async () => {
        const result = await graphqlHelper.mutation('{ testGraphQlOperation { createOperation(param1: "ABC") } }', {
            userEmail: user1Email,
        });
        assert.deepEqual(result, {
            testGraphQlOperation: {
                createOperation: 'ABC',
            },
        });
    });

    it("can't invoke delete operation", async () => {
        await assert.isRejected(
            graphqlHelper.mutation('{ testGraphQlOperation { deleteOperation(param1: "ABC") } }', {
                userEmail: user1Email,
            }),
            {
                message: 'Delete operation failed.',
                diagnoses: [
                    {
                        severity: 3,
                        message: 'You cannot perform this operation TestGraphQlOperation.deleteOperation',
                        path: [],
                    },
                ],
            },
        );
    });

    it("can't invoke lookupQueryWithoutGrant operation", async () => {
        await assertIsRejectedWithDiagnoses(
            graphqlHelper.query('{ testGraphQlOperation { lookupQueryWithoutGrant(param1: "ABC") } }', {
                userEmail: user1Email,
            }),
            {
                message: 'Lookup query without grant failed.',
                diagnoses: [
                    {
                        severity: 4,
                        message: 'You cannot perform this operation TestGraphQlOperation.lookupQueryWithoutGrant',
                        path: [],
                    },
                ],
            },
        );
    });

    it('can invoke lookupQueryWithGrant operation with isGrantedByLookup set to true,', async () => {
        const result = await graphqlHelper.query('{ testGraphQlOperation { lookupQueryWithGrant(param1: "ABC") } }', {
            userEmail: user1Email,
        });
        assert.deepEqual(result, {
            testGraphQlOperation: {
                lookupQueryWithGrant: 'ABC',
            },
        });
    });

    it('can query TestLookupAccess with lookup permission', async () => {
        const result = await graphqlHelper.execute(
            '{ xtremCore { testLookupAccess { query(filter: "{code: \'01\'}") { edges { node { code, noLookup } } } } } }',
            {
                userEmail: user2Email,
                withDiagnoses: true,
            },
        );

        assert.deepEqual(result, {
            data: {
                xtremCore: {
                    testLookupAccess: {
                        query: {
                            edges: [
                                {
                                    node: {
                                        code: '01',
                                        noLookup: null,
                                    },
                                },
                            ],
                        },
                    },
                },
            },
            extensions: {
                diagnoses: [
                    {
                        severity: 2,
                        path: ['_id'],
                        message: 'The property in the sort order is unavailable or unauthorized.',
                    },
                    {
                        severity: 3,
                        path: ['xtremCore', 'testLookupAccess', 'query', 'edges', 'node', 'noLookup'],
                        message: 'The property is unavailable.',
                    },
                ],
            },
        });
    });

    it('cannot filter on non-lookupAccess property with lookup permission', async () => {
        const result = await graphqlHelper.execute(
            '{ xtremCore { testLookupAccess { query(filter: "{noLookup: \'No lookup access 2\'}") { edges { node { code, noLookup } } } } } }',
            {
                userEmail: user2Email,
                withDiagnoses: true,
            },
        );

        assert.deepEqual(result, {
            data: {
                xtremCore: {
                    testLookupAccess: {
                        query: {
                            edges: [
                                {
                                    node: {
                                        code: '01',
                                        noLookup: null,
                                    },
                                },
                                {
                                    node: {
                                        code: '02',
                                        noLookup: null,
                                    },
                                },
                                {
                                    node: {
                                        code: '03',
                                        noLookup: null,
                                    },
                                },
                            ],
                        },
                    },
                },
            },
            extensions: {
                diagnoses: [
                    {
                        severity: 2,
                        path: ['_id'],
                        message: 'The property in the sort order is unavailable or unauthorized.',
                    },
                    {
                        severity: 2,
                        path: ['noLookup'],
                        message: 'The property in the filter is unavailable or unauthorized.',
                    },
                    {
                        severity: 3,
                        path: ['xtremCore', 'testLookupAccess', 'query', 'edges', 'node', 'noLookup'],
                        message: 'The property is unavailable.',
                    },
                ],
            },
        });
    });

    it('cannot sort on non-lookupAccess property with lookup permission', async () => {
        const result = await graphqlHelper.execute(
            '{ xtremCore { testLookupAccess { query(orderBy: "{noLookup: -1}") { edges { node { code, noLookup } } } } } }',
            {
                userEmail: user2Email,
                withDiagnoses: true,
            },
        );

        assert.deepEqual(result, {
            data: {
                xtremCore: {
                    testLookupAccess: {
                        query: {
                            edges: [
                                {
                                    node: {
                                        code: '01',
                                        noLookup: null,
                                    },
                                },
                                {
                                    node: {
                                        code: '02',
                                        noLookup: null,
                                    },
                                },
                                {
                                    node: {
                                        code: '03',
                                        noLookup: null,
                                    },
                                },
                            ],
                        },
                    },
                },
            },
            extensions: {
                diagnoses: [
                    {
                        severity: 2,
                        path: ['noLookup'],
                        message: 'The property in the sort order is unavailable or unauthorized.',
                    },
                    {
                        severity: 3,
                        path: ['xtremCore', 'testLookupAccess', 'query', 'edges', 'node', 'noLookup'],
                        message: 'The property is unavailable.',
                    },
                ],
            },
        });
    });
});
