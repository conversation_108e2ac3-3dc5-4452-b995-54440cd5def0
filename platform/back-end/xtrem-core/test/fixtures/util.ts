import { asyncArray } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as fs from 'fs';
import * as fsp from 'path';
import { QueryAggregateEdge, QueryAggregateNode, QueryNode, QueryPage } from '../../lib/graphql/paging/paging';
import { Application, datetime, Node, NodeCreateData, Test } from '../../lib/index';
import { NodeFactory } from '../../lib/runtime';
import { dropTestTable, initTables } from '../../lib/test/tables';
import { createApplicationWithApi } from './setup';

export { QueryAggregateEdge, QueryAggregateNode, QueryNode };

export type TestInitData<T extends Node> = NodeCreateData<T>;

export function readSampleFile(type: string, length?: number): string {
    if (length === 0) {
        return '';
    }
    let content;
    switch (type) {
        case 'png':
            content = fs.readFileSync(fsp.join(__dirname, 'sample-files/empty.png')).toString('base64');
            return length !== undefined ? content.slice(0, length) : content;
        case 'pdf':
            content = fs.readFileSync(fsp.join(__dirname, 'sample-files/empty.pdf')).toString('base64');
            return length !== undefined ? content.slice(0, length) : content;
        default:
            return '';
    }
}

export function sortByKey(unordered: any) {
    const ordered = {} as any;
    Object.keys(unordered)
        .sort()
        .forEach((key: string) => {
            ordered[key] = unordered[key];
        });
    return ordered;
}

export async function dropTestSchema(application: Application): Promise<void> {
    await Test.withCommittedContext(async context => {
        const factories = application.getSqlPackageFactories();
        await clearAllNullableReferences(factories);
        await asyncArray(factories).forEach(async factory => {
            if (factory.table.name.startsWith('test_') && (await factory.table.tableExists(context))) {
                await dropTestTable(factory.nodeConstructor);
                // force a reload when withContext is called
                factory.table.testLayers = [];
            }
        });
    }, Test.convertOptions(undefined));
}

async function clearAllNullableReferences(factories: NodeFactory[], where?: string): Promise<void> {
    await Test.withCommittedContext(
        context =>
            asyncArray(factories)
                .filter(
                    async factory =>
                        (await factory.table.tableExists(context)) &&
                        !!factory.properties.find(
                            property =>
                                property.isReferenceProperty() &&
                                property.isStored &&
                                !property.isInherited &&
                                property.isNullable,
                        ),
                )
                .forEach(async factory => {
                    const updateColumns = [] as string[];
                    factory.properties
                        .filter(
                            property =>
                                property.isReferenceProperty() &&
                                property.isStored &&
                                !property.isInherited &&
                                property.isNullable &&
                                property.column,
                        )
                        .forEach(property => {
                            updateColumns.push(`${property.column!.columnName}=NULL`);
                        });
                    if (updateColumns.length > 0) {
                        const sql = `UPDATE ${context.schemaName}.${
                            factory.table.name
                        } SET ${updateColumns.join()} WHERE ${where || '1=1'}`;

                        await context.executeSql(sql, []);
                    }
                }),
        { ...Test.convertOptions(undefined), withoutTransactionUser: true },
    );
}

export async function restoreApplication(api: any, schemaName?: string): Promise<void> {
    await dropTestSchema(Test.application);
    Test.application = await createApplicationWithApi(api, schemaName);
    await initTables([]);
}

// Hack to force computation of _local and _utc in datetime objects.
// Otherwise deepEqual fails because internal structures differ although objects have no observable differences.
export function fixDatetimes(...args: (datetime | null | undefined)[]): void {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    function nop(arg: any): void {}
    args.filter(arg => !!arg).forEach(arg => {
        nop(arg.date);
        nop(arg.utcDate);
    });
}

export function stripSysColumns(obj: Dict<any>): Dict<any> {
    return Object.keys(obj)
        .filter(k => k === '_id' || k[0] !== '_')
        .reduce((r, k) => {
            r[k] = obj[k];
            return r;
        }, {} as Dict<any>);
}

export function graphqlPageNodes<T>(page: QueryPage<T>): T[] {
    assert.isObject(page);
    assert.isArray(page.edges);
    return (page.edges || []).map(edge => {
        assert.isObject(edge);
        assert.isObject(edge.node);
        return edge.node;
    });
}
