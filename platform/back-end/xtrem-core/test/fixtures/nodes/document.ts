import { assert } from 'chai';
import { TestDocumentLine, TestReferred } from '.';
import { Collection, Node, NodeStatus, Reference, decorators } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import * as fixtures from '../index';

@decorators.node<TestDocument>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestDocument',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],

    async saveBegin() {
        if (this.$.status === NodeStatus.modified && (await this.code) === 'TEST_DELETE_LINE') {
            // see collection-test.ts for details
            // We are checking that the this.$.old.lines is correct, compared to what is in the db
            const old = await this.$.old;
            const lines = await old.lines.toArray();
            assert.equal(lines.length, 3, '$.old.lines: wrong length');
            for (let index = 0; index < lines.length; index += 1) {
                assert.equal(lines[index]._id, 13 + index, `$.old.lines[${index}]: wrong _id`);
            }
        }
    },
})
export class TestDocument extends Node {
    @decorators.stringProperty<TestDocument, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocument, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocument, 'mandatoryReference'>({
        isPublished: true,
        isStored: true,
        node: () => fixtures.nodes.TestReferred,
    })
    readonly mandatoryReference: Reference<TestReferred>;

    @decorators.collectionProperty<TestDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => fixtures.nodes.TestDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<TestDocumentLine>;

    @decorators.collectionProperty<TestDocument, 'computedLines'>({
        isPublished: true,
        node: () => fixtures.nodes.TestDocumentLine,
        reverseReference: 'document',
        computeValue() {
            return this.$.context
                .query(TestDocumentLine, {
                    filter: { document: this._id },
                    orderBy: {
                        description: +1,
                    },
                })
                .toArray();
        },
    })
    readonly computedLines: Collection<TestDocumentLine>;

    // This is the same as the lines property but non vital and joined with getFilter
    @decorators.collectionProperty<TestDocument, 'filteredLines'>({
        isPublished: true,
        node: () => fixtures.nodes.TestDocumentLine,
        getFilter() {
            return { document: this._id };
        },
    })
    readonly filteredLines: Collection<TestDocumentLine>;

    // This is the same as the lines property but non vital with reverse reference
    @decorators.collectionProperty<TestDocument, 'filteredLinesWithReverseReference'>({
        isPublished: true,
        node: () => fixtures.nodes.TestDocumentLine,
        reverseReference: 'document',
    })
    readonly filteredLinesWithReverseReference: Collection<TestDocumentLine>;
}
