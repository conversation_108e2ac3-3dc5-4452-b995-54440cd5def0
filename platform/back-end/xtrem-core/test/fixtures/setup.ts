import { AnyValue } from '@sage/xtrem-async-helper';
import { Graph } from '@sage/xtrem-client';
import { ConfigManager } from '@sage/xtrem-config';
import { addBundleLocalizationKeys } from '@sage/xtrem-i18n';
import { sourceMapSetup } from '@sage/xtrem-log';
import { Config, ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Request } from 'express';
import * as fs from 'fs';
import { AsyncExecutionResult, ExecutionPatchResult, ExecutionResult, GraphQLSchema, graphql } from 'graphql';
import * as fsp from 'path';
import {
    Application,
    Context,
    CoreHooks,
    Dict,
    IsolationOptions,
    PackageApi,
    SysGlobalLock,
    Test,
    TestOptions,
    TestResetTables,
    createMetadataSchema,
    mapGraphQlResult,
} from '../../lib/index';
import { loggers } from '../../lib/runtime/loggers';
import { setup as setupSecurity } from '../../lib/security';
import * as mocks from '../../lib/test/manager-mocks';
import { restoreTables } from '../../lib/test/tables';
import { GraphApi } from './client-nodes/index';
import { TestSysVendor, TestUser } from './nodes';

import * as lodash from 'lodash';
import * as sinon from 'sinon';

let resetTestTablesStub: sinon.SinonStub;

export function stubResetTablesWithoutContext() {
    restoreResetTablesWithoutContext();
    resetTestTablesStub = sinon.stub(TestResetTables, 'resetTestTables');
}

export function restoreResetTablesWithoutContext() {
    if (resetTestTablesStub) resetTestTablesStub.restore();
}

export async function createApplicationWithApi(api: PackageApi, schemaName?: string): Promise<Application> {
    ConfigManager.load(__dirname);
    await restoreTables();
    Application.resetMain();

    updateContext();

    const application = await Application.create({
        // api.nodes will override package manager nodes when we call from xtrem-system
        api: {
            ...api,
            nodes: { ...api.nodes, TestUser, TestSysVendor, SysGlobalLock },
        },
        buildDir: fsp.parse(__dirname).dir,
        applicationType: 'test',
        schemaName: schemaName || 'xtrem_core_test',
    });

    const loadI18nDir = (i18nDir: string) => {
        if (fs.existsSync(i18nDir)) {
            fs.readdirSync(i18nDir)
                .filter(f => f.endsWith('.json'))
                .forEach(f => {
                    const locale = f.replace('.json', '');
                    // eslint-disable-next-line global-require, import/no-dynamic-require
                    const content = require(fsp.join(i18nDir, f)) as Dict<string>;
                    const data: any = {};
                    data[locale] = content;
                    addBundleLocalizationKeys(data);
                });
        }
    };

    loadI18nDir(fsp.resolve('lib', 'i18n'));

    application.activityManager.resolvePermissions();

    return application;
}

interface SetupOptions {
    stubResetTables?: boolean;
    application: Application;
    activatesPackages?: string[];
}

export function updateContext() {
    CoreHooks.sysManager = {
        getUserNode: () => TestUser,
    };
    Context.accessRightsManager = mocks.accessRightsManagerMock;
    Context.localizationManager = mocks.localizationManagerMock;
    Context.tenantManager = mocks.tenantManagerMock;
    Context.dataSettingsManager = mocks.dataSettingsManagerMock;
}

export async function setup(setupOptions: SetupOptions): Promise<void> {
    const stubResetTables = setupOptions?.stubResetTables === undefined ? true : setupOptions?.stubResetTables;

    sourceMapSetup();

    ConfigManager.load(fsp.join(__dirname, '..'), 'test');
    updateContext();
    await setupSecurity();

    Test.application = setupOptions.application;

    const schemaName = Test.application.schemaName;
    if (!Test._schemaExists[schemaName]) {
        await Application.createDbSchema(schemaName);
        Test._schemaExists[schemaName] = true;
    }

    // As in most xtrem-core tests the tables are loaded through initTable, the method application.resetTablesForTests
    // has to be stubbed in order not to re-load tables with non-existent csv files.
    restoreResetTablesWithoutContext();
    if (stubResetTables) {
        stubResetTablesWithoutContext();
    }
}

export interface GraphQlSetupOptions {
    userEmail?: string;
    rootQueryType?: string;
    isolationOptions?: IsolationOptions;
    context?: Context;
    withDiagnoses?: boolean;
    dontThrow?: boolean;
    timeLimitAsTimestamp?: number;
}

export interface GraphQlResponse<T> {
    data: { [key: string]: T };
}

export class GraphQlHelper {
    constructor(private schema: GraphQLSchema) {}

    execute<T extends AnyValue>(q: string, options?: GraphQlSetupOptions): Promise<ExecutionResult<T>> {
        const executeWithContext = async (context: Context) => {
            const result = (await graphql({
                schema: this.schema,
                source: q,
                contextValue: context,
            })) as ExecutionResult<T>;
            if (result.errors && !options?.dontThrow) {
                loggers.graphQl.error(`graphql request failed: ${result.errors}`);
                context.addDiagnoseAtPath(ValidationSeverity.error, [''], result.errors[0].message);
                throw result.errors[0];
            }
            if (options?.withDiagnoses) {
                result.extensions = { ...(result.extensions || {}), diagnoses: context.diagnoses };
            }
            return result;
        };

        return Test.readonly(
            context => {
                const ctx = options?.context || context;
                // initialize a fake request to simulate an incomming http request
                if (ctx.request?.body == null) {
                    (ctx.request as Partial<Request>) = {
                        ...ctx.request,
                        method: 'POST',
                        baseUrl: '/api',
                        route: {
                            path: '{/*path}',
                        },
                        headers: {},
                        body: {
                            query: q,
                        },
                    };
                }
                if (!options?.isolationOptions) return executeWithContext(options?.context || context);
                return context.withChildContext(executeWithContext, options.isolationOptions);
            },
            {
                user: options?.userEmail ? { email: options?.userEmail } : undefined,
                timeLimitAsTimestamp: options?.timeLimitAsTimestamp,
            },
        );
    }

    /**
     * Executes a request containing `@stream` and/or `@defer` directives.
     * This method waits for all the chunks and returns a single result.
     * It is only meant for testing. A real client should process the chunks as they arrive,
     * to get the real benefits of these directives.
     */
    executeChunked(q: string, options?: GraphQlSetupOptions): Promise<AsyncExecutionResult[]> {
        const executeWithContext = async (context: Context) => {
            const result = await graphql({
                schema: this.schema,
                source: q,
                contextValue: context,
            });

            if (!(result as AsyncGenerator)[Symbol.asyncIterator]) {
                const singleResult = result as ExecutionResult;
                // if we got a single result because we sent an invalid query (syntax error, mismatch with schema), throw the error
                if (singleResult.errors && !options?.dontThrow) throw singleResult.errors[0];

                // Otherwise, this is probably because the request does not contain any @strem/@defer directives
                throw new Error(
                    'result is not async iterable (check that the query contains at least one @stream or @defer directive)',
                );
            }

            const chunks: AsyncExecutionResult[] = [];
            // collect all the results
            // eslint-disable-next-line no-restricted-syntax
            for await (const patch of result as AsyncGenerator<AsyncExecutionResult>) {
                chunks.push(patch);
            }
            return chunks;
        };

        return Test.withContext(
            context => {
                if (!options?.isolationOptions) return executeWithContext(context);
                return context.withChildContext(executeWithContext, options.isolationOptions);
            },
            {
                user: options?.userEmail ? { email: options?.userEmail } : undefined,
                timeLimitAsTimestamp: options?.timeLimitAsTimestamp,
            },
        );
    }

    /**
     * Merges chunks returned by a graphql request containing `@stream` and/or `@defer` directives.
     */
    static mergeChunks<T extends AnyValue>(chunks: AsyncExecutionResult[]): T {
        const [first, ...remain] = chunks;
        return lodash.merge(
            {},
            first.data,
            ...(remain as ExecutionPatchResult[]).map(r => lodash.set({}, r.path!, r.data)),
        );
    }

    private static unwrap<T extends AnyValue>(result: ExecutionResult<any>, rootQueryType: string): T {
        assert.isObject(result);
        assert.isObject(result.data);
        assert.isObject(result.data?.[rootQueryType]);
        return result.data[rootQueryType];
    }

    async query<T extends AnyValue>(q: string, options?: GraphQlSetupOptions): Promise<T> {
        const rootQueryType = options?.rootQueryType ?? 'xtremCore';
        const result = await this.execute<T>(`{ ${rootQueryType} ${q} }`, options);
        return GraphQlHelper.unwrap<T>(result, rootQueryType);
    }

    async mutation<T extends AnyValue>(q: string, options?: GraphQlSetupOptions): Promise<T> {
        const rootQueryType = options?.rootQueryType ?? 'xtremCore';
        const result = await this.execute<T>(`mutation { ${rootQueryType} ${q} }`, options);
        return GraphQlHelper.unwrap<T>(result, rootQueryType);
    }

    async runScenario(
        scenario: string,
        options: { dir?: string; format?: boolean; update?: boolean; graphqlOptions?: GraphQlSetupOptions } = {},
    ): Promise<void> {
        const dir = options.dir ?? fsp.join(__dirname, 'graphql', scenario);
        const requestFile = fsp.join(dir, 'request.graphql');
        const responseFile = fsp.join(dir, 'response.json');
        const request = fs.readFileSync(requestFile, 'utf8');
        const response = JSON.parse(fs.readFileSync(responseFile, 'utf8'));
        if (options.format) {
            fs.writeFileSync(responseFile, `${JSON.stringify(response, null, 4)}\n`);
        }
        const result = await this.execute(request, options.graphqlOptions);
        if (options.update) {
            fs.writeFileSync(responseFile, `${JSON.stringify(result, null, 4)}\n`);
        }
        assert.deepEqual(result, response);
    }
}

interface GraphqlSetupOptions extends SetupOptions {
    context?: Context;
}

export async function graphqlSetup(graphqlSetupOptions: GraphqlSetupOptions): Promise<GraphQlHelper> {
    await setup(graphqlSetupOptions);
    const schema = await Test.application.getGraphQLSchema(graphqlSetupOptions?.context);
    return new GraphQlHelper(schema);
}

export async function graphqlMetadataSetup(setupOptions: SetupOptions): Promise<GraphQlHelper> {
    await setup(setupOptions);
    const schema = createMetadataSchema(Test.application);
    return new GraphQlHelper(schema);
}

// testUser is a hack to override the user
export async function clientSetup(setupOptions: SetupOptions): Promise<Graph<GraphApi> & { testUser?: string }> {
    await setup(setupOptions);
    const schema = await Test.application.getGraphQLSchema();

    const fetcher = (query: string): Promise<AnyValue> => {
        return (() => {
            const options: TestOptions = { source: 'graphql' };
            if (graph.testUser) options.user = { email: graph.testUser };
            return Test.readonly(async context => {
                return mapGraphQlResult(
                    (await graphql({
                        schema,
                        source: query,
                        contextValue: context,
                    })) as ExecutionResult,
                );
            }, options);
        })();
    };
    const graph = new Graph<GraphApi>({
        fetcher,
    }) as Graph<GraphApi> & { testUser?: string };
    return graph;
}

export { ConfigManager };

let configManagerStub: sinon.SinonStub;

export function stubConfigManager(stubbedConfig: Config) {
    restoreConfigManager();
    const currentConfig = ConfigManager.current;

    configManagerStub = sinon.stub(ConfigManager, 'current').get((): Config => {
        return { ...currentConfig, ...stubbedConfig };
    });
}

export function restoreConfigManager() {
    if (configManagerStub) configManagerStub.restore();
}
