import { SysGlobalLock } from '../../lib/index';
import * as activities from './activities/_index';
import * as activityExtensions from './activity-extensions/_index';
import * as dataTypes from './data-types/_index';
import * as enumExtensions from './enum-extensions/index';
import * as enums from './enums/index';
import * as importedExtension from './node-extensions/index';
import * as importedNodes from './nodes/index';
import * as serviceOptions from './service-options/index';

export { dropTables, dropTestTable, initTables, restoreTables } from '../../lib/test/tables';
export * as applications from './applications';
export * from './data/index';
export * from './setup';
export * from './util';
export { activities, activityExtensions, dataTypes, enumExtensions, enums, importedNodes, serviceOptions };

export const nodes = {
    ...importedNodes,
    SysGlobalLock,
};

export const nodeExtensions = importedExtension;
