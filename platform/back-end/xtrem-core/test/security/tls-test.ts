// tslint:disable: no-unused-expression
import { assert } from 'chai';
import * as fs from 'node:fs/promises';
import * as https from 'node:https';
import { tmpdir } from 'node:os';
import * as fsp from 'node:path';
import { InternalServerConfig, registerTlsChangeListener } from '../../lib/security/tls';
import { ConfigManager, createApplicationWithApi, restoreTables, setup } from '../fixtures/index';
import { TestCertificateGenerator, generateCertificates } from './fixtures/test-certificate-generator';

interface ServerInfo {
    certGenerator: TestCertificateGenerator;
    port: number;
    server?: https.Server;
}

async function checkTlsRequestError(tlsClientRequest: () => Promise<string>): Promise<void> {
    // We cannot use assert.throw so we need to remove the top handler and manage the exception by ourself
    const oldHandler = process.listeners('uncaughtException').pop();
    if (oldHandler) {
        process.removeListener('uncaughtException', oldHand<PERSON>);
    }
    await new Promise<void>((resolve, reject) => {
        process.once('uncaughtException', err => {
            const errorMessage = err.message;
            process.nextTick(() => {
                if (oldHandler) {
                    process.listeners('uncaughtException').push(oldHandler);
                }
                assert.match(
                    errorMessage.replace('-', ' '),
                    /(self signed certificate in certificate chain|certificate signature failure)/,
                );

                resolve();
            });
        });
        tlsClientRequest().catch(reject);
    });
}

describe('TLS tests', () => {
    const serverInfo: ServerInfo[] = [];
    const prefix = fsp.join(tmpdir(), 'ssl-');

    const generateTestCertificates = async (): Promise<TestCertificateGenerator> => {
        const sslDir = await fs.mkdtemp(prefix);
        return generateCertificates(sslDir);
    };

    before(async function before(this: Mocha.Context) {
        const version = await TestCertificateGenerator.getOpenSslVersion();

        if (!version) {
            this.skip();
        }

        // eslint-disable-next-line no-plusplus
        for (let i = 0; i < 2; i++) {
            const certGenerator = await generateTestCertificates();
            serverInfo.push({ certGenerator, port: 6556 + i });
        }
        // eslint-disable-next-line no-restricted-syntax
        for (const si of serverInfo) {
            si.server = await startHttpsServer(si.certGenerator, si.port);
        }

        await setup({ application: await createApplicationWithApi({}) });
    });

    after(async () => {
        await restoreTables();
        // eslint-disable-next-line no-restricted-syntax
        for (const si of serverInfo) {
            await si.certGenerator.cleanup();
            si.server?.close();
        }
    });

    describe('extra CA', () => {
        it('can request a HTTPS server with a self sign certificate registered', async () => {
            const { certGenerator: certGenerator1, port: port1 } = serverInfo[0];
            const data = await clientRequestWithTlsConfig({ extraCaFiles: [certGenerator1.caCertFile], port: port1 });
            assert.strictEqual(data, 'Well done!');
        });

        it('can self request a HTTPS server with a self sign certificate without adding extra CA', async () => {
            const { certGenerator: certGenerator1, port: port1 } = serverInfo[0];
            const data = await clientRequestWithTlsConfig({
                extraCaFiles: [],
                serverCrtFiles: certGenerator1.serverCertFile,
                port: port1,
            });
            assert.strictEqual(data, 'Well done!');
        });

        it('can request a HTTPS server with several self sign certificate registered', async () => {
            const { certGenerator: certGenerator1, port: port1 } = serverInfo[0];
            const { certGenerator: certGenerator2 } = serverInfo[1];
            const data = await clientRequestWithTlsConfig({
                extraCaFiles: [certGenerator1.caCertFile, certGenerator2.caCertFile],
                port: port1,
            });
            assert.strictEqual(data, 'Well done!');
        });

        it('cannot request a HTTPS server without the self sign certificate registered', async () => {
            const { port: port2 } = serverInfo[1];
            await checkTlsRequestError(() => clientRequestWithTlsConfig({ extraCaFiles: [], port: port2 }));
        });

        it('cannot self request a HTTPS server without the correct self sign certificate', async () => {
            const { certGenerator: certGenerator1 } = serverInfo[0];
            const { port: port2 } = serverInfo[1];
            await checkTlsRequestError(() =>
                clientRequestWithTlsConfig({
                    extraCaFiles: [],
                    serverCrtFiles: certGenerator1.serverCertFile,
                    port: port2,
                }),
            );
        });

        it('cannot request a HTTPS server with a wrong self sign certificate registered', async () => {
            const { certGenerator: certGenerator1 } = serverInfo[0];
            const { port: port2 } = serverInfo[1];
            await checkTlsRequestError(() =>
                clientRequestWithTlsConfig({ extraCaFiles: [certGenerator1.caCertFile], port: port2 }),
            );
        });
    });

    describe('TLS change listener', () => {
        it('can change TLS certs', async () => {
            const { server: server1, certGenerator: certGenerator1, port: port1 } = serverInfo[0];
            const certGenerator3 = await generateTestCertificates();
            const config: any = ConfigManager.current;
            if (!server1) {
                assert.fail('server not available');
            }
            const serverInternals = server1 as any;
            const originalCerts = serverInternals.cert;
            registerTlsChangeListener(server1, 'testTls.server.ssl', 'HTTPS test');
            config.testTls = { server: { ssl: { ...certGenerator3.certificateOptions } } };

            // create the condition variable
            const tlsApplyCondition = new Promise(resolve => {
                ConfigManager.emitter.once('tlsApply', (propertyPath: string) => {
                    resolve(propertyPath);
                });
            });

            ConfigManager.emitter.emit('tlsChange', 'testTls.server.ssl', config.testTls);

            // wait for the change to be applied
            let propPath = await tlsApplyCondition;
            assert.strictEqual(propPath, 'testTls.server.ssl');

            assert.notStrictEqual(originalCerts, serverInternals.cert);
            let data = await clientRequestWithTlsConfig({ extraCaFiles: [certGenerator3.caCertFile], port: port1 });
            assert.strictEqual(data, 'Well done!');

            // restore original certificate config
            config.testTls.server.ssl = { ...certGenerator1.certificateOptions };

            // create the condition variable
            const tlsRestoreCondition = new Promise(resolve => {
                ConfigManager.emitter.once('tlsApply', (propertyPath: string) => {
                    resolve(propertyPath);
                });
            });

            ConfigManager.emitter.emit('tlsChange', 'testTls.server.ssl', config.testTls);

            // wait for the change to be applied
            propPath = await tlsRestoreCondition;
            assert.strictEqual(propPath, 'testTls.server.ssl');

            assert.strictEqual(originalCerts, serverInternals.cert);
            data = await clientRequestWithTlsConfig({ extraCaFiles: [certGenerator1.caCertFile], port: port1 });
            assert.strictEqual(data, 'Well done!');

            delete config.testTls;
        });
    });
});

function startHttpsServer(certGenerator: TestCertificateGenerator, port: number): Promise<https.Server> {
    const server = https.createServer({ ...certGenerator.certificateOptions });

    server.on('request', (req, res) => {
        res.writeHead(200);
        res.end('Well done!');
    });

    return new Promise((resolve, reject) => {
        try {
            server.listen(port, () => {
                resolve(server);
            });
        } catch (err) {
            server.close();
            reject(err instanceof Error ? err : new Error(`Failed to start server on port ${port}: ${err}`));
        }
    });
}

function clientRequest(port: number, path?: string): Promise<string> {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port,
            path: path || '/',
            method: 'GET',
        };

        https
            .request(options, res => {
                res.on('data', data => {
                    resolve(data.toString());
                }).on('error', err => {
                    reject(err);
                });
            })
            .end();
    });
}

interface RequestOptions {
    extraCaFiles: string[];
    serverCrtFiles?: string;
    port: number;
    path?: string;
    conditionResult?: { cafiles?: string[] };
}

async function clientRequestWithTlsConfig(options: RequestOptions): Promise<string> {
    const { extraCaFiles, serverCrtFiles, port, path, conditionResult } = options;
    const oldSecurity = ConfigManager.current.security;
    const oldServer = ConfigManager.current.server;
    ConfigManager.current.security = {
        tls: {
            extraCaFiles,
        },
    };
    if (serverCrtFiles) {
        ConfigManager.current.server = {
            sslShallowCopy: {
                cert: serverCrtFiles,
            },
        } as InternalServerConfig;
    }

    // create the condition variable
    const extraCaCondition = new Promise<string[]>(resolve => {
        ConfigManager.emitter.once('extraCa', (caFiles: string[]) => {
            resolve(caFiles);
        });
    });

    // to notify the security listener of the change
    globalThis.setImmediate(() => ConfigManager.emitter.emit('loaded'));

    // Wait to make the tls config listener reloading the ca pem
    const cafiles = await extraCaCondition;
    if (conditionResult) {
        conditionResult.cafiles = cafiles;
    }
    const data = await clientRequest(port, path);
    ConfigManager.current.security = oldSecurity;
    ConfigManager.current.server = oldServer;

    const extraCaRestoreCondition = new Promise<string[]>(resolve => {
        ConfigManager.emitter.once('extraCa', (caFiles: string[]) => {
            resolve(caFiles);
        });
    });
    // to notify the security listener of the change
    globalThis.setImmediate(() => ConfigManager.emitter.emit('loaded'));
    await extraCaRestoreCondition;
    return data;
}
