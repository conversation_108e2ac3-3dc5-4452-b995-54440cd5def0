// tslint:disable: no-unused-expression
import { assert } from 'chai';
import { htmlSanitizer } from '../../lib/security/xss';

describe('XSS', () => {
    describe('Sanitize with special report templates', () => {
        it('preserves comments and special attributes from a document template', async () => {
            const template = `<table class="query-table" data-context-object-type="SalesInvoice" data-context-object-path="xtremSales.salesInvoice.query.edges" data-context-filter="[]" data-context-list-order="{}">
<thead class="query-table-head">
  <tr class="query-table-row">
    <td class="query-table-cell" style="background-color: #0000001a;border: 1px solid #000000;padding: 2px;">
      <p>Invoice Number</p>
    </td>
    <td class="query-table-cell" style="background-color: #0000001a;border: 1px solid #000000;padding: 2px;">
      <p>Net Total</p>
    </td>
    <td class="query-table-cell" style="background-color: #0000001a;border: 1px solid #000000;padding: 2px;">
      <p>Gross Total</p>
    </td>
    <td class="query-table-cell" style="background-color: #0000001a;border: 1px solid #000000;padding: 2px;">
      <p>Country phone code</p>
    </td>
    <td class="query-table-cell" style="background-color: #0000001a;border: 1px solid #000000;padding: 2px;">
      <p>Order Date</p>
    </td>
    <td class="query-table-cell" style="background-color: #0000001a;border: 1px solid #000000;padding: 2px;">
      <p>Delivery Date</p>
    </td>
  </tr>
</thead>
<tbody class="query-table-body">
  <!--{{#each xtremSales.salesInvoice.query.edges}}{{#with node}}-->
  <tr class="query-table-row">
    <td class="query-table-cell" style="border: 1px solid #000000;padding: 2px">
      <p>
        <span class="property" data-property-display-label="Invoice Number" data-property-data-type="String" data-property-name="invoiceNumber" data-property-data-format="">{{invoiceNumber}}</span>
      </p>
    </td>
    <td class="query-table-cell" style="border: 1px solid #000000;padding: 2px">
      <p>
        <span class="property" data-property-display-label="Net Total" data-property-data-type="Float" data-property-name="totalWithoutTax" data-property-data-format="">{{totalWithoutTax}}</span>
      </p>
    </td>
    <td class="query-table-cell" style="border: 1px solid #000000;padding: 2px">
      <p>
        <span class="property" data-property-display-label="Gross Total" data-property-data-type="Float" data-property-name="totalWithTax" data-property-data-format="">{{totalWithTax}}</span>
      </p>
    </td>
    <td class="query-table-cell" style="border: 1px solid #000000;padding: 2px">
      <p>
        <span class="property" data-property-display-label="Country phone code" data-property-data-type="Int" data-property-name="phoneNumber" data-property-data-format="">{{phoneNumber}}</span>
      </p>
    </td>
    <td class="query-table-cell" style="border: 1px solid #000000;padding: 2px">
      <p>
        <span class="property" data-property-display-label="Order Date" data-property-data-type="Date" data-property-name="orderDate" data-property-data-format="FullDate">{{formatDate orderDate 'FullDate'}}</span>
      </p>
    </td>
    <td class="query-table-cell" style="border: 1px solid #000000;padding: 2px">
      <p>
        <span class="property" data-property-display-label="Delivery Date" data-property-data-type="Date" data-property-name="deliveryDate" data-property-data-format="FullDate">{{formatDate deliveryDate 'FullDate'}}</span>
      </p>
    </td>
  </tr>
  <!--{{/with}}{{/each}}-->
  <tr class="query-table-row" data-hidden="1">
    <td class="query-table-cell" colspan="6">&nbsp;</td>
  </tr>
</tbody>
<tfoot class="query-table-footer">
  <tr class="query-table-row">
    <td class="query-table-cell">&nbsp;</td>
    <td class="query-table-cell">&nbsp;</td>
    <td class="query-table-cell">&nbsp;</td>
    <td class="query-table-cell">&nbsp;</td>
    <td class="query-table-cell">&nbsp;</td>
    <td class="query-table-cell">&nbsp;</td>
  </tr>
</tfoot>
</table>
<p>sadasdsa</p>
`;

            const escapedTemplate = await htmlSanitizer(template);
            assert.strictEqual(escapedTemplate, template);
        });

        it('can sanitize prettyfied style attribute', async () => {
            const template = `<div
  style="
      text-align: left;
      padding-left: 300px;
      position: relative;
      top: -15px;
  "
>`;
            const sanitized = await htmlSanitizer(template, { throwIfModified: true });
            assert.strictEqual(
                sanitized,
                `<div style="text-align: left;
      padding-left: 300px;
      position: relative;
      top: -15px;"></div>`,
            );
        });

        it('reject comments that are not strict handlebars', async () => {
            const comments = [
                'a simple comment',
                'a comment with {{#mustache}}',
                `<!--[if gte mso 9]>
<xml>
    <o:OfficeDocumentSettings>
    <o:AllowPNG/>
    <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
</xml>
<![endif]-->`,
                `<!--[if gt mso 15]>
<style type="text/css" media="all">
    /* Outlook 2016 Height Fix */
    table, tr, td {
    border-collapse: collapse;
    }

    tr {
    font-size: 0px;
    line-height: 0px;
    border-collapse: collapse;
    }
</style>
<![endif]-->`,
            ];
            // eslint-disable-next-line no-restricted-syntax
            for (const comment of comments) {
                const escapedTemplate = await htmlSanitizer(`<div><!-- ${comment} --></div>`);
                assert.notInclude(escapedTemplate, comment);
            }
        });

        it('can preserve meta encoding tag with utf-8', async () => {
            const template = '<!DOCTYPE html><html><head><meta charset="utf-8"></head><body>Hi</body></html>';
            const sanitized = await htmlSanitizer(template);
            assert.strictEqual(
                sanitized,
                '<!DOCTYPE html><html><head><meta charset="utf-8"></head><body>Hi</body></html>',
            );
        });

        it('can remove meta encoding tag with encoding other than utf-8', async () => {
            const template = '<!DOCTYPE html><html><head><meta charset="ascii"></head><body>Hi</body></html>';
            const sanitized = await htmlSanitizer(template);
            assert.strictEqual(sanitized, '<!DOCTYPE html><html><head><meta></head><body>Hi</body></html>');
        });
    });

    describe('Sanitize medium size reports without blocking the events loop', () => {
        const itemCounts = 1000;
        it(`Sanitize html with ${itemCounts} items`, async () => {
            const genItems = (count: number) => {
                const itemLines: string[] = [];
                for (let i = 1; i <= count; i += 1) {
                    itemLines.push(`
                    <!---->
                    <tr>
                    <td style="border: 1px solid #000000; padding: 2px">
                        <p>
                            <span>${i}</span>
                        </p>
                    </td>
                    <td class="query-table-cell" style="border: 1px solid #000000; padding: 2px">
                        <p>
                            <span data-property-parent-context="Item">Item ID ${i}</span>
                        </p>
                    </td>
                    </tr>
                    <tr data-hidden="1">
                        <td colspan="2"><p>&nbsp;</p></td>
                    </tr>
`);
                }
                return itemLines.join('');
            };
            const body = `<table>
                <thead>
                    <tr>
                        <td style="background-color: #0000001a; border: 1px solid #000000; padding: 2px">
                            <p>_id</p>
                        </td>
                        <td style="background-color: #0000001a; border: 1px solid #000000; padding: 2px">
                            <p>ID</p>
                        </td>
                    </tr>
                </thead>
                <tbody class="query-table-body">
                    ${genItems(itemCounts)}
                    <tr data-hidden="1">
                        <td colspan="2">&nbsp;</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td><p>&nbsp;</p></td>
                        <td><p>&nbsp;</p></td>
                    </tr>
                </tfoot>
            </table>`;

            const html = `<!DOCTYPE html>
<html><head>
        <style>
        :root {
            --background: #FFFFFF;
            --black55: rgba(0, 0, 0, 0.55);
            --black65: rgba(0, 0, 0, 0.65);
            --black90: rgba(0, 0, 0, 0.90);
            --error: #C7384F;
            --gold: #FFB500;
            --info: #0077C8;
            --logo: #00DC00;
            --slate: #003349;
            --slate20: #335C6D;
            --slate40: #668592;
            --slate60: #99ADB6;
            --slate80: #CCD6DB;
            --slate90: #E5EAEC;
            --slate95: #F2F5F6;
            --success: #00B000;
            --tableSeparator: #D9E0E4;
            --textAndLabels: rgba(0, 0, 0, 0.85);
            --themePrimary: #0073C2;
            --themePrimaryHover: #005C9A;
            --warning: #E96400;
        }

        /* The \`<figure>\` element uses \`display:block\`, so \`<figcaption>\` also has to. */
        @media print { body { -webkit-print-color-adjust: exact; } }
        body {
            font-size: 12pt;
            margin: 0;
        }

        h1,h2,h3,h4,th{
            color: var(--themePrimary);
        }

        .xtrem-page-break {
            page-break-after: always;
        }


        </style>
    </head>
    <body class="ck ck-content">${body}
    </body>
</html>`;

            let hits = 0;
            const timer = setInterval(() => {
                hits += 1;
            }, 100);
            const changes = { records: [], removed: [] };
            const sanitized = await htmlSanitizer(html, { changes });
            clearInterval(timer);
            assert.isTrue(hits > 1);
            assert.strictEqual(changes.records?.length, 0);
            assert.strictEqual(changes.removed?.length, 0);
            // no xml tags from the comments in the above style tag
            assert.notInclude(sanitized, '`<figure>`');
            // style tag is preserved
            assert.match(sanitized, /<style>\s*:root\s*{/);
            assert.include(sanitized, '<span data-property-parent-context="Item">Item ID 1</span>');
            assert.include(sanitized, `<span data-property-parent-context="Item">Item ID ${itemCounts}</span>`);
        }).timeout(10000);
    });
});
