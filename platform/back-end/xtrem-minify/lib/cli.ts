import { spawnSync } from 'child_process';
import * as fs from 'fs';
import * as glob from 'glob';
import * as path from 'path';
import * as xtremBuild from './index.js';

const args = process.argv.slice(2);

if (args.includes('-c')) {
    args[args.indexOf('-c')] = '--compile';
}

if (args.includes('-h')) {
    args[args.indexOf('-h')] = '--help';
}

if (args.includes('-v')) {
    args[args.indexOf('-v')] = '--version';
}

if (args.includes('-z')) {
    args[args.indexOf('-z')] = '--compress';
}

if (args.includes('-e') || args.includes('--source')) {
    throw new Error(
        'The `-e` or `--source` option is not supported anymore. Use `--compile` with a file or a list of files instead.',
    );
}

if (args.includes('-d') || args.includes('--delete-source')) {
    console.warn('The `-d` or `--delete-source` option is deprecated.');
}

class Program {
    dirname: string;

    filename: string;

    nodeBin: string;

    flags: string[];

    inclusions: string[];

    exclusions: string[];

    files: string[];

    constructor() {
        this.dirname = __dirname;
        this.filename = __filename;
        this.nodeBin = process.argv[0];
        this.flags = args.filter((arg) => arg[0] === '-' && arg[1] === '-');
        this.inclusions = args.filter((arg) => arg[0] !== '-' && arg[1] !== '-');
        this.exclusions = args.filter((arg) => arg[0] === '-' && arg[1] !== '-').map((arg) => arg.substring(1));
        this.files = [];
    }

    async compile(): Promise<Program> {
        // eslint-disable-next-line import/no-dynamic-require, global-require
        const packageJson = require(path.join(dir, 'package.json'));
        const packageMain = packageJson.main;
        if (packageMain) this.exclusions.push(packageMain);
        const exclusionGlobs = this.exclusions.map((exclusion) => path.resolve(exclusion));
        const includeGlobs = [];
        if (this.inclusions.length === 0 && !this.flags.includes('--source')) {
            includeGlobs.push(`${dir}/**/*.js`);
        } else {
            this.inclusions.forEach((f) => includeGlobs.push(path.resolve(f)));
        }
        this.files = glob.sync(includeGlobs, { ignore: exclusionGlobs });
        if (!this.files?.length) {
            console.warn(`No files to process in '${dir}'.`);
            process.exit(0);
        }

        await Promise.all(
            this.files.map(async (f) => {
                const filename = path.resolve(f);

                if (fs.existsSync(filename) && fs.statSync(filename).isFile()) {
                    const compileAsModule = !this.flags.includes('--no-module');

                    try {
                        const compileFileOptions = {
                            filename,
                            compileAsModule,
                            compress,
                            minify: true,
                            module: packageJson.type === 'module',
                        };

                        await xtremBuild.compileFile({ ...compileFileOptions });
                    } catch (error) {
                        console.error(error);
                    }
                } else {
                    console.error(`Error: Cannot find file '${filename}'.`);
                }
            }),
        );

        return this;
    }
}

const program = new Program();

const compress = program.flags.includes('--compress');

const dir = process.cwd();

const startedAt = Date.now();

if (program.flags.includes('--compile')) {
    program
        .compile()
        .then(() => {
            const { files } = program;
            console.log(
                `Compiled to binary(minify) ${files.length} file${files.length === 1 ? '' : 's'} in ${Date.now() - startedAt}ms.`,
            );
        })
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
} else if (program.flags.includes('--help')) {
    // eslint-disable-next-line no-console
    console.log(`
  Usage: xtrem-minify [option] [ FILE... | - ] [arguments]

  Options:
    -c, --compile [ FILE... | - ]     compile stdin, a file, or a list of files
      --no-module                   compile without producing commonjs module
    -h, --help                        show help information.
    -v, --version                     show xtrem-minify version.
    -z, --compress                    compress binary source

  Examples:

  $ xtrem-minify -c script.js             compile 'script.js' in-place to its minitied version.
  $ xtrem-minify -c server.js app.js
  $ xtrem-minify -c                       compile all '.js' files in 'build/' directory.
  $ xtrem-minify -c src/*.js              compile all '.js' files in 'src/' directory.
  $ xtrem-minify                          open Node REPL with xtrem-minify pre-loaded.
`);
} else if (program.flags.includes('--version') && program.flags.length === 1 && program.inclusions.length === 0) {
    // eslint-disable-next-line global-require
    const pkg = require('./package.json');

    // eslint-disable-next-line no-console
    console.log(pkg.name, pkg.version);
} else {
    try {
        spawnSync(program.nodeBin, ['-r', path.resolve(__dirname, 'index.js')].concat(args), {
            stdio: 'inherit',
        });
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error);
    }
}
