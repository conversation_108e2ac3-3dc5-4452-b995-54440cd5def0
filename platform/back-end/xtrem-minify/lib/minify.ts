import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import type { MinifyOutput } from 'terser';
import * as minifier from 'terser';

const rootDir = path.resolve(__dirname, '../../../..');

const encoding: BufferEncoding = 'utf8';

interface BaseCompileOptions {
    output?: string;
    compileAsModule?: boolean;
    getPreservedRanges?: (source: string) => [number, number][];
    compress?: boolean;
    minify?: boolean;
    verbose?: boolean;
}

export interface CompileFileArguments extends BaseCompileOptions {
    filename: string;
}

export interface CompileOptions extends BaseCompileOptions {
    replacementSource?: Buffer;
    module?: boolean;
}

export interface CompileCodeOptions extends CompileOptions {
    filename: string;
}

export interface SystemInfo {
    nodeVersion: string;
    arch: string;
}

export interface Content {
    systemInfo: Uint8Array;
    dummyCode: Uint8Array;
    bytecodeBuffer: Uint8Array;
}

export function getSystemInfo(): SystemInfo {
    return { nodeVersion: process.version, arch: os.arch() };
}

async function minifyCode(javascriptCode: string, options?: CompileCodeOptions): Promise<MinifyOutput> {
    const { filename } = options || {};
    // console.log(
    //     `Uglifying ${filename?.substring(rootDir.length + 1)} (${Buffer.byteLength(javascriptCode)} bytes)...`,
    // );

    let mapContent = '';
    const mapFile = `${filename}.map`;
    if (filename && fs.existsSync(mapFile)) {
        mapContent = fs.readFileSync(mapFile, 'utf-8');
    }
    const requiredNames = Array.from(javascriptCode.matchAll(/const ([a-zA-Z0-9_]+) = require\(/g)).map(
        (match) => match[1],
    );
    const xtremRequires = requiredNames.filter((name) => name.startsWith('xtrem'));
    try {
        const result = await minifier.minify(javascriptCode, {
            // keep_fnames is required to prevent from having anonymous class
            keep_fnames: true,
            mangle: {
                // used in ts to sql functions, we may inspect the input code to allow mangling in other files
                // Context.getConfigurationValue is transformed to xtrem_core_1.Context.getConfigurationValue by tsc
                reserved: ['typesLib', ...xtremRequires],
            },
            compress: options?.compress
                ? {
                      // TO INVESTIGATE: boolean options create issues at runtime
                      // prevent replacing true and false by !0 and !1
                      booleans: false,
                      booleans_as_integers: false,
                      // prevent rewriting ternary operators
                      // ex: const a = b ? c.p : d.p; => const a = (b ? c : d).p;
                      conditionals: false,
                      // adjust compress options to prevent from breaking the code parsed by ts to sql
                      // do not reduce vars which can lead to const replaced by var
                      reduce_vars: false,
                      // The name of this option is confusing, we need to set it to false to prevent modifying 'if ... return x; return y;' to 'if ... return x else return y;'
                      if_return: false,
                  }
                : false,
            output: {
                preamble: `/* Copyright (c) 2020-${new Date().getFullYear()} The Sage Group plc or its licensors. Sage, Sage logos, and Sage product and service names mentioned herein are the trademarks of Sage Global Services Limited or its licensors. All other trademarks are the property of their respective owners. */`,
            },
            sourceMap: {
                content: mapContent,
                url: path.basename(mapFile),
            },
            // 2022 is not recognized by terser, so we use 2020
            ecma: 2020,
        });

        return result;
    } catch (error) {
        throw new Error(
            `Error minifying ${filename?.substring(rootDir.length + 1)}: ${error instanceof Error ? error.message : error}`,
        );
    }
}

export async function compileAllFiles(packageDir: string, files: string[], options: CompileOptions): Promise<void> {
    await Promise.all(
        files.map(async (filename) => {
            await compileFile({ ...options, minify: true, filename });
        }),
    );
}

/**
 * Compiles JavaScript file to a minified .js file.
 * @param   options
 * @returns The compiled filename
 */
export async function compileFile(options: CompileCodeOptions): Promise<string> {
    const { verbose, filename, minify } = options;
    const compileAsModule = options.compileAsModule === undefined ? true : options.compileAsModule;
    const extension = '.js';

    const compiledFilename = options.output || `${filename.slice(0, -3)}${extension}`;
    const scriptCode = fs.readFileSync(filename, encoding).replace(/^#!.*/, '');

    const codeOptions: CompileCodeOptions = {
        ...options,
        filename,
        compileAsModule,
        minify,
        replacementSource: undefined,
    };

    const { code, map } = await minifyCode(scriptCode, codeOptions);
    if (code === undefined) {
        throw new Error(`Failed to compile ${filename}. No code returned from minifier.`);
    }
    if (verbose) {
        // eslint-disable-next-line no-console
        console.log(
            `Compiled ${filename?.substring(rootDir.length + 1)} (${Buffer.byteLength(scriptCode)} bytes) to ${compiledFilename?.substring(rootDir.length + 1)} (${Buffer.byteLength(code)} bytes) `,
        );
    }
    fs.writeFileSync(compiledFilename, code, { encoding });
    if (map) {
        fs.writeFileSync(`${compiledFilename}.map`, typeof map === 'string' ? map : JSON.stringify(map), { encoding });
    }
    return compiledFilename;
}
