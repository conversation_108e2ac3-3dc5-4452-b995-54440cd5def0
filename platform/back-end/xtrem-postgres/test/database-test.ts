import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { AsyncResponse, SqlConfig } from '@sage/xtrem-shared';
import { assert, use } from 'chai';
import { DatabaseService } from '../lib/database-service';
import { DatabaseParametersType } from '../lib/types';
import { setup } from './fixtures/setup';

use(require('chai-as-promised'));

setup();

// clone config to avoid side effects
const storage = { ...ConfigManager.current.storage! };

const customConfig = {
    user: 'xtrem_user_test',
    database: 'xtrem_database_test',
    schemaName: 'xtrem_schema_test',
};

storage.sql = {
    ...storage.sql!,
    ...customConfig,
};

const sqlConfig = storage.sql;

const databaseOptions: DatabaseParametersType = {
    encoding: 'UTF8',
    lcCollate: 'und-x-icu',
    lcCtype: 'und-x-icu',
    connectionLimit: -1,
    template: 'template0',
    tableSpaceName: 'pg_default',
};

async function withTestDatabaseService(
    dbConfig: SqlConfig,
    body: (service: DatabaseService) => AsyncResponse<void> | void,
): Promise<void> {
    const service = (DatabaseService as any)._service;
    try {
        (DatabaseService as any)._service = null;
        await body(DatabaseService.getInstance(dbConfig));
    } finally {
        (DatabaseService as any)._service = service;
    }
}
describe('Database Create', () => {
    if (!sqlConfig?.sysUser) {
        it.skip('Test skipped on pool. sysUser missing.', () => {});
        return;
    }

    it('Create / Drop Database with default options', () =>
        withTestDatabaseService(sqlConfig, async service => {
            await service.createDatabaseIfNotExists();
            assert.isTrue(await service.databaseExists(sqlConfig.database));
            const sysPool = service.sysPool;
            assert.isDefined(sysPool);
            await service.dropDatabaseIfExists();
            assert.isFalse(await service.databaseExists(sqlConfig.database));
        }));

    // should give an error message if sysPool does not exist
    it('Throws an error if database does not exist', () =>
        withTestDatabaseService(sqlConfig, async service => {
            await assert.isRejected(service.checkThatDatabaseExists(), 'Database not created yet');
        }));

    // should give an error message if sysUser does not exist
    it('Throws an error if sysUser does not exist', async () => {
        const testConfig = { ...sqlConfig };
        delete (testConfig as any).sysUser;
        await withTestDatabaseService(testConfig, service =>
            assert.throw(() => {
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                service.sysPool;
            }, 'invalid config: sysUser config missing.'),
        );
    });

    // should give an error message if sysDatabase does not exist
    it('Throws an error if sysDatabase does not exist', async () => {
        const testConfig = { ...sqlConfig };
        delete (testConfig as any).sysDatabase;
        await withTestDatabaseService(testConfig, service =>
            assert.throw(() => {
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                service.serverPool;
            }, 'invalid config: sysDatabase config missing.'),
        );
    });

    // Seen with Walid:
    // Collation und-x-icu doesn't work on Aurora, so was removed from the options of createDatabase.
    // The database is thus created with Postgres default collation on the user machine.
    it('Check database parameters', () =>
        withTestDatabaseService(sqlConfig, async service => {
            await service.createDatabaseIfNotExists();
            assert.isTrue(await service.databaseExists(sqlConfig.database));

            const parameters = await service.serverPool.withConnection(
                async cnx =>
                    (
                        await service.serverPool.execute<{ name: string; connectionLimit: number }[]>(
                            cnx,
                            `SELECT c.datname as name, c.datconnlimit as "connectionLimit"
                    FROM pg_database c WHERE c.datname = $1`,
                            [sqlConfig.database],
                        )
                    )[0],
            );

            assert.deepEqual(parameters, {
                name: sqlConfig.database,
                connectionLimit: databaseOptions.connectionLimit,
            });

            await service.dropDatabaseIfExists();
            assert.isFalse(await service.databaseExists(sqlConfig.database));
        }));

    it('Check missing config', () => {
        const testConfig = { ...sqlConfig };
        delete (testConfig as any).sysPassword;

        return withTestDatabaseService(testConfig, service => {
            assert.throw(() => {
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                service.sysPool;
            }, 'invalid config: sysPassword config missing.');
        });
    });

    it('Security Check - SQL Injection for Space and Special characters', async () => {
        // These tests are sync. No need for async overhead
        await asyncArray(['hack sysUser', 'hack.[]{}()<>*+-=!?^$|sysUser']).forEach(sysUser =>
            withTestDatabaseService(
                {
                    ...sqlConfig,
                    sysUser,
                },
                service =>
                    assert.throw(() => {
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        service.sysPool;
                    }, `Invalid sysUser value: '${sysUser}'.`),
            ),
        );

        await asyncArray(['hack user', 'hack.[]{}()<>*+-=!?^$|user']).forEach(user =>
            withTestDatabaseService(
                {
                    ...sqlConfig,
                    user,
                },
                service =>
                    assert.throw(() => {
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        (service as any).userPool;
                    }, `Invalid user value: '${user}'.`),
            ),
        );

        await asyncArray(['hack database', 'hack.[]{}()<>*+-=!?^$|database']).forEach(database =>
            withTestDatabaseService(
                {
                    ...sqlConfig,
                    database,
                },
                service =>
                    assert.throw(() => {
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        service.sysPool;
                    }, `Invalid database value: '${database}'.`),
            ),
        );
    });

    it('can timeout statement', async () => {
        const testConfig: SqlConfig = { ...sqlConfig, statementTimeoutMillis: 1000 };
        await withTestDatabaseService(testConfig, async service => {
            await service.createDatabaseIfNotExists();
            assert.isTrue(await service.databaseExists(sqlConfig.database));
            const pool = service.serverPool;
            assert.isDefined(pool);
            await assert.isRejected(
                pool.withConnection(cnx => pool.execute(cnx, 'SELECT pg_sleep(2)')),
                'canceling statement due to statement timeout',
            );
        });
    });

    after(() => withTestDatabaseService(sqlConfig, service => service.dropDatabaseIfExists()));
});
