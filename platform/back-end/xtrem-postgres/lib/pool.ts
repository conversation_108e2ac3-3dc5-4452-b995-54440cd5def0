// /* eslint-disable class-methods-use-this */

/** @module @sage/xtrem-postgres */
// why query on collections when insert? --> planned work to separate collections vs references + scope *.
// too many connections errors in lists (cache?)  --> pool
// cache schemas?? --> double check and provide traces.
import {
    AnyValue,
    AsyncArrayReader,
    AsyncGenericReader,
    AsyncReader,
    AsyncResponse,
    Funnel,
    funnel,
} from '@sage/xtrem-async-helper';
import { DateValue, Datetime, Time } from '@sage/xtrem-date-time';
import { Logger } from '@sage/xtrem-log';
import { LogLevel, LoggerInterface, LogicError, ProfilerCallback, SystemError, isEnvVarTrue } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { EventEmitter } from 'node:events';
import * as postgres from 'pg';
import * as copyStreams from 'pg-copy-streams';
import * as PostgresCursor from 'pg-cursor';
import * as Pool from 'pg-pool';
import { Counter, Gauge } from 'prom-client';
import { createProfiler } from './profiler';
import { SqlRecorder } from './sql-recorder';
import {
    Connection,
    PoolConfig,
    PoolLoggerInterface,
    PostgresPoolReaderOptions,
    PostgresSqlExecuteOptions,
} from './types';

export const copyTo = copyStreams.to;
export const copyFrom = copyStreams.from;

export type CommonPool = Pick<Pool<postgres.Client>, 'connect' | 'on' | 'end'>;

function sanitizeSqlRequest(sql: string): string {
    return sql.replace(/`/g, '"');
}

function query(cnx: Connection, sql: string, args: any): Promise<any> {
    const sqlQuery = sanitizeSqlRequest(sql);

    return new Promise((resolve, reject) => {
        cnx.query(sqlQuery, args, (err: any, res: any) => {
            if (err) {
                reject(err instanceof Error ? err : new SystemError(`Postgres error: ${err.message}`, err));
                return;
            }
            if (sql.startsWith('/*RAW*/')) {
                resolve(res);
            } else if (res.rows && res.rows.length > 0 && res.command === 'INSERT') {
                resolve(res.rows[0]);
            } else if (!res.rows || (res.rows.length === 0 && res.command !== 'SELECT')) {
                resolve(res.rowCount);
            } else {
                resolve(res.rows);
            }
        });
    });
}

// https://github.com/brianc/node-pg-types/blob/master/lib/builtins.js
postgres.types.setTypeParser(postgres.types.builtins.INT8, val => {
    return parseInt(val, 10);
});

// Return dates as YYYY-MM-DD strings rather than JS dates.
// JS date is a bad choice because it has a time component and its date part may depend on the client's
// local timezone.
// See https://github.com/brianc/node-postgres/issues/1844
postgres.types.setTypeParser(postgres.types.builtins.DATE, val => val);

// Default logger so that we get messages on the console if pool is used before logger is installed
const defaultLogger: PoolLoggerInterface = {
    // eslint-disable-next-line no-console
    error: console.error,
    // eslint-disable-next-line no-console
    warn: console.warn,
    // eslint-disable-next-line no-console
    info: console.log,
    // eslint-disable-next-line no-console
    verbose: (formatMessage: () => string): void => console.log(formatMessage()),
    log: (logLevel: LogLevel, formatMessage: () => string): undefined => {
        // eslint-disable-next-line no-console
        console.log(formatMessage());
        return undefined;
    },
};

export interface DatabaseVersion {
    text: string;

    major: number;
    minor: number;
}

export interface DatabaseSettings {
    // max number of connections, may be much higher than pool size
    max_connections: number;
    // reserved connections that we should not allocate
    reserved_connections: number;
    // superuser reserved connections that we should not allocate
    superuser_reserved_connections: number;
    // shared buffers - size is returned as a human readable string
    shared_buffers: string;
}

export interface DatabaseInfo {
    version: DatabaseVersion;
    settings: DatabaseSettings;
}

export interface ConnectionPoolSetupOptions {
    logger: LoggerInterface;
    metricsEmitter?: EventEmitter;
}

export type ConnectionPoolType = 'server' | 'sys' | 'user';

interface SqlPoolStats {
    /** Number of connections that are currently idle */
    idleCount: number;
    /** Number of connections that are currently busy */
    busyCount: number;
    /** Number of connections that are currently waiting to be created */
    waitingCount: number;
    /** Number of connections that are currently being created */
    totalCount: number;
    /** Number of connection allocation failures for the given attempt, final failures are stored at index 0 */
    tryCounts: number[];
    /** Number of queues for connection allocation */
    allocationQueuesCount: number;
    /** Max number of queues reach for connection allocation */
    allocationQueuesMaxCount: number;
}

const allMetricsEnabled = isEnvVarTrue(process.env.XTREM_ALL_METRICS_ENABLED);
const sqlMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_SQL_METRICS_ENABLED);

const poolMetrics = sqlMetricsEnabled
    ? {
          stats: new Gauge({
              name: 'xtrem_sql_pool_stats',
              help: 'The SQL pool stats',
              labelNames: ['poolType', 'statName'],
          }),
          retries: new Counter({
              name: 'xtrem_sql_pool_retries',
              help: 'Number of retries for connection allocation',
              labelNames: ['poolType', 'attemptName'],
          }),
          // length of each allocation queue indexed by originId
          allocationQueues: new Gauge({
              name: 'xtrem_sql_pool_allocation_queue_length',
              help: 'Length of allocation queues per originId',
              labelNames: ['poolType', 'originId'],
          }),
      }
    : {};

/** Type for the callbacks that are queued in allocation queues */
type AllocationCallback = (error: Error | undefined, connection?: Connection) => void;

/**
 * Allocation queue.
 * We create one queue per GraphQl or notification request that is waiting for SQL connections.
 */
interface AllocationQueue {
    /** The originId of the request, '<unknown>' for request that don't have an origin id, e.g., CLI commands) */
    originId: string;

    /** The callbacks that have been queued and that will resolve the promises */
    callbacks: AllocationCallback[];
}

export class ConnectionPool {
    private _user: string;

    private _pool: Pool<postgres.Client> | undefined;

    /**
     * Throttling logger (sage/xtrem-postgres/throttling key).
     * Set level to verbose to trace connection throttling.
     */
    readonly #throttlingLogger: Logger;

    #updateMetricsStamp = Date.now();

    readonly #allocationFunnel: Funnel;

    sqlRecorder?: SqlRecorder;

    trace: (...args: any[]) => void;

    // /** internal */
    constructor(
        public readonly type: ConnectionPoolType,
        private readonly config: PoolConfig,
    ) {
        this._user = config.user;
        if (config.trace) {
            this.trace = config.trace;
        }
        this.config.connectString = `${config.hostname + (config.port ? `:${config.port}` : '')}/${config.database}`;
        this.#throttlingLogger = Logger.getLogger(__filename, 'throttling');
        if (ConnectionPool.metricsEmitter) {
            ConnectionPool.metricsEmitter?.on('collected', () => {
                this.updateMetrics();
            });
        }
        this.#allocationFunnel = funnel(Number(this.config.connectionAllocationFunnelSize) || 50);
    }

    get user(): string {
        return this._user;
    }

    static logger: PoolLoggerInterface = defaultLogger;

    private static metricsEmitter: EventEmitter | undefined;

    readonly #stats: Omit<SqlPoolStats, 'idleCount' | 'busyCount' | 'waitingCount' | 'totalCount'> = {
        // -1 to mark that the counter has not been initialized
        tryCounts: [-1, -1, -1, -1],
        allocationQueuesCount: 0,
        allocationQueuesMaxCount: 0,
    };

    private get stats(): SqlPoolStats | undefined {
        if (!this._pool) {
            return undefined;
        }

        this.#stats.allocationQueuesMaxCount = Math.max(
            this.#stats.allocationQueuesMaxCount,
            this.#allocationQueues.length,
        );

        return {
            idleCount: this._pool.idleCount,
            busyCount: this._pool.totalCount - this._pool.idleCount,
            waitingCount: this._pool.waitingCount,
            totalCount: this._pool.totalCount,
            tryCounts: this.#stats.tryCounts,
            allocationQueuesCount: this.#allocationQueues.length,
            allocationQueuesMaxCount: this.#stats.allocationQueuesMaxCount,
        };
    }

    static setup(options: ConnectionPoolSetupOptions): void {
        const { logger, metricsEmitter } = options;
        this.logger = logger;
        this.metricsEmitter = metricsEmitter;
    }

    private incrementRetryCounter(poolType: ConnectionPoolType, currentTry: number): void {
        const count = this.#stats.tryCounts[currentTry] ?? 0;
        this.#stats.tryCounts[currentTry] = count === -1 ? 1 : count + 1;
        if (sqlMetricsEnabled) {
            poolMetrics.retries?.inc(
                { poolType, attemptName: currentTry === 0 ? 'failures' : `retry${currentTry}` },
                1,
            );
        }
    }

    private updateMetrics(force = false): void {
        // we should not refresh to frequently otherwise nothing appears in grafana dashboard
        if (!sqlMetricsEnabled || !this.stats || (!force && Date.now() - this.#updateMetricsStamp < 15000)) {
            return;
        }

        this.#updateMetricsStamp = Date.now();
        const { totalCount, idleCount, waitingCount, allocationQueuesCount, allocationQueuesMaxCount } = this.stats;
        const busyCount = totalCount - idleCount;

        if (poolMetrics.stats) {
            // see https://node-postgres.com/apis/pool#properties
            // The number of clients which are not checked out but are currently idle in the pool.
            poolMetrics.stats.set({ poolType: this.type, statName: 'total' }, totalCount);
            // The total number of clients existing within the pool.
            poolMetrics.stats.set({ poolType: this.type, statName: 'idle' }, idleCount);
            // The number of queued requests waiting on a client when all clients are checked out.
            poolMetrics.stats.set({ poolType: this.type, statName: 'waiting' }, waitingCount);
            poolMetrics.stats.set({ poolType: this.type, statName: 'busy' }, busyCount);
            // Number of queues for connection allocation
            poolMetrics.stats.set({ poolType: this.type, statName: 'queues' }, allocationQueuesCount);
            // Max number of queues for connection allocation
            poolMetrics.stats.set({ poolType: this.type, statName: 'maxQueues' }, allocationQueuesMaxCount);
        }
        if (poolMetrics.allocationQueues) {
            this.#allocationQueues.forEach(queue => {
                poolMetrics.allocationQueues.set(
                    { poolType: this.type, originId: queue.originId },
                    queue.callbacks.length,
                );
            });
        }
        if (poolMetrics.retries) {
            // We use tryCounts[0] as the initialization marker
            if (this.#stats.tryCounts[0] === -1) {
                this.#stats.tryCounts[0] = 0;
                // we initialize the counters to have a 0 value
                for (let i = 0; i <= 3; i += 1) {
                    poolMetrics.retries?.inc(
                        { poolType: this.type, attemptName: i === 0 ? 'failures' : `retry${i}` },
                        0,
                    );
                }
            }
        }
    }

    /**
     * Return the max count of connection the pool can open concurrently
     */
    get poolSize(): number {
        return this.config.max || 20;
    }

    /**
     * Returns the configuration used by the postgres pool
     */
    private get runtimeConfig(): Pool.Config<postgres.Client> {
        return {
            host: this.config.hostname,
            database: this.config.database,
            user: this.config.user,
            password: this.config.password,
            port: this.config.port,
            ssl: this.config.ssl || false,
            max: this.poolSize,
            maxUses: this.config.maxUses,
            idleTimeoutMillis: this.config.idleTimeoutMillis || 10000,
            // Keep connection timeout low (a few seconds)
            connectionTimeoutMillis: this.config.connectionTimeoutMillis || 5000,
            statement_timeout: this.config.statementTimeoutMillis,
            query_timeout: this.config.statementTimeoutMillis ? this.config.statementTimeoutMillis * 1.2 : undefined,
            options: '-c TimeZone=UTC',
        };
    }

    private async queryDatabaseInfo(): Promise<DatabaseInfo> {
        const cnx = await this.pool.connect();
        if (!cnx) throw new LogicError('no cnx');
        try {
            const { rows } = await cnx.query<{
                version: string;
                superuser_reserved_connections: number;
                max_connections: number;
                shared_buffers: string;
            }>(`
                SELECT
                    version() as version,
                    current_setting('superuser_reserved_connections')::INT as superuser_reserved_connections,
                    current_setting('max_connections')::INT as max_connections,
                    current_setting('shared_buffers') as shared_buffers;
                `);

            if (rows.length !== 1) throw new LogicError(`bad rows length: ${rows.length}`);

            const result = rows[0];
            const matches = result.version.match(/^PostgreSQL (\d+)\.(\d+)/);
            if (!matches) throw new LogicError(`cannot parse version: ${result.version}`);
            const [major, minor] = matches.slice(1).map(s => Number.parseInt(s, 10));
            const version: DatabaseVersion = { text: result.version, major, minor };

            // reserved_connections was introduced in PostgreSQL 16 and we did not fully deprecate older versions yet.
            let reservedConnections: number;
            if (version.major >= 16) {
                const { rows: rows16 } = await cnx.query<{ reserved_connections: number }>(
                    "SELECT current_setting('reserved_connections')::INT as reserved_connections;",
                );
                if (rows16.length !== 1) throw new LogicError(`bad rows length: ${rows16.length}`);
                reservedConnections = rows16[0].reserved_connections;
            } else {
                // Guess it, probably an overestimate
                reservedConnections = result.superuser_reserved_connections;
            }
            const settings = { ..._.omit(result, 'version'), reserved_connections: reservedConnections };
            const info = { version, settings };

            ConnectionPool.logger.info(`Database info: ${JSON.stringify(info)}`);

            return info;
        } finally {
            cnx.release();
        }
    }

    // store the database info into a static so that we don't query it in every pool
    static #databaseInfo: Promise<DatabaseInfo>;

    static #usablePoolSize = -1;

    async getUsablePoolSize(): Promise<number> {
        if (ConnectionPool.#databaseInfo == null) ConnectionPool.#databaseInfo = this.queryDatabaseInfo();
        const { settings } = await ConnectionPool.#databaseInfo;
        const { max_connections, reserved_connections, superuser_reserved_connections } = settings;
        const usablePoolSize = Math.min(
            this.poolSize,
            max_connections - reserved_connections - superuser_reserved_connections,
        );

        if (usablePoolSize <= 0) {
            throw new LogicError(
                'Invalid configuration: all postgres connections are reserved or config.storage.sql.max <= 0!',
            );
        }

        ConnectionPool.#usablePoolSize = usablePoolSize;

        return usablePoolSize;
    }

    private get pool(): CommonPool {
        if (this._pool) return this._pool;

        this._pool = new Pool(this.runtimeConfig);

        this._pool.on('error', err => {
            ConnectionPool.logger.error(`pg-pool error: ${err.message}`);
        });

        return this._pool;
    }

    /**
     * Function to execute passed callback function passing the current allocated connection to the callback and then releasing the connection
     * @param body
     */
    async withConnection<T extends AnyValue | void>(
        body: (cnx: Connection) => AsyncResponse<T>,
        connection?: postgres.PoolClient,
    ): Promise<T> {
        const cnx = connection ?? (await this.allocConnection('<unknown>'));
        try {
            return await body(cnx);
        } finally {
            if (!connection) this.releaseConnection(cnx);
        }
    }

    /**
     * Returns a buffer reader for the supplied BLOB
     * @param value
     */
    static async readBlob(val: any): Promise<Buffer | null> {
        if (val == null) {
            return null;
        }
        if (val instanceof Buffer) {
            return val;
        }
        if (typeof val === 'string') {
            return Buffer.from(val, 'base64');
        }
        const data = Buffer.concat(await ConnectionPool.blobReader(val).readAll());
        if (data == null) {
            return null;
        }
        if (data instanceof Buffer) {
            return data;
        }
        if (typeof data === 'string') {
            return Buffer.from(data, 'base64');
        }
        // readAll could return undefined so we have to check
        return Buffer.from(data);
    }

    /**
     *  Returns a buffer reader for the supplied CLOB
     * @param value
     */
    static async readClob(val: any): Promise<string | null> {
        if (val == null) {
            return null;
        }
        const data = (await ConnectionPool.clobReader(val).readAll()).join('');
        return data == null ? null : data;
    }

    /**
     * Returns the relevant buffer reader for the supplied BLOB
     * @param val
     */
    static blobReader(val: any): AsyncReader<Buffer> {
        return new AsyncArrayReader<Buffer>(() => [val]);
    }

    /**
     *  Returns the relevant string reader for the supplied CLOB
     * @param val
     */
    static clobReader(val: any): AsyncReader<string> {
        return new AsyncArrayReader<string>(() => [val]);
    }

    // notice should be NoticeMessage but this is not exported by pg :-(
    // eslint-disable-next-line class-methods-use-this
    onNotice = (notice: any): void => {
        switch (notice.severity) {
            case 'ERROR':
                ConnectionPool.logger.error(`POSTGRES ERROR: ${notice.message}`);
                break;
            case 'WARNING':
                ConnectionPool.logger.warn(`POSTGRES WARNING: ${notice.message}`);
                break;
            case 'INFO':
                ConnectionPool.logger.info(`POSTGRES INFO: ${notice.message}`);
                break;
            default:
                // DEBUG, LOG, NOTICE
                ConnectionPool.logger.verbose(() => `POSTGRES ${notice.severity}: ${notice.message}`);
                break;
        }
    };

    /**
     * Gets the current database connection, if one does not exists then a connection is created
     */
    private async _allocConnection(): Promise<Connection> {
        const maximumTries = this.config.connectionMaxRetries || 3;
        const retryMillis = this.config.connectionRetryMillis || 2000;
        const allErrors = [];

        for (let currentTry = 1; currentTry <= maximumTries; currentTry += 1) {
            try {
                const connection = await this.#allocationFunnel(() => this.pool.connect());
                const waitingCount = this._pool?.waitingCount ?? 0;
                if (waitingCount > this.poolSize * 0.15) {
                    ConnectionPool.logger.warn(
                        `Waiting for connections! totalCount: ${this._pool?.totalCount}, idleCount: ${this._pool?.idleCount}, waitingCount: ${this._pool?.waitingCount}`,
                    );
                }
                connection.removeAllListeners('notice');
                connection.on('notice', this.onNotice);

                return connection;
            } catch (error) {
                // Number of connection allocation failures on the current attempt
                this.updateMetrics(true);
                this.incrementRetryCounter(this.type, currentTry);
                ConnectionPool.logger.error(
                    `Retry: try number ${currentTry}/${maximumTries} (total: ${this.#stats.tryCounts[currentTry]}): Error \r\n${error.stack}`,
                );
                allErrors.push(error.message);
                await new Promise<void>(resolve => {
                    setTimeout(resolve, retryMillis);
                });
            }
        }
        // Number of connection allocation failures after all attempts
        this.updateMetrics(true);
        this.incrementRetryCounter(this.type, 0);
        ConnectionPool.logger.error(`Failed after ${maximumTries} retries (total: ${this.#stats.tryCounts[0]})`);
        throw new SystemError(allErrors.join('\n'));
    }

    /** Number of connections currently allocated from the postgres pool */
    #allocatedCount = 0;

    /** Allocation queues, one for each originId which is waiting for connections */
    readonly #allocationQueues: AllocationQueue[] = [];

    /** Index of the last queue which got dequeued */
    #allocationQueueIndex = 0;

    /**
     * Allocates a postgres connection.
     *
     * The allocation is throttled, to ensure that:
     * - we don't exhaust the pool
     * - concurrent requests are treated fairly.
     *
     * @param originId the originId of the request
     * @returns a postgres connection promise
     */
    async allocConnection(originId: string): Promise<Connection> {
        const usablePoolSize =
            ConnectionPool.#usablePoolSize < 0 ? await this.getUsablePoolSize() : ConnectionPool.#usablePoolSize;
        // If have allocated less than `usablePoolSize` then allocate directly.
        if (this.#allocatedCount < usablePoolSize) {
            this.#allocatedCount += 1;
            this.#throttlingLogger.verbose(() => `CONNECTION ALLOC  : allocated=${this.#allocatedCount}`);
            return this._allocConnection();
        }

        // Find or create a queue for the requested originId.
        let queue = this.#allocationQueues.find(q => q.originId === originId);
        if (!queue) {
            queue = { originId, callbacks: [] };
            this.#allocationQueues.push(queue);
        }
        this.updateMetrics(true);

        // We have found the queue where we will push our allocation request.
        const validQueue = queue;
        this.#throttlingLogger.verbose(() => {
            const lengths = this.#allocationQueues.map(q => q.callbacks.length);
            return `CONNECTION QUEUED : allocated=${this.#allocatedCount}, originId=${originId}, lengths=${lengths}`;
        });
        // Return a promise which will be resolved later, when a connection is released
        return new Promise((resolve, reject) => {
            // We just push a callback which will resolve or reject our promise.
            validQueue.callbacks.push((error, connection) => {
                if (error) reject(error);
                else if (connection) resolve(connection);
                else reject(new LogicError('no connection'));
            });
        });
    }

    /**
     * Releases a postgres connection.
     *
     * This method MUST be used to release connections allocated by the allocConnection method.
     *
     * @param connection the connection
     * @returns void
     */
    releaseConnection(connection: Connection): void {
        // Release the connection to the postgres pool.
        connection.release();

        // If there are no pending requests, we just update the allocated count and we return.
        if (this.#allocationQueues.length === 0) {
            this.#throttlingLogger.verbose(() => `CONNECTION RELEASE: allocated=${this.#allocatedCount}`);
            this.#allocatedCount -= 1;
            this.updateMetrics(true);
            return;
        }

        // Otherwise, we find the queue that we are going to wake up.
        // We use a very simple round-robin to be fair to all the request that are pending.
        this.#allocationQueueIndex = (this.#allocationQueueIndex + 1) % this.#allocationQueues.length;
        const queue = this.#allocationQueues[this.#allocationQueueIndex];
        this.#throttlingLogger.verbose(
            () =>
                `CONNECTION WAKEUP : allocated=${this.#allocatedCount}, originId=${queue.originId}, pending=${queue.callbacks.length}`,
        );

        // Dequeue the first callback from the selected queue.
        const callback = queue.callbacks.shift();
        if (!callback) throw new LogicError('no callback');

        // If the queue is empty, remove it from the list of queues
        if (queue.callbacks.length === 0) this.#allocationQueues.splice(this.#allocationQueueIndex, 1);

        // Allocate a connection and resolve its promise (or reject its promise if the allocation failed).
        this._allocConnection()
            .then(cnx => callback(undefined, cnx))
            .catch(error => callback(error));
        this.updateMetrics(true);

        // TODO: investigate if we can reuse the connection instead of releasing it and reallocating.
        // But we have to be careful about connections in error and about allocating from new db readers and writers
        // when Aurora scales.
    }

    private getProfiler(sql: string, args?: any[], logLevel?: LogLevel): ProfilerCallback | undefined {
        return createProfiler(() => sql, this.config.mapArgsInLogs, args, logLevel, ConnectionPool.logger);
    }

    /**
     * Function that executes SQL command on the provided connection and with the provided arguments and options.
     * @param cnx Database connection
     * @param sql SQL command
     * @param args SQL arguments
     * @param opts SQL options
     */
    async execute<T extends AnyValue | void>(
        cnx: Connection,
        sql: string,
        args?: any[],
        opts?: PostgresSqlExecuteOptions,
    ): Promise<T> {
        const sqlQuery = sanitizeSqlRequest(sql);
        const profiler = this.getProfiler(`[${this.user}] Execute ${sqlQuery}`, args, opts?.logLevel);
        let result: any;
        try {
            if (this.sqlRecorder) {
                this.sqlRecorder.recordSqlCommand(sqlQuery, args);
            }
            result = await query(cnx, sqlQuery, ConnectionPool.convertArgs(args || []));
            if (typeof result === 'number') {
                result = { updateCount: result };
            }
            profiler?.success(`Result = ${JSON.stringify(result)}`);
        } catch (err) {
            profiler?.fail(`Error '${err.stack}' on ${sql} args=${args}`);
            if (err.detail) {
                // Detail may contain some very useful infos (for instance, when a FK is violated, detail really helps)
                err.message = `${err.message}: ${err.detail}`;
                ConnectionPool.logger.error(err.message);
            }
            if (err.internalQuery) {
                err.message = `${err.message}\ninternalQuery: ${err.internalQuery}`;
            }
            throw new SystemError(err.message, err);
        }

        return result;
    }

    // TODO: this should be done in xtrem-core, not here.
    private static convertArgs(args: any[]): any {
        return args.map(arg => {
            if (DateValue.isDate(arg) || Datetime.isDatetime(arg)) return arg.toJsDate();
            if (Time.isTime(arg)) return arg.toString();
            return arg;
        });
    }

    /**
     * Creates a reader for the provided connection and SQL statement
     * @param cnx
     * @param sql
     * @param args
     * @param opts
     */
    createReader<T extends AnyValue>(
        cnx: Connection,
        sql: string,
        args: any[],
        opts?: PostgresPoolReaderOptions,
    ): AsyncReader<T> {
        const profiler = this.getProfiler(`[${this.user}] Reader ${sql}`, args, opts?.logLevel);
        const cursor = cnx.query(new PostgresCursor(sanitizeSqlRequest(sql), args || []));
        let firstRead = true;
        let count = 0;
        let chunks = [] as any[];
        const reader = new AsyncGenericReader({
            read: async () => {
                try {
                    if (firstRead) {
                        profiler?.success('query result ready');
                        firstRead = false;
                    }
                    if (chunks.length === 0) {
                        // read returns a Promise if a callback is not in the second parameter
                        chunks = await cursor.read(opts?.pageSize || 200);
                    }
                    const val = chunks.shift();
                    if (val !== undefined) count += 1;
                    return val;
                } catch (err) {
                    profiler?.fail(`Error '${err.stack}' on ${sql} args=${args}`);
                    throw err;
                }
            },
            stop: () => {
                profiler?.success(`POSTGRES ${count} records read`);
                return cursor.close();
            },
        });
        return reader;
    }

    /**
     * Release all database connections
     */
    async release(): Promise<void> {
        if (this._pool) await this._pool.end();
        this._pool = undefined;
    }
}
