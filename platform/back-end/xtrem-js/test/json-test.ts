import { assert } from 'chai';
import * as json5 from 'json5';
import { Interpreter, InterpreterOptions } from '../lib/interpreter';

const evalTimeout = 250;
const safeEval = (code: string | Function, options?: InterpreterOptions) =>
    Interpreter.safeEval(code, { timeout: evalTimeout, ...options });

describe('js interpreter json', () => {
    it('parse / stringify', () => {
        const objects = [
            'string',
            1,
            1.2,
            [],
            {},
            { stringVal: 'a string', integerVal: 1, numberVal: 1.2, objectVal: { a: 'a' }, arrayVal: [1, 2, 3] },
        ];
        objects.forEach(o => {
            const stringified = JSON.stringify(o);
            assert.deepEqual(safeEval(`return JSON.parse('${stringified}');`)?.value, o);
            assert.deepEqual(safeEval(`return JSON.stringify(${stringified});`)?.value, stringified);
            assert.deepEqual(safeEval(`return JSON.stringify(${json5.stringify(o)});`)?.value, stringified);
        });
    });
});
