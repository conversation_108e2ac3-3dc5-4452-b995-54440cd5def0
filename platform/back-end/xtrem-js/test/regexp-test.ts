import { assert } from 'chai';
import { Interpreter, InterpreterOptions } from '../lib/interpreter';

const evalTimeout = 250;
const safeEval = (code: string | Function, options?: InterpreterOptions) =>
    Interpreter.safeEval(code, { timeout: evalTimeout, ...options });

describe('js interpreter regexp', () => {
    it('init regexp', () => {
        assert.deepEqual(safeEval('return /ab+c/i;')?.value, /ab+c/i);
        assert.deepEqual(safeEval("return new RegExp('ab+c', 'i');")?.value, /ab+c/i);
        assert.deepEqual(safeEval("return RegExp('ab+c', 'i');")?.value, /ab+c/i);
        assert.deepEqual(safeEval("return new RegExp(/ab+c/, 'i');")?.value, /ab+c/i);

        assert.deepEqual(safeEval('return /\\w+/;')?.value, /\w+/);
        assert.deepEqual(safeEval("return new RegExp('\\\\w+');")?.value, /\w+/);
    });

    it('regexp functions', () => {
        assert.strictEqual(
            safeEval(() => {
                const re = /(\w+)\s(\w+)/;
                const str = 'Maria Cruz';
                return str.replace(re, '$2, $1');
            })?.value,
            'Cruz, Maria',
        );

        assert.deepEqual(
            safeEval(() => {
                const text = 'Some text\nAnd some more\r\nAnd yet\rThis is the end';
                return text.split(/\r\n|\r|\n/);
            })?.value,
            ['Some text', 'And some more', 'And yet', 'This is the end'],
        );

        assert.deepEqual(
            safeEval(() => {
                const s = 'Please yes\nmake my day!';
                return s.match(/yes.*day/);
            })?.value,
            null,
        );

        assert.deepEqual(
            safeEval(() => {
                const s = 'Please yes\nmake my day!';
                return s.match(/yes[^]*day/);
            })?.value,
            ['yes\nmake my day'],
        );

        assert.deepEqual(
            safeEval(() => {
                const text = 'Образец text на русском языке';
                const regex = /[\u0400-\u04FF]+/g;

                const match = regex.exec(text) || [];
                const match2 = regex.exec(text) || [];
                return { m: match[0], m2: match2[0], lastIndex: regex.lastIndex };
            })?.value,
            { m: 'Образец', m2: 'на', lastIndex: 15 },
        );

        assert.deepEqual(
            safeEval(() => {
                const breakfasts = ['bacon', 'eggs', 'oatmeal', 'toast', 'cereal'];
                const order = 'Let me get some bacon and eggs, please';

                return order.match(new RegExp(`\\b(${breakfasts.join('|')})\\b`, 'g'));
            })?.value,
            ['bacon', 'eggs'],
        );
    });

    it('complex regexp should throw a timeout error', () => {
        assert.throws(
            () =>
                safeEval(() => {
                    const re = /^(a|a)*$/;
                    const input = `${'a'.repeat(31)}\x00`;
                    return re.exec(input);
                }),
            '[CallExpression] RegExp Timeout: /^(a|a)*$/',
        );
    });
});
