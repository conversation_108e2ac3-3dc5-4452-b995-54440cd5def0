/**
 * @license
 * Copyright 2013 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * @fileoverview Interpreting JavaScript in JavaScript.
 * <AUTHOR> (<PERSON>)
 *
 * Modified by Sage
 */
import * as acorn from 'acorn';
import { JsObject } from './js-object';
import { JsScope } from './js-scope';
import { JsState } from './js-state';
import { JsTask } from './js-task';
import { Dict, FunctionWithId, InterpreterCode, JsValue, Maybe, StringStringFunction } from './types';
import { isFunction, objectHasOwnProperty } from './utils';

const debug = require('debug')('xtrem:js');

export interface InterpreterOptions {
    initFunc?: (interpreter: Interpreter, globalObject: Dict<any>) => void;
    sandbox?: Dict<any>;
    timeout?: number;
}

interface AstNode extends acorn.Node {
    new (options: any, n: number): acorn.Node;
}

type StepFunction = (stack: JsState[], state: JsState | any, node: any) => Maybe<JsState>;

let AstNodeConstructor: AstNode;

/**
 * The global object
 */
const nativeGlobal: Dict<any> = globalThis;

/**
 * Configuration used for all Acorn parsing.
 */
const jsParseOptions = {
    locations: true,
    ecmaVersion: 2022,
} as acorn.Options;

export class Interpreter {
    /**
     * Currently executing interpreter.  Needed so JsObject instances
     * can know their environment.
     */
    static _currentInterpreter: Interpreter;

    private readonly options: any;

    private readonly ast: acorn.Node;

    private readonly tasks: JsTask[] = [];

    private _initFunc;

    private readonly timeout;

    // private readonly expires;

    private _paused = false;

    private _polyfills?: string[];

    /** Unique identifier for native functions.  Used in serialization. */
    private _functionCounter = 0;

    private readonly _stepFunctions: Dict<Function> = Object.create(null);

    private readonly globalScope;

    private readonly globalObject;

    public returnValue = undefined;

    private stateStack: JsState[] = [];

    public value = undefined;

    private _startTime = 0;

    private duration = 0;

    private ERROR: JsObject;

    private NUMBER: JsObject;

    private BOOLEAN: JsObject;

    private STRING: JsObject;

    private FUNCTION: JsObject;

    // private EVAL_ERROR: any;

    private RANGE_ERROR: any;

    private REFERENCE_ERROR: any;

    private SYNTAX_ERROR: any;

    private TYPE_ERROR: any;

    private URI_ERROR: any;

    private ARRAY: JsObject = Object.create(null);

    private ARRAY_PROTO: JsObject = Object.create(null);

    private REGEXP: JsObject = Object.create(null);

    private REGEXP_PROTO: JsObject = Object.create(null);

    private DATE = Object.create(null);

    private DATE_PROTO = Object.create(null);

    /**
     * Create a new interpreter.
     * @param codeOrAst Raw JavaScript text or AST.
     * @param options Optional initialization function.  Used to
     *     define APIs.  When called it is passed the interpreter object and the
     *     global scope object.
     * @constructor
     */

    constructor(codeOrAst: InterpreterCode, options: any) {
        let code = codeOrAst;
        if (typeof codeOrAst === 'string') {
            code = Interpreter._parse(codeOrAst, 'code');
        }
        AstNodeConstructor = AstNodeConstructor ?? code.constructor;

        // Clone the root 'Program' node so that the AST may be modified.
        const ast = Interpreter.newNode();
        for (const prop of Object.keys(code)) {
            (ast as any)[prop] = prop === 'body' ? (code as any)[prop].slice() : (code as any)[prop];
        }
        this.options = options;
        this.ast = ast;
        this._initFunc = this.options?.initFunc;
        this.timeout = this.options?.timeout || Number.MAX_VALUE;

        this._polyfills = [];
        // Map node types to our step function names; a property lookup is faster
        // than string concatenation with "step" prefix.

        const stepMatch = /^step([A-Z]\w*)$/;
        const thisObj = this as Dict<any>;
        const functionNames = [
            ...Object.getOwnPropertyNames(this),
            ...Object.getOwnPropertyNames(Object.getPrototypeOf(this)),
        ].filter(n => isFunction(thisObj[n]));
        for (const methodName of functionNames) {
            const m = methodName.match(stepMatch);
            if (m) {
                debug(`add step function ${m[1]} with ${methodName} of ${this}`);
                this._stepFunctions[m[1]] = thisObj[methodName].bind(this);
            }
        }
        // Create and initialize the global scope.
        this.globalScope = this.createScope(this.ast, null);
        this.globalObject = this.globalScope.object;
        // Run the polyfills.
        this.ast = Interpreter._parse(this._polyfills.join('\n'), 'polyfills');
        this._polyfills = undefined; // Allow polyfill strings to garbage collect.
        Interpreter.stripLocations_(this.ast);
        let state = new JsState(this.ast, this.globalScope);
        this.returnValue = undefined;
        this.stateStack = [state];
        this.run();
        this.value = undefined;
        // Point at the main program.
        this.ast = ast;
        state = new JsState(this.ast, this.globalScope);
        this.stateStack.length = 0;
        this.stateStack[0] = state;
    }

    private static newNode(): acorn.Node {
        return new AstNodeConstructor({ options: { ecmaVersion: jsParseOptions.ecmaVersion } } as any, 0);
    }

    public static safeEval(code: string | Function, options?: InterpreterOptions) {
        const initFunc = (interp: Interpreter, globalObject: Dict<any>) => {
            if (options?.sandbox) {
                for (const key of Object.keys(options.sandbox)) {
                    interp.setProperty(globalObject, key, interp.nativeToPseudo(options.sandbox[key]));
                }
            }
        };

        const interpreter = new Interpreter(
            typeof code === 'function' ? `(${code.toString()})()` : `(() => { ${code} })()`,
            {
                initFunc,
                timeout: options?.timeout ?? 1000,
                strict: true,
                allowAsync: false,
            },
        );
        interpreter.run();
        return { duration: interpreter.duration, value: interpreter.pseudoToNative(interpreter.value) };
    }

    /**
     * Completion Value Types.
     */
    public static Completion = {
        NORMAL: 0,
        BREAK: 1,
        CONTINUE: 2,
        RETURN: 3,
        THROW: 4,
    };

    /**
     * Property descriptor of readonly properties.
     */
    public static READONLY_DESCRIPTOR = {
        configurable: true,
        enumerable: true,
        writable: false,
    };

    /**
     * Property descriptor of non-enumerable properties.
     */
    public static NONENUMERABLE_DESCRIPTOR = {
        configurable: true,
        enumerable: false,
        writable: true,
    };

    /**
     * Property descriptor of readonly, non-enumerable properties.
     */
    public static READONLY_NONENUMERABLE_DESCRIPTOR = {
        configurable: true,
        enumerable: false,
        writable: false,
    };

    /**
     * Property descriptor of non-configurable, readonly, non-enumerable properties.
     * E.g. NaN, Infinity.
     */
    public static NONCONFIGURABLE_READONLY_NONENUMERABLE_DESCRIPTOR = {
        configurable: false,
        enumerable: false,
        writable: false,
    };

    /**
     * Property descriptor of variables.
     */
    public static VARIABLE_DESCRIPTOR = {
        configurable: false,
        enumerable: true,
        writable: true,
    };

    /**
     * Unique symbol for indicating that a step has encountered an error, has
     * added it to the stack, and will be thrown within the user's program.
     * When STEP_ERROR is thrown in the JS-Interpreter, the error can be ignored.
     */
    public static STEP_ERROR = { STEP_ERROR: true };

    /**
     * Unique symbol for indicating that a reference is a variable on the scope,
     * not an object property.
     */
    public static SCOPE_REFERENCE = { SCOPE_REFERENCE: true };

    /**
     * Unique symbol for indicating, when used as the value of the value
     * parameter in calls to setProperty and friends, that the value
     * should be taken from the property descriptor instead.
     */
    public static VALUE_IN_DESCRIPTOR: JsValue = {
        VALUE_IN_DESCRIPTOR: true,
    };

    /**
     * Unique symbol for indicating that a RegExp timeout has occurred in a VM.
     */
    public static REGEXP_TIMEOUT = { REGEXP_TIMEOUT: true };

    /**
     * Node's vm module, if loaded and required.
     * @type {Object}
     */
    public static vm: any = null;

    /**
     * Code for executing regular expressions in a thread.
     */
    public static WORKER_CODE = [
        'onmessage = function(e) {',
        'var result;',
        'var data = e.data;',
        'switch (data[0]) {',
        "case 'split':",
        // ['split', string, separator, limit]
        'result = data[1].split(data[2], data[3]);',
        'break;',
        "case 'match':",
        // ['match', string, regexp]
        'result = data[1].match(data[2]);',
        'break;',
        "case 'search':",
        // ['search', string, regexp]
        'result = data[1].search(data[2]);',
        'break;',
        "case 'replace':",
        // ['replace', string, regexp, newSubstr]
        'result = data[1].replace(data[2], data[3]);',
        'break;',
        "case 'exec':",
        // ['exec', regexp, lastIndex, string]
        'var regexp = data[1];',
        'regexp.lastIndex = data[2];',
        'result = [regexp.exec(data[3]), data[1].lastIndex];',
        'break;',
        'default:',
        "throw new Error('Unknown RegExp operation: ' + data[0]);",
        '}',
        'postMessage(result);',
        'close();',
        '};',
    ];

    // Create the objects which will become Object.prototype and
    // Function.prototype, which are needed to bootstrap everything else.
    OBJECT_PROTO = new JsObject(null);

    FUNCTION_PROTO = new JsObject(this.OBJECT_PROTO);

    OBJECT: JsObject;

    /**
     * Is a value a legal integer for an array length?
     * @param x Value to check.
     * @returns Zero, or a positive integer if the value can be
     *     converted to such.  NaN otherwise.
     */
    public static legalArrayLength(x: JsValue): number {
        const n = x >>> 0;
        // Array length must be between 0 and 2^32-1 (inclusive).
        return n === Number(x) ? n : NaN;
    }

    /**
     * Is a value a legal integer for an array index?
     * @param x Value to check.
     * @returns Zero, or a positive integer if the value can be
     *     converted to such.  NaN otherwise.
     */
    public static legalArrayIndex(x: JsValue): number {
        const n = x >>> 0;
        // Array index cannot be 2^32-1, otherwise length would be 2^32.
        // 0xffffffff is 2^32-1.
        return String(n) === String(x) && n !== 0xffffffff ? n : NaN;
    }

    /**
     * Remove start and end values from AST, or set start and end values to a
     * constant value.  Used to remove highlighting from polyfills and to set
     * highlighting in an eval to cover the entire eval expression.
     * @param node AST node.
     * @param start Starting character of all nodes, or undefined.
     * @param end Ending character of all nodes, or undefined.
     * @private
     */
    private static stripLocations_(node: any, start?: number, end?: number) {
        if (start) {
            node.start = start;
        } else {
            delete node.start;
        }
        if (end) {
            node.end = end;
        } else {
            delete node.end;
        }
        for (const name in node) {
            if (name !== 'loc' && objectHasOwnProperty(node, name)) {
                const prop = node[name];
                if (prop && typeof prop === 'object') {
                    Interpreter.stripLocations_(prop, start, end);
                }
            }
        }
    }

    /**
     * Some pathological regular expressions can take geometric time.
     * Regular expressions are handled in one of three ways:
     * 0 - throw as invalid.
     * 1 - execute natively (risk of unresponsive program).
     * 2 - execute in separate thread (not supported by IE 9).
     */
    REGEXP_MODE = 2;

    /**
     * If REGEXP_MODE = 2, the length of time (in ms) to allow a RegExp
     * thread to execute before terminating it.
     */
    REGEXP_THREAD_TIMEOUT = 1000;

    /**
     * Length of time (in ms) to allow a polyfill to run before ending step.
     * If set to 0, polyfills will execute step by step.
     * If set to 1000, polyfills will run for up to a second per step
     * (execution will resume in the polyfill in the next step).
     * If set to Infinity, polyfills will run to completion in a single step.
     */
    POLYFILL_TIMEOUT = 1000;

    /**
     * Flag indicating that a getter function needs to be called immediately.
     */
    private _getterStep = false;

    /**
     * Flag indicating that a setter function needs to be called immediately.
     */
    private _setterStep = false;

    /**
     * Number of code chunks appended to the interpreter.
     */
    private _appendCodeNumber = 0;

    /**
     * Number of parsed tasks.
     */
    private _taskCodeNumber = 0;

    /**
     * Parse JavaScript code into an AST using Acorn.
     * @param code Raw JavaScript text.
     * @param sourceFile Name of filename (for stack trace).
     * @returns AST.
     * @private
     */
    private static _parse(code: string, sourceFile: string): acorn.Node {
        // Clone options object, since Acorn will modify this object.
        const options: acorn.Options = { ...jsParseOptions };
        options.sourceFile = sourceFile;
        return acorn.parse(code, options);
    }

    /**
     * Add more code to the interpreter.
     * @param code Raw JavaScript text or AST.
     */
    appendCode(_code: InterpreterCode) {
        const state = this.stateStack[0];
        if (!state || state.node.type !== 'Program') {
            throw new Error('Expecting original AST to start with a Program node');
        }
        let code: any;
        if (typeof _code === 'string') {
            code = Interpreter._parse(_code, `appendCode ${this._appendCodeNumber++}`);
        } else {
            code = _code;
        }
        if (!code || code.type !== 'Program') {
            throw new Error('Expecting new AST to start with a Program node');
        }
        this.populateScope_(code, state.scope);
        // Append the new program to the old one.
        Array.prototype.push.apply(state.node.body, code.body);
        state.node.body.variableCache_ = null;
        state.done = false;
    }

    /**
     * Execute one step of the interpreter.
     * @returns {boolean} True if a step was executed, false if no more instructions.
     */
    step() {
        const stack = this.stateStack;
        let endTime = 0;
        let node: any;

        const ifCode = (cb: (node: any) => void) => node?.loc?.source === 'code' && cb(node);

        do {
            if (this._paused) {
                // Blocked by an asynchonous function.
                return true;
            }
            let state: JsState | null = stack[stack.length - 1];
            if (!state || (state.node.type === 'Program' && state.done)) {
                if (!this.tasks.length) {
                    // Main program complete and no queued tasks.  We're done!
                    return false;
                }
                state = this.nextTask_();
                if (!state) {
                    // Main program complete, queued tasks, but nothing to run right now.
                    return true;
                }
                // Found a queued task, execute it.
            }
            node = state.node;
            if (node.async && !this.options?.allowAsync) {
                this.throwException(this.ERROR, 'async not allowed');
            }
            const t0 = Date.now();
            const duration = t0 - this._startTime;
            ifCode(astNode => debug(`STEP: ${astNode.type} elapsed=${duration}ms`, astNode));
            if (duration >= this.timeout) {
                throw new Error(`Script execution timed out after ${this.timeout}ms`);
            }
            this.computeAndPushNextState(stack, state, node);
            // This may be polyfill code.  Keep executing until we arrive at user code.
            if (!endTime && !node.end) {
                // Ideally this would be defined at the top of the function, but that
                // wastes time if the step isn't a polyfill.
                endTime = Date.now() + this.POLYFILL_TIMEOUT;
            }
            ifCode(astNode => debug(`STEP END: ${astNode.type} duration=${Date.now() - t0}ms, value=${state?.value}`));
        } while (!node.end && endTime > Date.now());
        return true;
    }

    computeAndPushNextState(stack: JsState[], state: JsState, node: any) {
        // Record the interpreter in a global property so calls to toString/valueOf
        // can execute in the proper context.
        const oldInterpreterValue = Interpreter._currentInterpreter;
        Interpreter._currentInterpreter = this;
        let nextState;
        try {
            const stepFunction = this._stepFunctions[node.type];
            if (stepFunction == null) {
                throw new Error('not supported');
            }
            nextState = this._stepFunctions[node.type](stack, state, node);
        } catch (e) {
            // Eat any step errors.  They have been thrown on the stack.
            if (e !== Interpreter.STEP_ERROR) {
                // This is a real error, either in the JS-Interpreter, or an uncaught
                // error in the interpreted code.  Rethrow.
                if (this.value !== e) {
                    // Uh oh.  Internal error in the JS-Interpreter.
                    this.value = undefined;
                }
                throw new Error(`[${node.type}] ${e.message}`);
            }
        } finally {
            // Restore to previous value (probably null, maybe nested toString calls).
            Interpreter._currentInterpreter = oldInterpreterValue;
        }
        if (nextState) {
            stack.push(nextState);
        }
        if (this._getterStep) {
            // Getter from this step was not handled.
            this.value = undefined;
            throw new Error('Getter not supported in this context');
        }
        if (this._setterStep) {
            // Setter from this step was not handled.
            this.value = undefined;
            throw new Error('Setter not supported in this context');
        }
    }

    /**
     * Execute the interpreter to program completion.  Vulnerable to infinite loops.
     * @returns True if a execution is asynchronously blocked,
     *     false if no more instructions.
     */
    run(): boolean {
        this._startTime = Date.now();
        while (!this._paused && this.step()) {
            // nop
        }
        this.duration = Date.now() - this._startTime;
        return this._paused;
    }

    /**
     * Initialize the global object with buitin properties and functions.
     * @param globalObject Global object.
     */
    initGlobal(globalObject: JsObject) {
        // Initialize uneditable global properties.
        this.setProperty(globalObject, 'NaN', NaN, Interpreter.NONCONFIGURABLE_READONLY_NONENUMERABLE_DESCRIPTOR);
        this.setProperty(
            globalObject,
            'Infinity',
            Infinity,
            Interpreter.NONCONFIGURABLE_READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(
            globalObject,
            'undefined',
            undefined,
            Interpreter.NONCONFIGURABLE_READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(globalObject, 'window', globalObject, Interpreter.READONLY_DESCRIPTOR);
        this.setProperty(
            globalObject,
            'this',
            globalObject,
            Interpreter.NONCONFIGURABLE_READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(globalObject, 'self', globalObject); // Editable.

        // Initialize global objects.
        this.initFunction(globalObject);
        this.initObject(globalObject);
        // Unable to set globalObject's parent prior (OBJECT did not exist).
        // Note that in a browser this would be `Window`, whereas in Node.js it would
        // be `Object`.  This interpreter is closer to Node in that it has no DOM.
        globalObject.proto = this.OBJECT_PROTO;
        this.setProperty(globalObject, 'constructor', this.OBJECT, Interpreter.NONENUMERABLE_DESCRIPTOR);
        this.initArray(globalObject);
        this.initString(globalObject);
        this.initBoolean(globalObject);
        this.initNumber(globalObject);
        this.initDate(globalObject);
        this.initRegExp(globalObject);
        this.initError(globalObject);
        this.initMath(globalObject);
        this.initJSON(globalObject);

        // Initialize global functions.
        const thisInterpreter = this;
        this.setProperty(
            globalObject,
            'eval',
            this.createNativeFunction(
                () => {
                    throw EvalError("Can't happen");
                },
                false,
                { eval: true },
            ),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        this.setProperty(
            globalObject,
            'parseInt',
            this.createNativeFunction(parseInt, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(
            globalObject,
            'parseFloat',
            this.createNativeFunction(parseFloat, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        this.setProperty(
            globalObject,
            'isNaN',
            this.createNativeFunction(Number.isNaN, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        this.setProperty(
            globalObject,
            'isFinite',
            this.createNativeFunction(Number.isFinite, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        const makeStringWrapper = (nativeFunc: StringStringFunction) => {
            return (str: string) => {
                try {
                    return nativeFunc(str);
                } catch (e) {
                    // decodeURI('%xy') will throw an error.  Catch and rethrow.
                    thisInterpreter.throwException(thisInterpreter.URI_ERROR, e.message);
                }
                return undefined;
            };
        };

        let wrapper: any;
        const strFunctions = [
            [escape, 'escape'],
            [unescape, 'unescape'],
            [decodeURI, 'decodeURI'],
            [decodeURIComponent, 'decodeURIComponent'],
            [encodeURI, 'encodeURI'],
            [encodeURIComponent, 'encodeURIComponent'],
        ] as [StringStringFunction, string][];
        for (const func of strFunctions) {
            wrapper = makeStringWrapper(func[0]);
            this.setProperty(
                globalObject,
                func[1],
                this.createNativeFunction(wrapper, false),
                Interpreter.NONENUMERABLE_DESCRIPTOR,
            );
        }

        wrapper = function setTimeout(...args: any[]) {
            return thisInterpreter.createTask_(false, args);
        };
        this.setProperty(
            globalObject,
            'setTimeout',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function setInterval(...args: any[]) {
            return thisInterpreter.createTask_(true, args);
        };
        this.setProperty(
            globalObject,
            'setInterval',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function clearTimeout(pid: number) {
            thisInterpreter.deleteTask_(pid);
        };
        this.setProperty(
            globalObject,
            'clearTimeout',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function clearInterval(pid: number) {
            thisInterpreter.deleteTask_(pid);
        };
        this.setProperty(
            globalObject,
            'clearInterval',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        // Run any user-provided initialization.
        if (this._initFunc) {
            this._initFunc(this, globalObject);
        }
    }

    /**
     * Number of functions created by the interpreter.
     */
    private _functionCodeNumber = 0;

    /**
     * Initialize the Function class.
     * @param globalObject Global object.
     */
    initFunction(globalObject: JsObject) {
        const thisInterpreter = this;
        const identifierRegexp = /^[A-Za-z_$][\w$]*$/;
        // Function constructor.
        const wrapper = function EjsFunction(...vargs: any[]) {
            // safe eval does not support new Function call
            if (!thisInterpreter.options?.allowEval) {
                throw new Error('eval not allowed');
            }
            const code = arguments.length ? String(vargs[vargs.length - 1]) : '';
            const args = vargs.slice(0, -1);
            const argsStr = args.length ? args.join(', ') : '';
            if (argsStr) {
                for (const name of args) {
                    if (!identifierRegexp.test(name)) {
                        thisInterpreter.throwException(
                            thisInterpreter.SYNTAX_ERROR,
                            `Invalid function argument: ${name}`,
                        );
                    }
                }
            }
            // Acorn needs to parse code in the context of a function or else `return`
            // statements will be syntax errors.
            let ast: any;
            try {
                ast = Interpreter._parse(
                    `(function(${argsStr}) {${code}})`,
                    `function${thisInterpreter._functionCodeNumber++}`,
                );
            } catch (e) {
                // Acorn threw a SyntaxError.  Rethrow as a trappable error.
                thisInterpreter.throwException(thisInterpreter.SYNTAX_ERROR, `Invalid code: ${e.message}`);
            }
            if (ast.body.length !== 1) {
                // Function('a', 'return a + 6;}; {alert(1);');
                thisInterpreter.throwException(thisInterpreter.SYNTAX_ERROR, 'Invalid code in function body');
            }
            const node = ast.body[0].expression;
            // Note that if this constructor is called as `new Function()` the function
            // object created by stepCallExpression and assigned to `this` is discarded.
            // Interestingly, the scope for constructed functions is the global scope,
            // even if they were constructed in some other scope.
            return thisInterpreter.createFunction(node, thisInterpreter.globalScope, 'anonymous');
        };
        this.FUNCTION = this.createNativeFunction(wrapper, true);

        this.setProperty(globalObject, 'Function', this.FUNCTION, Interpreter.NONENUMERABLE_DESCRIPTOR);
        // Throw away the created prototype and use the root prototype.
        this.setProperty(this.FUNCTION, 'prototype', this.FUNCTION_PROTO, Interpreter.NONENUMERABLE_DESCRIPTOR);

        // Configure Function.prototype.
        this.setProperty(this.FUNCTION_PROTO, 'constructor', this.FUNCTION, Interpreter.NONENUMERABLE_DESCRIPTOR);
        const functionProto = this.FUNCTION_PROTO as any;
        functionProto.nativeFunc = function nf0() {};
        functionProto.nativeFunc.id = this._functionCounter++;
        functionProto.illegalConstructor = true;
        this.setProperty(this.FUNCTION_PROTO, 'length', 0, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        this.FUNCTION_PROTO.class = 'Function';

        const functionWrappers = {
            apply(thisArg: any, args: any[]) {
                const state = thisInterpreter.stateStack[thisInterpreter.stateStack.length - 1] as any;
                // Rewrite the current CallExpression state to apply a different function.
                state.func_ = this;
                // Assign the `this` object.
                state.funcThis_ = thisArg;
                // Bind any provided arguments.
                state.arguments_ = [];
                if (args !== null && args !== undefined) {
                    if (args instanceof JsObject) {
                        state.arguments_ = thisInterpreter.arrayPseudoToNative(args);
                    } else {
                        thisInterpreter.throwException(
                            thisInterpreter.TYPE_ERROR,
                            'CreateListFromArrayLike called on non-object',
                        );
                    }
                }
                state.doneExec_ = false;
            },

            call(...args: any[]) {
                const state = thisInterpreter.stateStack[thisInterpreter.stateStack.length - 1] as any;
                // Rewrite the current CallExpression state to call a different function.
                state.func_ = this;
                // Assign the `this` object.
                state.funcThis_ = args[0];
                // Bind any provided arguments.
                state.arguments_ = [];
                for (let i = 1; i < args.length; i++) {
                    state.arguments_.push(args[i]);
                }
                state.doneExec_ = false;
            },

            toString(): string {
                return String(this);
            },

            valueOf(): any {
                return this.valueOf();
            },
        };
        this.setNativeFunctionPrototype(this.FUNCTION, 'apply', functionWrappers.apply);

        this.setNativeFunctionPrototype(this.FUNCTION, 'call', functionWrappers.call);

        this._polyfills?.push(
            // Polyfill copied from:
            // developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_objects/Function/bind
            "Object.defineProperty(Function.prototype, 'bind',",
            '{configurable: true, writable: true, value:',
            'function bind(oThis) {',
            "if (typeof this !== 'function') {",
            "throw TypeError('What is trying to be bound is not callable');",
            '}',
            'var aArgs   = Array.prototype.slice.call(arguments, 1),',
            'fToBind = this,',
            'fNOP    = function() {},',
            'fBound  = function() {',
            'return fToBind.apply(this instanceof fNOP',
            '? this',
            ': oThis,',
            'aArgs.concat(Array.prototype.slice.call(arguments)));',
            '};',
            'if (this.prototype) {',
            'fNOP.prototype = this.prototype;',
            '}',
            'fBound.prototype = new fNOP();',
            'return fBound;',
            '}',
            '});',
            '',
        );

        // Function has no parent to inherit from, so it needs its own mandatory
        // toString and valueOf functions.
        this.setNativeFunctionPrototype(this.FUNCTION, 'toString', functionWrappers.toString);
        this.setProperty(
            this.FUNCTION,
            'toString',
            this.createNativeFunction(functionWrappers.toString, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );
        this.setNativeFunctionPrototype(this.FUNCTION, 'valueOf', functionWrappers.valueOf);
        this.setProperty(
            this.FUNCTION,
            'valueOf',
            this.createNativeFunction(functionWrappers.valueOf, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );
    }

    /**
     * Initialize the Object class.
     * @param globalObject Global object.
     */
    initObject(globalObject: JsObject) {
        if (!this._polyfills) {
            throw new Error('polyFills not initialized');
        }
        const thisInterpreter = this;
        // Object constructor.
        let wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsObject(value: any) {
                if (value === undefined || value === null) {
                    // Create a new object.
                    if (thisInterpreter.calledWithNew()) {
                        // Called as `new Object()`.
                        return this;
                    }
                    // Called as `Object()`.
                    return thisInterpreter.createObjectProto(thisInterpreter.OBJECT_PROTO);
                }
                if (!(value instanceof JsObject)) {
                    // Wrap the value as an object.
                    const box = thisInterpreter.createObjectProto(thisInterpreter.getPrototype(value));
                    box.data = value;
                    return box;
                }
                // Return the provided object.
                return value;
            },
        };
        this.OBJECT = this.createNativeFunction(wrapper.nativeFunc, true);
        // Throw away the created prototype and use the root prototype.
        this.setProperty(this.OBJECT, 'prototype', this.OBJECT_PROTO, Interpreter.NONENUMERABLE_DESCRIPTOR);
        this.setProperty(this.OBJECT_PROTO, 'constructor', this.OBJECT, Interpreter.NONENUMERABLE_DESCRIPTOR);
        this.setProperty(globalObject, 'Object', this.OBJECT, Interpreter.NONENUMERABLE_DESCRIPTOR);

        /**
         * Checks if the provided value is null or undefined.
         * If so, then throw an error in the call stack.
         * @param value Value to check.
         */
        const throwIfNullUndefined = function (value: JsValue) {
            if (value === undefined || value === null) {
                thisInterpreter.throwException(thisInterpreter.TYPE_ERROR, `Cannot convert '${value}' to object`);
            }
        };

        // Static methods on Object.
        wrapper = function getOwnPropertyNames(obj: { properties: any }) {
            throwIfNullUndefined(obj);
            const props = obj instanceof JsObject ? obj.properties : obj;
            return thisInterpreter.arrayNativeToPseudo(Object.getOwnPropertyNames(props));
        };
        this.setProperty(
            this.OBJECT,
            'getOwnPropertyNames',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function keys(_obj: Dict<any>) {
            let obj = _obj;
            throwIfNullUndefined(obj);
            if (obj instanceof JsObject) {
                obj = obj.properties;
            }
            return thisInterpreter.arrayNativeToPseudo(Object.keys(obj));
        };
        this.setProperty(
            this.OBJECT,
            'keys',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function create_(proto: JsObject | null) {
            // Support for the second argument is the responsibility of a polyfill.
            if (proto === null) {
                return thisInterpreter.createObjectProto(null);
            }
            if (!(proto instanceof JsObject)) {
                thisInterpreter.throwException(
                    thisInterpreter.TYPE_ERROR,
                    `Object prototype may only be an Object or null, not ${proto}`,
                );
            }
            return thisInterpreter.createObjectProto(proto);
        };
        this.setProperty(
            this.OBJECT,
            'create',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        // Add a polyfill to handle create's second argument.
        this._polyfills.push(
            '(function() {',
            'var create_ = Object.create;',
            'Object.create = function create(proto, props) {',
            'var obj = create_(proto);',
            'props && Object.defineProperties(obj, props);',
            'return obj;',
            '};',
            '})();',
            '',
        );

        wrapper = function defineProperty(obj: JsObject, _prop: any, descriptor: JsObject) {
            if (!(obj instanceof JsObject)) {
                thisInterpreter.throwException(
                    thisInterpreter.TYPE_ERROR,
                    `Object.defineProperty called on non-object: ${obj}`,
                );
            }
            if (!(descriptor instanceof JsObject)) {
                thisInterpreter.throwException(thisInterpreter.TYPE_ERROR, 'Property description must be an object');
            }
            const prop = String(_prop);
            if (obj.preventExtensions && !(prop in obj.properties)) {
                thisInterpreter.throwException(
                    thisInterpreter.TYPE_ERROR,
                    `Can't define property '${prop}', object is not extensible`,
                );
            }
            // The polyfill guarantees no inheritance and no getter functions.
            // Therefore the descriptor properties map is the native object needed.
            thisInterpreter.setProperty(obj, prop, Interpreter.VALUE_IN_DESCRIPTOR, descriptor.properties);
            return obj;
        };
        this.setProperty(
            this.OBJECT,
            'defineProperty',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        this._polyfills.push(
            // Flatten the descriptor to remove any inheritance or getter functions.
            '(function() {',
            'var defineProperty_ = Object.defineProperty;',
            'Object.defineProperty = function defineProperty(obj, prop, d1) {',
            'var d2 = {};',
            "if ('configurable' in d1) d2.configurable = d1.configurable;",
            "if ('enumerable' in d1) d2.enumerable = d1.enumerable;",
            "if ('writable' in d1) d2.writable = d1.writable;",
            "if ('value' in d1) d2.value = d1.value;",
            "if ('get' in d1) d2.get = d1.get;",
            "if ('set' in d1) d2.set = d1.set;",
            'return defineProperty_(obj, prop, d2);',
            '};',
            '})();',

            "Object.defineProperty(Object, 'defineProperties',",
            '{configurable: true, writable: true, value:',
            'function defineProperties(obj, props) {',
            'var keys = Object.keys(props);',
            'for (var i = 0; i < keys.length; i++) {',
            'Object.defineProperty(obj, keys[i], props[keys[i]]);',
            '}',
            'return obj;',
            '}',
            '});',
            '',
        );

        wrapper = function getOwnPropertyDescriptor(obj: any, _prop: PropertyKey) {
            if (!(obj instanceof JsObject)) {
                thisInterpreter.throwException(
                    thisInterpreter.TYPE_ERROR,
                    `Object.getOwnPropertyDescriptor called on non-object: ${obj}`,
                );
            }
            const prop = String(_prop);
            if (!(prop in obj.properties)) {
                return undefined;
            }
            const descriptor = Object.getOwnPropertyDescriptor(obj.properties, prop);
            const getter = obj.getter[prop];
            const setter = obj.setter[prop];

            const pseudoDescriptor = thisInterpreter.createObjectProto(thisInterpreter.OBJECT_PROTO);
            if (getter || setter) {
                thisInterpreter.setProperty(pseudoDescriptor, 'get', getter);
                thisInterpreter.setProperty(pseudoDescriptor, 'set', setter);
            } else {
                thisInterpreter.setProperty(pseudoDescriptor, 'value', descriptor?.value);
                thisInterpreter.setProperty(pseudoDescriptor, 'writable', descriptor?.writable);
            }
            thisInterpreter.setProperty(pseudoDescriptor, 'configurable', descriptor?.configurable);
            thisInterpreter.setProperty(pseudoDescriptor, 'enumerable', descriptor?.enumerable);
            return pseudoDescriptor;
        };
        this.setProperty(
            this.OBJECT,
            'getOwnPropertyDescriptor',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function getPrototypeOf(obj: any) {
            throwIfNullUndefined(obj);
            return thisInterpreter.getPrototype(obj);
        };
        this.setProperty(
            this.OBJECT,
            'getPrototypeOf',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function isExtensible(obj: any) {
            return Boolean(obj) && !obj.preventExtensions;
        };
        this.setProperty(
            this.OBJECT,
            'isExtensible',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        wrapper = function preventExtensions(obj: any) {
            if (obj instanceof JsObject) {
                obj.preventExtensions = true;
            }
            return obj;
        };
        this.setProperty(
            this.OBJECT,
            'preventExtensions',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        // Instance methods on Object.
        this.setNativeFunctionPrototype(this.OBJECT, 'toString', JsObject.prototype.toString);
        this.setNativeFunctionPrototype(this.OBJECT, 'toLocaleString', JsObject.prototype.toString);
        this.setNativeFunctionPrototype(this.OBJECT, 'valueOf', JsObject.prototype.valueOf);

        const objectWrappers = {
            hasOwnProperty(prop: PropertyKey) {
                throwIfNullUndefined(this);
                if (this instanceof JsObject) {
                    return String(prop) in this.properties;
                }
                // Primitive.
                return objectHasOwnProperty(this, prop);
            },

            propertyIsEnumerable(prop: PropertyKey) {
                throwIfNullUndefined(this);
                if (this instanceof JsObject) {
                    return Object.prototype.propertyIsEnumerable.call(this.properties, prop);
                }
                // Primitive.
                return Object.prototype.propertyIsEnumerable.call(this, prop);
            },

            isPrototypeOf(_obj: any) {
                let obj = _obj;
                // eslint-disable-next-line no-constant-condition
                while (true) {
                    // Note, circular loops shouldn't be possible.
                    obj = thisInterpreter.getPrototype(obj);
                    if (!obj) {
                        // No parent; reached the top.
                        return false;
                    }
                    if (obj === this) {
                        return true;
                    }
                }
            },
        };

        this.setNativeFunctionPrototype(this.OBJECT, 'hasOwnProperty', objectWrappers.hasOwnProperty);
        this.setNativeFunctionPrototype(this.OBJECT, 'propertyIsEnumerable', objectWrappers.propertyIsEnumerable);
        this.setNativeFunctionPrototype(this.OBJECT, 'isPrototypeOf', objectWrappers.isPrototypeOf);
    }

    /**
     * Initialize the Array class.
     * @param globalObject Global object.
     */
    initArray(globalObject: JsObject) {
        if (!this._polyfills) {
            throw new Error('polyFills not initialized');
        }
        const thisInterpreter = this;
        // Array constructor.
        let wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsArray(...args: any[]) {
                const newArray = thisInterpreter.calledWithNew()
                    ? // Called as `new Array()`.
                      (this as unknown as JsObject)
                    : // Called as `Array()`.
                      thisInterpreter.createArray();
                const first = args[0];
                if (args.length === 1 && typeof first === 'number') {
                    if (Number.isNaN(Interpreter.legalArrayLength(first))) {
                        thisInterpreter.throwException(thisInterpreter.RANGE_ERROR, `Invalid array length: ${first}`);
                    }
                    newArray.properties.length = first;
                } else {
                    let i;
                    for (i = 0; i < arguments.length; i++) {
                        newArray.properties[i] = args[i];
                    }
                    newArray.properties.length = i;
                }
                return newArray;
            },
        };
        this.ARRAY = this.createNativeFunction(wrapper.nativeFunc, true);
        this.ARRAY_PROTO = this.ARRAY.properties.prototype;
        this.setProperty(globalObject, 'Array', this.ARRAY, Interpreter.NONENUMERABLE_DESCRIPTOR);

        // Static methods on Array.
        wrapper = function isArray(obj: JsObject) {
            return obj?.class === 'Array';
        };
        this.setProperty(
            this.ARRAY,
            'isArray',
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        // Instance methods on Array.
        this.setProperty(this.ARRAY_PROTO, 'length', 0, {
            configurable: false,
            enumerable: false,
            writable: true,
        });

        this.ARRAY_PROTO.class = 'Array';

        this._polyfills.push(
            '(function() {',
            'function createArrayMethod_(f) {',
            'Object.defineProperty(Array.prototype, f.name,',
            '{configurable: true, writable: true, value: f});',
            '}',

            'createArrayMethod_(',
            'function pop() {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'if (!len || len < 0) {',
            'o.length = 0;',
            'return undefined;',
            '}',
            'len--;',
            'var x = o[len];',
            'delete o[len];', // Needed for non-arrays.
            'o.length = len;',
            'return x;',
            '}',
            ');',

            'createArrayMethod_(',
            'function push(var_args) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'for (var i = 0; i < arguments.length; i++) {',
            'o[len] = arguments[i];',
            'len++;',
            '}',
            'o.length = len;',
            'return len;',
            '}',
            ');',

            'createArrayMethod_(',
            'function shift() {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'if (!len || len < 0) {',
            'o.length = 0;',
            'return undefined;',
            '}',
            'var value = o[0];',
            'for (var i = 0; i < len - 1; i++) {',
            'if ((i + 1) in o) {',
            'o[i] = o[i + 1];',
            '} else {',
            'delete o[i];',
            '}',
            '}',
            'delete o[i];', // Needed for non-arrays.
            'o.length = len - 1;',
            'return value;',
            '}',
            ');',

            'createArrayMethod_(',
            'function unshift(var_args) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'if (!len || len < 0) {',
            'len = 0;',
            '}',
            'for (var i = len - 1; i >= 0; i--) {',
            'if (i in o) {',
            'o[i + arguments.length] = o[i];',
            '} else {',
            'delete o[i + arguments.length];',
            '}',
            '}',
            'for (var i = 0; i < arguments.length; i++) {',
            'o[i] = arguments[i];',
            '}',
            'return (o.length = len + arguments.length);',
            '}',
            ');',

            'createArrayMethod_(',
            'function reverse() {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'if (!len || len < 2) {',
            'return o;', // Not an array, or too short to reverse.
            '}',
            'for (var i = 0; i < len / 2 - 0.5; i++) {',
            'var x = o[i];',
            'var hasX = i in o;',
            'if ((len - i - 1) in o) {',
            'o[i] = o[len - i - 1];',
            '} else {',
            'delete o[i];',
            '}',
            'if (hasX) {',
            'o[len - i - 1] = x;',
            '} else {',
            'delete o[len - i - 1];',
            '}',
            '}',
            'return o;',
            '}',
            ');',

            'createArrayMethod_(',
            'function indexOf(searchElement, fromIndex) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'var n = fromIndex | 0;',
            'if (!len || n >= len) {',
            'return -1;',
            '}',
            'var i = Math.max(n >= 0 ? n : len - Math.abs(n), 0);',
            'while (i < len) {',
            'if (i in o && o[i] === searchElement) {',
            'return i;',
            '}',
            'i++;',
            '}',
            'return -1;',
            '}',
            ');',

            'createArrayMethod_(',
            'function lastIndexOf(searchElement, fromIndex) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'if (!len) {',
            'return -1;',
            '}',
            'var n = len - 1;',
            'if (arguments.length > 1) {',
            'n = fromIndex | 0;',
            'if (n) {',
            'n = (n > 0 || -1) * Math.floor(Math.abs(n));',
            '}',
            '}',
            'var i = n >= 0 ? Math.min(n, len - 1) : len - Math.abs(n);',
            'while (i >= 0) {',
            'if (i in o && o[i] === searchElement) {',
            'return i;',
            '}',
            'i--;',
            '}',
            'return -1;',
            '}',
            ');',

            'createArrayMethod_(',
            'function slice(start, end) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            // Handle negative value for "start"
            'start |= 0;',
            'start = (start >= 0) ? start : Math.max(0, len + start);',
            // Handle negative value for "end"
            "if (typeof end !== 'undefined') {",
            'if (end !== Infinity) {',
            'end |= 0;',
            '}',
            'if (end < 0) {',
            'end = len + end;',
            '} else {',
            'end = Math.min(end, len);',
            '}',
            '} else {',
            'end = len;',
            '}',
            'var size = end - start;',
            'var cloned = new Array(size);',
            'for (var i = 0; i < size; i++) {',
            'if ((start + i) in o) {',
            'cloned[i] = o[start + i];',
            '}',
            '}',
            'return cloned;',
            '}',
            ');',

            'createArrayMethod_(',
            'function splice(start, deleteCount, var_args) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'start |= 0;',
            'if (start < 0) {',
            'start = Math.max(len + start, 0);',
            '} else {',
            'start = Math.min(start, len);',
            '}',
            'if (arguments.length < 1) {',
            'deleteCount = len - start;',
            '} else {',
            'deleteCount |= 0;',
            'deleteCount = Math.max(0, Math.min(deleteCount, len - start));',
            '}',
            'var removed = [];',
            // Remove specified elements.
            'for (var i = start; i < start + deleteCount; i++) {',
            'if (i in o) {',
            'removed.push(o[i]);',
            '} else {',
            'removed.length++;',
            '}',
            'if ((i + deleteCount) in o) {',
            'o[i] = o[i + deleteCount];',
            '} else {',
            'delete o[i];',
            '}',
            '}',
            // Move other element to fill the gap.
            'for (var i = start + deleteCount; i < len - deleteCount; i++) {',
            'if ((i + deleteCount) in o) {',
            'o[i] = o[i + deleteCount];',
            '} else {',
            'delete o[i];',
            '}',
            '}',
            // Delete superfluous properties.
            'for (var i = len - deleteCount; i < len; i++) {',
            'delete o[i];',
            '}',
            'len -= deleteCount;',
            // Insert specified items.
            'var arl = arguments.length - 2;',
            'for (var i = len - 1; i >= start; i--) {',
            'if (i in o) {',
            'o[i + arl] = o[i];',
            '} else {',
            'delete o[i + arl];',
            '}',
            '}',
            'len += arl;',
            'for (var i = 2; i < arguments.length; i++) {',
            'o[start + i - 2] = arguments[i];',
            '}',
            'o.length = len;',
            'return removed;',
            '}',
            ');',

            'createArrayMethod_(',
            'function concat(var_args) {',
            'if (!this) throw TypeError();',
            'var o = Object(this);',
            'var cloned = [];',
            'for (var i = -1; i < arguments.length; i++) {',
            'var value = (i === -1) ? o : arguments[i];',
            'if (Array.isArray(value)) {',
            'for (var j = 0, l = value.length; j < l; j++) {',
            'if (j in value) {',
            'cloned.push(value[j]);',
            '} else {',
            'cloned.length++;',
            '}',
            '}',
            '} else {',
            'cloned.push(value);',
            '}',
            '}',
            'return cloned;',
            '}',
            ');',

            'createArrayMethod_(',
            'function join(_separator) {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            "var sep = typeof _separator === 'undefined' ?",
            "',' : ('' + _separator);",
            "var str = '';",
            'for (var i = 0; i < len; i++) {',
            'if (i && sep) str += sep;',
            "str += (o[i] === null || o[i] === undefined) ? '' : o[i];",
            '}',
            'return str;',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/every
            'createArrayMethod_(',
            'function every(callbackfn, thisArg) {',
            "if (!this || typeof callbackfn !== 'function') throw TypeError();",
            'var t, k = 0;',
            'var o = Object(this), len = o.length >>> 0;',
            'if (arguments.length > 1) t = thisArg;',
            'while (k < len) {',
            'if (k in o && !callbackfn.call(t, o[k], k, o)) return false;',
            'k++;',
            '}',
            'return true;',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/filter
            'createArrayMethod_(',
            'function filter(fun, var_args) {',
            "if (this === void 0 || this === null || typeof fun !== 'function') throw TypeError();",
            'var o = Object(this), len = o.length >>> 0;',
            'var res = [];',
            'var thisArg = arguments.length >= 2 ? arguments[1] : void 0;',
            'for (var i = 0; i < len; i++) {',
            'if (i in o) {',
            'var val = o[i];',
            'if (fun.call(thisArg, val, i, o)) res.push(val);',
            '}',
            '}',
            'return res;',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach
            'createArrayMethod_(',
            'function forEach(callback, thisArg) {',
            "if (!this || typeof callback !== 'function') throw TypeError();",
            'var t, k = 0;',
            'var o = Object(this), len = o.length >>> 0;',
            'if (arguments.length > 1) t = thisArg;',
            'while (k < len) {',
            'if (k in o) callback.call(t, o[k], k, o);',
            'k++;',
            '}',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/map
            'createArrayMethod_(',
            'function map(callback, thisArg) {',
            "if (!this || typeof callback !== 'function') throw TypeError();",
            'var t, k = 0;',
            'var o = Object(this), len = o.length >>> 0;',
            'if (arguments.length > 1) t = thisArg;',
            'var a = new Array(len);',
            'while (k < len) {',
            'if (k in o) a[k] = callback.call(t, o[k], k, o);',
            'k++;',
            '}',
            'return a;',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/Reduce
            'createArrayMethod_(',
            'function reduce(callback /*, initialValue*/) {',
            "if (!this || typeof callback !== 'function') throw TypeError();",
            'var o = Object(this), len = o.length >>> 0;',
            'var k = 0, value;',
            'if (arguments.length === 2) {',
            'value = arguments[1];',
            '} else {',
            'while (k < len && !(k in o)) k++;',
            'if (k >= len) {',
            "throw TypeError('Reduce of empty array with no initial value');",
            '}',
            'value = o[k++];',
            '}',
            'for (; k < len; k++) {',
            'if (k in o) value = callback(value, o[k], k, o);',
            '}',
            'return value;',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/ReduceRight
            'createArrayMethod_(',
            'function reduceRight(callback /*, initialValue*/) {',
            "if (null === this || 'undefined' === typeof this || 'function' !== typeof callback) throw TypeError();",
            'var o = Object(this), len = o.length >>> 0;',
            'var k = len - 1, value;',
            'if (arguments.length >= 2) {',
            'value = arguments[1];',
            '} else {',
            'while (k >= 0 && !(k in o)) k--;',
            'if (k < 0) {',
            "throw TypeError('Reduce of empty array with no initial value');",
            '}',
            'value = o[k--];',
            '}',
            'for (; k >= 0; k--) {',
            'if (k in o) value = callback(value, o[k], k, o);',
            '}',
            'return value;',
            '}',
            ');',

            // Polyfill copied from:
            // developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/some
            'createArrayMethod_(',
            'function some(fun/*, thisArg*/) {',
            "if (!this || typeof fun !== 'function') throw TypeError();",
            'var o = Object(this), len = o.length >>> 0;',
            'var thisArg = arguments.length >= 2 ? arguments[1] : void 0;',
            'for (var i = 0; i < len; i++) {',
            'if (i in o && fun.call(thisArg, o[i], i, o)) return true;',
            '}',
            'return false;',
            '}',
            ');',

            'createArrayMethod_(',
            'function sort(_comp) {', // Bubble sort!
            'if (!this) throw TypeError();',
            "if (typeof _comp !== 'function') {",
            '_comp = undefined;',
            '}',
            'for (var i = 0; i < this.length; i++) {',
            'var changes = 0;',
            'for (var j = 0; j < this.length - i - 1; j++) {',
            'if (_comp ? (_comp(this[j], this[j + 1]) > 0) :',
            '(String(this[j]) > String(this[j + 1]))) {',
            'var swap = this[j];',
            'var hasSwap = j in this;',
            'if ((j + 1) in this) {',
            'this[j] = this[j + 1];',
            '} else {',
            'delete this[j];',
            '}',
            'if (hasSwap) {',
            'this[j + 1] = swap;',
            '} else {',
            'delete this[j + 1];',
            '}',
            'changes++;',
            '}',
            '}',
            'if (!changes) break;',
            '}',
            'return this;',
            '}',
            ');',

            'createArrayMethod_(',
            'function toLocaleString() {',
            'if (!this) throw TypeError();',
            'var o = Object(this), len = o.length >>> 0;',
            'var out = [];',
            'for (var i = 0; i < len; i++) {',
            "out[i] = (o[i] === null || o[i] === undefined) ? '' : o[i].toLocaleString();",
            '}',
            "return out.join(',');",
            '}',
            ');',
            '})();',
            '',
        );
    }

    /**
     * Initialize the String class.
     * @param globalObject Global object.
     */
    initString(globalObject: JsObject) {
        if (!this._polyfills) {
            throw new Error('polyFills not initialized');
        }

        const thisInterpreter = this;
        // String constructor.
        const wrapper = {
            nativeFunc: function EjsString(_value: any) {
                const value = arguments.length ? nativeGlobal.String(_value) : '';
                if (thisInterpreter.calledWithNew()) {
                    // Called as `new String()`.
                    (this as any).data = value;
                    return this;
                }
                // Called as `String()`.
                return value;
            },
        };
        this.STRING = this.createNativeFunction(wrapper.nativeFunc, true);
        this.setProperty(globalObject, 'String', this.STRING, Interpreter.NONENUMERABLE_DESCRIPTOR);

        // Static methods on String.
        this.setProperty(
            this.STRING,
            'fromCharCode',
            this.createNativeFunction(String.fromCharCode, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        // Instance methods on String.
        // Methods with exclusively primitive arguments.
        const functions = [
            'charAt',
            'charCodeAt',
            'concat',
            'indexOf',
            'lastIndexOf',
            'repeat',
            'slice',
            'substr',
            'substring',
            'toLocaleLowerCase',
            'toLocaleUpperCase',
            'toLowerCase',
            'toUpperCase',
            'trim',
        ];
        const stringProto = String.prototype as Dict<any>;
        for (const func of functions) {
            this.setNativeFunctionPrototype(this.STRING, func, stringProto[func]);
        }

        const stringWrappers = {
            localeCompare(compareString: string, _locales: any, _options: any) {
                const locales = thisInterpreter.pseudoToNative(_locales);
                const options = thisInterpreter.pseudoToNative(_options);
                try {
                    return String(this).localeCompare(compareString, locales, options);
                } catch (e) {
                    thisInterpreter.throwException(thisInterpreter.ERROR, `localeCompare: ${e.message}`);
                }
                return undefined;
            },

            split(_separator: any, _limit: any, callback: any) {
                const string = String(this);
                let jsList;
                const limit = _limit ? Number(_limit) : undefined;
                // Example of catastrophic split RegExp:
                // 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaac'.split(/^(a+)+b/)
                if (thisInterpreter.isa(_separator, thisInterpreter.REGEXP)) {
                    const separator = _separator.data;
                    thisInterpreter.maybeThrowRegExp(separator, callback);
                    if (thisInterpreter.REGEXP_MODE === 2) {
                        if (Interpreter.vm) {
                            // Run split in vm.
                            const sandbox = {
                                string,
                                separator,
                                limit,
                            };
                            const code = 'string.split(separator, limit)';
                            jsList = thisInterpreter.vmCall(code, sandbox, separator, callback);
                            if (jsList !== Interpreter.REGEXP_TIMEOUT) {
                                callback(thisInterpreter.arrayNativeToPseudo(jsList));
                            }
                        } else {
                            // Run split in separate thread.
                            const splitWorker = thisInterpreter.createWorker();
                            const pid = thisInterpreter.regExpTimeout(separator, splitWorker, callback);
                            splitWorker.onmessage = function onmessage(e: JsObject) {
                                clearTimeout(pid);
                                callback(thisInterpreter.arrayNativeToPseudo(e.data));
                            };
                            splitWorker.postMessage(['split', string, separator, limit]);
                        }
                        return;
                    }
                }
                // Run split natively.
                jsList = string.split(_separator, limit);
                callback(thisInterpreter.arrayNativeToPseudo(jsList));
            },

            match(_regexp: any, callback: any) {
                const string = String(this);
                let m;
                const regexp = thisInterpreter.isa(_regexp, thisInterpreter.REGEXP)
                    ? _regexp.data
                    : new RegExp(_regexp);
                // Example of catastrophic match RegExp:
                // 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaac'.match(/^(a+)+b/)
                thisInterpreter.maybeThrowRegExp(regexp, callback);
                if (thisInterpreter.REGEXP_MODE === 2) {
                    if (Interpreter.vm) {
                        // Run match in vm.
                        const sandbox = {
                            string,
                            regexp,
                        };
                        const code = 'string.match(regexp)';
                        m = thisInterpreter.vmCall(code, sandbox, regexp, callback);
                        if (m !== Interpreter.REGEXP_TIMEOUT) {
                            callback(m && thisInterpreter.arrayNativeToPseudo(m));
                        }
                    } else {
                        // Run match in separate thread.
                        const matchWorker = thisInterpreter.createWorker();
                        const pid = thisInterpreter.regExpTimeout(regexp, matchWorker, callback);
                        matchWorker.onmessage = function onmessage(e: JsObject) {
                            clearTimeout(pid);
                            callback(e.data && thisInterpreter.arrayNativeToPseudo(e.data));
                        };
                        matchWorker.postMessage(['match', string, regexp]);
                    }
                    return;
                }
                // Run match natively.
                m = string.match(regexp);
                callback(m && thisInterpreter.arrayNativeToPseudo(m));
            },

            search(_regexp: any, callback: any) {
                const string = String(this);
                const regexp = thisInterpreter.isa(_regexp, thisInterpreter.REGEXP)
                    ? _regexp.data
                    : new RegExp(_regexp);
                // Example of catastrophic search RegExp:
                // 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaac'.search(/^(a+)+b/)
                thisInterpreter.maybeThrowRegExp(regexp, callback);
                if (thisInterpreter.REGEXP_MODE === 2) {
                    if (Interpreter.vm) {
                        // Run search in vm.
                        const sandbox = {
                            string,
                            regexp,
                        };
                        const code = 'string.search(regexp)';
                        const n = thisInterpreter.vmCall(code, sandbox, regexp, callback);
                        if (n !== Interpreter.REGEXP_TIMEOUT) {
                            callback(n);
                        }
                    } else {
                        // Run search in separate thread.
                        const searchWorker = thisInterpreter.createWorker();
                        const pid = thisInterpreter.regExpTimeout(regexp, searchWorker, callback);
                        searchWorker.onmessage = function onmessage(e: JsObject) {
                            clearTimeout(pid);
                            callback(e.data);
                        };
                        searchWorker.postMessage(['search', string, regexp]);
                    }
                    return;
                }
                // Run search natively.
                callback(string.search(regexp));
            },

            replace(_substr: any, _newSubstr: string, callback: any) {
                // Support for function replacements is the responsibility of a polyfill.
                const string = String(this);
                const newSubstr = String(_newSubstr);
                // Example of catastrophic replace RegExp:
                // 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaac'.replace(/^(a+)+b/, '')
                if (thisInterpreter.isa(_substr, thisInterpreter.REGEXP)) {
                    const substr = _substr.data;
                    thisInterpreter.maybeThrowRegExp(substr, callback);
                    if (thisInterpreter.REGEXP_MODE === 2) {
                        if (Interpreter.vm) {
                            // Run replace in vm.
                            const sandbox = {
                                string,
                                substr,
                                newSubstr,
                            };
                            const code = 'string.replace(substr, newSubstr)';
                            const str = thisInterpreter.vmCall(code, sandbox, substr, callback);
                            if (str !== Interpreter.REGEXP_TIMEOUT) {
                                callback(str);
                            }
                        } else {
                            // Run replace in separate thread.
                            const replaceWorker = thisInterpreter.createWorker();
                            const pid = thisInterpreter.regExpTimeout(substr, replaceWorker, callback);
                            replaceWorker.onmessage = function onmessage(e: JsObject) {
                                clearTimeout(pid);
                                callback(e.data);
                            };
                            replaceWorker.postMessage(['replace', string, substr, newSubstr]);
                        }
                        return;
                    }
                }
                // Run replace natively.
                callback(string.replace(_substr, newSubstr));
            },
        };
        this.setNativeFunctionPrototype(this.STRING, 'localeCompare', stringWrappers.localeCompare);
        this.setAsyncFunctionPrototype(this.STRING, 'split', stringWrappers.split);
        this.setAsyncFunctionPrototype(this.STRING, 'match', stringWrappers.match);
        this.setAsyncFunctionPrototype(this.STRING, 'search', stringWrappers.search);
        this.setAsyncFunctionPrototype(this.STRING, 'replace', stringWrappers.replace);
        // Add a polyfill to handle replace's second argument being a function.
        this._polyfills.push(
            '(function() {',
            'var replace_ = String.prototype.replace;',
            'String.prototype.replace = function replace(substr, newSubstr) {',
            "if (typeof newSubstr !== 'function') {",
            // string.replace(string|regexp, string)
            'return replace_.call(this, substr, newSubstr);',
            '}',
            'var str = this;',
            'if (substr instanceof RegExp) {', // string.replace(regexp, function)
            'var subs = [];',
            'var m = substr.exec(str);',
            'while (m) {',
            'm.push(m.index, str);',
            'var inject = newSubstr.apply(null, m);',
            'subs.push([m.index, m[0].length, inject]);',
            'm = substr.global ? substr.exec(str) : null;',
            '}',
            'for (var i = subs.length - 1; i >= 0; i--) {',
            'str = str.substring(0, subs[i][0]) + subs[i][2] + str.substring(subs[i][0] + subs[i][1]);',
            '}',
            '} else {', // string.replace(string, function)
            'var i = str.indexOf(substr);',
            'if (i !== -1) {',
            'var inject = newSubstr(str.substr(i, substr.length), i, str);',
            'str = str.substring(0, i) + inject + str.substring(i + substr.length);',
            '}',
            '}',
            'return str;',
            '};',
            '})();',
            '',
        );
    }

    /**
     * Initialize the Boolean class.
     * @param globalObject Global object.
     */
    initBoolean(globalObject: JsObject) {
        const thisInterpreter = this;
        // Boolean constructor.
        const wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsBoolean(_value: JsObject) {
                const value = nativeGlobal.Boolean(_value);
                if (thisInterpreter.calledWithNew()) {
                    // Called as `new Boolean()`.
                    (this as any).data = value;
                    return this;
                }
                // Called as `Boolean()`.
                return value;
            },
        };
        this.BOOLEAN = this.createNativeFunction(wrapper.nativeFunc, true);
        this.setProperty(globalObject, 'Boolean', this.BOOLEAN, Interpreter.NONENUMERABLE_DESCRIPTOR);
    }

    /**
     * Initialize the Number class.
     * @param globalObject Global object.
     */
    initNumber(globalObject: JsObject) {
        const thisInterpreter = this;
        // Number constructor.
        const wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsNumber(...args: any) {
                const value = args.length ? nativeGlobal.Number(args[0]) : 0;
                if (thisInterpreter.calledWithNew()) {
                    // Called as `new Number()`.
                    (this as any).data = value;
                    return this;
                }
                // Called as `Number()`.
                return value;
            },
        };
        this.NUMBER = this.createNativeFunction(wrapper.nativeFunc, true);
        this.setProperty(globalObject, 'Number', this.NUMBER, Interpreter.NONENUMERABLE_DESCRIPTOR);

        const numConsts = ['MAX_VALUE', 'MIN_VALUE', 'NaN', 'NEGATIVE_INFINITY', 'POSITIVE_INFINITY'];
        for (const n of numConsts) {
            this.setProperty(
                this.NUMBER,
                n,
                (Number as any)[n],
                Interpreter.NONCONFIGURABLE_READONLY_NONENUMERABLE_DESCRIPTOR,
            );
        }

        const numberWrappers = {
            toExponential(fractionDigits?: number) {
                try {
                    return Number(this).toExponential(fractionDigits);
                } catch (e) {
                    // Throws if fractionDigits isn't within 0-20.
                    thisInterpreter.throwException(thisInterpreter.ERROR, e.message);
                }
                return undefined;
            },

            toFixed(digits?: number) {
                try {
                    return Number(this).toFixed(digits);
                } catch (e) {
                    // Throws if digits isn't within 0-20.
                    thisInterpreter.throwException(thisInterpreter.ERROR, e.message);
                }
                return undefined;
            },

            toPrecision(precision?: number) {
                try {
                    return Number(this).toPrecision(precision);
                } catch (e) {
                    // Throws if precision isn't within range (depends on implementation).
                    thisInterpreter.throwException(thisInterpreter.ERROR, e.message);
                }
                return undefined;
            },

            toString(radix?: number) {
                try {
                    return Number(this).toString(radix);
                } catch (e) {
                    // Throws if radix isn't within 2-36.
                    thisInterpreter.throwException(thisInterpreter.ERROR, e.message);
                }
                return undefined;
            },

            toLocaleString(_locales: any, _options: any) {
                const locales = _locales ? thisInterpreter.pseudoToNative(_locales) : undefined;
                const options = _options ? thisInterpreter.pseudoToNative(_options) : undefined;
                try {
                    return Number(this).toLocaleString(/** @type {?} */ locales, /** @type {?} */ options);
                } catch (e) {
                    thisInterpreter.throwException(thisInterpreter.ERROR, `toLocaleString: ${e.message}`);
                }
                return undefined;
            },
        };

        // Instance methods on Number.
        this.setNativeFunctionPrototype(this.NUMBER, 'toExponential', numberWrappers.toExponential);
        this.setNativeFunctionPrototype(this.NUMBER, 'toFixed', numberWrappers.toFixed);
        this.setNativeFunctionPrototype(this.NUMBER, 'toPrecision', numberWrappers.toPrecision);
        this.setNativeFunctionPrototype(this.NUMBER, 'toString', numberWrappers.toString);
        this.setNativeFunctionPrototype(this.NUMBER, 'toLocaleString', numberWrappers.toLocaleString);
    }

    /**
     * Initialize the Date class.
     * @param globalObject Global object.
     */
    initDate(globalObject: JsObject) {
        const thisInterpreter = this;
        // Date constructor.
        const wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsDate(...vargs: any[]) {
                if (!thisInterpreter.calledWithNew()) {
                    // Called as `Date()`.
                    // Calling Date() as a function returns a string, no arguments are heeded.
                    return nativeGlobal.Date();
                }
                // Called as `new Date(...)`.
                const args = [null].concat(vargs) as any[];
                (this as any).data = new (Function.prototype.bind.apply(nativeGlobal.Date, args))();
                return this;
            },
        };
        this.DATE = this.createNativeFunction(wrapper.nativeFunc, true);
        this.DATE_PROTO = this.DATE.properties.prototype;
        this.setProperty(globalObject, 'Date', this.DATE, Interpreter.NONENUMERABLE_DESCRIPTOR);

        // Static methods on Date.
        this.setProperty(
            this.DATE,
            'now',
            this.createNativeFunction(Date.now, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        this.setProperty(
            this.DATE,
            'parse',
            this.createNativeFunction(Date.parse, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        this.setProperty(
            this.DATE,
            'UTC',
            this.createNativeFunction(Date.UTC, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );

        const makePrototypeWrapper = (nativeFunc: any) => {
            const protoWrapper = {
                nativeFunc: function datePrototype(...vargs: any[]) {
                    const date = (this as unknown as JsObject).data;
                    if (!(date instanceof Date)) {
                        thisInterpreter.throwException(
                            thisInterpreter.TYPE_ERROR,
                            `${nativeFunc} not called on a Date`,
                        );
                    }
                    const args = [];
                    for (let i = 0; i < vargs.length; i++) {
                        args[i] = thisInterpreter.pseudoToNative(vargs[i]);
                    }
                    // eslint-disable-next-line prefer-spread
                    return date[nativeFunc].apply(date, args);
                },
            };
            return protoWrapper.nativeFunc;
        };

        // Instance methods on Date.
        const functions = [
            'getDate',
            'getDay',
            'getFullYear',
            'getHours',
            'getMilliseconds',
            'getMinutes',
            'getMonth',
            'getSeconds',
            'getTime',
            'getTimezoneOffset',
            'getUTCDate',
            'getUTCDay',
            'getUTCFullYear',
            'getUTCHours',
            'getUTCMilliseconds',
            'getUTCMinutes',
            'getUTCMonth',
            'getUTCSeconds',
            'getYear',
            'setDate',
            'setFullYear',
            'setHours',
            'setMilliseconds',
            'setMinutes',
            'setMonth',
            'setSeconds',
            'setTime',
            'setUTCDate',
            'setUTCFullYear',
            'setUTCHours',
            'setUTCMilliseconds',
            'setUTCMinutes',
            'setUTCMonth',
            'setUTCSeconds',
            'setYear',
            'toDateString',
            'toISOString',
            'toJSON',
            'toGMTString',
            'toLocaleDateString',
            'toLocaleString',
            'toLocaleTimeString',
            'toTimeString',
            'toUTCString',
        ];
        for (const func of functions) {
            this.setNativeFunctionPrototype(this.DATE, func, makePrototypeWrapper(func));
        }
    }

    /**
     * Initialize Regular Expression object.
     * @param globalObject Global object.
     */
    initRegExp(globalObject: JsObject) {
        if (!this._polyfills) {
            throw new Error('polyFills not initialized');
        }
        const thisInterpreter = this;

        // RegExp constructor.
        const wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsRegExp(pattern: any, _flags?: string) {
                let rgx: JsObject;
                if (thisInterpreter.calledWithNew()) {
                    // Called as `new RegExp()`.
                    rgx = this as unknown as JsObject;
                } else {
                    // Called as `RegExp()`.
                    if (_flags === undefined && thisInterpreter.isa(pattern, thisInterpreter.REGEXP)) {
                        // Regexp(/foo/) returns the same obj.
                        return pattern;
                    }
                    rgx = thisInterpreter.createObjectProto(thisInterpreter.REGEXP_PROTO);
                }
                const flags = _flags ? String(_flags) : '';
                if (!/^[gmi]*$/.test(flags)) {
                    // Don't allow ES6 flags 'y' and 's' to pass through.
                    thisInterpreter.throwException(thisInterpreter.SYNTAX_ERROR, `Invalid regexp flag: ${flags}`);
                }
                let nativeRegExp;
                try {
                    let nativePattern;
                    if (pattern === undefined) {
                        nativePattern = '';
                    } else {
                        nativePattern = thisInterpreter.isa(pattern, thisInterpreter.REGEXP)
                            ? thisInterpreter.pseudoToNative(pattern)
                            : String(pattern);
                    }
                    nativeRegExp = new nativeGlobal.RegExp(nativePattern, flags);
                } catch (e) {
                    // Throws if flags are repeated.
                    thisInterpreter.throwException(thisInterpreter.SYNTAX_ERROR, e.message);
                }
                thisInterpreter.populateRegExp(rgx, nativeRegExp);
                return rgx;
            },
        };
        this.REGEXP = this.createNativeFunction(wrapper.nativeFunc, true);
        this.REGEXP_PROTO = this.REGEXP.properties.prototype;

        this.setProperty(globalObject, 'RegExp', this.REGEXP, Interpreter.NONENUMERABLE_DESCRIPTOR);

        this.setProperty(
            this.REGEXP.properties.prototype,
            'global',
            undefined,
            Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(
            this.REGEXP.properties.prototype,
            'ignoreCase',
            undefined,
            Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(
            this.REGEXP.properties.prototype,
            'multiline',
            undefined,
            Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(
            this.REGEXP.properties.prototype,
            'source',
            '(?:)',
            Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR,
        );

        // Use polyfill to avoid complexity of regexp threads.
        this._polyfills.push(
            "Object.defineProperty(RegExp.prototype, 'test',",
            '{configurable: true, writable: true, value:',
            'function test(str) {',
            'return !!this.exec(str);',
            '}',
            '});',
        );

        const regexpWrappers = {
            exec(_string: any, callback: (arg0: any) => void) {
                const regexp = (this as any).data;
                const string = String(_string);
                // Get lastIndex from wrapped regexp, since this is settable.
                regexp.lastIndex = Number(thisInterpreter.getProperty(this, 'lastIndex'));
                // Example of catastrophic exec RegExp:
                // /^(a+)+b/.exec('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaac')
                thisInterpreter.maybeThrowRegExp(regexp, callback);
                let match;
                if (thisInterpreter.REGEXP_MODE === 2) {
                    if (Interpreter.vm) {
                        // Run exec in vm.
                        const sandbox = {
                            string,
                            regexp,
                        };
                        const code = 'regexp.exec(string)';
                        match = thisInterpreter.vmCall(code, sandbox, regexp, callback);
                        if (match !== Interpreter.REGEXP_TIMEOUT) {
                            thisInterpreter.setProperty(this, 'lastIndex', regexp.lastIndex);
                            callback(matchToPseudo(match));
                        }
                    } else {
                        // Run exec in separate thread.
                        // Note that lastIndex is not preserved when a RegExp is passed to a
                        // Web Worker.  Thus it needs to be passed back and forth separately.
                        const execWorker = thisInterpreter.createWorker();
                        const pid = thisInterpreter.regExpTimeout(regexp, execWorker, callback);
                        const thisPseudoRegExp = this;
                        execWorker.onmessage = function onmessage(e: JsObject) {
                            clearTimeout(pid);
                            // Return tuple: [result, lastIndex]
                            thisInterpreter.setProperty(thisPseudoRegExp, 'lastIndex', e.data[1]);
                            callback(matchToPseudo(e.data[0]));
                        };
                        execWorker.postMessage(['exec', regexp, regexp.lastIndex, string]);
                    }
                    return;
                }
                // Run exec natively.
                match = regexp.exec(string);
                thisInterpreter.setProperty(this, 'lastIndex', regexp.lastIndex);
                callback(matchToPseudo(match));

                function matchToPseudo(match0: { index: any; input: any }) {
                    if (match0) {
                        const result = thisInterpreter.arrayNativeToPseudo(match0);
                        // match has additional properties.
                        thisInterpreter.setProperty(result, 'index', match0.index);
                        thisInterpreter.setProperty(result, 'input', match0.input);
                        return result;
                    }
                    return null;
                }
            },
        };
        this.setAsyncFunctionPrototype(this.REGEXP, 'exec', regexpWrappers.exec);
    }

    /**
     * Initialize the Error class.
     * @param globalObject Global object.
     */
    initError(globalObject: JsObject) {
        const thisInterpreter = this;
        // Error constructor.
        const wrapper: { nativeFunc: Function } | Function = {
            nativeFunc: function EjsError(message?: any) {
                const newError = thisInterpreter.calledWithNew()
                    ? // Called as `new Error()`.
                      (this as any)
                    : // Called as `Error()`.
                      thisInterpreter.createObject(thisInterpreter.ERROR);
                thisInterpreter.populateError(newError, message);
                return newError;
            },
        };

        this.ERROR = this.createNativeFunction(wrapper.nativeFunc, true);
        this.setProperty(globalObject, 'Error', this.ERROR, Interpreter.NONENUMERABLE_DESCRIPTOR);
        this.setProperty(this.ERROR.properties.prototype, 'message', '', Interpreter.NONENUMERABLE_DESCRIPTOR);
        this.setProperty(this.ERROR.properties.prototype, 'name', 'Error', Interpreter.NONENUMERABLE_DESCRIPTOR);

        const createErrorSubclass = (name: string) => {
            const errorWrapper = {
                nativeFunc: function errorSubclassConstructor(message: string) {
                    const newError = thisInterpreter.calledWithNew()
                        ? // Called as `new XyzError()`.
                          (this as any)
                        : // Called as `XyzError()`.
                          thisInterpreter.createObject(ctor);
                    thisInterpreter.populateError(newError, message);
                    return newError;
                },
            };
            const ctor = thisInterpreter.createNativeFunction(errorWrapper.nativeFunc, true);
            thisInterpreter.setProperty(
                ctor,
                'prototype',
                thisInterpreter.createObject(thisInterpreter.ERROR),
                Interpreter.NONENUMERABLE_DESCRIPTOR,
            );
            thisInterpreter.setProperty(ctor.properties.prototype, 'name', name, Interpreter.NONENUMERABLE_DESCRIPTOR);
            thisInterpreter.setProperty(globalObject, name, ctor, Interpreter.NONENUMERABLE_DESCRIPTOR);

            return ctor;
        };

        // this.EVAL_ERROR = createErrorSubclass('EvalError');
        this.RANGE_ERROR = createErrorSubclass('RangeError');
        this.REFERENCE_ERROR = createErrorSubclass('ReferenceError');
        this.SYNTAX_ERROR = createErrorSubclass('SyntaxError');
        this.TYPE_ERROR = createErrorSubclass('TypeError');
        this.URI_ERROR = createErrorSubclass('URIError');
    }

    /**
     * Initialize Math object.
     * @param globalObject Global object.
     */
    initMath(globalObject: JsObject) {
        const jsMath = this.createObjectProto(this.OBJECT_PROTO);
        this.setProperty(globalObject, 'Math', jsMath, Interpreter.NONENUMERABLE_DESCRIPTOR);
        const mathConsts = ['E', 'LN2', 'LN10', 'LOG2E', 'LOG10E', 'PI', 'SQRT1_2', 'SQRT2'];
        for (const math of mathConsts) {
            this.setProperty(jsMath, math, (Math as any)[math], Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        }
        const numFunctions = [
            'abs',
            'acos',
            'asin',
            'atan',
            'atan2',
            'ceil',
            'cos',
            'exp',
            'floor',
            'log',
            'max',
            'min',
            'pow',
            'random',
            'round',
            'sin',
            'sqrt',
            'tan',
        ];
        for (const func of numFunctions) {
            this.setProperty(
                jsMath,
                func,
                this.createNativeFunction((Math as any)[func], false),
                Interpreter.NONENUMERABLE_DESCRIPTOR,
            );
        }
    }

    /**
     * Initialize JSON object.
     * @param globalObject Global object.
     */
    initJSON(globalObject: JsObject) {
        const thisInterpreter = this;
        const myJSON = thisInterpreter.createObjectProto(this.OBJECT_PROTO);
        this.setProperty(globalObject, 'JSON', myJSON, Interpreter.NONENUMERABLE_DESCRIPTOR);

        const jsonWrappers = {
            parse(text: any) {
                let nativeObj;
                try {
                    nativeObj = JSON.parse(String(text));
                } catch (e) {
                    thisInterpreter.throwException(thisInterpreter.SYNTAX_ERROR, e.message);
                }
                return thisInterpreter.nativeToPseudo(nativeObj);
            },

            stringify(value: any, _replacer: any, _space: string | number | undefined) {
                let replacer = _replacer;
                let space = _space;
                if (replacer?.class === 'Function') {
                    thisInterpreter.throwException(
                        thisInterpreter.TYPE_ERROR,
                        'Function replacer on JSON.stringify not supported',
                    );
                } else if (replacer?.class === 'Array') {
                    replacer = thisInterpreter.arrayPseudoToNative(_replacer).filter(
                        (word: any) =>
                            // Spec says we should also support boxed primitives here.
                            typeof word === 'string' || typeof word === 'number',
                    );
                } else {
                    replacer = null;
                }
                // Spec says we should also support boxed primitives here.
                if (typeof space !== 'string' && typeof space !== 'number') {
                    space = undefined;
                }

                const nativeObj = thisInterpreter.pseudoToNative(value);
                let str;
                try {
                    str = JSON.stringify(nativeObj, replacer, space);
                } catch (e) {
                    thisInterpreter.throwException(thisInterpreter.TYPE_ERROR, e.message);
                }
                return str;
            },
        };
        this.setProperty(myJSON, 'parse', this.createNativeFunction(jsonWrappers.parse, false));
        this.setProperty(myJSON, 'stringify', this.createNativeFunction(jsonWrappers.stringify, false));
    }

    /**
     * Is an object of a certain class?
     * @param child Object to check.
     * @param ctor Constructor of object.
     * @returns True if object is the class or inherits from it.
     *     False otherwise.
     */
    isa(child: JsValue, ctor: JsObject): boolean {
        if (child == null || !ctor) {
            return false;
        }
        const proto = ctor.properties.prototype;
        if (child === proto) {
            return true;
        }
        // The first step up the prototype chain is harder since the child might be
        // a primitive value.  Subsequent steps can just follow the .proto property.
        let childProto = this.getPrototype(child);
        while (childProto) {
            if (childProto === proto) {
                return true;
            }
            childProto = childProto.proto;
        }
        return false;
    }

    /**
     * Initialize a pseudo regular expression object based on a native regular
     * expression object.
     * @param pseudoRegexp The existing object to set.
     * @param nativeRegexp The native regular expression.
     */
    populateRegExp(pseudoRegexp: JsObject, nativeRegexp: RegExp) {
        pseudoRegexp.data = new RegExp(nativeRegexp.source, nativeRegexp.flags);
        // lastIndex is settable, all others are read-only attributes
        this.setProperty(pseudoRegexp, 'lastIndex', nativeRegexp.lastIndex, Interpreter.NONENUMERABLE_DESCRIPTOR);
        this.setProperty(pseudoRegexp, 'source', nativeRegexp.source, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        this.setProperty(pseudoRegexp, 'global', nativeRegexp.global, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        this.setProperty(
            pseudoRegexp,
            'ignoreCase',
            nativeRegexp.ignoreCase,
            Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR,
        );
        this.setProperty(
            pseudoRegexp,
            'multiline',
            nativeRegexp.multiline,
            Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR,
        );
    }

    /**
     * Initialize a pseudo error object.
     * @param pseudoError The existing object to set.
     * @param message Error's message.
     */
    populateError(pseudoError: JsObject, message?: string) {
        if (message) {
            this.setProperty(pseudoError, 'message', String(message), Interpreter.NONENUMERABLE_DESCRIPTOR);
        }
        const tracebackData: any[] = [];
        for (let i = this.stateStack.length - 1; i >= 0; i--) {
            const state: any = this.stateStack[i];
            const node = state.node;
            if (node.type === 'CallExpression') {
                const func = state.func_;
                if (func && tracebackData.length) {
                    tracebackData[tracebackData.length - 1].datumName = this.getProperty(func, 'name');
                }
            }
            if (node.loc && (!tracebackData.length || node.type === 'CallExpression')) {
                tracebackData.push({ datumLoc: node.loc });
            }
        }
        const errorName = String(this.getProperty(pseudoError, 'name'));
        const errorMessage = String(this.getProperty(pseudoError, 'message'));
        let stackString = `${errorName}: ${errorMessage}\n`;
        for (const data of tracebackData) {
            const loc = data.datumLoc;
            const name = data.datumName;
            const locString = `${loc.source}:${loc.start.line}:${loc.start.column}`;
            if (name) {
                stackString += `  at ${name} (${locString})\n`;
            } else {
                stackString += `  at ${locString}\n`;
            }
        }
        this.setProperty(pseudoError, 'stack', stackString.trim(), Interpreter.NONENUMERABLE_DESCRIPTOR);
    }

    /**
     * Create a Web Worker to execute regular expressions.
     * Using a separate file fails in Chrome when run locally on a file:// URI.
     * Using a data encoded URI fails in IE and Edge.
     * Using a blob works in IE11 and all other browsers.
     * @returns Web Worker with regexp execution code loaded.
     */
    createWorker(): any {
        let blob = (this.createWorker as any).blob_;
        if (!blob) {
            blob = new Blob([Interpreter.WORKER_CODE.join('\n')], {
                type: 'application/javascript',
            });
            // Cache the blob, so it doesn't need to be created next time.
            (this.createWorker as any).blob_ = blob;
        }
        return new Worker(URL.createObjectURL(blob));
    }

    /**
     * Execute regular expressions in a node vm.
     * @param code Code to execute.
     * @param sandbox Global variables for new vm.
     * @param nativeRegExp Regular expression.
     * @param callback Asynchronous callback function.
     */
    vmCall(code: string, sandbox: any, nativeRegExp: any, callback: (arg0: null) => void) {
        const options = { timeout: this.REGEXP_THREAD_TIMEOUT };
        try {
            return Interpreter.vm?.runInNewContext(code, sandbox, options);
        } catch (_e) {
            callback(null);
            this.throwException(this.ERROR, `RegExp Timeout: ${nativeRegExp}`);
        }
        return Interpreter.REGEXP_TIMEOUT;
    }

    /**
     * If REGEXP_MODE is 0, then throw an error.
     * Also throw if REGEXP_MODE is 2 and JS doesn't support Web Workers or vm.
     * @param nativeRegExp Regular expression.
     * @param callback Asynchronous callback function.
     */
    maybeThrowRegExp(nativeRegExp: RegExp, callback: Function) {
        let ok;
        if (this.REGEXP_MODE === 0) {
            // Fail: No RegExp support.
            ok = false;
        } else if (this.REGEXP_MODE === 1) {
            // Ok: Native RegExp support.
            ok = true;
        } else if (Interpreter.vm) {
            // Sandboxed RegExp handling.
            // Ok: Node's vm module already loaded.
            ok = true;
        } else if (typeof Worker === 'function' && typeof URL === 'function') {
            // Ok: Web Workers available.
            ok = true;
        } else if (typeof require === 'function') {
            // Try to load Node's vm module.
            try {
                // eslint-disable-next-line global-require
                Interpreter.vm = require('vm');
            } catch (_e) {
                // ok = false;
            }
            ok = !!Interpreter.vm;
        } else {
            // Fail: Neither Web Workers nor vm available.
            ok = false;
        }

        if (!ok) {
            callback(null);
            this.throwException(this.ERROR, `Regular expressions not supported: ${nativeRegExp}`);
        }
    }

    /**
     * Set a timeout for regular expression threads.  Unless cancelled, this will
     * terminate the thread and throw an error.
     * @param nativeRegExp Regular expression (used for error message).
     * @param worker Thread to terminate.
     * @param callback Async callback function to continue execution.
     * @returns PID of timeout.  Used to cancel if thread completes.
     */
    regExpTimeout(nativeRegExp: RegExp, worker: any, callback: Function): NodeJS.Timeout {
        const thisInterpreter = this;
        return setTimeout(() => {
            worker.terminate();
            callback(null);
            try {
                thisInterpreter.throwException(thisInterpreter.ERROR, `RegExp Timeout: ${nativeRegExp}`);
            } catch (_e) {
                // Eat the expected Interpreter.STEP_ERROR.
            }
        }, this.REGEXP_THREAD_TIMEOUT);
    }

    /**
     * Create a new data object based on a constructor's prototype.
     * @param ctor Parent constructor function,
     *     or null if scope object.
     * @returns New data object.
     */
    createObject(ctor: JsObject): JsObject {
        return this.createObjectProto(ctor && ctor.properties.prototype);
    }

    /**
     * Create a new data object based on a prototype.
     * @param proto Prototype object.
     * @returns New data object.
     */
    createObjectProto(proto: JsObject | null) {
        if (typeof proto !== 'object') {
            throw new Error('Non object prototype');
        }
        const obj = new JsObject(proto);
        if (this.isa(obj, this.ERROR)) {
            // Record this object as being an error so that its toString function can
            // process it correctly (toString has no access to the interpreter and could
            // not otherwise determine that the object is an error).
            obj.class = 'Error';
        }
        return obj;
    }

    /**
     * Create a new array.
     * @returns {!JsObject} New array.
     */
    createArray() {
        const array = this.createObjectProto(this.ARRAY_PROTO);
        // Arrays have length.
        this.setProperty(array, 'length', 0, {
            configurable: false,
            enumerable: false,
            writable: true,
        });
        array.class = 'Array';
        return array;
    }

    /**
     * Create a new function object (could become interpreted or native or async).
     * @param argumentLength Number of arguments.
     * @param isConstructor True if function can be used with 'new'.
     * @returns New function.
     * @private
     */
    createFunctionBase_(argumentLength: number, isConstructor: boolean): JsObject {
        const func: any = this.createObjectProto(this.FUNCTION_PROTO);
        if (isConstructor) {
            const proto = this.createObjectProto(this.OBJECT_PROTO);
            this.setProperty(func, 'prototype', proto, Interpreter.NONENUMERABLE_DESCRIPTOR);
            this.setProperty(proto, 'constructor', func, Interpreter.NONENUMERABLE_DESCRIPTOR);
        } else {
            func.illegalConstructor = true;
        }
        this.setProperty(func, 'length', argumentLength, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        func.class = 'Function';
        // When making changes to this function, check to see if those changes also
        // need to be made to the creation of FUNCTION_PROTO in initFunction.
        return func;
    }

    /**
     * Create a new interpreted function.
     * @param node AST node defining the function.
     * @param scope Parent scope.
     * @param _name Optional name for function.
     * @returns New function.
     */
    createFunction(node: any, scope: JsScope, _name?: string): JsObject {
        const func: any = this.createFunctionBase_(node.params.length, true);
        func.parentScope = scope;
        func.node = node;
        // Choose a name for this function.
        // function foo() {}               -> 'foo'
        // const bar = function() {};      -> 'bar'
        // const bar = function foo() {};  -> 'foo'
        // foo.bar = function() {};        -> ''
        // const bar = new Function('');   -> 'anonymous'
        const name = node.id ? String(node.id.name) : _name || '';
        this.setProperty(func, 'name', name, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        return func;
    }

    /**
     * Create a new native function.
     * @param nativeFunc JavaScript function.
     * @param isConstructor True if function can be used with 'new'.
     * @returns New function.
     */
    createNativeFunction(nativeFunc: Function, isConstructor: boolean, options?: any): JsObject {
        const func: any = this.createFunctionBase_(nativeFunc.length, isConstructor);
        func.nativeFunc = nativeFunc;
        (nativeFunc as FunctionWithId).id = this._functionCounter++;
        this.setProperty(func, 'name', nativeFunc.name, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        if (options) {
            for (const key of Object.keys(options)) {
                func[key] = options[key];
            }
        }
        return func;
    }

    /**
     * Create a new native asynchronous function.
     * @param asyncFunc JavaScript function.
     * @returns New function.
     */
    createAsyncFunction(asyncFunc: Function): JsObject {
        const func: any = this.createFunctionBase_(asyncFunc.length, true);
        func.asyncFunc = asyncFunc;
        (asyncFunc as FunctionWithId).id = this._functionCounter++;
        this.setProperty(func, 'name', asyncFunc.name, Interpreter.READONLY_NONENUMERABLE_DESCRIPTOR);
        return func;
    }

    /**
     * Converts from a native JavaScript object or value to a JS-Interpreter object.
     * Can handle JSON-style values, regular expressions, dates and functions.
     * Does NOT handle cycles.
     * @param nativeObj The native JavaScript object to be converted.
     * @returns The equivalent JS-Interpreter object.
     */
    nativeToPseudo(nativeObj: any): JsValue {
        if (nativeObj instanceof JsObject) {
            throw new Error('Object is already pseudo');
        }
        if (
            nativeObj === null ||
            nativeObj === undefined ||
            nativeObj === true ||
            nativeObj === false ||
            typeof nativeObj === 'string' ||
            typeof nativeObj === 'number'
        ) {
            return nativeObj;
        }

        if (nativeObj instanceof RegExp) {
            const pseudoRegexp = this.createObjectProto(this.REGEXP_PROTO);
            this.populateRegExp(pseudoRegexp, nativeObj);
            return pseudoRegexp;
        }

        if (nativeObj instanceof Date) {
            const pseudoDate = this.createObjectProto(this.DATE_PROTO);
            pseudoDate.data = new Date(nativeObj.valueOf());
            return pseudoDate;
        }

        if (typeof nativeObj === 'function') {
            const thisInterpreter = this;
            const wrapper = function (...vargs: any) {
                const args = Array.prototype.slice.call(vargs).map((i: number) => thisInterpreter.pseudoToNative(i));
                const value = nativeObj.apply(thisInterpreter, args);
                return thisInterpreter.nativeToPseudo(value);
            };
            const prototype = Object.getOwnPropertyDescriptor(nativeObj, 'prototype');
            return this.createNativeFunction(wrapper, !!prototype);
        }

        if (Array.isArray(nativeObj)) {
            // Array.
            const pseudoArray = this.createArray();
            for (let i = 0; i < nativeObj.length; i++) {
                if (i in nativeObj) {
                    this.setProperty(pseudoArray, i, this.nativeToPseudo(nativeObj[i]));
                }
            }
            return pseudoArray;
        }

        // Object.
        const pseudoObj = this.createObjectProto(this.OBJECT_PROTO);
        for (const key of Object.keys(nativeObj)) {
            this.setProperty(pseudoObj, key, this.nativeToPseudo(nativeObj[key]));
        }
        return pseudoObj;
    }

    /**
     * Converts from a JS-Interpreter object to native JavaScript object.
     * Can handle JSON-style values, regular expressions, and dates.
     * Does handle cycles.
     * @param pseudoObj The JS-Interpreter object to be
     * converted.
     * @param _cycles Cycle detection object (used by recursive calls).
     * @returns The equivalent native JavaScript object or value.
     */
    pseudoToNative(pseudoObj: JsValue, _cycles?: any): any {
        if ((typeof pseudoObj !== 'object' && typeof pseudoObj !== 'function') || pseudoObj === null) {
            return pseudoObj;
        }
        if (!(pseudoObj instanceof JsObject)) {
            throw new Error('Object is not pseudo');
        }

        if (this.isa(pseudoObj, this.REGEXP)) {
            // Regular expression.
            const nativeRegExp = new RegExp(pseudoObj.data.source, pseudoObj.data.flags);
            nativeRegExp.lastIndex = pseudoObj.data.lastIndex;
            return nativeRegExp;
        }

        if (this.isa(pseudoObj, this.DATE)) {
            // Date.
            return new Date(pseudoObj.data.valueOf());
        }

        const cycles = _cycles || {
            pseudo: [],
            native: [],
        };
        const index = cycles.pseudo.indexOf(pseudoObj);
        if (index !== -1) {
            return cycles.native[index];
        }
        cycles.pseudo.push(pseudoObj);
        let nativeObj: any;
        if (this.isa(pseudoObj, this.ARRAY)) {
            // Array.
            nativeObj = [];
            cycles.native.push(nativeObj);
            const len = this.getProperty(pseudoObj, 'length');
            nativeObj.length = len;
            for (let i = 0; i < len; i++) {
                if (this.hasProperty(pseudoObj, i)) {
                    nativeObj[i] = this.pseudoToNative(this.getProperty(pseudoObj, i), cycles);
                }
            }
        } else {
            // Object.
            nativeObj = {};
            cycles.native.push(nativeObj);
            let val;
            for (const key of Object.keys(pseudoObj.properties)) {
                val = this.pseudoToNative(pseudoObj.properties[key], cycles);
                // Use defineProperty to avoid side effects if setting '__proto__'.
                Object.defineProperty(nativeObj, key, {
                    value: val,
                    writable: true,
                    enumerable: true,
                    configurable: true,
                });
            }
        }
        cycles.pseudo.pop();
        cycles.native.pop();
        return nativeObj;
    }

    /**
     * Converts from a native JavaScript array to a JS-Interpreter array.
     * Does handle non-numeric properties (like str.match's index prop).
     * Does NOT recurse into the array's contents.
     * @param nativeArray The JavaScript array to be converted.
     * @returns The equivalent JS-Interpreter array.
     */
    arrayNativeToPseudo(nativeArray: any): JsObject {
        const pseudoArray = this.createArray();
        const props = Object.getOwnPropertyNames(nativeArray);
        for (const prop of props) {
            this.setProperty(pseudoArray, prop, nativeArray[prop]);
        }
        return pseudoArray;
    }

    /**
     * Converts from a JS-Interpreter array to native JavaScript array.
     * Does handle non-numeric properties (like str.match's index prop).
     * Does NOT recurse into the array's contents.
     * @param pseudoArray The JS-Interpreter array,
     *     or JS-Interpreter object pretending to be an array.
     * @returns {!Array} The equivalent native JavaScript array.
     */
    arrayPseudoToNative(pseudoArray: JsObject) {
        const nativeArray: any = [];
        for (const key of Object.keys(pseudoArray.properties)) {
            nativeArray[key] = this.getProperty(pseudoArray, key);
        }
        // pseudoArray might be an object pretending to be an array.  In this case
        // it's possible that length is non-existent, invalid, or smaller than the
        // largest defined numeric property.  Set length explicitly here.
        nativeArray.length = Interpreter.legalArrayLength(this.getProperty(pseudoArray, 'length')) || 0;
        return nativeArray;
    }

    /**
     * Look up the prototype for this value.
     * @param value Data object.
     * @returns {JsObject} Prototype object, null if none.
     */
    getPrototype(value: JsValue) {
        switch (typeof value) {
            case 'number':
                return this.NUMBER.properties.prototype;
            case 'boolean':
                return this.BOOLEAN.properties.prototype;
            case 'string':
                return this.STRING.properties.prototype;
            default:
                if (value) {
                    return value.proto;
                }
                return null;
        }
    }

    /**
     * Fetch a property value from a data object.
     * @param _obj Data object.
     * @param name Name of property.
     * @returns Property value (may be undefined).
     */
    getProperty(_obj: JsValue, _name: JsValue): JsValue {
        if (this._getterStep) {
            throw new Error('Getter not supported in that context');
        }
        const name = String(_name);
        if (_obj == null) {
            this.throwException(this.TYPE_ERROR, `Cannot read property '${name}' of ${_obj}`);
        }
        if (typeof _obj === 'object' && !(_obj instanceof JsObject)) {
            throw TypeError('Expecting native value or pseudo object');
        }
        if (name === 'length') {
            // Special cases for magic length property.
            if (this.isa(_obj, this.STRING)) {
                return String(_obj).length;
            }
        } else if (name.charCodeAt(0) < 0x40) {
            // Might have numbers in there?
            // Special cases for string array indexing
            if (this.isa(_obj, this.STRING)) {
                const n = Interpreter.legalArrayIndex(name);
                if (!Number.isNaN(n) && n < String(_obj).length) {
                    return String(_obj)[n];
                }
            }
        }
        for (let obj = _obj; obj; obj = this.getPrototype(obj)) {
            if (obj.properties && name in obj.properties) {
                const getter = obj.getter[name];
                if (getter) {
                    // Flag this function as being a getter and thus needing immediate
                    // execution (rather than being the value of the property).
                    this._getterStep = true;
                    if (name === 'constructor') {
                        debug(`getProperty name=${name} getter=${String(getter)}`);
                    }
                    return getter;
                }
                if (name === 'constructor') {
                    debug(
                        `getProperty name=${name} ${obj instanceof JsObject} '${
                            obj.constructor.name
                        }' properties=[${Object.keys(obj.properties)}] property=${String(obj.properties[name])}`,
                    );
                }
                return obj.properties[name];
            }
        }
        return undefined;
    }

    /**
     * Does the named property exist on a data object.
     * @param _obj Data object.
     * @param name Name of property.
     * @returns True if property exists.
     */
    hasProperty(_obj: JsObject, _name: JsValue): boolean {
        if (!(_obj instanceof JsObject)) {
            throw TypeError('Primitive data type has no properties');
        }
        const name = String(_name);
        if (name === 'length' && this.isa(_obj, this.STRING)) {
            return true;
        }
        if (this.isa(_obj, this.STRING)) {
            const n = Interpreter.legalArrayIndex(name);
            if (!Number.isNaN(n) && n < String(_obj).length) {
                return true;
            }
        }
        for (let obj = _obj; obj; obj = this.getPrototype(obj)) {
            if (obj.properties && name in obj.properties) {
                return true;
            }
        }
        return false;
    }

    /**
     * Set a property value on a data object.
     * @param obj Data object.
     * @param name Name of property.
     * @param value New property value.
     *     Use Interpreter.VALUE_IN_DESCRIPTOR if value is handled by
     *     descriptor instead.
     * @param _descriptor Optional descriptor object.
     * @returns Returns a setter function if one
     *     needs to be called, otherwise undefined.
     */
    setProperty(obj: JsValue, _name: JsValue, _value: JsValue, _descriptor?: any): Maybe<JsObject> {
        if (this._setterStep) {
            // Getter from previous call to setProperty was not handled.
            throw new Error('Setter not supported in that context');
        }
        const name = String(_name);

        if (obj == null) {
            this.throwException(this.TYPE_ERROR, `Cannot set property '${name}' of ${obj}`);
        }
        if (typeof obj === 'object' && !(obj instanceof JsObject)) {
            throw TypeError('Expecting native value or pseudo object');
        }
        if (
            _descriptor &&
            ('get' in _descriptor || 'set' in _descriptor) &&
            ('value' in _descriptor || 'writable' in _descriptor)
        ) {
            this.throwException(
                this.TYPE_ERROR,
                'Invalid property descriptor. Cannot both specify accessors and a value or writable attribute',
            );
        }
        const strict = !this.stateStack?.[0] || this.getScope().strict;
        if (!(obj instanceof JsObject)) {
            if (strict) {
                this.throwException(this.TYPE_ERROR, `Can't create property '${name}' on '${obj}'`);
            }
            return undefined;
        }
        if (this.isa(obj, this.STRING)) {
            const n = Interpreter.legalArrayIndex(name);
            if (name === 'length' || (!Number.isNaN(n) && n < String(obj).length)) {
                // Can't set length or letters on String objects.
                if (strict) {
                    this.throwException(
                        this.TYPE_ERROR,
                        `Cannot assign to read only property '${name}' of String '${obj.data}'`,
                    );
                }
                return undefined;
            }
        }

        let value = _value;

        if (obj.class === 'Array') {
            // Arrays have a magic length variable that is bound to the elements.
            const len = obj.properties.length;
            if (name === 'length') {
                // Delete elements if length is smaller.
                if (_descriptor) {
                    if (!('value' in _descriptor)) {
                        return undefined;
                    }
                    value = _descriptor.value;
                }
                value = Interpreter.legalArrayLength(value);
                if (Number.isNaN(value)) {
                    this.throwException(this.RANGE_ERROR, 'Invalid array length');
                }
                if (value < len) {
                    for (const i of Object.keys(obj.properties)) {
                        const index = Interpreter.legalArrayIndex(i);
                        if (!Number.isNaN(index) && value <= index) {
                            delete obj.properties[index];
                        }
                    }
                }
            } else {
                const i = Interpreter.legalArrayIndex(name);
                if (!Number.isNaN(i)) {
                    // Increase length if this index is larger.
                    obj.properties.length = Math.max(len, i + 1);
                }
            }
        }
        if (obj.preventExtensions && !(name in obj.properties)) {
            if (strict) {
                this.throwException(this.TYPE_ERROR, `Can't add property '${name}', object is not extensible`);
            }
            return undefined;
        }
        if (_descriptor) {
            // Define the property.
            const descriptor = {} as any;
            if ('get' in _descriptor && _descriptor.get) {
                obj.getter[name] = _descriptor.get;
                descriptor.get = this.setPropertyPlaceholderGet_;
            }
            if ('set' in _descriptor && _descriptor.set) {
                obj.setter[name] = _descriptor.set;
                descriptor.set = this.setPropertyPlaceholderSet_;
            }
            if ('configurable' in _descriptor) {
                descriptor.configurable = _descriptor.configurable;
            }
            if ('enumerable' in _descriptor) {
                descriptor.enumerable = _descriptor.enumerable;
            }
            if ('writable' in _descriptor) {
                descriptor.writable = _descriptor.writable;
                delete obj.getter[name];
                delete obj.setter[name];
            }
            if ('value' in _descriptor) {
                descriptor.value = _descriptor.value;
                delete obj.getter[name];
                delete obj.setter[name];
            } else if (value !== Interpreter.VALUE_IN_DESCRIPTOR) {
                descriptor.value = value;
                delete obj.getter[name];
                delete obj.setter[name];
            }
            try {
                Object.defineProperty(obj.properties, name, descriptor);
            } catch {
                this.throwException(this.TYPE_ERROR, `Cannot redefine property: ${name}`);
            }
            // Now that the definition has suceeded, clean up any obsolete get/set funcs.
            if ('get' in _descriptor && !_descriptor.get) {
                delete obj.getter[name];
            }
            if ('set' in _descriptor && !_descriptor.set) {
                delete obj.setter[name];
            }
        } else {
            // Set the property.
            if (value === Interpreter.VALUE_IN_DESCRIPTOR) {
                throw ReferenceError('Value not specified');
            }
            // Determine the parent (possibly self) where the property is defined.
            let defObj = obj;
            while (!(name in defObj.properties)) {
                defObj = this.getPrototype(defObj);
                if (!defObj) {
                    // This is a new property.
                    defObj = obj;
                    break;
                }
            }
            if (defObj.setter && defObj.setter[name]) {
                this._setterStep = true;
                return defObj.setter[name];
            }
            if (defObj.getter && defObj.getter[name]) {
                if (strict) {
                    this.throwException(
                        this.TYPE_ERROR,
                        `Cannot set property '${name}' of object '${obj}' which only has a getter`,
                    );
                }
            } else {
                // No setter, simple assignment.
                try {
                    obj.properties[name] = value;
                } catch (_e) {
                    if (strict) {
                        this.throwException(
                            this.TYPE_ERROR,
                            `Cannot assign to read only property '${name}' of object '${obj}'`,
                        );
                    }
                }
            }
        }
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    setPropertyPlaceholderGet_() {
        throw new Error('Placeholder getter');
    }

    // eslint-disable-next-line class-methods-use-this
    setPropertyPlaceholderSet_() {
        throw new Error('Placeholder setter');
    }

    /**
     * Convenience method for adding a native function as a non-enumerable property
     * onto an object's prototype.
     * @param obj Data object.
     * @param {Interpreter.Value} name Name of property.
     * @param wrapper Function object.
     */
    setNativeFunctionPrototype(obj: JsObject, name: JsValue, wrapper: Function) {
        this.setProperty(
            obj.properties.prototype,
            name,
            this.createNativeFunction(wrapper, false),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );
    }

    /**
     * Convenience method for adding an async function as a non-enumerable property
     * onto an object's prototype.
     * @param obj Data object.
     * @param name Name of property.
     * @param wrapper Function object.
     */
    setAsyncFunctionPrototype(obj: JsObject, name: JsValue, wrapper: Function) {
        this.setProperty(
            obj.properties.prototype,
            name,
            this.createAsyncFunction(wrapper),
            Interpreter.NONENUMERABLE_DESCRIPTOR,
        );
    }

    /**
     * Returns the current scope from the stateStack.
     * @returns {!JsScope} Current scope.
     */
    getScope() {
        const scope = this.stateStack[this.stateStack.length - 1].scope;
        if (!scope) {
            throw new Error('No scope found');
        }
        return scope;
    }

    /**
     * Create a new scope dictionary.
     * @param node AST node defining the scope container
     *     (e.g. a function).
     * @param parentScope Scope to link to.
     * @returns New scope.
     */
    createScope(node: acorn.Node, parentScope: JsScope | null): JsScope {
        // Determine if this scope starts with `use strict`.
        let strict = this.options?.strict ?? false;
        if (parentScope?.strict) {
            strict = true;
        } else {
            const firstNode = (node as any)?.body[0];
            if (
                firstNode &&
                firstNode.expression &&
                firstNode.expression.type === 'Literal' &&
                firstNode.expression.value === 'use strict'
            ) {
                strict = true;
            }
        }
        const object = this.createObjectProto(null);
        const scope = new JsScope(parentScope, strict, object);
        if (!parentScope) {
            this.initGlobal(scope.object);
        }
        this.populateScope_(node, scope);
        return scope;
    }

    /**
     * Create a new special scope dictionary. Similar to createScope(), but
     * doesn't assume that the scope is for a function body.
     * This is used for 'catch' clauses, 'with' statements,
     * and named function expressions.
     * @param parentScope Scope to link to.
     * @param _object Optional object to transform into
     *     scope.
     * @returns New scope.
     */
    createSpecialScope(parentScope: JsScope | null, _object?: any): JsScope {
        if (!parentScope) {
            throw new Error('parentScope required');
        }
        const object = _object || this.createObjectProto(null);
        return new JsScope(parentScope, parentScope.strict, object);
    }

    /**
     * Retrieves a value from the scope chain.
     * @param name Name of variable.
     * @returns Any value.
     *   May be flagged as being a getter and thus needing immediate execution
     *   (rather than being the value of the property).
     */
    getValueFromScope(name: string): JsValue {
        let scope: JsScope | null = this.getScope();
        while (scope && scope !== this.globalScope) {
            if (name in scope.object.properties) {
                return scope.object.properties[name];
            }
            scope = scope.parentScope;
        }
        // The root scope is also an object which has inherited properties and
        // could also have getters.
        if (scope === this.globalScope && this.hasProperty(scope.object, name)) {
            return this.getProperty(scope.object, name);
        }
        // Typeof operator is unique: it can safely look at non-defined variables.
        const prevNode = this.stateStack[this.stateStack.length - 1].node;
        if (prevNode.type === 'UnaryExpression' && prevNode.operator === 'typeof') {
            return undefined;
        }
        this.throwException(this.REFERENCE_ERROR, `${name} is not defined`);
        return undefined;
    }

    /**
     * Sets a value to the current scope.
     * @param name Name of variable.
     * @param value Value.
     * @returns Returns a setter function if one
     *     needs to be called, otherwise undefined.
     */
    setValueToScope(name: string, value: JsValue, defineAsReadOnly = false): Maybe<JsObject> {
        let scope: JsScope | null = this.getScope();
        const strict = scope.strict;
        while (scope && scope !== this.globalScope) {
            if (name in scope.object.properties) {
                try {
                    if (defineAsReadOnly) {
                        Object.defineProperty(scope.object.properties, name, { value, writable: false });
                    } else {
                        scope.object.properties[name] = value;
                    }
                } catch (_e) {
                    if (strict) {
                        this.throwException(this.TYPE_ERROR, `Cannot assign to read only variable '${name}'`);
                    }
                }
                return undefined;
            }
            scope = scope.parentScope;
        }
        // The root scope is also an object which has readonly properties and
        // could also have setters.
        if (scope === this.globalScope && (!strict || this.hasProperty(scope.object, name))) {
            if (defineAsReadOnly) {
                return this.setProperty(scope.object, name, value, { value, writable: false });
            }
            return this.setProperty(scope.object, name, value);
        }
        this.throwException(this.REFERENCE_ERROR, `'${name}' is not defined`);
        return undefined;
    }

    /**
     * Create a new scope for the given node and populate it with all variables
     * and named functions.
     * @param node AST node (usually a program or function when initally
     *   calling this function, though it recurses to scan many child nodes).
     * @param scope Scope dictionary to populate.
     * @returns Map of all variable and function names.
     * @private
     */
    populateScope_(node: any, scope: JsScope): any {
        let variableCache;
        if (!node.variableCache_) {
            variableCache = Object.create(null);
            switch (node.type) {
                case 'VariableDeclaration':
                    for (const declaration of node.declarations) {
                        variableCache[declaration.id.name] = true;
                    }
                    break;
                case 'FunctionDeclaration':
                    variableCache[node.id.name] = node;
                    break;
                case 'BlockStatement':
                case 'CatchClause':
                case 'DoWhileStatement':
                case 'ForInStatement':
                case 'ForStatement':
                case 'IfStatement':
                case 'LabeledStatement':
                case 'Program':
                case 'SwitchCase':
                case 'SwitchStatement':
                case 'TryStatement':
                case 'WithStatement':
                case 'WhileStatement':
                    {
                        // All the structures within which a variable or function could hide.
                        const nodeClass = node.constructor;
                        for (const name in node) {
                            // eslint-disable-next-line no-continue
                            if (name === 'loc') continue;
                            const prop: any = node[name];
                            if (prop && typeof prop === 'object') {
                                let childCache;
                                if (Array.isArray(prop)) {
                                    for (const p of prop) {
                                        if (p && p.constructor === nodeClass) {
                                            childCache = this.populateScope_(p, scope);
                                            for (const childName of Object.keys(childCache)) {
                                                variableCache[childName] = childCache[childName];
                                            }
                                        }
                                    }
                                } else if (prop.constructor === nodeClass) {
                                    childCache = this.populateScope_(prop, scope);
                                    for (const childName of Object.keys(childCache)) {
                                        variableCache[childName] = childCache[childName];
                                    }
                                }
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            node.variableCache_ = variableCache;
        } else {
            variableCache = node.variableCache_;
        }
        for (const name in variableCache) {
            if (variableCache[name] === true) {
                this.setProperty(scope.object, name, undefined, Interpreter.VARIABLE_DESCRIPTOR);
            } else {
                this.setProperty(
                    scope.object,
                    name,
                    this.createFunction(variableCache[name], scope),
                    Interpreter.VARIABLE_DESCRIPTOR,
                );
            }
        }
        return variableCache;
    }

    /**
     * Is the current state directly being called with as a construction with 'new'.
     * @returns {boolean} True if 'new foo()', false if 'foo()'.
     */
    calledWithNew() {
        return (this.stateStack[this.stateStack.length - 1] as any).isConstructor;
    }

    /**
     * Gets a value from the scope chain or from an object property.
     * @param ref Name of variable or object/propname tuple.
     * @returns Any value.
     *   May be flagged as being a getter and thus needing immediate execution
     *   (rather than being the value of the property).
     */
    getValue(ref: any[]): JsValue {
        if (ref[0] === Interpreter.SCOPE_REFERENCE) {
            // A null/varname variable lookup.
            return this.getValueFromScope(ref[1]);
        }
        // An obj/prop components tuple (foo.bar).
        return this.getProperty(ref[0], ref[1]);
    }

    /**
     * Sets a value to the scope chain or to an object property.
     * @param ref Name of variable or object/propname tuple.
     * @param value Value.
     * @returns Returns a setter function if one
     *     needs to be called, otherwise undefined.
     */
    setValue(ref: any[], value: JsValue): Maybe<JsObject> {
        if (ref[0] === Interpreter.SCOPE_REFERENCE) {
            // A null/varname variable lookup.
            return this.setValueToScope(ref[1], value);
        }
        // An obj/prop components tuple (foo.bar).
        return this.setProperty(ref[0], ref[1], value);
    }

    /**
     * Throw an exception in the interpreter that can be handled by an
     * interpreter try/catch statement.  If unhandled, a real exception will
     * be thrown.  Can be called with either an error class and a message, or
     * with an actual object to be thrown.
     * @param {!JsObject|Interpreter.Value} errorClass Type of error
     *   (if message is provided) or the value to throw (if no message).
     * @param {string=} message Message being thrown.
     */
    throwException(errorClass: JsObject | any, message?: string) {
        if (!this.globalScope) {
            // This is an error being thrown in the initialization, throw a real error.
            throw message === undefined ? errorClass : message;
        }
        let error;
        if (message === undefined || !(errorClass instanceof JsObject)) {
            error = errorClass; // This is a value to throw, not an error class.
        } else {
            error = this.createObject(errorClass);
            this.populateError(error, message);
        }
        this.unwind(Interpreter.Completion.THROW, error, undefined);
        // Abort anything related to the current step.
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw Interpreter.STEP_ERROR;
    }

    /**
     * Unwind the stack to the innermost relevant enclosing TryStatement,
     * For/ForIn/WhileStatement or Call/NewExpression.  If this results in
     * the stack being completely unwound the thread will be terminated
     * and the appropriate error being thrown.
     * @param type Completion type.
     * @param value Value computed, returned or thrown.
     * @param label Target label for break or return.
     */
    unwind(type: any, value: JsValue, label?: string) {
        if (type === Interpreter.Completion.NORMAL) {
            throw TypeError('Should not unwind for NORMAL completions');
        }

        // eslint-disable-next-line no-labels
        loop: for (let stack = this.stateStack; stack.length > 0; stack.pop()) {
            const state: any = stack[stack.length - 1];
            switch (state.node.type) {
                case 'TryStatement':
                    state.cv = { type, value, label };
                    return;
                case 'CallExpression':
                case 'NewExpression':
                    if (type === Interpreter.Completion.RETURN) {
                        state.value = value;
                        this.returnValue = value;
                        return;
                    }
                    if (type !== Interpreter.Completion.THROW) {
                        throw new Error('Unsyntactic break/continue not rejected by Acorn');
                    }
                    break;
                case 'Program':
                    // Don't pop the stateStack.
                    // Leave the root scope on the tree in case the program is appended to.
                    state.done = true;
                    // eslint-disable-next-line no-labels
                    break loop;

                default:
                    break;
            }
            if (type === Interpreter.Completion.BREAK) {
                if (label ? state.labels && state.labels.indexOf(label) !== -1 : state.isLoop || state.isSwitch) {
                    stack.pop();
                    return;
                }
            } else if (type === Interpreter.Completion.CONTINUE) {
                if (label ? state.labels && state.labels.indexOf(label) !== -1 : state.isLoop) {
                    return;
                }
            }
        }

        // Unhandled completion.  Throw a real error.
        let realError;
        if (this.isa(value, this.ERROR)) {
            const errorTable: any = {
                EvalError,
                RangeError,
                ReferenceError,
                SyntaxError,
                TypeError,
                URIError,
            };
            const name = String(this.getProperty(value, 'name'));
            const message = this.getProperty(value, 'message').valueOf();
            const errorConstructor = errorTable[name] || Error;
            realError = errorConstructor(message);
            realError.stack = String(this.getProperty(value, 'stack'));
        } else {
            realError = String(value);
        }
        // Overwrite the previous (more or less random) interpreter return value.
        // Replace it with the error.
        this.value = realError;
        throw realError;
    }

    /**
     * AST to code.  Summarizes the expression at the given node.  Currently
     * not guaranteed to be correct or complete.  Used for error messages.
     * E.g. `escape('hello') + 42` -> 'escape(...) + 42'
     * @param node AST node.
     * @returns Code string.
     */
    nodeSummary(node: any): string {
        switch (node.type) {
            case 'ArrayExpression':
                return '[...]';
            case 'BinaryExpression':
            case 'LogicalExpression':
                return `${this.nodeSummary(node.left)} ${node.operator} ${this.nodeSummary(node.right)}`;
            case 'CallExpression':
                return `${this.nodeSummary(node.callee)}(...)`;
            case 'ConditionalExpression':
                return `${this.nodeSummary(node.test)} ? ${this.nodeSummary(node.consequent)} : ${this.nodeSummary(
                    node.alternate,
                )}`;
            case 'Identifier':
                return node.name;
            case 'Literal':
                return node.raw;
            case 'MemberExpression': {
                const obj = this.nodeSummary(node.object);
                const prop = this.nodeSummary(node.property);
                return node.computed ? `${obj}[${prop}]` : `${obj}.${prop}`;
            }
            case 'NewExpression':
                return `new ${this.nodeSummary(node.callee)}(...)`;
            case 'ObjectExpression':
                return '{...}';
            case 'ThisExpression':
                return 'this';
            case 'UnaryExpression':
                return `${node.operator} ${this.nodeSummary(node.argument)}`;
            case 'UpdateExpression': {
                const argument = this.nodeSummary(node.argument);
                return node.prefix ? node.operator + argument : argument + node.operator;
            }
            default:
                return '???';
        }
    }

    /**
     * Create a new queued task.
     * @param isInterval True if setInterval, false if setTimeout.
     * @param args Arguments from setInterval and setTimeout.
     *     [code, delay]
     *     [functionRef, delay, param1, param2, param3, ...]
     * @returns PID of new task.
     * @private
     */
    createTask_(isInterval: boolean, args: any[]): number {
        if (!this.options?.allowTask) {
            throw new Error('task not allowed');
        }
        const parentState = this.stateStack[this.stateStack.length - 1];
        const argsArray = Array.from(args);
        const exec = argsArray.shift();
        const delay = Math.max(Number(argsArray.shift() || 0), 0);
        const node: any = Interpreter.newNode();
        let scope;
        let functionRef;

        if (exec instanceof JsObject && exec.class === 'Function') {
            // setTimeout/setInterval with a function reference.
            functionRef = exec;
            node.type = 'CallExpression';
            scope = parentState.scope;
        } else {
            let ast: any;
            // setTimeout/setInterval with code string.
            try {
                ast = Interpreter._parse(String(exec), `taskCode${this._taskCodeNumber++}`);
            } catch (e) {
                // Acorn threw a SyntaxError.  Rethrow as a trappable error.
                this.throwException(this.SYNTAX_ERROR, `Invalid code: ${e.message}`);
            }
            node.type = 'EvalProgram_';
            node.body = ast?.body;
            // Change highlighting to encompas the string.
            const execNode = parentState.node.arguments[0];
            Interpreter.stripLocations_(node, execNode?.start, execNode?.end);
            scope = this.globalScope;
            argsArray.length = 0;
        }

        const task = new JsTask(functionRef, argsArray, scope, node, isInterval ? delay : -1);
        this.scheduleTask_(task, delay);
        return task.pid;
    }

    /**
     * Schedule a task to execute at some time in the future.
     * @param task Task to schedule.
     * @param delay Number of ms before the task should execute.
     * @private
     */
    scheduleTask_(task: JsTask, delay: number) {
        task.time = Date.now() + delay;
        // For optimum efficiency we could do a binary search and inject the task
        // at the right spot.  But 'push' & 'sort' is just two lines of code.
        this.tasks.push(task);
        this.tasks.sort((a, b) => {
            return a.time - b.time;
        });
    }

    /**
     * Delete a queued task.
     * @param pid PID of task to delete.
     * @private
     */
    deleteTask_(pid: number) {
        for (let i = 0; i < this.tasks.length; i++) {
            if (this.tasks[i].pid === pid) {
                this.tasks.splice(i, 1);
                break;
            }
        }
    }

    /**
     * Find the next queued task that's due to run.
     * @returns Starting state of next task.  Null if no task.
     * @private
     */
    nextTask_(): JsState | null {
        const task = this.tasks[0];
        if (!task || task.time > Date.now()) {
            return null;
        }
        // Found a task that's due to run.
        this.tasks.shift();
        if (task.interval >= 0) {
            this.scheduleTask_(task, task.interval);
        }
        const state: any = new JsState(task.node, task.scope);
        if (task.functionRef) {
            // setTimeout/setInterval with a function reference.
            state.doneCallee_ = 2;
            state.funcThis_ = this.globalObject;
            state.func_ = task.functionRef;
            state.doneArgs_ = true;
            state.arguments_ = task.argsArray;
        }
        return state;
    }

    /**
     * Create a call to a getter function.
     * @param func Function to execute.
     * @param left
     *     Name of variable or object/propname tuple.
     * @private
     */
    createGetter_(func: JsObject, left: JsObject | any[]) {
        if (!this._getterStep) {
            throw new Error('Unexpected call to createGetter');
        }
        // Clear the getter flag.
        this._getterStep = false;
        // Normally `this` will be specified as the object component (o.x).
        // Sometimes `this` is explicitly provided (o).
        const funcThis = Array.isArray(left) ? left[0] : left;
        const node = Interpreter.newNode();
        node.type = 'CallExpression';
        const state: any = new JsState(node, this.stateStack[this.stateStack.length - 1].scope);
        state.doneCallee_ = 2;
        state.funcThis_ = funcThis;
        state.func_ = func;
        state.doneArgs_ = true;
        state.arguments_ = [];
        return state;
    }

    /**
     * Create a call to a setter function.
     * @param func Function to execute.
     * @param left Name of variable or object/propname tuple.
     * @param value Value to set.
     */
    private createSetter_(func: JsObject, left: JsObject | any[], value: JsValue) {
        if (!this._setterStep) {
            throw new Error('Unexpected call to createSetter');
        }
        // Clear the setter flag.
        this._setterStep = false;
        // Normally `this` will be specified as the object component (o.x).
        // Sometimes `this` is implicitly the global object (x).
        const funcThis = Array.isArray(left) ? left[0] : this.globalObject;
        const node = Interpreter.newNode();
        node.type = 'CallExpression';
        const state: any = new JsState(node, this.stateStack[this.stateStack.length - 1].scope);
        state.doneCallee_ = 2;
        state.funcThis_ = funcThis;
        state.func_ = func;
        state.doneArgs_ = true;
        state.arguments_ = [value];
        return state;
    }

    /**
     * In non-strict mode `this` must be an object.
     * Must not be called in strict mode.
     * @param value Proposed value for `this`.
     * @returns Final value for `this`.
     */
    private boxThis_(value: JsValue): JsObject {
        if (value === undefined || value === null) {
            // `Undefined` and `null` are changed to the global object.
            return this.globalObject;
        }
        if (!(value instanceof JsObject)) {
            // Primitives must be boxed.
            const box = this.createObjectProto(this.getPrototype(value));
            box.data = value;
            return box;
        }
        return value;
    }

    /**
     * Return the global scope object.
     * @returns Scope object.
     */
    getGlobalScope(): JsScope {
        return this.globalScope;
    }

    /**
     * Return the state stack.
     * @returns State stack.
     */
    getStateStack(): JsState[] {
        return this.stateStack;
    }

    /**
     * Replace the state stack with a new one.
     * @param newStack New state stack.
     */
    setStateStack(newStack: JsState[]) {
        this.stateStack = newStack;
    }

    // ==========================================================================
    // Functions to handle each node type.
    // ==========================================================================

    stepArrayExpression(stack: JsState[], state: any, node: any): Maybe<JsState> {
        const elements = node.elements;
        let n = state.n_ || 0;
        if (!state.array_) {
            state.array_ = this.createArray();
            state.array_.properties.length = elements.length;
        } else {
            this.setProperty(state.array_, n, state.value);
            n++;
        }
        while (n < elements.length) {
            // Skip missing elements - they're not defined, not undefined.
            if (elements[n]) {
                state.n_ = n;
                return new JsState(elements[n], state.scope);
            }
            n++;
        }
        stack.pop();
        stack[stack.length - 1].value = state.array_;
        return undefined;
    }

    stepAssignmentExpression(stack: JsState[], state: JsState, node: any): Maybe<JsState> {
        if (!state.$.doneLeft) {
            state.$.doneLeft = true;
            const nextState: any = new JsState(node.left, state.scope);
            nextState.components = true;
            return nextState;
        }
        if (!state.$.doneRight) {
            if (!state.$.leftReference) {
                state.$.leftReference = state.value;
            }
            if (state.$.doneGetter) {
                state.$.leftValue = state.value;
            }
            if (!state.$.doneGetter && node.operator !== '=') {
                const leftValue = this.getValue(state.$.leftReference);
                state.$.leftValue = leftValue;
                if (this._getterStep) {
                    // Call the getter function.
                    state.$.doneGetter = true;
                    const func = /** @type {!JsObject} */ leftValue;
                    return this.createGetter_(func, state.$.leftReference);
                }
            }
            state.$.doneRight = true;
            // When assigning an unnamed function to a variable, the function's name
            // is set to the variable name.  Record the variable name in case the
            // right side is a functionExpression.
            // E.g. foo = function() {};
            if (node.operator === '=' && node.left.type === 'Identifier') {
                state.$.destinationName = node.left.name;
            }
            return new JsState(node.right, state.scope);
        }
        if (state.$.doneSetter) {
            // Return if setter function.
            // Setter method on property has completed.
            // Ignore its return value, and use the original set value instead.
            stack.pop();
            stack[stack.length - 1].value = state.$.setterValue;
            return undefined;
        }
        let value = state.$.leftValue;
        const rightValue = state.value;
        switch (node.operator) {
            case '=':
                value = rightValue;
                break;
            case '+=':
                value += rightValue;
                break;
            case '-=':
                value -= rightValue;
                break;
            case '*=':
                value *= rightValue;
                break;
            case '/=':
                value /= rightValue;
                break;
            case '%=':
                value %= rightValue;
                break;
            case '<<=':
                value <<= rightValue;
                break;
            case '>>=':
                value >>= rightValue;
                break;
            case '>>>=':
                value >>>= rightValue;
                break;
            case '&=':
                value &= rightValue;
                break;
            case '^=':
                value ^= rightValue;
                break;
            case '|=':
                value |= rightValue;
                break;
            default:
                throw SyntaxError(`Unknown assignment expression: ${node.operator}`);
        }
        const setter = this.setValue(state.$.leftReference, value);
        if (setter) {
            state.$.doneSetter = true;
            state.$.setterValue = value;
            return this.createSetter_(setter, state.$.leftReference, value);
        }
        // Return if no setter function.
        stack.pop();
        stack[stack.length - 1].value = value;
        return undefined;
    }

    stepBinaryExpression(stack: JsState[], state: any, node: any) {
        if (!state.$.doneLeft) {
            state.$.doneLeft = true;
            return new JsState(node.left, state.scope);
        }
        if (!state.$.doneRight) {
            state.$.doneRight = true;
            state.$.leftValue = state.value;
            return new JsState(node.right, state.scope);
        }
        stack.pop();
        const leftValue = state.$.leftValue;
        const rightValue = state.value;
        let value;
        switch (node.operator) {
            case '==':
                // eslint-disable-next-line eqeqeq
                value = leftValue == rightValue;
                break;
            case '!=':
                // eslint-disable-next-line eqeqeq
                value = leftValue != rightValue;
                break;
            case '===':
                value = leftValue === rightValue;
                break;
            case '!==':
                value = leftValue !== rightValue;
                break;
            case '>':
                value = leftValue > rightValue;
                break;
            case '>=':
                value = leftValue >= rightValue;
                break;
            case '<':
                value = leftValue < rightValue;
                break;
            case '<=':
                value = leftValue <= rightValue;
                break;
            case '+':
                value = leftValue + rightValue;
                break;
            case '-':
                value = leftValue - rightValue;
                break;
            case '*':
                value = leftValue * rightValue;
                break;
            case '/':
                value = leftValue / rightValue;
                break;
            case '%':
                value = leftValue % rightValue;
                break;
            case '&':
                value = leftValue & rightValue;
                break;
            case '|':
                value = leftValue | rightValue;
                break;
            case '^':
                value = leftValue ^ rightValue;
                break;
            case '<<':
                value = leftValue << rightValue;
                break;
            case '>>':
                value = leftValue >> rightValue;
                break;
            case '>>>':
                value = leftValue >>> rightValue;
                break;
            case 'in':
                if (!(rightValue instanceof JsObject)) {
                    this.throwException(this.TYPE_ERROR, `'in' expects an object, not '${rightValue}'`);
                }
                value = this.hasProperty(rightValue, leftValue);
                break;
            case 'instanceof':
                if (!this.isa(rightValue, this.FUNCTION)) {
                    this.throwException(this.TYPE_ERROR, `'instanceof' expects an object, not '${rightValue}'`);
                }
                value = leftValue instanceof JsObject ? this.isa(leftValue, rightValue) : false;
                break;
            default:
                throw SyntaxError(`Unknown binary operator: ${node.operator}`);
        }
        stack[stack.length - 1].value = value;
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepBlockStatement(stack: JsState[], state: any, node: any) {
        const n = state.n_ || 0;
        const expression = node.body[n];
        if (expression) {
            state.n_ = n + 1;
            return new JsState(expression, state.scope);
        }
        stack.pop();
        return undefined;
    }

    stepBreakStatement(_stack: JsState[], _state: JsState, node: any) {
        const label = node.label && node.label.name;
        this.unwind(Interpreter.Completion.BREAK, undefined, label);
    }
    // (stack: JsState[], state: JsState, node: any)

    /**
     * Number of evals called by the interpreter.
     */
    private _evalCodeNumber = 0;

    stepCallExpression(stack: JsState[], state: any, node: any) {
        // Handles both CallExpression and NewExpression.
        if (!state.doneCallee_) {
            state.doneCallee_ = 1;
            // Components needed to determine value of `this`.
            const nextState: any = new JsState(node.callee, state.scope);
            nextState.components = true;
            return nextState;
        }
        let func: any;
        if (state.doneCallee_ === 1) {
            // Determine value of the function.
            state.doneCallee_ = 2;
            func = state.value;
            if (Array.isArray(func)) {
                // when evaluating a chain expression, we need to stop evaluation if the object is null or undefined
                if (state.node.callee?.optional && func[0] == null) {
                    state.value = func[0];
                    stack.pop();
                    stack[stack.length - 1].value = state.value;
                    return undefined;
                }
                state.func_ = this.getValue(func);
                if (func[0] === Interpreter.SCOPE_REFERENCE) {
                    // (Globally or locally) named function.  Is it named 'eval'?
                    state.directEval_ = func[1] === 'eval';
                } else {
                    // Method function, `this` is object (ignored if invoked as `new`).
                    state.funcThis_ = func[0];
                }
                func = state.func_;
                if (this._getterStep) {
                    // Call the getter function.
                    state.doneCallee_ = 1;
                    return this.createGetter_(/** @type {!JsObject} */ func, state.value);
                }
            } else {
                // Already evaluated function: (function(){...})();
                state.func_ = func;
            }
            state.arguments_ = [];
            state.n_ = 0;
        }
        func = state.func_;
        if (!state.doneArgs_) {
            if (state.n_ !== 0) {
                state.arguments_.push(state.value);
            }
            if (node.arguments[state.n_]) {
                return new JsState(node.arguments[state.n_++], state.scope);
            }
            // Determine value of `this` in function.
            if (node.type === 'NewExpression') {
                if (!(func instanceof JsObject) || (func as any).illegalConstructor) {
                    // Illegal: new escape();
                    this.throwException(this.TYPE_ERROR, `${this.nodeSummary(node.callee)} is not a constructor`);
                }
                // Constructor, `this` is new object.
                if (func === this.ARRAY) {
                    state.funcThis_ = this.createArray();
                } else {
                    let proto = func.properties.prototype;
                    if (typeof proto !== 'object' || proto === null) {
                        // Non-object prototypes default to `Object.prototype`.
                        proto = this.OBJECT_PROTO;
                    }
                    state.funcThis_ = this.createObjectProto(proto);
                }
                state.isConstructor = true;
            }
            state.doneArgs_ = true;
        }
        if (!state.doneExec_) {
            state.doneExec_ = true;
            if (!(func instanceof JsObject)) {
                this.throwException(this.TYPE_ERROR, `${this.nodeSummary(node.callee)} is not a function`);
            }
            const funcNode = func.node;
            if (funcNode) {
                const scope = this.createScope(funcNode.body, func.parentScope);
                // Build arguments variable.
                const argsList = this.createArray();
                for (let i = 0; i < state.arguments_.length; i++) {
                    this.setProperty(argsList, i, state.arguments_[i]);
                }
                this.setProperty(scope.object, 'arguments', argsList);
                // Add all arguments (may clobber 'arguments' if a param is named such).
                for (let i = 0; i < funcNode.params.length; i++) {
                    const paramName = funcNode.params[i].name;
                    const paramValue = state.arguments_.length > i ? state.arguments_[i] : undefined;
                    this.setProperty(scope.object, paramName, paramValue);
                }
                if (!scope.strict) {
                    state.funcThis_ = this.boxThis_(state.funcThis_);
                }
                this.setProperty(scope.object, 'this', state.funcThis_, Interpreter.READONLY_DESCRIPTOR);
                state.value = undefined; // Default value if no explicit return.
                return new JsState(funcNode.body, scope);
            }
            if (func.eval) {
                if (!this.options?.allowEval) {
                    this.throwException(this.ERROR, 'eval not allowed');
                }
                const code = state.arguments_[0];
                if (typeof code !== 'string') {
                    // JS does not parse String objects:
                    // eval(new String('1 + 1')) -> '1 + 1'
                    state.value = code;
                } else {
                    let ast: any;
                    try {
                        ast = Interpreter._parse(String(code), `eval${this._evalCodeNumber++}`);
                    } catch (e) {
                        // Acorn threw a SyntaxError.  Rethrow as a trappable error.
                        this.throwException(this.SYNTAX_ERROR, `Invalid code: ${e.message}`);
                    }
                    const evalNode: any = Interpreter.newNode();
                    evalNode.type = 'EvalProgram_';
                    evalNode.body = ast.body;
                    Interpreter.stripLocations_(evalNode, node.start, node.end);
                    // Create new scope and update it with definitions in eval().
                    let scope = state.directEval_ ? state.scope : this.globalScope;
                    if (scope.strict) {
                        // Strict mode get its own scope in eval.
                        scope = this.createScope(ast, scope);
                    } else {
                        // Non-strict mode pollutes the current scope.
                        this.populateScope_(ast, scope);
                    }
                    this.value = undefined; // Default value if no code.
                    return new JsState(evalNode, scope);
                }
            } else if (func.nativeFunc) {
                if (!state.scope.strict) {
                    state.funcThis_ = this.boxThis_(state.funcThis_);
                }
                state.value = func.nativeFunc.apply(state.funcThis_, state.arguments_);
            } else if (func.asyncFunc) {
                // asyncFunc are not only async functions ;-(
                // for instance split is async!
                // if (!this.options.allowAsync) {
                //     this.throwException(this.ERROR, 'Async function not allowed');
                // }
                const thisInterpreter = this;
                const callback = function (value: any) {
                    state.value = value;
                    thisInterpreter._paused = false;
                };
                // Force the argument lengths to match, then append the callback.
                const argLength = func.asyncFunc.length - 1;
                const argsWithCallback = state.arguments_.concat(Array.from({ length: argLength })).slice(0, argLength);
                argsWithCallback.push(callback);
                this._paused = true;
                if (!state.scope.strict) {
                    state.funcThis_ = this.boxThis_(state.funcThis_);
                }
                func.asyncFunc.apply(state.funcThis_, argsWithCallback);
                return undefined;
            } else {
                /* A child of a function is a function but is not callable. For example:
                const F = function() {};
                F.prototype = escape;
                const f = new F();
                f();
            */
                this.throwException(this.TYPE_ERROR, `${this.nodeSummary(node.callee)} is not callable`);
            }
        } else {
            // Execution complete.  Put the return value on the stack.
            stack.pop();
            if (state.isConstructor && typeof state.value !== 'object') {
                // Normal case for a constructor is to use the `this` value.
                stack[stack.length - 1].value = state.funcThis_;
            } else {
                // Non-constructors or constructions explicitly returning objects use
                // the return value.
                stack[stack.length - 1].value = state.value;
            }
        }
        return undefined;
    }

    stepConditionalExpression(stack: JsState[], state: any, node: any) {
        // Handles both ConditionalExpression and IfStatement.
        const mode = state.mode_ || 0;
        if (mode === 0) {
            state.mode_ = 1;
            return new JsState(node.test, state.scope);
        }
        if (mode === 1) {
            state.mode_ = 2;
            const value = Boolean(state.value);
            if (value && node.consequent) {
                // Execute `if` block.
                return new JsState(node.consequent, state.scope);
            }
            if (!value && node.alternate) {
                // Execute `else` block.
                return new JsState(node.alternate, state.scope);
            }
            // eval('1;if(false){2}') -> undefined
            this.value = undefined;
        }
        stack.pop();
        if (node.type === 'ConditionalExpression') {
            stack[stack.length - 1].value = state.value;
        }
        return undefined;
    }

    stepContinueStatement(_stack: JsState[], _state: JsState, node: any) {
        const label = node.label && node.label.name;
        this.unwind(Interpreter.Completion.CONTINUE, undefined, label);
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    stepDebuggerStatement(stack: JsState[], _state: JsState, _node: any) {
        // Do nothing.  May be overridden by developers.
        stack.pop();
    }

    // eslint-disable-next-line class-methods-use-this
    stepDoWhileStatement(stack: JsState[], state: any, node: any) {
        // Handles both DoWhileStatement and WhileStatement.
        if (node.type === 'DoWhileStatement' && state.test_ === undefined) {
            // First iteration of do/while executes without checking test.
            state.value = true;
            state.test_ = true;
        }
        if (!state.test_) {
            state.test_ = true;
            return new JsState(node.test, state.scope);
        }
        if (!state.value) {
            // Done, exit loop.
            stack.pop();
        } else if (node.body) {
            // Execute the body.
            state.test_ = false;
            state.isLoop = true;
            return new JsState(node.body, state.scope);
        }
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    stepEmptyStatement(stack: JsState[], _state: JsState, _node: any) {
        stack.pop();
    }

    // eslint-disable-next-line consistent-return
    stepEvalProgram_(stack: JsState[], state: any, node: any) {
        const n = state.n_ || 0;
        const expression = node.body[n];
        if (expression) {
            state.n_ = n + 1;
            return new JsState(expression, state.scope);
        }
        stack.pop();
        stack[stack.length - 1].value = this.value;
        return undefined;
    }

    // eslint-disable-next-line consistent-return
    stepExpressionStatement(stack: JsState[], state: any, node: any) {
        if (!state.done_) {
            this.value = undefined;
            state.done_ = true;
            return new JsState(node.expression, state.scope);
        }
        stack.pop();
        // Save this value to interpreter.value for use as a return value if
        // this code is inside an eval function.
        this.value = state.value;
        return undefined;
    }

    // eslint-disable-next-line consistent-return
    stepForInStatement(stack: JsState[], state: any, node: any) {
        // First, initialize a variable if exists.  Only do so once, ever.
        if (!state.doneInit_) {
            state.doneInit_ = true;
            if (node.left.declarations && node.left.declarations[0].init) {
                if (state.scope.strict) {
                    this.throwException(
                        this.SYNTAX_ERROR,
                        'for-in loop variable declaration may not have an initializer',
                    );
                }
                // Variable initialization: for (let x = 4 in y)
                return new JsState(node.left, state.scope);
            }
        }
        // Second, look up the object.  Only do so once, ever.
        if (!state.doneObject_) {
            state.doneObject_ = true;
            if (!state.variable_) {
                state.variable_ = state.value;
            }
            return new JsState(node.right, state.scope);
        }
        if (!state.isLoop) {
            // First iteration.
            state.isLoop = true;
            state.object_ = state.value;
            state.visited_ = Object.create(null);
        }
        // Third, find the property name for this iteration.
        if (state.name_ === undefined) {
            // eslint-disable-next-line no-labels, no-constant-condition
            gotPropName: while (true) {
                if (state.object_ instanceof JsObject) {
                    if (!state.props_) {
                        state.props_ = Object.getOwnPropertyNames(state.object_.properties);
                    }
                    // eslint-disable-next-line no-constant-condition
                    while (true) {
                        const prop = state.props_.shift();
                        if (prop === undefined) {
                            break; // Reached end of this object's properties.
                        }
                        if (!objectHasOwnProperty(state.object_.properties, prop)) {
                            // eslint-disable-next-line no-continue
                            continue; // Property has been deleted in the loop.
                        }
                        if (state.visited_[prop]) {
                            // eslint-disable-next-line no-continue
                            continue; // Already seen this property on a child.
                        }
                        state.visited_[prop] = true;
                        if (!Object.prototype.propertyIsEnumerable.call(state.object_.properties, prop)) {
                            // eslint-disable-next-line no-continue
                            continue; // Skip non-enumerable property.
                        }
                        state.name_ = prop;
                        // eslint-disable-next-line no-labels
                        break gotPropName;
                    }
                } else if (state.object_ !== null && state.object_ !== undefined) {
                    // Primitive value (other than null or undefined).
                    if (!state.props_) {
                        state.props_ = Object.getOwnPropertyNames(state.object_);
                    }
                    // eslint-disable-next-line no-constant-condition
                    while (true) {
                        const prop = state.props_.shift();
                        if (prop === undefined) {
                            break; // Reached end of this value's properties.
                        }
                        state.visited_[prop] = true;
                        if (!Object.prototype.propertyIsEnumerable.call(state.object_, prop)) {
                            // eslint-disable-next-line no-continue
                            continue; // Skip non-enumerable property.
                        }
                        state.name_ = prop;
                        // eslint-disable-next-line no-labels
                        break gotPropName;
                    }
                }
                state.object_ = this.getPrototype(state.object_);
                state.props_ = null;
                if (state.object_ === null) {
                    // Done, exit loop.
                    stack.pop();
                    return undefined;
                }
            }
        }
        // Fourth, find the variable
        if (!state.doneVariable_) {
            state.doneVariable_ = true;
            const left = node.left;
            if (left.type === 'VariableDeclaration') {
                // Inline variable declaration: for (const x in y)
                state.variable_ = [Interpreter.SCOPE_REFERENCE, left.declarations[0].id.name];
            } else {
                // Arbitrary left side: for (foo().bar in y)
                state.variable_ = null;
                const nextState = new JsState(left, state.scope);
                (nextState as any).components = true;
                return nextState;
            }
        }
        if (!state.variable_) {
            state.variable_ = state.value;
        }
        // Fifth, set the variable.
        if (!state.$.doneSetter) {
            state.$.doneSetter = true;
            const value = state.name_;
            const setter = this.setValue(state.variable_, value);
            if (setter) {
                return this.createSetter_(setter, state.variable_, value);
            }
        }
        // Next step will be step three.
        state.name_ = undefined;
        // Reevaluate the variable since it could be a setter on the global object.
        state.doneVariable_ = false;
        state.$.doneSetter = false;
        // Sixth and finally, execute the body if there was one.
        if (node.body) {
            return new JsState(node.body, state.scope);
        }
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepForStatement(stack: JsState[], state: any, node: any) {
        switch (state.mode_) {
            case 1:
                state.mode_ = 2;
                if (node.test) {
                    return new JsState(node.test, state.scope);
                }
                break;
            case 2:
                state.mode_ = 3;
                if (node.test && !state.value) {
                    // Done, exit loop.
                    stack.pop();
                } else {
                    // Execute the body.
                    state.isLoop = true;
                    return new JsState(node.body, state.scope);
                }
                break;
            case 3:
                state.mode_ = 1;
                if (node.update) {
                    return new JsState(node.update, state.scope);
                }
                break;
            default:
                state.mode_ = 1;
                if (node.init) {
                    return new JsState(node.init, state.scope);
                }
                break;
        }
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    stepFunctionDeclaration(stack: JsState[], _state: JsState, _node: any) {
        // This was found and handled when the scope was populated.
        stack.pop();
    }

    stepFunctionExpression(stack: JsState[], _state: JsState, node: any): Maybe<JsState> {
        stack.pop();
        const state: any = stack[stack.length - 1];
        let parentScope = state.scope;
        if (node.id) {
            // Create a tiny scope to store the function name.
            // E.g. const x = function foo(){};
            parentScope = this.createSpecialScope(parentScope);
        }
        state.value = this.createFunction(node, parentScope, state.$.destinationName);
        if (node.id) {
            // Record the function name, read-only.
            this.setProperty(parentScope.object, node.id.name, state.value, Interpreter.READONLY_DESCRIPTOR);
        }
        return undefined;
    }

    // Quick hack but we may have an issue with the evaluation of this
    stepArrowFunctionExpression: StepFunction = this.stepFunctionExpression;

    stepIdentifier(stack: JsState[], state: any, node: any) {
        stack.pop();
        if (state.components) {
            stack[stack.length - 1].value = [Interpreter.SCOPE_REFERENCE, node.name];
            return undefined;
        }
        const value = this.getValueFromScope(node.name);
        // An identifier could be a getter if it's a property on the global object.
        if (this._getterStep) {
            // Call the getter function.
            const func = /** @type {!JsObject} */ value;
            return this.createGetter_(func, this.globalObject);
        }
        stack[stack.length - 1].value = value;
        return undefined;
    }

    stepIfStatement: StepFunction = this.stepConditionalExpression;

    // eslint-disable-next-line class-methods-use-this
    stepLabeledStatement(stack: JsState[], state: any, node: any) {
        // No need to hit this node again on the way back up the stack.
        stack.pop();
        // Note that a statement might have multiple labels.
        const labels = state.labels || [];
        labels.push(node.label.name);
        const nextState: any = new JsState(node.body, state.scope);
        nextState.labels = labels;
        return nextState;
    }

    stepLiteral(stack: JsState[], _state: JsState, node: any) {
        stack.pop();
        let value = node.value;
        if (value instanceof RegExp) {
            const pseudoRegexp = this.createObjectProto(this.REGEXP_PROTO);
            this.populateRegExp(pseudoRegexp, value);
            value = pseudoRegexp;
        }
        stack[stack.length - 1].value = value;
    }

    n = {
        type: 'TemplateLiteral',
        start: 94,
        end: 120,
        loc: {
            start: { line: 4, column: 23 },
            end: { line: 4, column: 49 },
            source: 'code',
        },
        expressions: [
            {
                type: 'Identifier',
                start: 104,
                end: 105,
                loc: [],
                name: 's',
            },
            {
                type: 'Identifier',
                start: 117,
                end: 118,
                loc: [],
                name: 'n',
            },
        ],
        quasis: [
            {
                type: 'TemplateElement',
                start: 95,
                end: 102,
                loc: [],
                value: [],
                tail: false,
            },
            {
                type: 'TemplateElement',
                start: 106,
                end: 115,
                loc: [],
                value: [],
                tail: false,
            },
            {
                type: 'TemplateElement',
                start: 119,
                end: 119,
                loc: [],
                value: [],
                tail: true,
            },
        ],
    };

    // eslint-disable-next-line class-methods-use-this
    stepTemplateLiteral(stack: JsState[], state: any, node: any) {
        if (!state.$.expressionsDone) {
            let n = state.n_ ?? 0;
            if (!state.$.expressions) {
                state.$.expressions = [];
            } else {
                n++;
                state.$.expressions.push(state.value);
            }

            const expressions = node.expressions;
            if (n < expressions.length) {
                state.n_ = n;
                return new JsState(expressions[n], state.scope);
            }
            state.$.expressionsDone = true;
            state.n_ = 0;
        }
        const quasis = node.quasis.map((q: any) => {
            if (q.type !== 'TemplateElement') throw new Error(`unexpected node type ${q.type}`);
            return q.value.cooked;
        });

        const strings = [];
        const quasisLen = quasis.length;
        const expr = state.$.expressions;
        const exprLen = expr.length;

        for (let i = 0; i < quasisLen; i++) {
            strings.push(quasis[i]);
            if (i < exprLen) {
                strings.push(String(expr[i]));
            }
        }
        state.value = strings.join('');
        stack.pop();
        stack[stack.length - 1].value = state.value;
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepLogicalExpression(stack: JsState[], state: any, node: any) {
        if (node.operator !== '&&' && node.operator !== '||') {
            throw SyntaxError(`Unknown logical operator: ${node.operator}`);
        }
        if (!state.$.doneLeft) {
            state.$.doneLeft = true;
            return new JsState(node.left, state.scope);
        }
        if (!state.$.doneRight) {
            if ((node.operator === '&&' && !state.value) || (node.operator === '||' && state.value)) {
                // Shortcut evaluation.
                stack.pop();
                stack[stack.length - 1].value = state.value;
            } else {
                state.$.doneRight = true;
                return new JsState(node.right, state.scope);
            }
        } else {
            stack.pop();
            stack[stack.length - 1].value = state.value;
        }
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepChainExpression(stack: JsState[], state: JsState, node: any) {
        if (!state.$.expression) {
            state.$.expression = true;
            return new JsState(node.expression, state.scope);
        }
        stack.pop();
        stack[stack.length - 1].value = state.value;
        return undefined;
    }

    stepMemberExpression(stack: JsState[], state: any, node: any) {
        if (!state.doneObject_) {
            state.doneObject_ = true;
            return new JsState(node.object, state.scope);
        }
        let propName;
        if (!node.computed) {
            state.object_ = state.value;
            // obj.foo -- Just access `foo` directly.
            propName = node.property.name;
            if (node.loc.source === 'code') {
                debug(`stepMemberExpression propName=${propName}`);
            }
        } else if (!state.doneProperty_) {
            state.object_ = state.value;
            // obj[foo] -- Compute value of `foo`.
            state.doneProperty_ = true;
            if (node.loc.source === 'code') {
                debug(`stepMemberExpression computed=${node.computed} propName=${node.property.name}`);
            }
            return new JsState(node.property, state.scope);
        } else {
            propName = state.value;
            if (node.loc.source === 'code') {
                debug(`stepMemberExpression from state propName=${propName}`);
            }
        }
        stack.pop();
        if (state.components) {
            stack[stack.length - 1].value = [state.object_, propName];
        } else {
            // when evaluating a chain expression, we need to stop evaluation if the object is null or undefined
            if (node.optional && state.object_ == null) {
                state.value = state.object_;
                stack.pop();
                stack[stack.length - 1].value = state.value;
                return undefined;
            }

            const value = this.getProperty(state.object_, propName);
            if (node.loc.source === 'code') {
                debug(`stepMemberExpression value=${String(value)}`);
            }
            if (this._getterStep) {
                // Call the getter function.
                const func = /** @type {!JsObject} */ value;
                return this.createGetter_(func, state.object_);
            }
            stack[stack.length - 1].value = value;
        }
        return undefined;
    }

    stepNewExpression: StepFunction = this.stepCallExpression;

    stepObjectExpression(stack: JsState[], state: any, node: any) {
        let n = state.n_ || 0;
        let property = node.properties[n];
        if (!state.object_) {
            // First execution.
            state.object_ = this.createObjectProto(this.OBJECT_PROTO);
            state.properties_ = Object.create(null);
        } else {
            // Set the property computed in the previous execution.
            const propName = state.$.destinationName;
            if (!state.properties_[propName]) {
                // Create temp object to collect value, getter, and/or setter.
                state.properties_[propName] = {};
            }
            state.properties_[propName][property.kind] = state.value;
            state.n_ = ++n;
            property = node.properties[n];
        }
        if (property) {
            // Determine property name.
            const key = property.key;
            let propName;
            if (key.type === 'Identifier') {
                propName = key.name;
            } else if (key.type === 'Literal') {
                propName = key.value;
            } else {
                throw SyntaxError(`Unknown object structure: ${key.type}`);
            }
            // When assigning an unnamed function to a property, the function's name
            // is set to the property name.  Record the property name in case the
            // value is a functionExpression.
            // E.g. {foo: function() {}}
            state.$.destinationName = propName;
            return new JsState(property.value, state.scope);
        }
        for (const key of Object.keys(state.properties_)) {
            const kinds = state.properties_[key];
            if ('get' in kinds || 'set' in kinds) {
                // Set a property with a getter or setter.
                const descriptor = {
                    configurable: true,
                    enumerable: true,
                    get: kinds.get,
                    set: kinds.set,
                };
                this.setProperty(state.object_, key, Interpreter.VALUE_IN_DESCRIPTOR, descriptor);
            } else {
                // Set a normal property with a value.
                this.setProperty(state.object_, key, kinds.init);
            }
        }
        stack.pop();
        stack[stack.length - 1].value = state.object_;
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepProgram(_stack: JsState[], state: JsState, node: any) {
        const expression = node.body.shift();
        if (expression) {
            state.done = false;
            return new JsState(expression, state.scope);
        }
        state.done = true;
        // Don't pop the stateStack.
        // Leave the root scope on the tree in case the program is appended to.
        return undefined;
    }

    stepReturnStatement(_stack: JsState[], state: any, node: any) {
        if (node.argument && !state.done_) {
            state.done_ = true;
            return new JsState(node.argument, state.scope);
        }
        this.unwind(Interpreter.Completion.RETURN, state.value, undefined);
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepSequenceExpression(stack: JsState[], state: any, node: any) {
        const n = state.n_ || 0;
        const expression = node.expressions[n];
        if (expression) {
            state.n_ = n + 1;
            return new JsState(expression, state.scope);
        }
        stack.pop();
        stack[stack.length - 1].value = state.value;
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    stepSwitchStatement(stack: JsState[], state: any, node: any) {
        if (!state.test_) {
            state.test_ = 1;
            return new JsState(node.discriminant, state.scope);
        }
        if (state.test_ === 1) {
            state.test_ = 2;
            // Preserve switch value between case tests.
            state.switchValue_ = state.value;
            state.defaultCase_ = -1;
        }

        // eslint-disable-next-line no-constant-condition
        while (true) {
            const index = state.index_ || 0;
            const switchCase = node.cases[index];
            if (!state.matched_ && switchCase && !switchCase.test) {
                // Test on the default case is null.
                // Bypass (but store) the default case, and get back to it later.
                state.defaultCase_ = index;
                state.index_ = index + 1;
                // eslint-disable-next-line no-continue
                continue;
            }
            if (!switchCase && !state.matched_ && state.defaultCase_ !== -1) {
                // Ran through all cases, no match.  Jump to the default.
                state.matched_ = true;
                state.index_ = state.defaultCase_;
                // eslint-disable-next-line no-continue
                continue;
            }
            if (switchCase) {
                if (!state.matched_ && !state.tested_ && switchCase.test) {
                    state.tested_ = true;
                    return new JsState(switchCase.test, state.scope);
                }
                if (state.matched_ || state.value === state.switchValue_) {
                    state.matched_ = true;
                    const n = state.n_ || 0;
                    if (switchCase.consequent[n]) {
                        state.isSwitch = true;
                        state.n_ = n + 1;
                        return new JsState(switchCase.consequent[n], state.scope);
                    }
                }
                // Move on to next case.
                state.tested_ = false;
                state.n_ = 0;
                state.index_ = index + 1;
            } else {
                stack.pop();
                return undefined;
            }
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    stepThisExpression(stack: JsState[], _state: any, _node: any) {
        stack.pop();
        stack[stack.length - 1].value = this.getValueFromScope('this');
    }

    stepThrowStatement(_stack: JsState[], state: any, node: any) {
        if (!state.done_) {
            state.done_ = true;
            return new JsState(node.argument, state.scope);
        }
        this.throwException(state.value);
        return undefined;
    }

    stepTryStatement(stack: JsState[], state: any, node: any) {
        // This step also handles all CatchClause nodes, since these nodes can
        // only appear inside the `handler` property of a TryStatement node.
        if (!state.doneBlock_) {
            state.doneBlock_ = true;
            return new JsState(node.block, state.scope);
        }
        if (state.cv && state.cv.type === Interpreter.Completion.THROW && !state.doneHandler_ && node.handler) {
            state.doneHandler_ = true;
            // Create an new scope and add the error variable.
            const scope = this.createSpecialScope(state.scope);
            this.setProperty(scope.object, node.handler.param.name, state.cv.value);
            state.cv = undefined; // This error has been handled, don't rethrow.
            // Execute catch clause.
            return new JsState(node.handler.body, scope);
        }
        if (!state.doneFinalizer_ && node.finalizer) {
            state.doneFinalizer_ = true;
            return new JsState(node.finalizer, state.scope);
        }
        stack.pop();
        if (state.cv) {
            // There was no catch handler, or the catch/finally threw an error.
            // Throw the error up to a higher try.
            this.unwind(state.cv.type, state.cv.value, state.cv.label);
        }
        return undefined;
    }

    stepUnaryExpression(stack: JsState[], state: any, node: any) {
        if (!state.done_) {
            state.done_ = true;
            const nextState: any = new JsState(node.argument, state.scope);
            nextState.components = node.operator === 'delete';
            return nextState;
        }
        stack.pop();
        let value = state.value;
        switch (node.operator) {
            case '-':
                value = -value;
                break;
            case '+':
                value = +value;
                break;
            case '!':
                value = !value;
                break;
            case '~':
                value = ~value;
                break;
            case 'delete':
                {
                    let result = true;
                    // If value is not an array, then it is a primitive, or some other value.
                    // If so, skip the delete and return true.
                    if (Array.isArray(value)) {
                        let obj = value[0];
                        if (obj === Interpreter.SCOPE_REFERENCE) {
                            // `delete foo;` is the same as `delete window.foo;`.
                            obj = state.scope;
                        }
                        const name = String(value[1]);
                        try {
                            delete obj.properties[name];
                        } catch (_e) {
                            if (state.scope.strict) {
                                this.throwException(this.TYPE_ERROR, `Cannot delete property '${name}' of '${obj}'`);
                            } else {
                                result = false;
                            }
                        }
                    }
                    value = result;
                }
                break;
            case 'typeof':
                value = value && value.class === 'Function' ? 'function' : typeof value;
                break;
            case 'void':
                value = undefined;
                break;
            default:
                throw SyntaxError(`Unknown unary operator: ${node.operator}`);
        }
        stack[stack.length - 1].value = value;
        return undefined;
    }

    stepUpdateExpression(stack: JsState[], state: any, node: any) {
        if (!state.$.doneLeft) {
            state.$.doneLeft = true;
            const nextState: any = new JsState(node.argument, state.scope);
            nextState.components = true;
            return nextState;
        }
        if (!state.$.leftSide) {
            state.$.leftSide = state.value;
        }
        if (state.$.doneGetter) {
            state.$.leftValue = state.value;
        }
        if (!state.$.doneGetter) {
            const lvalue = this.getValue(state.$.leftSide);
            state.$.leftValue = lvalue;
            if (this._getterStep) {
                // Call the getter function.
                state.$.doneGetter = true;
                const func = lvalue;
                return this.createGetter_(func, state.$.leftSide);
            }
        }
        if (state.$.doneSetter) {
            // Return if setter function.
            // Setter method on property has completed.
            // Ignore its return value, and use the original set value instead.
            stack.pop();
            stack[stack.length - 1].value = state.$.setterValue;
            return undefined;
        }
        const leftValue = Number(state.$.leftValue);
        let changeValue;
        if (node.operator === '++') {
            changeValue = leftValue + 1;
        } else if (node.operator === '--') {
            changeValue = leftValue - 1;
        } else {
            throw SyntaxError(`Unknown update expression: ${node.operator}`);
        }
        const returnValue = node.prefix ? changeValue : leftValue;
        const setter = this.setValue(state.$.leftSide, changeValue);
        if (setter) {
            state.$.doneSetter = true;
            state.$.setterValue = returnValue;
            return this.createSetter_(setter, state.$.leftSide, changeValue);
        }
        // Return if no setter function.
        stack.pop();
        stack[stack.length - 1].value = returnValue;
        return undefined;
    }

    stepVariableDeclaration(stack: JsState[], state: any, node: any) {
        // This step also handles all VariableDeclarator nodes, since these nodes can
        // only appear inside the `declarations` array of a VariableDeclaration node.
        const declarations = node.declarations;
        let n = state.n_ || 0;
        let declarationNode = declarations[n];
        if (state.init_ && declarationNode) {
            // This setValue call never needs to deal with calling a setter function.
            // Note that this is setting the init value, not defining the variable.
            // Variable definition is done when scope is populated.
            this.setValueToScope(declarationNode.id.name, state.value, node.kind === 'const');
            state.init_ = false;
            declarationNode = declarations[++n];
        }
        while (declarationNode) {
            // Skip any declarations that are not initialized.  They have already
            // been defined as undefined in populateScope_.
            if (declarationNode.init) {
                state.n_ = n;
                state.init_ = true;
                // When assigning an unnamed function to a variable, the function's name
                // is set to the variable name.  Record the variable name in case the
                // right side is a functionExpression.
                // E.g. const foo = function() {};
                state.$.destinationName = declarationNode.id.name;
                return new JsState(declarationNode.init, state.scope);
            }
            declarationNode = declarations[++n];
        }
        stack.pop();
        return undefined;
    }

    stepWithStatement(stack: JsState[], state: any, node: any) {
        if (!state.doneObject_) {
            state.doneObject_ = true;
            return new JsState(node.object, state.scope);
        }
        stack.pop();
        const scope = this.createSpecialScope(state.scope, state.value);
        return new JsState(node.body, scope);
    }

    stepWhileStatement: StepFunction = this.stepDoWhileStatement;
}

nativeGlobal.Interpreter = Interpreter;
