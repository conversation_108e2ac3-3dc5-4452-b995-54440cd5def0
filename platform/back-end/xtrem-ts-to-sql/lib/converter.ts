import {
    AnyValue,
    AsyncResponse,
    DataInputError,
    removeCodeCoverageInstrumentation,
    SystemError,
} from '@sage/xtrem-shared';
import * as estree from 'estree';
import { getLogger } from './logger';
import { <PERSON> } from './walker';

import { ConversionError } from './conversion-error';
import { ArrayConverter } from './converter-lib/array-converter';
import {
    DateConverter,
    OracleDateConverter,
    PostgresDateConverter,
    SqlServerDateConverter,
} from './converter-lib/date';
import { RegExpConverter } from './converter-lib/regexp-converter';
import { StringConverter } from './converter-lib/string-converter';
import { BuiltInFunctionEntry, ConvertibleFunctionEntry, registry } from './registry';
import { Resolver } from './resolver';
import { TypeConverter } from './type-converter';
import {
    BasicConversionResult,
    ConversionResultType,
    ConverterFactory,
    ConverterOptions,
    ConverterProperty,
    Dialect,
    GenericConversionResult,
    getColumnName,
} from './types';

const parse = require('espree').parse;

export function quote(s: string): string {
    return `'${s.replace(/'/g, "''")}'`;
}

/**
 * Convert numeric value `val` to a SQL string.
 * Throws an error if `val` is not a valid number.
 */
export function sqlNumber(slot: BasicConversionResult, val: AnyValue): string {
    // TODO: we should throw if val is not a number, instead of calling parseFloat
    const number = typeof val === 'number' ? val : parseFloat(String(val));
    // todo XT-859: we don't have the context to localize the error.
    if (!Number.isFinite(number))
        throw new DataInputError(`${slot.factory?.name}.${slot.property?.name}: invalid numeric value: '${val}'`);
    return number.toString();
}

export interface CastOptions<PropertyT extends ConverterProperty = ConverterProperty> {
    type: ConversionResultType;
    property?: PropertyT;
}

interface IfSqlResult {
    statement: estree.IfStatement;
    test: string;
    consequent: BasicConversionResult;
}

export class Converter<
    ContextT = unknown,
    FactoryT extends ConverterFactory = ConverterFactory,
    PropertyT extends ConverterProperty = ConverterProperty,
> extends Walker<ContextT, FactoryT, PropertyT> {
    readonly typeConverter: TypeConverter;

    constructor(
        context: ContextT,
        rootFactory: FactoryT,
        resolver: Resolver<ContextT, FactoryT, PropertyT>,
        options: ConverterOptions,
    ) {
        super(context, rootFactory, resolver, options);
        this.typeConverter = new TypeConverter(this.dialect);
    }

    get dialect(): Dialect {
        return this.options.dialect;
    }

    get dateConverter(): DateConverter {
        switch (this.dialect) {
            case 'oracle':
                return new OracleDateConverter(this);
            case 'postgres':
                return new PostgresDateConverter(this);
            case 'sqlServer':
                return new SqlServerDateConverter(this);
            default:
                throw new ConversionError(undefined, `unsupported dialect: ${this.dialect}`);
        }
    }

    get regexConverter(): RegExpConverter {
        return new RegExpConverter(this);
    }

    get stringConverter(): StringConverter {
        return new StringConverter(this);
    }

    cast(result: BasicConversionResult, type: ConversionResultType): BasicConversionResult {
        if (result.type === type || type === 'unknown' || result.sql.toUpperCase() === 'NULL') return result;

        if (type === 'date' && this.dialect === 'oracle') {
            return this.typeConverter.castOracleDate(result, type);
        }

        const sqlType = this.typeConverter.sqlType(type);
        const sql = this.dialect === 'postgres' ? `${result.sql}::${sqlType}` : `CAST(${result.sql} AS ${sqlType})`;

        return { ...result, type, sql };
    }

    static booleanResult(sql: string): BasicConversionResult {
        return { type: 'boolean', sql };
    }

    static stringResult(sql: string): BasicConversionResult {
        return { type: 'string', sql };
    }

    // called by resolvers (investigate if we can avoid this)
    convertDatePropertyResult(result: GenericConversionResult, propertyName: string): BasicConversionResult {
        const propertyEntry = registry.getPropertyEntry('date', propertyName);

        if (!propertyEntry) throw new ConversionError(undefined, `unknown property ${propertyName}`);

        return propertyEntry.convertExpression(this, result, propertyName);
    }

    private convertMemberExpression(
        expression: estree.MemberExpression,
        optionalMember = false,
    ): BasicConversionResult {
        const objectResult = this.convertExpression(
            expression.object,
            true,
        ) as BasicConversionResult as GenericConversionResult;
        if (optionalMember) {
            if (objectResult.property && !objectResult.property.isNullable && !objectResult.isNullable) {
                throw new ConversionError(
                    expression.property,
                    `The member ${objectResult.property.name} is not nullable. Please consider using a statement like 'this.${objectResult.property.name}.foo' instead of 'this.${objectResult.property.name}?.foo'`,
                );
            }
            if (objectResult.join) objectResult.join.isNullable = true;
            objectResult.isNullable = true;
        }

        if (expression.property.type !== 'Identifier') {
            // expression.expression.type should always be an Identifier
            /* istanbul ignore next */
            throw new ConversionError(expression.property, 'property is not a literal');
        }

        const propertyName = expression.property.name;
        const propertyType = objectResult.type;
        const propertyEntry = registry.getPropertyEntry(propertyType, propertyName);

        if (propertyEntry) return propertyEntry.convertExpression(this, objectResult, propertyName);

        return this.walk(objectResult, propertyName);
    }

    private convertChainExpression(expression: estree.ChainExpression): BasicConversionResult {
        if (expression.expression.type !== 'MemberExpression') {
            // expression.expression.type should always be a MemberExpression
            /* istanbul ignore next */
            throw new ConversionError(expression.expression, 'kind of chain expression not supporterd');
        }
        return this.convertMemberExpression(expression.expression, true);
    }

    // eslint-disable-next-line class-methods-use-this
    checkResultType(
        expression: estree.BaseExpression,
        result: BasicConversionResult,
        expectedType: ConversionResultType,
    ): void {
        if (result.type !== 'unknown' && result.type !== expectedType)
            throw new ConversionError(expression, `Invalid type: expected ${expectedType}, got: ${result.type}`);
    }

    // eslint-disable-next-line class-methods-use-this
    protected getCommonType(expression: estree.BaseNode, results: BasicConversionResult[]): ConversionResultType {
        const commonType = results.reduce((prevType, result) => {
            const currentType = result.type;
            if (prevType === currentType) return currentType;
            if (prevType === 'unknown' || prevType === 'json') return currentType;
            if (currentType === 'unknown') return prevType;

            const pickCommonType = (
                fromTypes: ConversionResultType[],
                toType: ConversionResultType,
            ): ConversionResultType | null => {
                if (fromTypes.includes(prevType) && currentType === toType) return toType;
                if (fromTypes.includes(currentType) && prevType === toType) return toType;
                return null;
            };

            const type =
                pickCommonType(['integer', 'short'], 'decimal') ??
                pickCommonType(['short'], 'integer') ??
                pickCommonType(['enum'], 'string') ??
                pickCommonType(['reference'], 'integer');

            if (!type) {
                if (prevType === 'string' || currentType === 'string') return 'string';

                throw new ConversionError(expression, `incompatible types: ${prevType} and ${currentType}`);
            }

            return type;
        }, 'unknown' as ConversionResultType);
        if (commonType == null) throw new ConversionError(expression, 'no common type');
        return commonType;
    }

    private convertToCommonType(
        expression: estree.BaseNode,
        result1: BasicConversionResult,
        result2: BasicConversionResult,
    ): [ConversionResultType, BasicConversionResult, BasicConversionResult] {
        const type = this.getCommonType(expression, [result1, result2]);
        return [type, this.cast(result1, type), this.cast(result2, type)];
    }

    private convertConditionalExpression(expression: estree.ConditionalExpression): BasicConversionResult {
        const test = this.convertExpression(expression.test);
        this.checkResultType(expression.test, test, 'boolean');
        const consequent = this.convertExpression(expression.consequent);
        const alternate = this.convertExpression(expression.alternate);
        const [type, thenResult, elseResult] = this.convertToCommonType(expression, consequent, alternate);
        return { type, sql: `(CASE WHEN ${test.sql} THEN ${thenResult.sql} ELSE ${elseResult.sql} END)` };
    }

    private convertNullEqualityExpression(expression: estree.BinaryExpression): BasicConversionResult {
        // rhs must be the null literal
        if (expression.right.type !== 'Literal' || expression.right.value !== null) {
            throw new ConversionError(expression.right, '== and !=  operations can only be used with null');
        }
        const left = this.convertExpression(expression.left);
        const sqlOp = expression.operator === '==' ? 'IS' : 'IS NOT';
        return Converter.booleanResult(`(${left.sql} ${sqlOp} NULL)`);
    }

    protected convertBinaryExpression(expression: estree.BinaryExpression): BasicConversionResult<FactoryT, PropertyT> {
        const op = expression.operator;
        // Double equals are reserved for tests against null.
        // Handle them separately
        if (op === '==' || op === '!=')
            return this.convertNullEqualityExpression(expression) as BasicConversionResult<FactoryT, PropertyT>;

        const left = this.convertExpression(expression.left);
        const right = this.convertExpression(expression.right);

        switch (op) {
            case '+': {
                const leftType = left.type;
                const rightType = right.type;
                if (leftType === 'string' || rightType === 'string') {
                    return Converter.stringResult(this.concat([left.sql, right.sql])) as BasicConversionResult<
                        FactoryT,
                        PropertyT
                    >;
                }
                const [type, leftResult, rightResult] = this.convertToCommonType(expression, left, right);
                return { type, sql: `(${leftResult.sql} ${op} ${rightResult.sql})` };
            }
            case '-':
            case '*':
            case '/': {
                const [type, leftResult, rightResult] = this.convertToCommonType(expression, left, right);
                if (!this.typeConverter.isNumericType(type))
                    throw new ConversionError(expression, `invalid type for operator ${op}: ${type}`);
                return { type, sql: `(${leftResult.sql} ${op} ${rightResult.sql})` };
            }
            case '<':
            case '<=':
            case '>':
            case '>=': {
                const [, leftResult, rightResult] = this.convertToCommonType(expression, left, right);
                return Converter.booleanResult(`(${leftResult.sql} ${op} ${rightResult.sql})`) as BasicConversionResult<
                    FactoryT,
                    PropertyT
                >;
            }
            case '===':
            case '!==': {
                if (expression.right.type === 'Literal' && expression.right.value === null) {
                    throw new ConversionError(
                        expression.right,
                        '=== and !==  operators cannot be used with null (use == or != instead)',
                    );
                }
                const sqlOp = op === '===' ? '=' : '!=';
                const andNotNull = op === '!==' && left.isNullable ? ` OR (${left.sql} IS NULL)` : '';
                const [, leftResult, rightResult] = this.convertToCommonType(expression, left, right);

                return Converter.booleanResult(
                    `(${leftResult.sql} ${sqlOp} ${rightResult.sql})${andNotNull}`,
                ) as BasicConversionResult<FactoryT, PropertyT>;
            }
            default:
                throw new ConversionError(expression, `cannot convert operator: ${op}`);
        }
    }

    private convertLogicalExpression(expression: estree.LogicalExpression): BasicConversionResult {
        const left = this.convertExpression(expression.left);
        const right = this.convertExpression(expression.right);

        if (expression.operator === '&&' || expression.operator === '||') {
            this.checkResultType(expression.left, left, 'boolean');
            this.checkResultType(expression.right, right, 'boolean');
        }
        // parenthesize OR because this result may get AND-ed with other results
        switch (expression.operator) {
            case '&&':
                return Converter.booleanResult(`${left.sql} AND ${right.sql}`);
            case '||':
                return Converter.booleanResult(`(${left.sql} OR ${right.sql})`);
            case '??':
                return {
                    type: this.getCommonType(expression, [left, right]),
                    sql: `(CASE WHEN ${left.sql} IS NULL THEN ${right.sql} ELSE ${left.sql} END)`,
                    // TODO: enhance to get common factory ancestor
                    factory: left.factory ?? right.factory,
                };

            default:
                // situation should not arrive, only known cases are &&, || and ??
                /* istanbul ignore next */
                throw new ConversionError(expression, `cannot convert operator: ${expression.operator}`);
        }
    }

    private convertUnaryExpression(expression: estree.UnaryExpression): BasicConversionResult {
        const arg = this.convertExpression(expression.argument);
        const op = expression.operator;
        switch (op) {
            case '!':
                return this.convertNotArgument(arg);
            case '-':
                // pass only arg.sql to force a cast
                return { type: arg.type, sql: `(- ${this.cast({ sql: arg.sql, type: 'unknown' }, arg.type).sql})` };
            default:
                throw new ConversionError(expression, `Unsupported unary operator: ${op}`);
        }
    }

    private convertNotArgument(arg: BasicConversionResult): BasicConversionResult {
        let sql = '';
        switch (arg.type) {
            case 'integer':
            case 'short':
                // the minifier (aka. binary build) converts true/false to !0/!1
                sql = this.resolver.resolveLiteral(arg.sql === '0');
                break;
            case 'reference':
                sql = `(${arg.sql} IS NULL)`;
                break;
            default:
                sql = `(NOT ${arg.sql})`;
                break;
        }

        return Converter.booleanResult(sql);
    }

    // eslint-disable-next-line class-methods-use-this
    protected concat(strings: string[]): string {
        return `CONCAT(${strings.join(',')})`;
    }

    private convertTemplateLiteral(expression: estree.TemplateLiteral): BasicConversionResult {
        const strings = [] as string[];
        expression.expressions.forEach((_exp, i) => {
            strings.push(this.convertExpression(expression.quasis[i]).sql);
            strings.push(this.convertExpression(expression.expressions[i]).sql);
        });
        strings.push(this.convertExpression(expression.quasis[expression.quasis.length - 1]).sql);
        return Converter.stringResult(this.concat(strings));
    }

    // eslint-disable-next-line class-methods-use-this
    convertTemplateElementString(str: string): string {
        return quote(str);
    }

    convertTemplateElement(expression: estree.TemplateElement): BasicConversionResult {
        return Converter.stringResult(this.convertTemplateElementString(expression.value.cooked || ''));
    }

    convertLiteral(value: AnyValue | RegExp | bigint): BasicConversionResult {
        /* istanbul ignore next */
        if (typeof value === 'bigint') throw new SystemError(`cannot convert bigint literal: ${value}`);
        return {
            sql: this.resolver.resolveLiteral(value),
            type:
                typeof value === 'boolean'
                    ? 'boolean'
                    : typeof value === 'number'
                      ? 'integer'
                      : typeof value === 'string'
                        ? 'string'
                        : 'unknown',
        };
    }

    /**
     * Converts an item of an object (see convertObject) a:'aaa' -> "a = 'aaa'" / c:d -> "c = t0.d"
     */
    private convertPropertyAssignments(expression: estree.Property): BasicConversionResult {
        if (expression.key.type !== 'Identifier') {
            // it is imposed that the expression.key.type should be "Identifier"
            /* istanbul ignore next */
            throw new Error(`Invalid property type ${expression.key.type}`);
        }

        return {
            sql: `${getColumnName(expression.key.name)} = ${this.convertExpression(expression.value).sql}`,
            type: 'void',
        };
    }

    /**
     * Converts an object {a:'aaa', b:'bbb', c:d} into sth like "a='aaa', b='bbb', c=t0.d"
     * Can be used for update ... set ... operations
     */
    private convertObjectAssignments(expression: estree.ObjectExpression): BasicConversionResult {
        const sqls = [] as string[];
        expression.properties.forEach(prop => {
            // it is imposed that the prop.type should be "Property"
            if (prop.type !== 'Property') {
                /* istanbul ignore next */
                throw new Error(`Invalid object member ${prop.type}`);
            }
            const propResult = this.convertPropertyAssignments(prop);
            sqls.push(propResult.sql);
        });
        return { type: 'void', sql: sqls.join(',') };
    }

    convertNaryCallExpression(expression: estree.BaseCallExpression, sqlFunctionName: string): BasicConversionResult {
        if (expression.arguments.length === 0) throw new ConversionError(expression, 'no arguments');
        const args = expression.arguments.map(arg => this.convertExpression(arg));
        const type = this.getCommonType(expression, args);
        const sql = `${sqlFunctionName}(${args.map(arg => arg.sql).join(',')})`;
        return { type, sql };
    }

    // getConfigurationValue is a placeholder for classes that derive from Converted
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    getConfigurationValue(_name: string): string {
        return '';
    }

    private convertIdentifier(identifier: estree.Identifier): BasicConversionResult {
        return this.variableScope.resolve(identifier.name);
    }

    /**
     * Convert a list of arguments to a list of conversion results,
     * after checking their type.
     */
    convertArgs(args: estree.BaseExpression[], types: ConversionResultType[], name: string): BasicConversionResult[] {
        if (args.length !== types.length)
            throw new ConversionError(args[0], `${name}: expected ${types.length} argument(s), got ${args.length}`);
        return args.map((arg, i) => {
            const argResult = this.convertExpression(arg);
            if (argResult.type !== types[i]) {
                throw new ConversionError(
                    arg,
                    `${name}: expected argument ${i + 1} of type ${types[i]}, got type ${argResult.type}`,
                );
            }
            return argResult;
        });
    }

    protected convertMethodCall(
        expression: estree.CallExpression,
        result: BasicConversionResult,
        methodName: string,
        args: (estree.Expression | estree.SpreadElement)[],
    ): BasicConversionResult {
        const type = result.extraType ?? result.type;
        const methodEntry = registry.getMethodEntry(type, methodName);
        if (!methodEntry) throw new ConversionError(expression, `cannot find method ${methodName} on type ${type}`);

        return methodEntry.convertExpression(this, result as GenericConversionResult, args, methodName);
    }

    private getFullMemberName(member: estree.MemberExpression): string {
        if (member.property.type !== 'Identifier') {
            throw new ConversionError(member.property, 'cannot convert: expected an identifier');
        }
        if (member.object.type === 'Identifier') {
            return `${member.object.name}.${member.property.name}`;
        }
        if (member.object.type === 'MemberExpression') {
            const parentName = this.getFullMemberName(member.object);
            return `${parentName}.${member.property.name}`;
        }
        return member.property.name;
    }

    protected convertRegistryFunctionCall(
        callExpression: estree.CallExpression,
        functionEntry: ConvertibleFunctionEntry,
        args: (estree.Expression | estree.SpreadElement)[],
    ): BasicConversionResult {
        if (!functionEntry.parsedFunction) functionEntry.parsedFunction = this.parseFunction(functionEntry.fn);

        // convert the arguments before pushing the scope
        const argResults = args.map(arg => this.convertExpression(arg));

        this.pushVariableScope();
        try {
            functionEntry.parameterNames.forEach((parameterName, i) => {
                const argResult = argResults[i];
                this.variableScope.set(parameterName, argResult);
            });
            return this.convertFunctionBodyExpression(functionEntry.parsedFunction);
        } catch (error) {
            throw new ConversionError(callExpression, `Cannot convert ${functionEntry.fullName}`, error);
        } finally {
            this.popVariableScope();
        }
    }

    protected convertMemberCallExpression(
        expression: estree.CallExpression,
        member: estree.MemberExpression,
    ): BasicConversionResult {
        if (member.property.type !== 'Identifier') throw new ConversionError(member.property, 'expected an identifier');

        let fullMemberName = this.getFullMemberName(member);

        // Handle foo_1. prefix generated by TS transpiler
        const isGlobalName = /^\w+_\d+\./.test(fullMemberName);
        if (isGlobalName) fullMemberName = fullMemberName.split('.').slice(1).join('.');

        const registryEntry = registry.getFunctionEntry(fullMemberName);
        if (registryEntry) {
            if ((registryEntry as ConvertibleFunctionEntry).fn)
                return this.convertRegistryFunctionCall(
                    expression,
                    registryEntry as ConvertibleFunctionEntry,
                    expression.arguments,
                );
            return (registryEntry as BuiltInFunctionEntry).convertExpression(
                this,
                expression,
                expression.arguments,
                fullMemberName,
            );
        }
        if (isGlobalName)
            throw new ConversionError(
                member,
                `${fullMemberName}: Unsupported ${fullMemberName.split('.')[0]} function`,
            );

        if (member.object.type === 'Identifier') {
            const variableResult = this.variableScope.peekVariable(member.object.name);
            if (variableResult) {
                return this.convertMethodCall(expression, variableResult, member.property.name, expression.arguments);
            }
        }

        if (
            member.object.type === 'Literal' &&
            member.object.value instanceof RegExp &&
            member.property.type === 'Identifier'
        ) {
            return this.regexConverter.convertMethodCall(
                expression,
                member.object.value,
                member.property.name,
                expression.arguments,
            );
        }

        if (member.object.type === 'ArrayExpression') {
            if (member.property.type !== 'Identifier')
                throw new ConversionError(member.property, 'cannot convert: expected an identifier');

            return new ArrayConverter(this).convertLiteralMethodCall(
                member.object,
                member.property.name,
                expression.arguments,
            );
        }

        // We have exhausted the build-in possibilities for member.object - so we convert it
        if (member.object.type === 'Identifier')
            // We tested scope variable earlier so this is an error
            throw new ConversionError(
                member,
                `${fullMemberName}: Unsupported ${fullMemberName.split('.')[0]} function`,
            );

        const objectResult = this.convertExpression(member.object, true);

        return this.convertMethodCall(expression, objectResult, member.property.name, expression.arguments);
    }

    private convertCallExpression(expression: estree.CallExpression): BasicConversionResult {
        if (expression.callee.type === 'MemberExpression') {
            return this.convertMemberCallExpression(expression, expression.callee);
        }

        // Check for `notNull(arg)`, transpiled to `(0, index_1.notNull)(arg)`
        if (
            expression.callee.type === 'SequenceExpression' &&
            expression.callee.expressions.length === 2 &&
            expression.callee.expressions[0].type === 'Literal' &&
            expression.callee.expressions[0].value === 0 &&
            expression.callee.expressions[1].type === 'MemberExpression' &&
            expression.callee.expressions[1].property.type === 'Identifier' &&
            expression.callee.expressions[1].property.name === 'notNull' &&
            expression.arguments.length === 1
        ) {
            return this.convertExpression(expression.arguments[0]);
        }

        throw new ConversionError(expression, 'Unsupported call');
    }

    private convertTSNonNullExpression(
        expression: any, // tstree.TSNonNullExpression,
    ): BasicConversionResult {
        return this.convertExpression(expression.expression);
    }

    private convertTSAsExpression(
        expression: any, // tstree.TSAsExpression,
    ): BasicConversionResult {
        return this.convertExpression(expression.expression);
    }

    private convertAwaitExpression(expression: estree.AwaitExpression): BasicConversionResult {
        return this.convertExpression(expression.argument);
    }

    private _convertExpression(expression: estree.BaseExpression, isMemberOf = false): BasicConversionResult {
        switch (expression.type) {
            // Expression nodes for which we get a result object
            case 'TSNonNullExpression':
                return this.convertTSNonNullExpression(expression as any); // tstree.TSNonNullExpression);
            case 'TSAsExpression':
                return this.convertTSAsExpression(expression as any); // tstree.TSAsExpression);
            case 'MemberExpression':
                return this.convertMemberExpression(expression as estree.MemberExpression);
            case 'ChainExpression':
                return this.convertChainExpression(expression as estree.ChainExpression);
            case 'ThisExpression':
                return this.convertThisExpression(isMemberOf);
            case 'AwaitExpression':
                return this.convertAwaitExpression(expression as estree.AwaitExpression);
            case 'Literal':
                return this.convertLiteral((expression as estree.Literal).value);
            case 'Identifier':
                return this.convertIdentifier(expression as estree.Identifier);

            // Expression nodes for which we just get a SQL string.
            case 'TemplateLiteral':
                return this.convertTemplateLiteral(expression as estree.TemplateLiteral);
            case 'TemplateElement':
                return this.convertTemplateElement(expression as estree.TemplateElement);
            case 'BinaryExpression':
                return this.convertBinaryExpression(expression as estree.BinaryExpression);
            case 'LogicalExpression':
                return this.convertLogicalExpression(expression as estree.LogicalExpression);
            case 'UnaryExpression':
                return this.convertUnaryExpression(expression as estree.UnaryExpression);
            case 'CallExpression':
                return this.convertCallExpression(expression as estree.CallExpression);
            case 'ObjectExpression':
                return this.convertObjectAssignments(expression as estree.ObjectExpression);
            case 'ConditionalExpression':
                return this.convertConditionalExpression(expression as estree.ConditionalExpression);

            default:
                throw new ConversionError(expression, `invalid expression type: ${expression.type}`);
        }
    }

    convertExpression(
        expression: estree.BaseExpression,
        isMemberOf = false,
    ): BasicConversionResult<FactoryT, PropertyT> {
        try {
            return this._convertExpression(expression, isMemberOf) as BasicConversionResult<FactoryT, PropertyT>;
        } catch (error) {
            if (error instanceof ConversionError && !error.node) error.node = expression;
            throw error;
        }
    }

    convertVariableDeclaration(statement: estree.VariableDeclaration): void {
        if (statement.kind !== 'const') throw new ConversionError(statement, 'only const declarations allowed');
        statement.declarations.forEach(declaration => {
            if (declaration.type !== 'VariableDeclarator')
                throw new ConversionError(declaration, `expected VariableDeclarator, got ${declaration.type}`);
            if (declaration.id.type !== 'Identifier')
                throw new ConversionError(declaration.id, `expected Identifier, got ${declaration.id.type}`);
            if (!declaration.init) throw new ConversionError(declaration, 'missing initializer');

            const name = declaration.id.name;
            const result = this.convertExpression(declaration.init);
            this.variableScope.set(name, result);
        });
    }

    // eslint-disable-next-line class-methods-use-this
    private convertExpressionStatement(statement: estree.ExpressionStatement): void {
        if (
            statement.expression.type === 'CallExpression' &&
            statement.expression.callee.type === 'MemberExpression' &&
            statement.expression.callee.object.type === 'Identifier' &&
            statement.expression.callee.object.name === 'console'
        )
            return;
        throw new ConversionError(statement, `expected a console.xyz(...) call, got ${statement.expression.type}`);
    }

    convertIfStatement(statement: estree.IfStatement): IfSqlResult {
        if (statement.alternate) throw new ConversionError(statement.alternate, 'else clause not allowed');
        const test = this.convertExpression(statement.test).sql;
        let consequent: BasicConversionResult;
        if (statement.consequent.type === 'BlockStatement') {
            consequent = this.convertStatements(statement.consequent.body);
        } else {
            consequent = this.convertStatements([statement.consequent]);
        }
        return { statement, test, consequent };
    }

    convertReturnStatement(statement: estree.ReturnStatement): BasicConversionResult {
        if (!statement.argument) throw new ConversionError(statement, 'expression missing after return');
        return this.convertExpression(statement.argument);
    }

    convertStatements(statements: estree.Statement[]): BasicConversionResult {
        this.pushVariableScope();
        const ifResults = [] as IfSqlResult[];
        for (let i = 0; i < statements.length - 1; i += 1) {
            const statement = statements[i];
            switch (statement.type) {
                case 'ExpressionStatement':
                    this.convertExpressionStatement(statement);
                    break;
                case 'VariableDeclaration':
                    this.convertVariableDeclaration(statement);
                    break;
                case 'IfStatement':
                    ifResults.push(this.convertIfStatement(statement));
                    break;
                default:
                    throw new ConversionError(
                        statement,
                        `expected an if statement, a const declaration or a console.xyz(...) call, got a statement of type ${statement.type}`,
                    );
            }
        }
        const lastStatement = statements[statements.length - 1];
        if (lastStatement.type !== 'ReturnStatement')
            throw new ConversionError(statements[0], `expected return statement, got ${statements[0].type}`);
        const returnResult = this.convertReturnStatement(lastStatement);

        this.popVariableScope();

        const buildResult = (): BasicConversionResult => {
            const ifResult = ifResults.shift();
            if (ifResult === undefined) return returnResult;
            const elseResult = buildResult();
            const type = this.getCommonType(ifResult.statement, [ifResult.consequent, elseResult]);
            const sql = `(CASE WHEN ${ifResult.test} THEN ${ifResult.consequent.sql} ELSE ${elseResult.sql} END)`;
            return { type, sql };
        };

        return buildResult();
    }

    convertFunctionExpression(
        expression: estree.FunctionExpression | estree.ArrowFunctionExpression,
    ): BasicConversionResult {
        const statements = (expression.body as estree.BlockStatement).body;
        if (statements.length === 0) throw new ConversionError(expression.body, 'cannot convert empty block to SQL');
        return this.convertStatements(statements);
    }

    convertArrowFunctionExpression(expression: estree.ArrowFunctionExpression): BasicConversionResult {
        // Arrow function does not contain curly braces or return statement
        // Eg. () => 'foo'
        if ((expression.body as estree.BlockStatement).body == null) return this.convertExpression(expression.body);

        // Arrow funct ion contains curly braces or return statement
        // Eg. () => { return 'foo' }
        return this.convertFunctionExpression(expression);
    }

    parseFunctionSource(str: string): estree.ArrowFunctionExpression | estree.FunctionExpression {
        try {
            const parsed = parse(str, { ecmaVersion: 2022, loc: true });
            // below are cases that cannot happen, so the tests will never pass into them

            // parse.type cant be different from 'Program', it is imposed;
            /* istanbul ignore next */
            if (parsed.type !== 'Program') throw new ConversionError(parsed, `expected Program, got ${parsed.type}`);
            const body = (parsed as estree.Program).body[0]; // since parse.type would always be 'Program', so body would always exist
            /* istanbul ignore next */
            if (!body) throw new ConversionError(parsed, 'no program body');
            // body.type is imposed as ExpressionStatement so we will never the case of the error
            /* istanbul ignore next */
            if (body.type !== 'ExpressionStatement')
                throw new ConversionError(parsed, `expected ExpressionStatement, got ${body.type}`);

            const allowedExpressions = ['FunctionExpression', 'ArrowFunctionExpression'];
            const expression = (body as estree.ExpressionStatement).expression;
            if (!allowedExpressions.includes(expression.type)) {
                //  never entering the case of the error
                /* istanbul ignore next */
                throw new ConversionError(
                    parsed,
                    `expected ${allowedExpressions.join(' or ')}, got ${expression.type}`,
                );
            }
            return expression as estree.ArrowFunctionExpression | estree.FunctionExpression;
        } catch (e) {
            if (!this.options.quiet)
                getLogger().error(`Failed to convert function in factory ${this.rootFactory.name}: ${str}`);
            throw e;
        }
    }

    parseFunction(fn: Function): estree.ArrowFunctionExpression | estree.FunctionExpression {
        let str = removeCodeCoverageInstrumentation(fn.toString()).trim();
        if (str.length === 0) {
            throw new Error(`Source missing for ${fn.name}`);
        }
        if (str.startsWith('async ')) {
            str = str.replace(/^async (\w+)/, 'async function');
            /* istanbul ignore next */
        } else if (!str.startsWith('function ') && !str.startsWith('(')) {
            str = `async function ${str}`;
        }
        str = `(${str})`;
        return this.parseFunctionSource(str);
    }

    convertFunctionBodyExpression(
        expression: estree.ArrowFunctionExpression | estree.FunctionExpression,
    ): BasicConversionResult {
        return expression.type === 'ArrowFunctionExpression'
            ? this.convertArrowFunctionExpression(expression)
            : this.convertFunctionExpression(expression);
    }

    convertFunctionSource(str: string): BasicConversionResult {
        return this.convertFunctionBodyExpression(this.parseFunctionSource(str));
    }

    convertFunction(fn: () => AsyncResponse<AnyValue>): BasicConversionResult {
        return this.convertFunctionBodyExpression(this.parseFunction(fn));
    }

    convertFunctionBody(result: GenericConversionResult<FactoryT, PropertyT>, body: string): BasicConversionResult {
        return this.withThisResultScope(result, () => this.convertFunctionSource(`(function() { return (${body}); })`));
    }

    // List of safe string literals that we exclude from the SQL injection check
    private static readonly safeStringLiterals = new Set<string>([
        '',
        ' ',
        '%',
        'year',
        'quarter',
        'month',
        'day',
        'week',
        'dow',
        'doy',
        '1 day',
        '1 year - 1 day',
        '1 month - 1 day',
        '3 month - 1 day',
        '1 week - 1 day',
    ]);

    static addSafeStringLiterals(literals: string[]): void {
        literals.forEach(literal => this.safeStringLiterals.add(literal));
    }

    static isSafeStringLiteral(literal: string): boolean {
        return this.safeStringLiterals.has(literal);
    }
}
