import type { decimal, Dict, ExtractEdgesPartial, Logical } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import {
    confirmDialogWithAcceptButtonText,
    notesOverwriteWarning,
} from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Currency,
    Customer,
    CustomerPriceReason,
    DeliveryDetail,
    DeliveryMode,
    Incoterm,
    Item,
    ItemCustomer,
    PaymentTerm,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type {
    GraphApi,
    SalesDocumentReceiptStatus,
    SalesDocumentReturnStatus,
    SalesInvoice,
    SalesInvoiceLine,
    SalesOrder,
    SalesOrderLine,
    SalesOrderLineToSalesShipmentLine,
    SalesReturnReceipt,
    SalesReturnReceiptLine,
    SalesReturnRequest,
    SalesReturnRequestLine,
    SalesShipmentDisplayStatus,
    SalesShipmentLine,
    SalesShipmentLineBinding,
    SalesShipment as SalesShipmentNode,
} from '@sage/xtrem-sales-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableFieldActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';
import * as displayButtons from '../client-functions/display-buttons-sales-shipment';
import { getSiteAndCustomer } from '../client-functions/finance-integration';
import type { SalesShipmentStepSequenceStatus } from '../client-functions/interfaces/interfaces';
import * as PillColorSales from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/sales-shipment-actions-functions';
import {
    addLinesOnHoldCustomer,
    resynchronizeShipmentStatus,
} from '../client-functions/sales-shipment-actions-functions';
import { addLineFromOrder } from '../client-functions/shipment-from-order';

@ui.decorators.page<SalesShipment, SalesShipmentNode>({
    title: 'Sales shipment',
    objectTypeSingular: 'Sales shipment',
    objectTypePlural: 'Sales shipments',
    idField() {
        return this.number;
    },
    menuItem: sales,
    module: 'sales',
    node: '@sage/xtrem-sales/SalesShipment',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 200,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.confirm,
                this.post,
                this.repost,
                this.revert,
                this.createSalesInvoicesFromShipments,
                this.createSalesReturnRequestFromShipments,
            ],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            quickActions: [this.print],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.shipmentStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        this.shipmentStepSequence.statuses = this.getDisplayStatusStepSequence();
        await this.initPage();
        this.initPosting();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (this.fromNotificationHistory) {
            this.customSave.isHidden = false;
        }
        this.manageDisplayButtonGoToSysNotificationPageAction();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'printBulkPackingSlip',
                title: 'Print packing slip',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
            {
                mutation: 'printBulkPickList',
                title: 'Print pick list',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-sales/SalesShipment',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id || '' };
                },
            }),
            line2: ui.nestedFields.reference<SalesShipment, SalesShipmentNode, Customer>({
                bind: 'shipToCustomer',
                title: 'Ship-to customer',
                node: '@sage/xtrem-master-data/Customer',
                valueField: { businessEntity: { name: true } },
                tunnelPage: undefined,
            }),
            shipToCustomerId: ui.nestedFields.text({
                bind: { shipToCustomer: { id: true } },
                title: 'Ship-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'date', title: 'Shipping date', isMandatory: true }),
            line_4: ui.nestedFields.reference<SalesShipment, SalesShipmentNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                title: 'Site',
                tunnelPage: undefined,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesShipmentDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line_5: ui.nestedFields.text({ bind: 'reference', title: 'Reference', isHiddenOnMainField: true }),
            line6: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData?.isPrinted ? 'tick' : 'none'),
            }),
            line7: ui.nestedFields.label({
                title: 'Return request status',
                bind: 'returnRequestStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentReturnStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentReturnStatus', rowData?.returnRequestStatus),
            }),
            line8: ui.nestedFields.label({
                title: 'Return receipt status',
                bind: 'returnReceiptStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentReceiptStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentReceiptStatus', rowData?.returnReceiptStatus),
            }),
            line9: ui.nestedFields.reference<SalesShipment, SalesShipmentNode>({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                title: 'Stock site',
                valueField: 'name',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
                columns: [
                    ui.nestedFields.reference<SalesShipment, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.technical({ bind: 'customerOnHoldCheck' })],
                    }),
                ],
            }),
            line10: ui.nestedFields.text({
                bind: 'trackingNumber',
                title: 'Tracking number',
                isHiddenOnMainField: true,
            }),
            line11: ui.nestedFields.reference<SalesShipment, SalesShipmentNode>({
                bind: 'incoterm',
                node: '@sage/xtrem-master-data/Incoterm',
                valueField: 'name',
                title: 'Incoterms® rule',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line12: ui.nestedFields.reference<SalesShipment, SalesShipmentNode>({
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                valueField: 'name',
                title: 'Delivery mode',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line13: ui.nestedFields.numeric({
                bind: 'deliveryLeadTime',
                title: 'Delivery lead time',
                postfix: 'days',
                isHiddenOnMainField: true,
            }),
            line14: ui.nestedFields.date({ bind: 'deliveryDate', title: 'Delivery date', isHiddenOnMainField: true }),
            line15: ui.nestedFields.reference<SalesShipment, SalesShipmentNode>({
                bind: 'billToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                title: 'Bill-to customer',
                valueField: { businessEntity: { name: true } },
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            billToCustomerId: ui.nestedFields.text({
                bind: { billToCustomer: { id: true } },
                title: 'Bill-to customer ID',
                isHiddenOnMainField: true,
            }),
            line16: ui.nestedFields.reference<SalesShipment, SalesShipmentNode>({
                title: 'Payment term',
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                valueField: 'name',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line17: ui.nestedFields.technical({ bind: 'status' }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            invoiceStatus: ui.nestedFields.technical({ bind: 'invoiceStatus' }),
            date: ui.nestedFields.technical({ bind: 'date' }),
            isOnHold: ui.nestedFields.checkbox<SalesShipment, SalesShipmentNode>({
                title: 'On hold',
                bind: 'isOnHold',
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: {
                    displayStatus: { _nin: ['shipped', 'partiallyInvoiced', 'invoiced', 'postingInProgress', 'error'] },
                },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Ready to process', graphQLFilter: { displayStatus: { _eq: 'readyToProcess' } } },
            { title: 'Ready to ship', graphQLFilter: { displayStatus: { _eq: 'readyToShip' } } },
            { title: 'Shipped', graphQLFilter: { displayStatus: { _eq: 'shipped' } } },
            { title: 'Partially invoiced', graphQLFilter: { displayStatus: { _eq: 'partiallyInvoiced' } } },
            { title: 'Invoiced', graphQLFilter: { displayStatus: { _eq: 'invoiced' } } },
            { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
            { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
        ],
        dropdownActions: [
            {
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.confirmAction({
                            isCalledFromRecordPage: false,
                            salesShipmentPage: this,
                            recordId,
                            recordNumber: rowItem.number,
                            isConfirmed: true,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonConfirmAction({
                            parameters: { status: rowItem.status },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Post stock',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.postAction({
                            salesShipmentPage: this,
                            recordId,
                            recordNumber: rowItem.number,
                            isOnHold: rowItem.isOnHold || false,
                            date: rowItem.date || '',
                            status: rowItem.status || '',
                            customerOnHoldCheck: rowItem.stockSite?.legalCompany?.customerOnHoldCheck || '',
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonPostAction({
                            parameters: {
                                status: rowItem.status,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Revert',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.revertAction({
                            salesShipmentPage: this,
                            recordId,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonRevertAction({
                            parameters: {
                                status: rowItem.status,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Create invoice',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string) {
                    await actionFunctions.createInvoiceAction({
                        salesShipmentPage: this,
                        recordId,
                        isCalledFromRecordPage: false,
                    });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonCreateSalesInvoicesFromShipmentsAction({
                            parameters: {
                                status: rowItem.status,
                                invoiceStatus: rowItem.invoiceStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Create return request',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.requestReturnAction({
                            salesShipmentPage: this,
                            recordId,
                        });
                    }
                },
                onError(error: string | Error) {
                    actionFunctions.showErrorMessage({ salesShipmentPage: this, error });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonCreateSalesReturnRequestFromShipmentsAction({
                            parameters: {
                                status: rowItem.status,
                                returnRequestStatus: rowItem.returnRequestStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                icon: 'print',
                refreshesMainList: 'record',
                access: { node: '@sage/xtrem-sales/SalesShipment', bind: 'afterPrintPackingSlip' },
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (recordId && rowItem.number && rowItem.status) {
                        await actionFunctions.printShipmentAction({
                            salesShipmentPage: this,
                            recordNumber: rowItem.number,
                            recordId: rowItem._id,
                            status: rowItem.status,
                        });
                    }
                },
                isDisabled(recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (recordId && rowItem) {
                        return (
                            displayButtons.isDisabledButtonPrintAction({
                                parameters: { status: rowItem.status },
                                recordId,
                                isDirty: false,
                            }) || false
                        );
                    }
                    return true;
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    if (this.status.value !== 'shipped' || this.fromNotificationHistory) {
                        const { site, customer } = await getSiteAndCustomer({
                            page: this,
                            siteId: rowItem.site?._id ?? '',
                            customerId: rowItem.shipToCustomer?._id ?? '',
                        });
                        await actionFunctions.setDimensions({
                            salesShipmentPage: this,
                            recordNumber: rowItem.number ?? '',
                            status: rowItem.status ?? null,
                            site,
                            customer,
                        });
                    }
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status, displayStatus: rowItem.displayStatus },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-sales/SalesShipment',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesShipmentNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status, displayStatus: rowItem.displayStatus || '' },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class SalesShipment extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    salesOrderLineQueryFields: Dict<any> = {};

    fromNotificationHistory: boolean;

    private readonly shipmentStepSequenceCreate = ui.localize(
        '@sage/xtrem-sales/pages__sales_shipment__step_sequence_creation',
        'Create',
    );

    private readonly shipmentStepSequenceConfirm = ui.localize(
        '@sage/xtrem-sales/pages__sales_shipment__step_sequence_confirm',
        'Confirm',
    );

    private readonly shipmentStepSequencePost = ui.localize(
        '@sage/xtrem-sales/pages__sales_shipment__step_sequence_post',
        'Post',
    );

    private readonly shipmentStepSequenceInvoice = ui.localize(
        '@sage/xtrem-sales/pages__sales_shipment__step_sequence_invoice',
        'Invoice',
    );

    private readonly shipmentStepSequenceRequestReturn = ui.localize(
        '@sage/xtrem-sales/pages__sales_shipment__step_sequence_return_request',
        'Request return',
    );

    private readonly shipmentStepSequenceReceiveReturn = ui.localize(
        '@sage/xtrem-sales/pages__sales_shipment__step_sequence_return_receipt',
        'Receive return',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        let currentInvoiceStatus: ui.StepSequenceStatus = 'incomplete';

        if (this.invoiceStatus.value === 'invoiced') {
            currentInvoiceStatus = 'complete';
        } else if (this.invoiceStatus.value === 'partiallyInvoiced') {
            currentInvoiceStatus = 'current';
        }

        let currentRequestReturnStatus: ui.StepSequenceStatus = 'incomplete';

        if (this.returnRequestStatus.value === 'returnRequested') {
            currentRequestReturnStatus = 'complete';
        } else if (this.returnRequestStatus.value === 'returnPartiallyRequested') {
            currentRequestReturnStatus = 'current';
        }

        let currentReturnReceiptStatus: ui.StepSequenceStatus = 'incomplete';

        if (this.returnReceiptStatus.value === 'returned') {
            currentReturnReceiptStatus = 'complete';
        } else if (this.returnReceiptStatus.value === 'partiallyReturned') {
            currentReturnReceiptStatus = 'current';
        }

        if (['inProgress', 'error'].includes(this.stockTransactionStatus.value || '')) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                post: 'current',
                invoice: currentInvoiceStatus,
                requestReturn: currentRequestReturnStatus,
                receiveReturn: currentReturnReceiptStatus,
            });
        }

        if (this.stockTransactionStatus.value === 'completed') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                post: 'complete',
                invoice: currentInvoiceStatus,
                requestReturn: currentRequestReturnStatus,
                receiveReturn: currentReturnReceiptStatus,
            });
        }

        if (this.status.value === 'readyToShip') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                confirm: 'complete',
                post: 'incomplete',
                invoice: currentInvoiceStatus,
                requestReturn: currentRequestReturnStatus,
                receiveReturn: currentReturnReceiptStatus,
            });
        }

        return this._setStepSequenceStatusObject({
            create: 'current',
            confirm: 'incomplete',
            post: 'incomplete',
            invoice: currentInvoiceStatus,
            requestReturn: currentRequestReturnStatus,
            receiveReturn: currentReturnReceiptStatus,
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.customSave,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.confirm,
                this.post,
                this.repost,
                this.revert,
                this.createSalesInvoicesFromShipments,
                this.createSalesReturnRequestFromShipments,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.customSave.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });

        if (this.fromNotificationHistory) {
            this.customSave.isHidden = false;
        }

        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value, displayStatus: this.displayStatus.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonConfirmAction(isDirty);
        this.manageDisplayRevertAction(isDirty);
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        this.manageDisplayButtonCreateSalesInvoicesFromShipmentsAction(isDirty);
        this.manageDisplayButtonCreateSalesReturnRequestFromShipmentsAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonPrintAction(isDirty);
        this.manageDisplayButtonSelectFromSalesOrderLinesAction();
    }

    private manageDisplayButtonConfirmAction(isDirty: boolean) {
        this.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayRevertAction(isDirty: boolean) {
        this.revert.isHidden = displayButtons.isHiddenButtonRevertAction({
            parameters: { status: this.status.value, stockTransactionStatus: this.stockTransactionStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: { status: this.status.value, stockTransactionStatus: this.stockTransactionStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { fromNotificationHistory: this.fromNotificationHistory },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCreateSalesInvoicesFromShipmentsAction(isDirty: boolean) {
        this.createSalesInvoicesFromShipments.isHidden =
            this.fromNotificationHistory ||
            displayButtons.isHiddenButtonCreateSalesInvoicesFromShipmentsAction({
                parameters: { status: this.status.value, invoiceStatus: this.invoiceStatus.value },
                recordId: this.$.recordId,
                isDirty,
            });
    }

    private manageDisplayButtonCreateSalesReturnRequestFromShipmentsAction(isDirty: boolean) {
        this.createSalesReturnRequestFromShipments.isHidden =
            displayButtons.isHiddenButtonCreateSalesReturnRequestFromShipmentsAction({
                parameters: { status: this.status.value, returnRequestStatus: this.returnRequestStatus.value },
                recordId: this.$.recordId,
                isDirty,
            });
    }

    private manageDisplayButtonPrintAction(isDirty: boolean) {
        this.print.isHidden = displayButtons.isHiddenButtonPrintAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
        });

        this.print.isDisabled = displayButtons.isDisabledButtonPrintAction({
            parameters: {
                status: this.status.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonSelectFromSalesOrderLinesAction() {
        this.selectFromSalesOrderLines.isDisabled = displayButtons.isDisabledButtonSelectFromSalesOrderLinesAction({
            parameters: {
                stockSite: this.stockSite.value,
                date: this.date.value,
                shipToCustomer: this.shipToCustomer.value,
                status: this.status.value,
            },
        });

        this.selectFromSalesOrderLines.isHidden = displayButtons.isHiddenButtonSelectFromSalesOrderLinesAction({
            parameters: {
                stockSite: this.stockSite.value,
                date: this.date.value,
                shipToCustomer: this.shipToCustomer.value,
                status: this.status.value,
            },
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value, displayStatus: this.displayStatus.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                stockSite: this.stockSite.value,
                shipToCustomer: this.shipToCustomer.value,
                date: this.date.value,
            },
        });
    }

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Confirm',
        isHidden: true,
        async onClick() {
            if (this.number.value && this._id.value) {
                await actionFunctions.confirmAction({
                    isCalledFromRecordPage: true,
                    salesShipmentPage: this,
                    recordId: this._id.value,
                    recordNumber: this.number.value,
                    isConfirmed: true,
                });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Revert',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                await actionFunctions.revertAction({
                    salesShipmentPage: this,
                    recordId: this._id.value,
                });
            }
        },
    })
    revert: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Save',
        isHidden: true,
        access: { bind: '$update' },
        async onClick() {
            if (!this._id.value) {
                if (
                    this.linesFromSingleOrder() &&
                    ((this.headerNotesChanged() && this.orderIsTransferHeaderNote()) ||
                        (this.lineNotesChanged() && this.ordersIsTransferLineNote()))
                ) {
                    this.isOverwriteNote.value = await notesOverwriteWarning(this);
                } else if (!this.linesFromSingleOrder() && this.lineNotesChanged() && this.ordersIsTransferLineNote()) {
                    this.isOverwriteNote.value = false;
                }
            }
            await this.$standardSaveAction.execute(true);
        },
    })
    customSave: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Print',
        icon: 'print',
        access: { node: '@sage/xtrem-sales/SalesShipment', bind: 'afterPrintPackingSlip' },
        async onClick() {
            if (this.number.value && this._id.value && this.status.value)
                await actionFunctions.printShipmentAction({
                    salesShipmentPage: this,
                    recordNumber: this.number.value,
                    recordId: this._id.value,
                    status: this.status.value,
                });
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        icon: 'search',
        title: 'Add lines from orders',
        isHidden: true,
        async onClick() {
            if (
                await addLinesOnHoldCustomer(
                    this,
                    this.isOnHold.value || false,
                    this.stockSite.value?.legalCompany?.customerOnHoldCheck,
                )
            ) {
                await addLineFromOrder(this);
            }
        },
    })
    selectFromSalesOrderLines: ui.PageAction;

    async initPage() {
        this.salesOrderLineToSalesShipmentLines.value.forEach(elt =>
            this.salesOrderLineToSalesShipmentLines.removeRecord(elt._id),
        );
        this.salesOrderLineToSalesShipmentLines.isHidden = true;
        this.billToAddress.isReadOnly = true;
        this.shipToAddress.isReadOnly = true;
        this.salesOrderLineToSalesShipmentLines.value = [];
        if (this.$.recordId) {
            if (!this.status.value) {
                this.status.value = 'readyToProcess';
            }
            const isHeaderDisabled = this.status.value === 'shipped';
            // Disable header fields if necessary
            this.number.isDisabled = isHeaderDisabled;
            this.reference.isDisabled = isHeaderDisabled;
            this.stockSite.isDisabled = isHeaderDisabled;
            this.site.isDisabled = isHeaderDisabled;
            this.date.isDisabled = isHeaderDisabled;
            this.shipToCustomer.isReadOnly = isHeaderDisabled;
            this.shipToCustomerAddress.isDisabled = isHeaderDisabled;
            this.shipToAddress.isDisabled = isHeaderDisabled;
            this.billToCustomer.isReadOnly = isHeaderDisabled;
            this.billToAddress.isDisabled = isHeaderDisabled;
            this.currency.isDisabled = isHeaderDisabled;
            this.paymentTerm.isDisabled = isHeaderDisabled;

            // Disable shipment fields if necessary
            this.incoterm.isDisabled = isHeaderDisabled;
            this.deliveryMode.isDisabled = isHeaderDisabled;
            this.deliveryLeadTime.isDisabled = isHeaderDisabled;
            this.deliveryDate.isDisabled = isHeaderDisabled;
        } else {
            await setReferenceIfSingleValue([this.stockSite]);
            this.date.value = new Date(Date.now()).toISOString().substring(0, 10);
            this.disableSomeHeaderPropertiesIfLines();
        }

        if (!this.status.value) {
            this.status.value = 'readyToProcess';
        }

        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
    }

    @ui.decorators.textField<SalesShipment>({}) _id: ui.fields.Text;

    @ui.decorators.section<SalesShipment>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<SalesShipment>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textAreaField<SalesShipment>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<SalesShipment>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'salesShipment',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.stepSequenceField<SalesShipment>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return [
                this.shipmentStepSequenceCreate,
                this.shipmentStepSequenceConfirm,
                this.shipmentStepSequencePost,
                this.shipmentStepSequenceInvoice,
                this.shipmentStepSequenceRequestReturn,
                this.shipmentStepSequenceReceiveReturn,
            ];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    shipmentStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<SalesShipment, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        node: '@sage/xtrem-system/Site',
        title: 'Stock site',
        lookupDialogTitle: 'Select stock site',
        valueField: { businessEntity: { name: true } },
        helperTextField: { businessEntity: { id: true } },
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.reference<SalesShipment, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'customerOnHoldCheck' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
        ],
        filter() {
            return {
                isInventory: true,
                ...(this.site.value?.legalCompany?._id
                    ? { legalCompany: { _id: this.site.value.legalCompany._id } }
                    : {}),
            };
        },
        onChange() {
            this.manageDisplayButtonSelectFromSalesOrderLinesAction();
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesShipment, Customer>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        node: '@sage/xtrem-master-data/Customer',
        title: 'Ship-to customer',
        lookupDialogTitle: 'Select ship-to customer',
        valueField: { businessEntity: { name: true } },
        helperTextField: { businessEntity: { id: true } },
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        shouldSuggestionsIncludeColumns: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        warningMessage() {
            if (this.isOnHold.value) {
                if (this.shipToCustomer.value?._id === this.billToCustomer.value?._id) {
                    return ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__display_customer_is_on_hold_status_credit_limit',
                        'Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}',
                        {
                            currencySymbol: this.currency.value?.symbol || '',
                            creditLimit: this.shipToCustomer.value?.creditLimit || '',
                        },
                    );
                }
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_shipment__display_bill_to_customer_is_on_hold_status_credit_limit',
                    'Bill-to customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}',
                );
            }
            return '';
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical({ bind: 'creditLimit' }),
            ui.nestedFields.technical<SalesShipment, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesShipment, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],

        async onChange() {
            this.manageDisplayButtonSelectFromSalesOrderLinesAction();
            await this.fetchDefaultsFromShipToCustomer();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    shipToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<SalesShipment>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<SalesShipment>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Shipping date',
        isMandatory: true,
        async onChange() {
            this.manageDisplayButtonSelectFromSalesOrderLinesAction();
            this.manageDisplayButtonDefaultDimensionAction();
            await this.updateDeliveryDate();
        },
    })
    date: ui.fields.Date;

    @ui.decorators.tile<SalesShipment>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<SalesShipment>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    salesOrderLineCount: ui.fields.Count;

    @ui.decorators.dateField<SalesShipment>({
        parent() {
            return this.tileContainer;
        },
        title: 'Estimated delivery date',
        bind: 'deliveryDate',
        width: 'medium',
    })
    deliveryDateHeader: ui.fields.Date;

    @ui.decorators.referenceField<SalesShipment, BusinessEntityAddress>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary ship-to address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesShipment, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesShipment, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<SalesShipment, BusinessEntityAddress, DeliveryDetail>({
                bind: 'deliveryDetail',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/DeliveryDetail',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<SalesShipment, DeliveryDetail, Incoterm>({
                        bind: 'incoterm',
                        title: 'Incoterms® rule',
                        node: '@sage/xtrem-master-data/Incoterm',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.reference<SalesShipment, DeliveryDetail, DeliveryMode>({
                        bind: 'mode',
                        title: 'Delivery mode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name' })],
                    }),
                    ui.nestedFields.numeric({ bind: 'leadTime', title: 'Delivery lead time', postfix: 'day(s)' }),
                    ui.nestedFields.reference<SalesShipment, DeliveryDetail, Site>({
                        bind: 'shipmentSite',
                        title: 'Stock site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.reference<SalesShipment, Site, Company>({
                                bind: 'legalCompany',
                                title: 'Company',
                                node: '@sage/xtrem-system/Company',
                                tunnelPage: '@sage/xtrem-master-data/Company',
                                valueField: 'name',
                                helperTextField: 'id',
                                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                            }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.shipToCustomer.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: this.shipToCustomer.value?.businessEntity?._id },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
        async onChange() {
            await this.cascadeAndFetchDefaultsFromShipToAddress();
        },
    })
    shipToCustomerAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.labelField<SalesShipment>({
        title: 'Display status',
        optionType: '@sage/xtrem-sales/SalesShipmentDisplayStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
        async onClick() {
            await resynchronizeShipmentStatus(this, this._id.value ?? '');
        },
    })
    displayStatus: ui.fields.Label<SalesShipmentDisplayStatus>;

    @ui.decorators.labelField<SalesShipment>({
        parent() {
            return this.informationBlock;
        },
        title: 'Status',
        bind: 'status',
        isHidden: true,
        optionType: '@sage/xtrem-sales/SalesShipmentStatus',
    })
    status: ui.fields.Label;

    @ui.decorators.checkboxField<SalesShipment>({})
    isOnHold: ui.fields.Checkbox;

    @ui.decorators.tableField<SalesShipment, SalesShipmentLine>({
        parent() {
            return this.itemsSection;
        },
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        hasLineNumbers: true,
        node: '@sage/xtrem-sales/SalesShipmentLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-sales/salesOriginDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.technical<SalesShipment, SalesShipmentLine>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesShipment',
                nestedFields: [ui.nestedFields.text({ bind: '_id' })],
            }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to customer document',
                isExcludedFromMainField: true,
                isDisabled() {
                    return ['posted', 'inProgress', 'error'].includes(this.status.value || '');
                },
            }),
            ui.nestedFields.label<SalesShipment, SalesShipmentLine>({
                title: 'Status',
                isTitleHidden: true,
                bind: 'status',
                optionType: '@sage/xtrem-sales/SalesShipmentStatus',
                isExcludedFromMainField: true,
                style: (_id, rowData) => PillColorSales.getLabelColorByStatus('SalesShipmentStatus', rowData?.status),
            }),
            ui.nestedFields.label<SalesShipment, SalesShipmentLine>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value || '');
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<SalesShipment, SalesShipmentLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                helperTextField: 'id',
                valueField: 'name',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.checkbox({ bind: 'isExpiryManaged', title: 'Expiration managed' }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<SalesShipment, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly() {
                    return this.status.value === 'shipped';
                },
            }),
            ui.nestedFields.reference<SalesShipment, SalesShipmentLine, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isReadOnly: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                onClick() {
                    this.changeLinesFieldState(
                        'unit',
                        ui.localize(
                            '@sage/xtrem-sales/page__sales_shipment_line__salesUnit_update_not_allowed_status_readyToShip',
                            'The sales shipment is ready to ship. You cannot update the sales unit.',
                        ),
                    );
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isMandatory: true,
                isDisabled() {
                    return this.status.value !== 'readyToProcess';
                },
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
                onChange(_id, value) {
                    value.quantityInStockUnit = String(value.quantity * value.unitToStockUnitConversionFactor);
                    this.lines.addOrUpdateRecordValue(value);

                    this.addEditSalesShipmentLine(_id, value);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                scale: 10,
                isMandatory: true,
                isReadOnly: true,
                width: 'large',
                isHidden(_value, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Allocated quantity',
                bind: 'quantityAllocated',
                isReadOnly: true,
                isHidden() {
                    return this.status.value === 'shipped' || !this.$.recordId;
                },
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity to allocate',
                bind: 'remainingQuantityToAllocate',
                isReadOnly: true,
                isHiddenOnMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.reference<SalesShipment, SalesShipmentLine, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden(value, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantity',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHidden() {
                    return this.status.value === 'shipped' || !this.$.recordId;
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData?.allocationStatus),
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.label<SalesShipment, SalesShipmentLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-sales/SalesPriceOrigin',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.technical<SalesShipment, SalesShipmentLine, CustomerPriceReason>({
                bind: 'priceReason',
                node: '@sage/xtrem-master-data/CustomerPriceReason',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'priority' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: 'invoiceStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentInvoiceStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentInvoiceStatus', rowData?.invoiceStatus),
                width: 'large',
            }),
            ui.nestedFields.label({
                title: 'Return request status',
                bind: 'returnRequestStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentReturnStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentReturnStatus', rowData?.returnRequestStatus),
                width: 'large',
            }),
            ui.nestedFields.label({
                title: 'Return receipt status',
                bind: 'returnReceiptStatus',
                optionType: '@sage/xtrem-sales/SalesDocumentReceiptStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesDocumentReceiptStatus', rowData?.returnReceiptStatus),
                width: 'large',
            }),
            ui.nestedFields.numeric({
                title: 'In Progress quantity',
                bind: 'quantityInvoicedInProgressInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Invoiced quantity',
                bind: 'quantityInvoicedPostedInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Requested quantity',
                bind: 'quantityRequestedInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Receipt quantity',
                bind: 'quantityReceiptInSalesUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({
                title: 'Customer order reference',
                bind: 'customerNumber',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
        ],
        /** FIXME:
         * We cannot update header fields from the grid
         * https://jira.sage.com/browse/XT-678
         */
        onChange() {
            this.disableSomeHeaderPropertiesIfLines();
        },
        optionsMenu: [
            {
                title: 'All statuses',
                graphQLFilter: {},
            },
            {
                title: 'Allocation required',
                graphQLFilter: { allocationStatus: { _nin: ['notManaged', 'allocated'] } },
            },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                async onClick(_id, recordValue: ui.PartialCollectionValue<SalesShipmentLineBinding>) {
                    await this.fillBaseDocumentLine(_id);
                    const linkedSalesOrderLine = this.baseDocumentLine.value;
                    const line = MasterDataUtils.removeExtractEdgesPartial(recordValue);
                    const allocationChanged = await MasterDataUtils.confirmDialogToBoolean(
                        StockDetailHelper.editStockDetails(this, line, {
                            movementType: 'allocation',
                            data: {
                                isEditable: ['readyToProcess', 'readyToShip'].includes(line.status),
                                needFullAllocation: line.status === 'readyToShip',
                                cannotOverAllocate: true,
                                orderDocumentLine: linkedSalesOrderLine?._id,
                                documentLineHasAlreadySomeAllocations: Number(line.quantityAllocated) > 0,
                                effectiveDate: this.date.value ?? '',
                                documentLineSortValue: line._sortValue,
                                documentLine: _id,
                                item: line.item?._id,
                                stockSite: this.stockSite.value?._id,
                                quantity: +line.quantityInStockUnit,
                                unit: line.item.stockUnit?._id,
                                searchCriteria: {
                                    activeQuantityInStockUnit: +line.quantityInStockUnit,
                                    item: line.item?._id,
                                    site: this.stockSite.value?._id ?? '',
                                    stockUnit: line.item.stockUnit?._id,
                                    statusList: [{ statusType: 'accepted' }],
                                },
                            },
                        }),
                    );
                    if (allocationChanged) {
                        await this.lines.refresh();
                        this.manageDisplayButtonAllOtherActions(false);
                    }
                },
                isDisabled() {
                    return this.$.isDirty || ['shipped', 'draft'].includes(this.status.value ?? '');
                },
                isHidden(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    return this.status.value === 'shipped' || !rowItem.item?.isStockManaged;
                },
            },
            {
                icon: 'three_boxes',
                title: 'Issued stock',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                    await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
                        StockDetailHelper.editStockDetails(this, line, {
                            movementType: 'issue',
                            data: {
                                isEditable: false,
                                documentLineSortValue: line._sortValue,
                                documentLine: rowId,
                                item: line.item?._id,
                                stockSite: this.stockSite.value?._id,
                                quantity: +line.quantityInStockUnit,
                                number: this.number.value ?? '',
                                unit: line.item.stockUnit?._id,
                                searchCriteria: {
                                    activeQuantityInStockUnit: +line.quantityInStockUnit,
                                    item: line.item?._id,
                                    site: this.stockSite.value?._id ?? '',
                                    stockUnit: line.item.stockUnit?.id,
                                    statusList: [],
                                },
                            },
                        }) as Promise<SalesShipmentLine>,
                    );
                },
                isHidden(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    return this.status.value !== 'shipped' || !rowItem.item?.isStockManaged;
                },
            },
            {
                icon: 'three_boxes',
                title: 'Manage allocations',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    await this.transferAllocationDialog(rowId, rowItem);
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    const isStockManaged = rowItem?.item?.isStockManaged;
                    return !isStockManaged || this.status.value === 'shipped';
                },
                isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    return rowItem.allocationStatus === 'allocated' || rowItem.status === 'shipped';
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_id, recordValue: ui.PartialCollectionValue<SalesShipmentLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: recordValue.status !== 'shipped' || this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden() {
                    return ['shipped', 'readyToShip'].includes(this.status.value ?? '');
                },
                async onClick(rowId, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                    if (rowItem.item?.isStockManaged && rowItem.allocationStatus !== 'notAllocated') {
                        throw new Error(
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_shipment__cannot_delete_line_quantity_allocated',
                                'Remove the stock allocation before deleting the line.',
                            ),
                        );
                    }
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_shipment__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_shipment__line_delete_action_dialog_content',
                                'You are about to delete this sales shipment line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        if (rowItem.salesOrderLines) {
                            this.salesOrderLineToSalesShipmentLines.removeRecord(
                                rowItem.salesOrderLines.at(0)?._id ?? '',
                            );
                            if (this.salesOrderLineToSalesShipmentLines.value.length === 0) {
                                this.salesOrderLineToSalesShipmentLines.isHidden = true;
                            }
                        }
                    }
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions<SalesShipment>({
                actions: [],
            });
        },
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions<SalesShipment>({
                actions: [this.selectFromSalesOrderLines],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-sales/SalesShipmentStatus',
                style: (_id, rowData) => PillColorSales.getLabelColorByStatus('SalesShipmentStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
        },
        sidebar: {
            title(_id, recordValue) {
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'three_boxes',
                    title: 'Issued stock',
                    async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                        const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                        await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
                            StockDetailHelper.editStockDetails(this, line, {
                                movementType: 'issue',
                                data: {
                                    isEditable: false,
                                    documentLineSortValue: line._sortValue,
                                    documentLine: rowId,
                                    item: line.item?._id,
                                    stockSite: this.stockSite.value?._id,
                                    quantity: +line.quantityInStockUnit,
                                    number: this.number.value ?? '',
                                    unit: line.item.stockUnit?._id,
                                    searchCriteria: {
                                        activeQuantityInStockUnit: +line.quantityInStockUnit,
                                        item: line.item?._id,
                                        site: this.stockSite.value?._id ?? '',
                                        stockUnit: line.item.stockUnit?.id,
                                        statusList: [],
                                    },
                                },
                            }) as Promise<SalesShipmentLine>,
                        );
                    },
                    isHidden(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                        return this.status.value !== 'shipped' || !rowItem.item?.isStockManaged;
                    },
                },
                {
                    icon: 'three_boxes',
                    title: 'Manage allocations',
                    async onClick(rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                        await this.transferAllocationDialog(rowId, rowItem);
                    },
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                        const isStockManaged = rowItem?.item?.isStockManaged;
                        return !isStockManaged || this.status.value === 'shipped';
                    },
                    isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<SalesShipmentLine>) {
                        return rowItem.allocationStatus === 'allocated' || rowItem.status === 'shipped';
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_id, recordValue: ui.PartialCollectionValue<SalesShipmentLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: recordValue.status !== 'shipped' || this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden() {
                        return ['shipped', 'readyToShip'].includes(this.status.value ?? '');
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    this.externalNoteLine.value = recordValue.externalNote ? recordValue.externalNote.value : '';
                    this.isExternalNoteLine.value = recordValue.isExternalNote || false;
                    this.externalNoteLine.isDisabled =
                        !this.isExternalNoteLine.value || this.status.value === 'shipped';

                    if (+recordValue._id > 0) {
                        await this.fillBaseDocumentLine(recordValue._id);
                        await this.fillSalesInvoiceLines(recordValue._id);
                        await this.fillSalesLinkedDocuments(recordValue._id);
                        await this.fillSalesReturnReceiptLines(recordValue._id);
                        await this.fillSalesReturnRequestLines(recordValue._id);
                    }
                }
            },
            // @ts-ignore:next-line: Type 'void' is not assignable to type 'Promise<void>'
            onRecordConfirmed(_id, recordValue) {
                const record = recordValue as unknown as ExtractEdgesPartial<SalesShipmentLineBinding>;
                if (record) {
                    if (record.salesOrderLines && record.salesOrderLines.length) {
                        record.salesOrderLines = record.salesOrderLines.filter(
                            (orderLine, index, array) => index === array.findIndex(line => line._id === orderLine._id),
                        );
                    }
                    record.internalNote = {
                        value: this.internalNoteLine.value ? this.internalNoteLine.value : '',
                    };
                    record.externalNote = { value: this.externalNoteLine.value ? this.externalNoteLine.value : '' };
                    record.isExternalNote = this.isExternalNoteLine.value || false;
                    this.lines.addOrUpdateRecordValue(record);
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'origin', 'itemDescription'],
                            },
                            salesBlock: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_sales', 'Sales'),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden(rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged || rowItem?.item.type === 'service';
                                },
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    stock: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_stock', 'Stock'),
                        isHidden(rowId, rowItem) {
                            if (!rowItem?.item.isStockManaged) {
                                return true;
                            }
                            return false;
                        },

                        blocks: {
                            allocation: {
                                title: ui.localize(
                                    '@sage/xtrem-sales/pages_sidebar_block_title_allocation',
                                    'Allocation',
                                ),
                                fields: ['allocationStatus', 'quantityAllocated', 'remainingQuantityToAllocate'],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.salesLinkedDocuments.value.length === 0;
                        },
                        blocks: {
                            mainBlock: {
                                fields: [this.salesLinkedDocuments],
                            },
                        },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_progress', 'Progress'),
                        isHidden() {
                            if (this.baseDocumentLine.value) {
                                return !['inProgress', 'pending', 'closed'].includes(
                                    this.baseDocumentLine.value.status ?? '',
                                );
                            }
                            return true;
                        },

                        blocks: {
                            invoicing: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_invoice', 'Invoice'),
                                fields: [
                                    'invoiceStatus',
                                    'quantityInvoicedInProgressInSalesUnit',
                                    'quantityInvoicedPostedInSalesUnit',
                                    this.toInvoiceLines,
                                ],
                            },
                            returnRequest: {
                                title: ui.localize(
                                    '@sage/xtrem-sales/pages_sidebar_block_title_return_request',
                                    'Return request',
                                ),
                                fields: [
                                    'returnRequestStatus',
                                    'quantityRequestedInSalesUnit',
                                    this.toReturnRequestLines,
                                ],
                            },
                            returnReceipt: {
                                title: ui.localize(
                                    '@sage/xtrem-sales/pages_sidebar_block_title_return_receipt',
                                    'Return receipt',
                                ),
                                fields: [
                                    'returnReceiptStatus',
                                    'quantityReceiptInSalesUnit',
                                    this.salesReturnReceiptLines,
                                ],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<SalesShipmentLine>;

    @ui.decorators.richTextField<SalesShipment>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'shipped';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<SalesShipment>({
        title: 'Add notes to customer document',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'shipped';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<SalesShipment>({
        isFullWidth: true,
        title: 'Customer line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'shipped';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.section<SalesShipment>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.informationSection;
        },
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<SalesShipment>({
        parent() {
            return this.informationBlock;
        },
        title: 'Printed',
        isDisabled: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.labelField<SalesShipment>({
        parent() {
            return this.informationBlock;
        },
        title: 'Return request status',
        bind: 'returnRequestStatus',
        optionType: '@sage/xtrem-sales/SalesDocumentReturnStatus',
        isHidden() {
            return this.returnRequestStatus.value === 'noReturnRequested';
        },
        style() {
            return PillColorSales.getLabelColorByStatus('SalesDocumentReturnStatus', this.returnRequestStatus.value);
        },
    })
    returnRequestStatus: ui.fields.Label<SalesDocumentReturnStatus>;

    @ui.decorators.labelField<SalesShipment>({
        parent() {
            return this.informationBlock;
        },
        title: 'Return receipt status',
        bind: 'returnReceiptStatus',
        optionType: '@sage/xtrem-sales/SalesDocumentReceiptStatus',
        isHidden() {
            return this.returnRequestStatus.value === 'noReturnRequested';
        },
        style() {
            return PillColorSales.getLabelColorByStatus('SalesDocumentReceiptStatus', this.returnReceiptStatus.value);
        },
    })
    returnReceiptStatus: ui.fields.Label<SalesDocumentReceiptStatus>;

    @ui.decorators.labelField<SalesShipment>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        isHidden: true,
        parent() {
            return this.informationBlock;
        },
    })
    stockTransactionStatus: ui.fields.Label;

    @ui.decorators.referenceField<SalesShipment, Site>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-system/Site',
        title: 'Site',
        lookupDialogTitle: 'Select site',
        valueField: 'name',
        helperTextField: 'id',
        isAutoSelectEnabled: true,
        minLookupCharacters: 0,
        fetchesDefaults: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesShipment, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'doStockPosting' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isSales' }),
        ],
        filter() {
            return this.stockSite.value
                ? ({
                      isSales: true,
                      legalCompany: this.stockSite.value.legalCompany,
                  } as Logical<Site>)
                : {
                      isSales: true,
                  };
        },
        async onChange() {
            await this.fetchDefaultsFromSalesSite();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.textField<SalesShipment>({
        parent() {
            return this.informationBlock;
        },
        title: 'Reference',
    })
    reference: ui.fields.Text;

    @ui.decorators.section<SalesShipment>({
        title: 'Shipping',
    })
    shippingSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.shippingSection;
        },
        width: 'large',
        title: 'Shipping',
        isTitleHidden: true,
    })
    shippingBlock: ui.containers.Block;

    @ui.decorators.textField<SalesShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Tracking number',
    })
    trackingNumber: ui.fields.Text;

    @ui.decorators.referenceField<SalesShipment, Incoterm>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/Incoterm',
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select incoterms rule',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.vitalPodField<SalesShipment, Address>({
        parent() {
            return this.shippingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.shipToCustomerAddress.value) {
                const { ...values } = { ...this.shipToCustomerAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.shipToCustomerAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'shipped';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'shipped' || !this.shipToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddress.isReadOnly = true;
                    if (this.shipToAddress.value) {
                        this.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(this.shipToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'shipped' || this.shipToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.dropdownList({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.referenceField<SalesShipment, DeliveryMode>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.numericField<SalesShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Work days',
        isReadOnly: true,
        isHidden: true,
    })
    workDays: ui.fields.Numeric;

    @ui.decorators.numericField<SalesShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Delivery lead time',
        isMandatory: true,
        async onChange() {
            if (this.date.value) {
                await this.updateDeliveryDate();
            }
        },
        postfix: 'day(s)',
    })
    deliveryLeadTime: ui.fields.Numeric;

    async updateDeliveryDate() {
        this.deliveryDate.value = await this.$.graph
            .node('@sage/xtrem-sales/SalesShipment')
            .mutations.addWorkDays(true, {
                shippingDate: this.date.value ?? '',
                deliveryLeadTime: this.deliveryLeadTime.value ?? '',
                workDays: this.workDays.value ?? '',
            })
            .execute();
    }

    @ui.decorators.dateField<SalesShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Delivery date',
        isMandatory: true,
    })
    deliveryDate: ui.fields.Date;

    @ui.decorators.section<SalesShipment>({
        title: 'Financial',
    })
    financialSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.financialSection;
        },
        width: 'large',
        title: 'Financial',
        isTitleHidden: true,
    })
    financialSectionFinancialBlock: ui.containers.Block;

    @ui.decorators.section<SalesShipment>({
        title: 'Posting',
        isHidden() {
            return !this.site.value?.legalCompany?.doStockPosting || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.postingSection;
        },
        title: 'Error detail',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesShipment, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.referenceField<SalesShipment, Customer>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/Customer',
        title: 'Bill-to customer',
        lookupDialogTitle: 'Select bill-to customer',
        valueField: { businessEntity: { name: true } },
        helperTextField: { businessEntity: { id: true } },
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        shouldSuggestionsIncludeColumns: true,
        isReadOnly: true,
        warningMessage() {
            if (this.isOnHold.value) {
                return ui.localize(
                    '@sage/xtrem-sales/pages__sales_shipment__display_customer_is_on_hold_status_credit_limit',
                    'Customer on hold \n\nCredit limit {{currencySymbol}} {{creditLimit}}',
                    {
                        currencySymbol: this.billToCustomer.value?.businessEntity?.currency?.symbol || '',
                        creditLimit: this.billToCustomer.value?.creditLimit || '',
                    },
                );
            }
            return '';
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.reference<SalesShipment, Customer, Country>({
                bind: { businessEntity: { country: true } },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isOnHold' }),
            ui.nestedFields.technical<SalesShipment, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesShipment, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesShipment, Customer, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
            ui.nestedFields.technical({ bind: 'creditLimit' }),
        ],

        async onChange() {
            await this.fetchDefaultsFromBillToCustomer();
        },
    })
    billToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<SalesShipment, BusinessEntityAddress>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-to address',
        lookupDialogTitle: 'Select bill-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        isReadOnly: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesShipment, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesShipment, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            return this.billToCustomer.value
                ? { businessEntity: { id: this.billToCustomer.value.businessEntity?.id } }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromBillToAddress();
        },
    })
    billToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.vitalPodField<SalesShipment, Address>({
        parent() {
            return this.financialSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.billToLinkedAddress.value) {
                const { ...values } = { ...this.billToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.billToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'shipped';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.billToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'shipped' || !this.billToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.billToAddress.isReadOnly = true;
                    if (this.billToAddress.value) {
                        this.billToAddress.value.concatenatedAddress = getConcatenatedAddress(this.billToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'shipped' || this.billToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Bill-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billToAddress.value);
                },
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    billToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.referenceField<SalesShipment, Currency>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/Currency',
        title: 'Currency',
        lookupDialogTitle: 'Select currency',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isDisabled: true, // isDisabled for the moment
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
        ],
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.referenceField<SalesShipment, PaymentTerm>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        node: '@sage/xtrem-master-data/PaymentTerm',
        title: 'Payment term',
        lookupDialogTitle: 'Select payment term',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'description' })],
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.separatorField<SalesShipment>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    invoiceStatusSeparator: ui.fields.Separator;

    @ui.decorators.labelField<SalesShipment>({
        parent() {
            return this.financialSectionFinancialBlock;
        },
        title: 'Invoice status',
        bind: 'invoiceStatus',
        optionType: '@sage/xtrem-sales/SalesDocumentInvoiceStatus',
        isHidden: true,
    })
    invoiceStatus: ui.fields.Label;

    @ui.decorators.section<SalesShipment>({ isHidden: true, title: 'On order quantity change' })
    onOrderQuantityChangeSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        parent() {
            return this.onOrderQuantityChangeSection;
        },
        title: '',
        isTitleHidden: true,
    })
    onOrderQuantityChangeBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesShipment, UnitOfMeasure>({
        parent() {
            return this.onOrderQuantityChangeBlock;
        },
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        title: 'Sales unit',
        lookupDialogTitle: 'Select sales unit',
        valueField: 'name',
        helperTextField: 'symbol',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isTransient: true,
        isReadOnly: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<SalesShipment>({
        parent() {
            return this.onOrderQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'Remaining quantity',
        bind: 'remainingOnOrderQuantity',
        isReadOnly: true,
    })
    remainingOnOrderQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesShipment>({
        parent() {
            return this.onOrderQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'Actual quantity',
        bind: 'actualOnOrderQuantity',
        isReadOnly: true,
    })
    actualOnOrderQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesShipment>({
        parent() {
            return this.onOrderQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'New quantity',
        bind: 'newOnOrderQuantity',
    })
    newOnOrderQuantity: ui.fields.Numeric;

    @ui.decorators.tableField<SalesShipment>({
        title: 'Sales order lines - ordering in progress',
        isTransient: true,
        canFilter: false,
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine',
        orderBy: { _sortValue: +1 },
        cardView: true,
        mobileCard: {
            title: ui.nestedFields.text<SalesShipment>({
                title: 'Sales order',
                bind: 'number',
                isReadOnly: true,
            }),
            titleRight: ui.nestedFields.reference<SalesShipment>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            line2: ui.nestedFields.date<SalesShipment>({
                title: 'Shipping date',
                bind: 'shippingDate',
            }),
            line2Right: ui.nestedFields.numeric({
                title: 'Quantity to ship',
                bind: 'quantity',
                postfix: (_rowId, rowData) => rowData?.unit?.symbol,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
            }),
            image: ui.nestedFields.image<SalesShipment>({
                bind: { item: { image: true } },
                title: 'Image',
                size: 'small',
            }),
        },
        columns: [
            ui.nestedFields.reference<SalesShipment, SalesOrderLineToSalesShipmentLine, SalesOrderLine>({
                title: 'Sales order line',
                bind: 'linkedDocument',
                node: '@sage/xtrem-sales/SalesOrderLine',
                valueField: '_id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.reference<SalesShipment, SalesOrderLine, Item>({
                        title: 'Item',
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'description' }),
                            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                            ui.nestedFields.technical({ bind: 'isStockManaged' }),
                            ui.nestedFields.numeric({ bind: 'minimumSalesQuantity' }),
                            ui.nestedFields.numeric({ bind: 'maximumSalesQuantity' }),
                        ],
                    }),
                    ui.nestedFields.date({
                        title: 'Shipping date',
                        bind: 'shippingDate',
                    }),
                    ui.nestedFields.technical<SalesShipment, SalesOrderLine, SalesOrder>({
                        bind: 'document',
                        node: '@sage/xtrem-sales/SalesOrder',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                    ui.nestedFields.technical<SalesShipment, SalesOrderLine, UnitOfMeasure>({
                        bind: 'unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<SalesShipment, SalesOrderLineToSalesShipmentLine, SalesShipmentLine>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesShipmentLine',
                nestedFields: [ui.nestedFields.text({ bind: '_id' })],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity to ship',
                bind: 'quantity',
                postfix: (_rowId, rowData) => rowData?.unit?.symbol,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
            }),
            ui.nestedFields.reference<SalesShipment, SalesOrderLineToSalesShipmentLine, SalesShipmentLine>({
                node: '@sage/xtrem-sales/SalesShipmentLine',
                bind: 'document',
                title: 'Unit',
                valueField: '_id',
                size: 'small',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.reference<SalesShipment, SalesShipmentLine, UnitOfMeasure>({
                        title: 'Sales unit',
                        bind: 'unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'name',
                        helperTextField: 'symbol',
                        isMandatory: true,
                        isReadOnly: true, // isReadOnly for the moment
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Quantities',
                isDisabled() {
                    return false;
                },

                async onClick(_id, rowData) {
                    this.remainingOnOrderQuantity.value = rowData.quantity;
                    this.actualOnOrderQuantity.value = rowData.quantity;
                    this.newOnOrderQuantity.value = rowData.quantity;
                    this.unit.value = rowData.salesUnit;
                    this.remainingOnOrderQuantity.scale = this.unit.value?.decimalDigits || 0;
                    this.actualOnOrderQuantity.scale = this.unit.value?.decimalDigits || 0;
                    this.newOnOrderQuantity.scale = this.unit.value?.decimalDigits || 0;
                    this.remainingOnOrderQuantity.postfix = this.unit.value?.symbol;
                    this.actualOnOrderQuantity.postfix = this.unit.value?.symbol;
                    this.newOnOrderQuantity.postfix = this.unit.value?.symbol;
                    this.onOrderQuantityChangeSection.isHidden = false;
                    let response = await this.$.dialog
                        .custom('info', this.onOrderQuantityChangeSection, {
                            cancelButton: { isHidden: false },
                            acceptButton: { isHidden: false },
                            size: 'small',
                        })
                        .then(() => true)
                        .catch(() => false);
                    this.onOrderQuantityChangeSection.isHidden = true;
                    if (
                        response &&
                        this.newOnOrderQuantity.value &&
                        this.newOnOrderQuantity.value > 0 &&
                        this.newOnOrderQuantity.value !== rowData.quantity &&
                        (await this.checkQuantityInSalesUnit(this.newOnOrderQuantity.value, rowData.salesOrderLine))
                    ) {
                        if (
                            this.remainingOnOrderQuantity.value &&
                            this.newOnOrderQuantity.value <= this.remainingOnOrderQuantity.value
                        ) {
                            response = await confirmDialogWithAcceptButtonText(
                                this,
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_confirmation_title',
                                    'Update shipment line quantity confirmation?',
                                ),
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_confirmation',
                                    'Update the quantity from the related shipment line?',
                                ),
                                ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                            );
                            rowData.quantity = this.newOnOrderQuantity.value;
                            const relatedShipmentLine = this.lines.getRecordValue(rowData.salesShipmentLine._id);
                            if (relatedShipmentLine) {
                                const relatedSalesOrderLineIndex = (
                                    relatedShipmentLine.salesOrderLines as Partial<ui.plugin.Dict<any>>[]
                                ).findIndex(soLine => soLine.salesOrderLine === rowData.salesOrderLine._id);
                                if (relatedShipmentLine.salesOrderLines && relatedSalesOrderLineIndex !== -1) {
                                    relatedShipmentLine.salesOrderLines[relatedSalesOrderLineIndex].quantity =
                                        rowData.quantity;
                                }
                                if (response) {
                                    relatedShipmentLine.quantity = String(
                                        Number.parseInt(relatedShipmentLine.quantity ?? '0', 10) -
                                            ((this.actualOnOrderQuantity.value ?? 0) - this.newOnOrderQuantity.value),
                                    );
                                    relatedShipmentLine.quantityInStockUnit = String(
                                        Number(relatedShipmentLine.quantity) *
                                            Number(relatedShipmentLine.unitToStockUnitConversionFactor),
                                    );
                                    this.lines.setRecordValue(relatedShipmentLine);
                                }
                            }
                            this.salesOrderLineToSalesShipmentLines.addOrUpdateRecordValue(rowData);
                        } else {
                            await this.errorOnQuantity(
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_error',
                                    'The new quantity cannot be greater than the actual quantity',
                                ),
                            );
                        }
                    }
                    this.actualOnOrderQuantity.value = 0;
                    this.newOnOrderQuantity.value = 0;
                },
            },
        ],
    })
    salesOrderLineToSalesShipmentLines: ui.fields.Table<
        SalesOrderLineToSalesShipmentLine & { salesOrderLines: { _id: number } }
    >;

    @ui.decorators.section<SalesShipment>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<SalesShipment>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesShipment>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'shipped';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesShipment>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to customer document',
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'shipped';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<SalesShipment>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'shipped';
        },
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesShipment>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isDisabled() {
            return this.status.value === 'shipped';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<SalesShipment>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isDisabled() {
            return this.status.value === 'shipped';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.switchField<SalesShipment>({
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Post stock',
        isHidden: true,
        async onClick() {
            if (this.number.value && this._id.value) {
                await actionFunctions.postAction({
                    salesShipmentPage: this,
                    recordId: this._id.value,
                    recordNumber: this.number.value,
                    isOnHold: this.isOnHold.value || false,
                    date: this.date.value || '',
                    status: this.status.value || '',
                    customerOnHoldCheck: this.stockSite?.value?.legalCompany?.customerOnHoldCheck || '',
                });
            }
        },
    })
    post: ui.PageAction;

    async checkForUpdate() {
        // TODO: This is a hack. It can be removed when XT-23948 is ready.
        let refreshCounter: number = 0;
        const checkForUpdate = async () => {
            try {
                await this.status.refresh();
                if (this.status.value === 'shipped') {
                    await this.$.router.refresh(true); // true overrides the dirty checks
                    await this.$.refreshNavigationPanel();
                    this.$.loader.isHidden = true;
                    return;
                }
                refreshCounter += 1;
                if (refreshCounter < 5) {
                    setTimeout(() => checkForUpdate, 1000);
                } else {
                    await this.$.router.refresh(true); // true overrides the dirty checks
                    await this.$.refreshNavigationPanel();
                    this.$.loader.isHidden = true;
                }
            } catch {
                this.$.loader.isHidden = true;
            }
        };
        await checkForUpdate();
    }

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const documentLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-sales/SalesShipment')
                .mutations.repost(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    {
                        salesShipment: this._id.value || '',
                        documentLines,
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__repost_errors',
                        'Errors while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Create invoice',
        isHidden: true,
        async onClick() {
            if (this._id.value) {
                await actionFunctions.createInvoiceAction({
                    salesShipmentPage: this,
                    recordId: this._id.value,
                    isCalledFromRecordPage: true,
                });
            }
        },
        onError(error) {
            this.$.loader.isHidden = true;
            return ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__invoice_exception',
                'The invoice creation failed: {{exception}}',
                { exception: MasterDataUtils.formatError(this, error) },
            );
        },
    })
    createSalesInvoicesFromShipments: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        title: 'Create return request',
        isHidden: true,
        async onClick() {
            await actionFunctions.requestReturnAction({
                salesShipmentPage: this,
                recordId: String(this._id.value) || '',
            });
        },
        onError(error: string | Error) {
            actionFunctions.showErrorMessage({
                salesShipmentPage: this,
                error,
            });
        },
    })
    createSalesReturnRequestFromShipments: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return !['shipped', 'invoiced'].includes(this.displayStatus.value || '');
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<SalesShipment>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    @ui.decorators.referenceField<SalesShipment, SalesOrderLine>({
        title: 'Order lines',
        isHidden: true,
        isTransient: true,
        node: '@sage/xtrem-sales/SalesOrderLine',
        valueField: '_id',
        columns: [ui.nestedFields.technical({ bind: 'status' })],
    })
    baseDocumentLine: ui.fields.Reference<SalesOrderLine>;

    @ui.decorators.tableField<SalesShipment, SalesOrder & { documentType: string }>({
        title: 'Order',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesOrder',
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Origin document number',
                bind: '_id',
                map(_fieldValue, rowData) {
                    return `${rowData?.number}`;
                },
                page: '@sage/xtrem-sales/SalesOrder',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id || '' };
                },
            }),
            ui.nestedFields.text({
                title: 'Document type',
                bind: 'documentType',
                isTransient: true,
                isDisabled: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-sales/SalesOrderDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.text({
                title: 'Customer order reference',
                bind: 'customerNumber',
                isDisabled: true,
            }),
        ],
    })
    salesLinkedDocuments: ui.fields.Table<SalesOrder & { documentType: string }>;

    @ui.decorators.tableField<SalesShipment, SalesInvoiceLine>({
        title: 'Invoice lines',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesInvoiceLine',
        orderBy: { _sortValue: +1 },
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Invoice number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-sales/SalesInvoice',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.document._id ?? '' };
                },
            }),
            ui.nestedFields.technical<SalesShipment, SalesInvoiceLine, SalesInvoice>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesInvoice',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.technical<SalesShipment, SalesInvoiceLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: { document: { status: true } },
                map(rowData) {
                    switch (rowData.document.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__draft',
                                rowData.document.status,
                            );
                        case 'posted':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__posted',
                                rowData.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__inProgress',
                                rowData.document.status,
                            );
                        case 'error':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_invoice_status__error',
                                rowData.document.status,
                            );
                        default:
                            return '';
                    }
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesInvoiceStatus', rowData?.document?.status),
            }),
            ui.nestedFields.numeric({
                title: 'Invoiced quantity in sales unit',
                bind: 'quantity',
                isReadOnly: true,
                width: 'large',
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.numeric({
                title: 'Invoiced amount',
                bind: 'netPrice',
                isReadOnly: true,
                width: 'large',
                unit() {
                    return this.currency.value;
                },
            }),
        ],
    })
    toInvoiceLines: ui.fields.Table<SalesInvoiceLine>;

    @ui.decorators.tableField<SalesShipment, SalesReturnRequestLine>({
        title: 'Return lines',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesReturnRequestLine',
        orderBy: { _sortValue: +1 },
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Return request number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-sales/SalesReturnRequest',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.document._id ?? '' };
                },
            }),
            ui.nestedFields.technical<SalesShipment, SalesReturnRequestLine, SalesReturnRequest>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesReturnRequest',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.technical<SalesShipment, SalesReturnRequestLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Return request status',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                // optionType: '@sage/xtrem-sales/SalesReturnRequestStatus',
                map(value, rowData) {
                    return ui.localizeEnumMember('@sage/xtrem-sales/SalesReturnRequestStatus', rowData.status);
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesReturnRequestStatus', rowData?.status),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isReadOnly: true,
                width: 'large',
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
        ],
    })
    toReturnRequestLines: ui.fields.Table<SalesReturnRequestLine>;

    @ui.decorators.tableField<SalesShipment, SalesReturnReceiptLine>({
        title: 'Receipt lines',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesReturnReceiptLine',
        orderBy: { _sortValue: +1 },
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Return receipt number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-sales/SalesReturnReceipt',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.document._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Return receipt status',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                map(value, rowData) {
                    switch (rowData.document.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_return_receipt_status__draft',
                                rowData.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_return_receipt_status__in_progress',
                                rowData.document.status,
                            );
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-sales/enums__sales_return_receipt_status__closed',
                                rowData.document.status,
                            );
                        default:
                            return '';
                    }
                },
                style: (_id, rowData) =>
                    PillColorSales.getLabelColorByStatus('SalesReturnReceiptStatus', rowData?.document?.status),
            }),
            ui.nestedFields.technical<SalesShipment, SalesReturnReceiptLine, SalesReturnReceipt>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesReturnReceipt',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.technical<SalesShipment, SalesReturnReceiptLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isReadOnly: true,
                width: 'large',
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_value, rowData) => rowData?.unit?.symbol,
            }),
            ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
        ],
    })
    salesReturnReceiptLines: ui.fields.Table<SalesReturnReceiptLine>;

    async fillSalesReturnRequestLines(rowId: any) {
        const oldIsDirty = this.$.isDirty;
        this.toReturnRequestLines.value.forEach(podLine => {
            this.toReturnRequestLines.removeRecord(podLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLineToSalesReturnRequestLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        linkedDocument: {
                            _id: true,
                        },
                        document: {
                            _id: true,
                            status: true,
                            item: {
                                _id: true,
                                name: true,
                            },
                            quantity: true,
                            unit: {
                                _id: true,
                                name: true,
                                decimalDigits: true,
                                id: true,
                                symbol: true,
                            },
                            quantityInStockUnit: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                            },
                        },
                        quantity: true,
                    },
                    {
                        filter: { linkedDocument: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.toReturnRequestLines.value = result.edges.map(e => e.node.document);
        this.toReturnRequestLines.isHidden = this.toReturnRequestLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesLinkedDocuments(rowId: any) {
        const LinkDocument = await this.$.graph
            .node('@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        document: {
                            _id: true,
                        },
                        linkedDocument: {
                            _id: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                                displayStatus: true,
                                customerNumber: true,
                            },
                        },
                    },
                    {
                        filter: { document: { _id: rowId } },
                    },
                ),
            )
            .execute();

        if (LinkDocument.edges.length) {
            this.salesLinkedDocuments.value = [
                {
                    ...LinkDocument.edges[0].node.linkedDocument.document,
                    documentType: ui.localizeEnumMember('@sage/xtrem-sales/SalesDocumentType', 'salesOrder'),
                } as ui.PartialNodeWithId<SalesOrder & { documentType: string }>,
            ];
        }

        this.salesLinkedDocuments.isHidden = this.salesLinkedDocuments.value.length === 0;
    }

    async fillSalesInvoiceLines(rowId: any) {
        const oldIsDirty = this.$.isDirty;
        this.toInvoiceLines.value.forEach(podLine => {
            this.toInvoiceLines.removeRecord(podLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLineToSalesInvoiceLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        linkedDocument: {
                            _id: true,
                        },
                        document: {
                            _id: true,
                            item: {
                                _id: true,
                                name: true,
                            },
                            quantity: true,
                            unit: {
                                _id: true,
                                name: true,
                                decimalDigits: true,
                                id: true,
                                symbol: true,
                            },
                            netPrice: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                            },
                        },
                        quantity: true,
                    },
                    {
                        filter: { linkedDocument: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.toInvoiceLines.value = result.edges.map(e => e.node.document);
        this.toInvoiceLines.isHidden = this.toInvoiceLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillBaseDocumentLine(rowId: any) {
        const oldIsDirty = this.$.isDirty;

        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesOrderLineToSalesShipmentLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            linkedDocument: {
                                _id: true,
                                document: {
                                    _id: true,
                                    number: true,
                                },
                                status: true,
                            },
                            document: {
                                _id: true,
                            },
                        },
                        {
                            filter: { document: { _id: rowId } },
                        },
                    ),
                )
                .execute(),
        );
        this.baseDocumentLine.value = result[0].linkedDocument;
        this.baseDocumentLine.isHidden = result.length > 0;

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesReturnReceiptLines(rowId: any) {
        const oldIsDirty = this.$.isDirty;
        this.salesReturnReceiptLines.value.forEach(elt => {
            this.salesReturnReceiptLines.removeRecord(elt._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesShipmentLineToSalesReturnReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        linkedDocument: { _id: true },
                        document: {
                            _id: true,
                            item: { _id: true, name: true },
                            quantity: true,
                            unit: { _id: true, name: true, decimalDigits: true, id: true, symbol: true },
                            quantityInStockUnit: true,
                            document: { _id: true, number: true, status: true },
                        },
                        quantity: true,
                    },
                    { filter: { linkedDocument: { _id: rowId } } },
                ),
            )
            .execute();
        this.salesReturnReceiptLines.value = result.edges.map(e => e.node.document);
        this.salesReturnReceiptLines.isHidden = this.salesReturnReceiptLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    orderIsTransferHeaderNote() {
        return this.salesOrderLineToSalesShipmentLines.value.some(
            line => line.linkedDocument?.document?.isTransferHeaderNote === true,
        );
    }

    ordersIsTransferLineNote() {
        return this.salesOrderLineToSalesShipmentLines.value.some(
            line => line.linkedDocument?.document?.isTransferLineNote === true,
        );
    }

    linesFromSingleOrder() {
        const orderLineNumbers = this.salesOrderLineToSalesShipmentLines.value.map(
            orderLine => orderLine.linkedDocument?.documentNumber,
        );
        return orderLineNumbers.every(number => number === orderLineNumbers[0]);
    }

    lineNotesChanged() {
        return this.lines.value.some(
            line => line.internalNote?.value || line.isExternalNote || line.externalNote?.value,
        );
    }

    headerNotesChanged() {
        return this.internalNote.value !== '' || this.isExternalNote.value || this.externalNote.value !== '';
    }

    addEditSalesShipmentLine(_id: string, line: any) {
        if (line.salesOrderLines && line.salesOrderLines.at(0)) {
            line.salesOrderLines.at(0).quantity = line.quantity;
            line.salesOrderLines.at(0).quantityInStockUnit = line.quantity;
            this.salesOrderLineToSalesShipmentLines.addOrUpdateRecordValue(line);
        }
    }

    changeLinesFieldState(name: string, message: string) {
        if (this.status.value === 'readyToShip') {
            this.$.showToast(message, {
                type: 'error',
            });
        }
    }

    private disableSomeHeaderPropertiesIfLines() {
        const isDisabled = this.lines.value.length > 0;
        this.site.isDisabled = isDisabled;
        this.stockSite.isDisabled = isDisabled;
        this.shipToCustomer.isReadOnly = isDisabled;
        this.shipToCustomerAddress.isDisabled = isDisabled;
        this.shipToAddress.isDisabled = isDisabled;
    }

    async cascadeAndFetchDefaultsFromShipToAddress() {
        if (this.incoterm.value || this.deliveryMode.value) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__cascade_ship_to_address_update_dialog_title',
                        'Confirm update',
                    ),
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_shipment__cascade_ship_to_address_update_dialog_content',
                        'You are about to update delivery information.',
                    ),
                    ui.localize('@sage/xtrem-sales/pages-confirm-update', 'Update'),
                )
            ) {
                await this.fetchDefaultsFromShipToAddress();
                await this.updateDeliveryDate();
            } else {
                await this.fetchDefaultsFromShipToAddressOnly();
            }
        } else {
            await this.fetchDefaultsFromShipToAddress();
            await this.updateDeliveryDate();
        }
    }

    async checkQuantityInSalesUnit(newOnOrderQuantity: decimal, salesOrderLine: SalesOrderLine): Promise<any> {
        let itemCustomer: Dict<any> = {};
        let minimumSalesQuantity: decimal = 0;
        let maximumSalesQuantity: decimal = 0;

        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ItemCustomer')
                .query(
                    ui.queryUtils.edgesSelector<ItemCustomer>(
                        {
                            _id: true,
                            salesUnit: {
                                _id: true,
                                symbol: true,
                            },
                            minimumSalesQuantity: true,
                            maximumSalesQuantity: true,
                        },
                        {
                            filter: {
                                item: salesOrderLine.item._id,
                                customer: String(this.shipToCustomer.value?._id),
                            },
                        },
                    ),
                )
                .execute(),
        );

        if (result.length) itemCustomer = result.shift() as Dict<any>;

        if (result.length && itemCustomer.unit._id === salesOrderLine.unit._id) {
            minimumSalesQuantity = +itemCustomer.minimumSalesQuantity;
            maximumSalesQuantity = +itemCustomer.maximumSalesQuantity;
        } else {
            minimumSalesQuantity = +salesOrderLine.item.minimumSalesQuantity;
            maximumSalesQuantity = +salesOrderLine.item.maximumSalesQuantity;
        }
        if (minimumSalesQuantity !== 0 && newOnOrderQuantity < minimumSalesQuantity) {
            await this.errorOnQuantity(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_minimum_quantity',
                    'The quantity in sales unit ({{value}}) must not be less than {{minValue}}.',
                    {
                        value: newOnOrderQuantity,
                        minValue: minimumSalesQuantity,
                    },
                ),
            );
            return false;
        }

        if (maximumSalesQuantity !== 0 && newOnOrderQuantity > maximumSalesQuantity) {
            await this.errorOnQuantity(
                ui.localize(
                    '@sage/xtrem-sales/pages__sales__quantity_in_sales_unit_maximum_quantity',
                    'The quantity in sales unit ({{value}}) must not be larger than {{maxValue}}.',
                    {
                        value: newOnOrderQuantity,
                        maxValue: maximumSalesQuantity,
                    },
                ),
            );
            return false;
        }

        return true;
    }

    async errorOnQuantity(message: string) {
        await this.$.dialog.message(
            'error',
            ui.localize(
                '@sage/xtrem-sales/pages__sales_shipment__on_shipment_quantity_changed_error_title',
                'Error on quantity',
            ),
            message,
        );
    }

    async fetchDefaultsFromShipToCustomer() {
        await this.$.fetchDefaults([
            'shipToCustomerAddress',
            'shipToAddress',
            'billToCustomer',
            'billToLinkedAddress',
            'billToAddress',
            'currency',
            'paymentTerm',
            'isOnHold',
            'incoterm',
            'deliveryMode',
            'deliveryLeadTime',
            'workDays',
        ]);
        if (!this.stockSite.value) {
            await this.$.fetchDefaults(['stockSite']);
        }
    }

    async fetchDefaultsFromShipToAddress() {
        await this.$.fetchDefaults(['shipToAddress', 'incoterm', 'deliveryMode', 'deliveryLeadTime', 'workDays']);
        if (this.shipToCustomerAddress.value?.deliveryDetail?.shipmentSite && !this.stockSite.value) {
            await this.$.fetchDefaults(['stockSite']);
        }
        await this.fetchDefaultsFromShipToAddressExtension();
    }

    // eslint-disable-next-line require-await
    async fetchDefaultsFromShipToAddressExtension() {
        noop(this._id.value);
    }

    async fetchDefaultsFromShipToAddressOnly() {
        await this.$.fetchDefaults(['shipToAddress']);
    }

    async fetchDefaultsFromBillToCustomer() {
        await this.$.fetchDefaults(['billToLinkedAddress', 'billToAddress', 'currency', 'paymentTerm', 'isOnHold']);
    }

    async fetchDefaultsFromBillToAddress() {
        await this.$.fetchDefaults(['billToAddress']);
    }

    async fetchDefaultsFromSalesSite() {
        await this.$.fetchDefaults(['stockSite']);
    }

    /**
     * Fetch Return requests created from this sales shipment and returns the last one
     */
    async getLastReturnRequestCreatedFromSalesShipment() {
        return extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesShipmentLineToSalesReturnRequestLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            document: {
                                document: {
                                    _id: true,
                                    number: true,
                                },
                            },
                        },
                        {
                            filter: { linkedDocument: { document: { _in: [this._id.value] } } },
                        },
                    ),
                )
                .execute(),
        ).pop()?.document.document._id;
    }

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.billToAddress._id) < 0) {
            delete values.billToAddress._id;
        }
        if (Number(values.shipToAddress._id) < 0) {
            delete values.shipToAddress._id;
        }
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        return values;
    }

    _setStepSequenceStatusObject(stepSequenceValues: SalesShipmentStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        return {
            [this.shipmentStepSequenceCreate]: stepSequenceValues.create,
            [this.shipmentStepSequenceConfirm]: stepSequenceValues.confirm,
            [this.shipmentStepSequencePost]: stepSequenceValues.post,
            [this.shipmentStepSequenceInvoice]: stepSequenceValues.invoice,
            [this.shipmentStepSequenceRequestReturn]: stepSequenceValues.requestReturn,
            [this.shipmentStepSequenceReceiveReturn]: stepSequenceValues.receiveReturn,
        };
    }

    async transferAllocationDialog(
        rowId: string,
        rowItem: ui.PartialCollectionValue<SalesShipmentLineBinding>,
    ): Promise<void> {
        await MasterDataUtils.catchPanelCrossQuitButtonAsNoop(
            this.$.dialog.page(
                '@sage/xtrem-stock-data/TransferAllocationsDialog',
                {
                    item: JSON.stringify(rowItem.item),
                    site: JSON.stringify(this.stockSite.value),
                    shippingDate: JSON.stringify(this.date.value),
                    lineId: rowId,
                    requiredQuantity: rowItem.quantityInStockUnit ?? 0,
                    allocatedQuantity: rowItem.quantityAllocated || 0,
                    documentTypes: JSON.stringify(['SalesOrderLine', 'SalesShipmentLine']),
                    documentNumber: this.number.value ?? 0,
                },
                {
                    rightAligned: false,
                    size: 'extra-large',
                },
            ),
        );
        await this.lines.refreshRecord(rowId);
        this.manageDisplayButtonAllOtherActions(false);
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
