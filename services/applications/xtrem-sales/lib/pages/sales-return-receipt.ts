import type { Dict, ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import {
    confirmDialogWithAcceptButtonText,
    notesOverwriteWarning,
} from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Customer,
    DeliveryDetail,
    DeliveryMode,
    Incoterm,
    Item,
    Location,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { sales } from '@sage/xtrem-master-data/build/lib/menu-items/sales';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type {
    GraphApi,
    SalesReturnReceiptDisplayStatus,
    SalesReturnReceiptLine,
    SalesReturnReceiptLineBinding,
    SalesReturnReceipt as SalesReturnReceiptNode,
    SalesReturnRequest,
    SalesReturnRequestLine,
    SalesReturnRequestLineToSalesReturnReceiptLine,
} from '@sage/xtrem-sales-api';
import type { Lot, StockDocumentTransactionStatus, StockStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageTableFieldActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { isString } from 'lodash';
import * as displayButtons from '../client-functions/display-buttons-sales-return-receipt';
import { getSiteAndCustomer } from '../client-functions/finance-integration';
import type {
    SalesReturnReceiptStepSequenceStatus,
    StockReceiptDetailJson,
} from '../client-functions/interfaces/interfaces';
import { addLineFromReturnRequest } from '../client-functions/return-receipt-from-return-request';
import * as actionFunctions from '../client-functions/sales-return-receipt-actions-functions';

@ui.decorators.page<SalesReturnReceipt, SalesReturnReceiptNode>({
    title: 'Sales return receipt',
    objectTypeSingular: 'Sales return receipt',
    objectTypePlural: 'Sales return receipts',
    idField() {
        return this.number;
    },
    menuItem: sales,
    module: 'sales',
    node: '@sage/xtrem-sales/SalesReturnReceipt',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 370,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [this.post, this.repost],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.returnReceiptStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    onLoad() {
        this.returnReceiptStepSequence.statuses = this.getDisplayStatusStepSequence();
        this.initPage();
        this.initPosting();
        this.manageDisplayButtonGoToSysNotificationPageAction();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-sales/SalesReturnReceipt',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id ?? '',
                    };
                },
            }),
            line2: ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptNode>({
                bind: 'shipToCustomer',
                title: 'Ship-to customer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            shipToCustomerId: ui.nestedFields.text({
                bind: { shipToCustomer: { id: true } },
                title: 'Ship-to customer ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'date', title: 'Return receipt date', isMandatory: true }),
            line_4: ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Site',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-sales/SalesReturnReceiptDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line5Technical: ui.nestedFields.technical({ bind: 'status' }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
        },
        optionsMenu: [
            { title: 'All open statuses', graphQLFilter: { displayStatus: { _ne: 'closed' } } },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
            { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
        ],
        dropdownActions: [
            {
                title: 'Post stock',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string) {
                    await actionFunctions.post({
                        salesReturnReceiptPage: this,
                        recordId,
                    });
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnReceiptNode>) {
                    return (
                        displayButtons.isHiddenButtonPostAction({
                            parameters: { status: rowItem.displayStatus },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<SalesReturnReceiptNode>) {
                    const { site, customer } = await getSiteAndCustomer({
                        page: this,
                        siteId: rowItem.site?._id ?? '',
                        customerId: rowItem.shipToCustomer?._id ?? '',
                    });
                    await actionFunctions.setDimensions({
                        salesReturnReceiptPage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        site,
                        customer,
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesReturnReceiptNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.displayStatus },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<SalesReturnReceiptNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-sales/SalesReturnReceipt',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<SalesReturnReceiptNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: {
                            status: rowItem.displayStatus,
                            stockTransactionStatus: rowItem.stockTransactionStatus,
                        },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class SalesReturnReceipt
    extends ui.Page<GraphApi, SalesReturnReceiptNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    isEditable: boolean;

    private readonly returnReceiptStepSequenceCreate = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_receipt__step_sequence_creation',
        'Create',
    );

    private readonly returnReceiptStepSequencePost = ui.localize(
        '@sage/xtrem-sales/pages__sales_return_receipt__step_sequence_post',
        'Post',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (['inProgress', 'error'].includes(this.status.value ?? '')) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'current',
            });
        }
        if (this.status.value === 'closed' && this.stockTransactionStatus.value === 'completed') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                post: 'complete',
            });
        }

        return this._setStepSequenceStatusObject({
            create: 'current',
            post: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.customSave,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [this.$standardOpenCustomizationPageWizardAction, this.post, this.repost],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.customSave.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.displayStatus.value, stockTransactionStatus: this.stockTransactionStatus.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonSelectFromSalesReturnRequestLines();
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: { status: this.displayStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { fromNotificationHistory: this.fromNotificationHistory },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonSelectFromSalesReturnRequestLines() {
        this.selectFromSalesReturnRequestsLookup.isDisabled =
            displayButtons.isDisabledButtonSelectFromSalesReturnRequestLinesAction({
                parameters: {
                    site: this.site.value,
                    shipToCustomer: this.shipToCustomer.value,
                    status: this.status.value,
                },
            });

        this.selectFromSalesReturnRequestsLookup.isHidden =
            displayButtons.isHiddenButtonSelectFromSalesReturnRequestLinesAction({
                parameters: {
                    site: this.site.value,
                    shipToCustomer: this.shipToCustomer.value,
                    status: this.status.value,
                },
            });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.displayStatus.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                shipToCustomer: this.shipToCustomer.value,
                date: this.date.value,
            },
        });
    }

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.shipToAddress._id) < 0) {
            delete values.shipToAddress._id;
        }
        this.getLineValues(values.lines);

        if (values.lines?.length && this.lines.value.length > 0) {
            StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(values.lines, this.lines, 'receipt');
        }

        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }

        return values;
    }

    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines: any) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        lines &&
            lines.forEach((receiptTableLine: any) => {
                if (receiptTableLine.stockDetails) {
                    receiptTableLine.stockDetails = JSON.stringify(
                        (JSON.parse(receiptTableLine.stockDetails) as StockReceiptDetailJson).detailLines,
                    );
                }
                delete receiptTableLine.uStockDetails;
                delete receiptTableLine.location;
                delete receiptTableLine.quantityInStockUnit;
                delete receiptTableLine.origin;
                if (+receiptTableLine._id > 0) {
                    delete receiptTableLine.unitToStockUnitConversionFactor;
                }
                delete receiptTableLine.stockDetailStatus;
                delete receiptTableLine.isReceiptExpected;
            });
    }

    @ui.decorators.pageAction<SalesReturnReceipt>({
        title: 'Post stock',
        isHidden: true,
        onError(error: string) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.$.loader.isHidden = true;
            this.post.isDisabled = true;
            await actionFunctions.post({
                salesReturnReceiptPage: this,
                recordId: this._id.value ?? '',
            });
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        title: 'Save',
        access: {
            bind: '$update',
        },
        isHidden: true,
        async onClick() {
            if (!this._id.value) {
                if (
                    this.linesFromSingleRequestReceipt() &&
                    ((this.headerNotesChanged() && this.requestReceiptIsTransferHeaderNote()) ||
                        (this.lineNotesChanged() && this.requestReceiptIsTransferLineNote()))
                ) {
                    this.isOverwriteNote.value = await notesOverwriteWarning(this);
                } else if (
                    !this.linesFromSingleRequestReceipt() &&
                    this.lineNotesChanged() &&
                    this.requestReceiptIsTransferLineNote()
                ) {
                    this.isOverwriteNote.value = false;
                }
            }
            await this.$standardSaveAction.execute(true);
        },
    })
    customSave: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const documentLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-sales/SalesReturnReceipt')
                .mutations.repost(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    {
                        salesReturnReceipt: this._id.value || '',
                        documentLines,
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-sales/pages__sales_return_receipt__repost_errors',
                        'Errors occurred while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'salesDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        icon: 'search',
        title: 'Add lines from return requests',
        isHidden: true,
        async onClick() {
            await addLineFromReturnRequest(this);
        },
    })
    selectFromSalesReturnRequestsLookup: ui.PageAction;

    initPage() {
        this.salesReturnRequestLineToSalesReturnReceiptLines.value.forEach(elt =>
            this.salesReturnRequestLineToSalesReturnReceiptLines.removeRecord(elt._id),
        );
        this.shipToAddress.isReadOnly = true;
        this.salesReturnRequestLineToSalesReturnReceiptLines.value = [];

        if (this.$.recordId) {
            // Disable header fields if necessary
            this.number.isDisabled = true;
            this.site.isDisabled = true;
            this.date.isDisabled = true;
            this.shipToCustomer.isDisabled = true;
            this.shipToCustomerAddress.isDisabled = true;
            this.shipToAddress.isDisabled = true;
        } else {
            this.date.value = DateValue.today().toString();
            this.site.isDisabled = false;
            this.date.isDisabled = false;
            this.shipToCustomer.isDisabled = false;
            this.shipToCustomerAddress.isDisabled = false;
            this.shipToAddress.isDisabled = false;
        }

        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
    }

    @ui.decorators.textField<SalesReturnReceipt>({}) _id: ui.fields.Text;

    @ui.decorators.section<SalesReturnReceipt>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<SalesReturnReceipt>({
        title: 'Header section',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnReceipt>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<SalesReturnReceipt>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<SalesReturnReceipt>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return [this.returnReceiptStepSequenceCreate, this.returnReceiptStepSequencePost];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    returnReceiptStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<SalesReturnReceipt, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<SalesReturnReceipt, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'doStockPosting' }),
                ],
            }),
            ui.nestedFields.technical<SalesReturnReceipt, Site, Location>({
                bind: 'defaultLocation',
                node: '@sage/xtrem-master-data/Location',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<SalesReturnReceipt, Site, Location>({
                bind: 'defaultStockStatus',
                node: '@sage/xtrem-stock-data/StockStatus',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
        ],
        filter() {
            return { isInventory: true };
        },
        onChange() {
            this.manageDisplayButtonSelectFromSalesReturnRequestLines();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<SalesReturnReceipt, Customer>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        node: '@sage/xtrem-master-data/Customer',
        title: 'Ship-to customer',
        lookupDialogTitle: 'Select ship-to customer',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<SalesReturnReceipt, Customer, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<SalesReturnReceipt, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
            }),
        ],
        async onChange() {
            this.manageDisplayButtonSelectFromSalesReturnRequestLines();
            await this.fetchDefaultsFromShipToCustomer();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    shipToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.textField<SalesReturnReceipt>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<SalesReturnReceipt>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Return receipt date',
        isMandatory: true,
        onChange() {
            if (Date.parse(this.date.value ?? '') > Date.now()) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-sales/pages__sales_return_receipt__return_date__cannot__be__future',
                        'The return date cannot be later than today.',
                    ),
                );
            }
            this.manageDisplayButtonSelectFromSalesReturnRequestLines();
            this.manageDisplayButtonDefaultDimensionAction();
        },
    })
    date: ui.fields.Date;

    @ui.decorators.tile<SalesReturnReceipt>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<SalesReturnReceipt>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    salesReturnReceiptLineCount: ui.fields.Count;

    @ui.decorators.referenceField<SalesReturnReceipt, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary ship-to address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<SalesReturnReceipt, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<SalesReturnReceipt, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<SalesReturnReceipt, BusinessEntityAddress, DeliveryDetail>({
                bind: 'deliveryDetail',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/DeliveryDetail',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<SalesReturnReceipt, DeliveryDetail, Incoterm>({
                        bind: 'incoterm',
                        title: 'Incoterms® rule',
                        node: '@sage/xtrem-master-data/Incoterm',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.reference<SalesReturnReceipt, DeliveryDetail, DeliveryMode>({
                        bind: 'mode',
                        title: 'Delivery mode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name' })],
                    }),
                    ui.nestedFields.numeric({ bind: 'leadTime', title: 'Delivery lead time', postfix: 'day(s)' }),
                    ui.nestedFields.reference<SalesReturnReceipt, DeliveryDetail, Site>({
                        bind: 'shipmentSite',
                        title: 'Stock site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.reference<SalesReturnReceipt, Site, Company>({
                                bind: 'legalCompany',
                                title: 'Company',
                                node: '@sage/xtrem-system/Company',
                                tunnelPage: '@sage/xtrem-master-data/Company',
                                valueField: 'name',
                                helperTextField: 'id',
                                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                            }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.shipToCustomer.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: this.shipToCustomer.value?.businessEntity?._id },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
        async onChange() {
            await this.fetchDefaultsFromShipToAddress();
        },
    })
    shipToCustomerAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.labelField<SalesReturnReceipt>({
        optionType: '@sage/xtrem-sales/SalesReturnReceiptStatus',
        isHidden: true,
        parent() {
            return this.informationBlock;
        },
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<SalesReturnReceipt>({
        title: 'Display status',
        optionType: '@sage/xtrem-sales/SalesReturnReceiptDisplayStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<SalesReturnReceiptDisplayStatus>;

    @ui.decorators.labelField({
        optionType: '@sage/xtrem-stock-data/StockDetailStatus',
        isHidden: true,
        title: 'Stock details status',
        isTitleHidden: true,
    })
    stockDetailStatus: ui.fields.Label;

    @ui.decorators.tableField<SalesReturnReceipt, SalesReturnReceiptLineBinding>({
        parent() {
            return this.itemsSection;
        },
        bind: 'lines',
        title: 'Lines',
        canSelect: false,
        isTitleHidden: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-sales/SalesReturnReceiptLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-sales/salesOriginDocumentType',
                isReadOnly: true,
                isMandatory: true,
            }),
            ui.nestedFields.technical<SalesReturnReceipt, SalesReturnReceiptLineBinding>({
                bind: 'document',
                node: '@sage/xtrem-sales/SalesReturnReceipt',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLineBinding, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                lookupDialogTitle: 'Select item',
                valueField: 'name',
                helperTextField: 'id',
                isReadOnly: true,
                minLookupCharacters: 3,
                isAutoSelectEnabled: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.technical<SalesReturnReceipt, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesReturnReceipt, Item, UnitOfMeasure>({
                        bind: 'salesUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isDisabled() {
                    return !this.isEditable;
                },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                size: 'small',
                width: 'small',
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLineBinding, UnitOfMeasure>({
                title: 'Sales unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                lookupDialogTitle: 'Select unit',
                valueField: 'name',
                helperTextField: 'symbol',
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isDisabled() {
                    return this._id.value !== null;
                },
                filter(rowData) {
                    const filterArray: Array<string> = [];
                    if (rowData.unit) {
                        filterArray.push(rowData.unit._id);
                        if (rowData.item && rowData.item.salesUnit._id !== rowData.unit._id) {
                            filterArray.push(rowData.item.salesUnit._id);
                        }
                    }
                    if (rowData.stockUnit) {
                        filterArray.push(rowData.stockUnit._id);
                    }
                    return {
                        _id: { _in: filterArray } as any,
                    };
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in sales unit',
                bind: 'quantity',
                isMandatory: true,
                isDisabled() {
                    return !this.isEditable;
                },
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol || '',
                onChange(_rowId, rowData: ui.PartialCollectionValue<SalesReturnReceiptLine>) {
                    if (rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                        rowData.quantityInStockUnit = String(
                            +rowData.quantity * +rowData.unitToStockUnitConversionFactor,
                        );
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
                async validation(value, rowData: SalesReturnReceiptLineBinding) {
                    let filterCriteria = {};
                    if (Number(rowData._id) > 0) {
                        filterCriteria = { salesReturnReceiptLine: `_id:${rowData._id}` };
                    } else {
                        filterCriteria = {
                            salesReturnRequestLine: `_id:${(rowData as any).toReturnRequestLines[0].linkedDocument}`,
                        };
                    }
                    if (rowData.jsonStockDetails) {
                        const sumOfStockDetailQuantity = JSON.parse(rowData.jsonStockDetails).reduce(
                            (accumulator: number, stockDetail: any) =>
                                accumulator + Number(stockDetail.quantityInStockUnit),
                            0,
                        );
                        filterCriteria = { ...filterCriteria, sumOfStockDetailQuantity };
                    }
                    this.$.loader.isHidden = false;
                    const result = await this.$.graph
                        .node('@sage/xtrem-sales/SalesReturnReceipt')
                        .queries.validateQuantityInSalesUnit(
                            {
                                hasErrors: true,
                                errorMessage: true,
                            },
                            {
                                newQuantityInSalesUnit: value,
                                ...filterCriteria,
                            },
                        )
                        .execute();
                    this.$.loader.isHidden = true;
                    if (result.hasErrors) {
                        return result.errorMessage;
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Stock unit conversion factor',
                bind: 'unitToStockUnitConversionFactor',
                isReadOnly: true,
                scale: 10,
                isMandatory: true,
                isHiddenOnMainField: true,
                isHidden(_rowId, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol,
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                lookupDialogTitle: 'Select unit',
                valueField: 'name',
                helperTextField: 'symbol',
                minLookupCharacters: 1,
                isAutoSelectEnabled: true,
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden(_rowId, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),

            ui.nestedFields.label({
                title: 'Stock detail status',
                bind: 'stockDetailStatus',
                optionType: '@sage/xtrem-stock-data/StockDetailStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockDetailStatus', rowData?.stockDetailStatus),
                isHidden() {
                    return this.status.value === 'closed' && this.stockTransactionStatus.value === 'completed';
                },
            }),
            ui.nestedFields.technical({ bind: 'isReceiptExpected' }),
            ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLineBinding, Location>({
                title: 'Stock location',
                lookupDialogTitle: 'Select location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                helperTextField: 'id',
                bind: 'location',
                minLookupCharacters: 1,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference({
                        title: 'Type',
                        bind: 'locationType',
                        node: '@sage/xtrem-master-data/LocationType',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'locationCategory', title: 'Category' }),
                        ],
                    }),
                    ui.nestedFields.reference<SalesReturnReceipt, Location, Location['locationZone']>({
                        bind: 'locationZone',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: '_id',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.technical<
                                SalesReturnReceipt,
                                Location['locationZone'],
                                Location['locationZone']['site']
                            >({
                                bind: 'site',
                                node: '@sage/xtrem-system/Site',
                                nestedFields: [ui.nestedFields.technical({ bind: { _id: true } })],
                            }),
                        ],
                    }),
                ],
                filter() {
                    return {
                        locationZone: { site: { _id: { _eq: this.site.value?._id } } },
                        locationType: { locationCategory: { _ne: 'virtual' } },
                    };
                },
                isHidden(_rowId, rowData) {
                    return (
                        (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                        !rowData?.item.isStockManaged
                    );
                },
            }),
            ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLineBinding, StockStatus>({
                lookupDialogTitle: 'Select status',
                bind: 'stockStatus',
                minLookupCharacters: 1,
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden(_rowId, rowData) {
                    return (
                        (rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                        !rowData?.item.isStockManaged
                    );
                },
            }),

            ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLineBinding, Lot>({
                title: 'Lot',
                lookupDialogTitle: 'Select lot',
                node: '@sage/xtrem-stock-data/Lot',
                valueField: 'id',
                bind: 'lot',
                minLookupCharacters: 1,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.date({
                        bind: 'expirationDate',
                        title: 'Expiration date',
                    }),
                    ui.nestedFields.technical({ bind: 'sublot' }),
                    ui.nestedFields.technical({ bind: 'supplierLot' }),
                    ui.nestedFields.technical<SalesReturnReceipt, Lot, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'description' }),
                        ],
                    }),
                ],
                filter(rowData) {
                    return { item: { _id: rowData.item._id } };
                },
                isHidden(_rowId, rowData) {
                    return (
                        ((rowData?.item?.type === 'service' || rowData?.item?.type === 'good') &&
                            !rowData?.item.isStockManaged) ||
                        rowData?.item.value?.lotManagement === 'notManaged'
                    );
                },
            }),
            /**
             * TODO: To be able to retrieve the value from the panel, we need to
             * have the text field here (textStream), but it's not yet implemented
             * by the platform. Tbe implemented soon ...
             */
            // ui.nestedFields.text({
            //     bind: 'text',
            //     title: 'Text',
            //     isHidden: true,
            // }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),

            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
            ui.nestedFields.label<SalesReturnReceipt, SalesReturnReceiptLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value ?? '');
                },
                // async onClick(_id: string, data: Partial<StockInterfaces.StockIssueLinePageBinding>) {
                //     await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                //         origin: 'line',
                //         _id,
                //     });
                // },
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],
        /** FIXME:
         * We cannot update header fields from the grid
         * https://jira.sage.com/browse/XT-678
         */
        onChange() {
            this.disableSomeHeaderPropertiesIfLines();
        },
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'three_boxes',
                title: 'Stock details',
                onClick(_rowId: any, rowItem) {
                    return this.editStockDetails(rowItem);
                },
                isHidden(_rowId: any, rowItem: any) {
                    return (
                        (rowItem.item?.type === 'service' || rowItem.item?.type === 'good') &&
                        !rowItem.item.isStockManaged
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId: any, rowItem: ui.PartialCollectionValue<SalesReturnReceiptLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: this.status.value !== 'closed',
                            },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowId, rowData) {
                    if (this.status.value !== 'closed' && rowData.stockTransactionStatus === 'draft') {
                        return false;
                    }
                    return !!this.$.recordId;
                },
                async onClick(rowId: any, rowItem: any) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_return_receipt__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-sales/pages__sales_return_receipt__line_delete_action_dialog_content',
                                'You are about to delete this sales return receipt line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-sales/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        if (rowItem.toReturnRequestLines) {
                            this.salesReturnRequestLineToSalesReturnReceiptLines.removeRecord(
                                rowItem.toReturnRequestLines[0]._id,
                            );
                        }
                        this.$.loader.isHidden = true;
                    }
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions({
                actions: [],
            });
        },
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectFromSalesReturnRequestsLookup],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (recordValue && +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-sales/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'three_boxes',
                    title: `Stock details`,
                    onClick(_rowId: any, rowItem) {
                        return this.editStockDetails(rowItem);
                    },
                    isHidden(_rowId: any, rowItem: any) {
                        return (
                            (rowItem.item?.type === 'service' || rowItem.item?.type === 'good') &&
                            !rowItem.item.isStockManaged
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId: any, rowItem: ui.PartialCollectionValue<SalesReturnReceiptLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: !this.$.recordId || this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden(_rowId, rowData) {
                        if (this.status.value !== 'closed' && rowData.stockTransactionStatus === 'draft') {
                            return false;
                        }
                        return !!this.$.recordId;
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    this.isEditable = await this.isFieldEditable(_id, recordValue);
                    if (+recordValue._id > 0) {
                        await this.fillBaseDocumentLine(recordValue._id);
                        await this.fillSalesLinkedDocuments(recordValue._id);
                    }
                }
            },

            // @ts-ignore:next-line: Type 'void' is not assignable to type 'Promise<void>'
            onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    const record = recordValue as unknown as ExtractEdgesPartial<SalesReturnReceiptLineBinding>;

                    if (record.toReturnRequestLines && record.toReturnRequestLines.length) {
                        record.toReturnRequestLines = record.toReturnRequestLines.filter(
                            (requestLine, index, array) =>
                                index === array.findIndex(line => line._id === requestLine._id),
                        );
                    }

                    record.internalNote = {
                        value: this.internalNoteLine.value ? this.internalNoteLine.value : '',
                    };
                    this.lines.addOrUpdateRecordValue(record);
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'origin', 'itemDescription'],
                            },
                            salesBlock: {
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_sales', 'Sales'),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden(_rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged || rowItem?.item?.type === 'service';
                                },
                                title: ui.localize('@sage/xtrem-sales/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.salesLinkedDocuments.value.length === 0;
                        },
                        blocks: {
                            mainBlock: {
                                fields: [this.salesLinkedDocuments],
                            },
                        },
                    },
                    stock: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_stock', 'Stock'),
                        isHidden(_rowId, rowItem) {
                            return rowItem?.item?.type === 'service';
                        },
                        blocks: {
                            stockBlock: {
                                fields: ['location', 'stockStatus', 'lot'],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-sales/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<SalesReturnReceiptLine>;

    @ui.decorators.richTextField<SalesReturnReceipt>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.section<SalesReturnReceipt>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnReceipt>({
        parent() {
            return this.informationSection;
        },
        width: 'large',
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.labelField<SalesReturnReceipt>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.informationBlock;
        },
        isHidden: true,
    })
    stockTransactionStatus: ui.fields.Label;

    @ui.decorators.vitalPodField<SalesReturnReceipt, Address>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.shipToCustomerAddress.value) {
                const { ...values } = { ...this.shipToCustomerAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.shipToCustomerAddress.openDialog();
                },
                isDisabled() {
                    return !!this.$.recordId;
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return !!this.$.recordId || !this.shipToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddress.isReadOnly = true;
                    if (this.shipToAddress.value) {
                        this.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(this.shipToAddress.value);
                    }
                },
                isDisabled() {
                    return this.shipToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Bill-to address',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.referenceField<SalesReturnReceipt, SalesReturnRequestLine>({
        title: 'Return request lines',
        isHidden: true,
        isTransient: true,
        node: '@sage/xtrem-sales/SalesReturnRequestLine',
        valueField: '_id',
        columns: [ui.nestedFields.technical({ bind: 'status' }), ui.nestedFields.technical({ bind: '_id' })],
    })
    baseDocumentLine: ui.fields.Reference<SalesReturnRequestLine>;

    @ui.decorators.linkField<SalesReturnReceipt>({
        // title: 'Origin document number',
        title: 'Origin document number',
        isTransient: true,
        onClick() {
            this.$.router.goTo(`@sage/xtrem-sales/SalesReturnRequest`, {
                _id: this.baseDocumentLine.value?.document?._id ?? '',
            });
        },
        map() {
            if (this.baseDocumentLine.value && this.baseDocumentLine.value._id) {
                return this.baseDocumentLine.value.document?.number || '';
            }
            return '';
        },
    })
    returnRequestLineLink: ui.fields.Link;

    @ui.decorators.tableField<SalesReturnReceipt, SalesReturnRequest & { documentType: string }>({
        title: 'Return request',
        isTitleHidden: true,
        width: 'large',
        isTransient: true,
        node: '@sage/xtrem-sales/SalesReturnRequest',
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Origin document number',
                bind: '_id',
                map(_fieldValue, rowData) {
                    return `${rowData.number}`;
                },
                page: '@sage/xtrem-sales/SalesReturnRequest',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id ?? '' };
                },
            }),
            ui.nestedFields.text({ title: 'Document type', bind: 'documentType', isDisabled: true }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-sales/SalesReturnRequestDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
        ],
    })
    salesLinkedDocuments: ui.fields.Table<SalesReturnRequest & { documentType: string }>;

    @ui.decorators.section<SalesReturnReceipt>({ isHidden: true, title: 'On request quantity change' })
    onRequestQuantityChangeSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnReceipt>({
        parent() {
            return this.onRequestQuantityChangeSection;
        },
        title: '',
        isTitleHidden: true,
    })
    onRequestQuantityChangeBlock: ui.containers.Block;

    @ui.decorators.referenceField<SalesReturnReceipt, UnitOfMeasure>({
        parent() {
            return this.onRequestQuantityChangeBlock;
        },
        title: 'Unit',
        lookupDialogTitle: 'Select unit',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        isTransient: true,
        isReadOnly: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
            ui.nestedFields.technical({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
        ],
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<SalesReturnReceipt>({
        parent() {
            return this.onRequestQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'Remaining quantity',
        bind: 'remainingOnRequestQuantity',
        isReadOnly: true,
    })
    remainingOnRequestQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesReturnReceipt>({
        parent() {
            return this.onRequestQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'Actual quantity',
        bind: 'actualOnRequestQuantity',
        isReadOnly: true,
    })
    actualOnRequestQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<SalesReturnReceipt>({
        parent() {
            return this.onRequestQuantityChangeBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'New quantity',
        bind: 'newOnRequestQuantity',
    })
    newOnRequestQuantity: ui.fields.Numeric;

    @ui.decorators.tableField<SalesReturnReceipt, any>({
        title: 'Sales return request lines - ordering in progress',
        isTransient: true,
        isHidden: true,
        canFilter: false,
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine',
        orderBy: { _sortValue: +1 },
        cardView: true,
        mobileCard: {
            title: ui.nestedFields.text<SalesReturnReceipt, any>({
                title: 'Sales return request',
                bind: 'number',
                isReadOnly: true,
            }),
            titleRight: ui.nestedFields.reference<SalesReturnReceipt, any>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: undefined,
                valueField: 'name',
            }),
            line2Right: ui.nestedFields.numeric<SalesReturnReceipt, any>({
                title: 'Quantity to return',
                bind: 'quantity',
                postfix: (_rowId, rowData) => rowData?.unit?.symbol,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
            }),
            image: ui.nestedFields.image<SalesReturnReceipt, any>({
                bind: { item: { image: true } },
                title: 'Image',
                size: 'small',
            }),
        },
        columns: [
            ui.nestedFields.reference<
                SalesReturnReceipt,
                SalesReturnRequestLineToSalesReturnReceiptLine,
                SalesReturnRequestLine
            >({
                title: 'Sales return request line',
                bind: 'linkedDocument',
                node: '@sage/xtrem-sales/SalesReturnRequestLine',
                valueField: '_id',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.reference<SalesReturnReceipt, SalesReturnRequestLine, Item>({
                        title: 'Item',
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'description' }),
                            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                            ui.nestedFields.technical({ bind: 'minimumSalesQuantity' }),
                            ui.nestedFields.technical({ bind: 'maximumSalesQuantity' }),
                        ],
                    }),
                    ui.nestedFields.technical<SalesReturnReceipt, SalesReturnRequestLine, SalesReturnRequest>({
                        bind: 'document',
                        node: '@sage/xtrem-sales/SalesReturnRequest',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                    ui.nestedFields.technical<SalesReturnReceipt, SalesReturnRequestLine, UnitOfMeasure>({
                        bind: 'unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<
                SalesReturnReceipt,
                SalesReturnRequestLineToSalesReturnReceiptLine,
                SalesReturnReceiptLine
            >({
                title: 'Sales return receipt line',
                bind: 'document',
                node: '@sage/xtrem-sales/SalesReturnReceiptLine',
                valueField: '_id',
                isReadOnly: true,
                columns: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity to return',
                bind: 'quantity',
                postfix: (_rowId, rowData) => rowData?.unit?.symbol,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
            }),
            ui.nestedFields.reference<
                SalesReturnReceipt,
                SalesReturnRequestLineToSalesReturnReceiptLine,
                SalesReturnReceiptLine
            >({
                node: '@sage/xtrem-sales/SalesReturnReceiptLine',
                bind: 'document',
                title: 'Unit',
                valueField: '_id',
                size: 'small',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.reference<SalesReturnReceipt, SalesReturnReceiptLine, UnitOfMeasure>({
                        title: 'Sales unit',
                        bind: 'unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        valueField: 'name',
                        helperTextField: 'symbol',
                        isMandatory: true,
                        isReadOnly: true, // isReadOnly for the moment
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Quantities',
                isDisabled() {
                    return false;
                },

                async onClick(_id: any, rowData: any) {
                    this.remainingOnRequestQuantity.value = rowData.quantity;
                    this.actualOnRequestQuantity.value = rowData.quantity;
                    this.newOnRequestQuantity.value = rowData.quantity;
                    this.unit.value = rowData.salesUnit;
                    this.remainingOnRequestQuantity.scale = this.unit.value?.decimalDigits || 0;
                    this.actualOnRequestQuantity.scale = this.unit.value?.decimalDigits || 0;
                    this.newOnRequestQuantity.scale = this.unit.value?.decimalDigits || 0;
                    this.remainingOnRequestQuantity.postfix = this.unit.value?.symbol;
                    this.actualOnRequestQuantity.postfix = this.unit.value?.symbol;
                    this.newOnRequestQuantity.postfix = this.unit.value?.symbol;
                    this.onRequestQuantityChangeSection.isHidden = false;
                    let response = await this.$.dialog
                        .custom('info', this.onRequestQuantityChangeSection, {
                            cancelButton: { isHidden: false },
                            acceptButton: { isHidden: false },
                            size: 'small',
                        })
                        .then(() => true)
                        .catch(() => false);
                    this.onRequestQuantityChangeSection.isHidden = true;
                    if (
                        response &&
                        this.newOnRequestQuantity.value &&
                        this.newOnRequestQuantity.value > 0 &&
                        this.newOnRequestQuantity.value !== rowData.quantity
                    ) {
                        if (this.newOnRequestQuantity.value <= (this.remainingOnRequestQuantity.value ?? 0)) {
                            response = await confirmDialogWithAcceptButtonText(
                                this,
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_return_receipt__on_return_receipt_quantity_changed_confirmation_title',
                                    'Update return receipt line quantity confirmation?',
                                ),
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_return_receipt__on_return_receipt_quantity_changed_confirmation',
                                    'Update the quantity from the related return receipt line?',
                                ),
                                ui.localize('@sage/xtrem-sales/pages-confirm-confirm', 'Confirm'),
                            );
                            rowData.quantity = this.newOnRequestQuantity.value;
                            const relatedReturnReceiptLine = this.lines.getRecordValue(
                                rowData.salesReturnReceiptLine._id,
                            );
                            if (relatedReturnReceiptLine) {
                                const relatedSalesReturnRequestLineIndex = (
                                    relatedReturnReceiptLine.toReturnRequestLines as Partial<ui.plugin.Dict<any>>[]
                                ).findIndex(
                                    reqLine => reqLine.salesReturnRequestLine === rowData.salesReturnRequestLine._id,
                                );
                                if (
                                    relatedSalesReturnRequestLineIndex !== -1 &&
                                    relatedReturnReceiptLine.toReturnRequestLines
                                ) {
                                    relatedReturnReceiptLine.toReturnRequestLines[
                                        relatedSalesReturnRequestLineIndex
                                    ].quantity = rowData.quantity;
                                }
                                if (response) {
                                    relatedReturnReceiptLine.quantity = String(
                                        Number(relatedReturnReceiptLine.quantity) -
                                            ((this.actualOnRequestQuantity.value ?? 0) -
                                                this.newOnRequestQuantity.value),
                                    );
                                    relatedReturnReceiptLine.quantityInStockUnit = String(
                                        Number(relatedReturnReceiptLine.quantity) *
                                            Number(relatedReturnReceiptLine.unitToStockUnitConversionFactor),
                                    );
                                    this.lines.setRecordValue(relatedReturnReceiptLine);
                                }
                            }
                            this.salesReturnRequestLineToSalesReturnReceiptLines.addOrUpdateRecordValue(rowData);
                        } else {
                            await this.errorOnQuantity(
                                ui.localize(
                                    '@sage/xtrem-sales/pages__sales_return-_receipt__on_return_receipt_quantity_changed_error',
                                    'The new quantity cannot be greater than the actual quantity',
                                ),
                            );
                        }
                    }
                    this.actualOnRequestQuantity.value = 0;
                    this.newOnRequestQuantity.value = 0;
                },
            },
        ],
    })
    salesReturnRequestLineToSalesReturnReceiptLines: ui.fields.Table<
        SalesReturnRequestLineToSalesReturnReceiptLine & { salesReturnReceiptLine: { _id: number } }
    >;

    @ui.decorators.podCollectionField<SalesReturnReceipt, SalesReturnRequestLine>({
        title: 'Return request lines',
        isTitleHidden: true,
        isTransient: true,
        node: '@sage/xtrem-sales/SalesReturnRequestLine',
        canAddRecord: false,
        canRemoveRecord: false,
        columns: [
            ui.nestedFields.link({
                title: 'Sales return request',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map: (_fieldValue, rowData) => `${rowData.document.number}`,
                page: '@sage/xtrem-sales/SalesReturnRequest',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.document._id ?? '' };
                },
            }),
        ],
    })
    podReturnRequestLines: ui.fields.PodCollection<SalesReturnRequestLine>;

    @ui.decorators.section<SalesReturnReceipt>({
        title: 'Posting',
        isHidden() {
            return !this.site.value?.legalCompany?.doStockPosting || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnReceipt>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<SalesReturnReceipt, FinanceTransactionBinding>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table;

    @ui.decorators.textAreaField<SalesReturnReceipt>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'salesReturnReceipt',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.displayStatus.value !== 'closed';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<SalesReturnReceipt>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    @ui.decorators.section<SalesReturnReceipt>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<SalesReturnReceipt>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<SalesReturnReceipt>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<SalesReturnReceipt>({
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    async errorOnQuantity(message: string) {
        await this.$.dialog.message(
            'error',
            ui.localize(
                '@sage/xtrem-sales/pages__sales_return_receipt__on_receipt_quantity_changed_error_title',
                'Error on quantity',
            ),
            message,
        );
    }

    private disableSomeHeaderPropertiesIfLines() {
        const isDisabled = this.lines.value.length > 0;
        this.site.isDisabled = isDisabled;
        this.shipToCustomer.isDisabled = isDisabled;
        this.shipToCustomerAddress.isDisabled = isDisabled;
        this.shipToAddress.isDisabled = isDisabled;
    }

    async fetchDefaultsFromShipToCustomer() {
        await this.$.fetchDefaults(['shipToCustomerAddress', 'shipToAddress']);
    }

    async fetchDefaultsFromShipToAddress() {
        await this.$.fetchDefaults(['shipToAddress']);
    }

    /**
     * Stock detail management.
     * Calls the StockReceiptDetailsPanel and sets the line stockDetails to the return of the StockReceiptDetailsPanel
     */
    async editStockDetails(
        lineData: ui.PartialCollectionValue<
            SalesReturnReceiptLine & {
                location: Location;
                stockStatus: StockStatus;
                lot: Lot;
                expirationDate: string;
            }
        >,
    ) {
        const line = MasterDataUtils.removeExtractEdgesPartial(lineData);
        let salesShipmentBaseDocumentId: integer | undefined;
        if (lineData._id && +lineData._id > 0) {
            salesShipmentBaseDocumentId = await this.getSalesShipmentLineFromSalesReturnReceiptLine(lineData._id);
        } else {
            const linkedDocument = lineData.toReturnRequestLines?.at(0)?.linkedDocument;

            salesShipmentBaseDocumentId =
                lineData.toReturnRequestLines &&
                lineData.toReturnRequestLines.length &&
                linkedDocument &&
                isString(linkedDocument)
                    ? await this.getSalesShipmentLineFromSalesReturnRequestLine(linkedDocument.toString())
                    : undefined;
        }

        await MasterDataUtils.applyPanelToLineIfChanged(
            this.lines,
            StockDetailHelper.editStockDetails(this, line, {
                movementType: 'receipt',
                data: {
                    isEditable: line.stockTransactionStatus
                        ? ['draft', 'error'].includes(line.stockTransactionStatus)
                        : true,
                    fieldCustomizations: { lotNumber: { isHidden: true }, date: { isHidden: true } },
                    effectiveDate: this.date.value ?? '',
                    documentLineSortValue: line._sortValue,
                    documentLine: lineData._id,
                    jsonStockDetails: line.jsonStockDetails,
                    item: line.item?._id,
                    stockSite: this.site.value?._id,
                    quantity: +line.quantityInStockUnit,
                    unit: line.item.stockUnit?._id,
                    trackCheckStock: 'track',
                    stockStatus:
                        line.stockStatus?._id ||
                        (await StockDataUtils.getDefaultQuality({
                            defaultQualityType: 'inboundDefaultQuality',
                            page: this,
                            itemId: line.item?._id || '',
                            site: this.site.value,
                        })),
                    location:
                        line.location?._id ||
                        (await StockDataUtils.getDefaultLocation({
                            defaultLocationType: 'inboundDefaultLocation',
                            page: this,
                            itemId: line.item?._id || '',
                            site: this.site.value,
                        })),
                    existingLot: undefined,
                    lotCreateData: undefined,
                    orderCost: line.orderCost,
                    valuedCost: line.valuedCost,
                    baseDocumentLineSysId: Number(salesShipmentBaseDocumentId),
                    stockTransactionStatus: line.stockTransactionStatus,
                },
            }) as Promise<SalesReturnReceiptLine>,
        );
    }

    async fillPodReturnRequestLines(rowId: any) {
        const oldIsDirty = this.$.isDirty;
        this.podReturnRequestLines.value.forEach(podLine => {
            this.podReturnRequestLines.removeRecord(podLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        linkedDocument: {
                            _id: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                            },
                        },
                    },
                    {
                        filter: { document: { _id: rowId } },
                    },
                ),
            )
            .execute();
        this.podReturnRequestLines.value = result.edges.map(e => e.node.linkedDocument);
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillBaseDocumentLine(rowId: any) {
        const oldIsDirty = this.$.isDirty;

        const resultSalesReturnRequestLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            document: {
                                _id: true,
                            },
                            linkedDocument: {
                                _id: true,
                                document: {
                                    _id: true,
                                    number: true,
                                },
                                status: true,
                            },
                        },
                        {
                            filter: { document: { _id: rowId } },
                        },
                    ),
                )
                .execute(),
        );

        if (resultSalesReturnRequestLines.length) {
            const salesReturnRequestLine = resultSalesReturnRequestLines.shift();
            if (salesReturnRequestLine) {
                this.baseDocumentLine.value = salesReturnRequestLine.linkedDocument;
            }
            this.baseDocumentLine.isHidden = true;
        } else {
            this.baseDocumentLine.isHidden = false;
        }
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async fillSalesLinkedDocuments(rowId: string) {
        const LinkDocument = await this.$.graph
            .node('@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        document: {
                            _id: true,
                        },
                        linkedDocument: {
                            _id: true,
                            document: {
                                _id: true,
                                number: true,
                                status: true,
                                displayStatus: true,
                            },
                        },
                    },
                    {
                        filter: { document: { _id: rowId } },
                    },
                ),
            )
            .execute();

        if (LinkDocument.edges.length) {
            this.salesLinkedDocuments.value = [
                {
                    ...LinkDocument.edges[0].node.linkedDocument.document,
                    documentType: ui.localizeEnumMember('@sage/xtrem-sales/SalesDocumentType', 'salesReturnRequest'),
                } as ui.PartialNodeWithId<SalesReturnRequest & { documentType: string }>,
            ];
        }

        this.salesLinkedDocuments.isHidden = this.salesLinkedDocuments.value.length === 0;
    }

    private async isFieldEditable(rowId: any, rowData: SalesReturnReceiptLine) {
        if (!this.$.recordId) {
            return true;
        }

        if (
            rowData.item.isStockManaged === true &&
            ['inProgress', 'completed'].includes(rowData.stockTransactionStatus)
        ) {
            return false;
        }

        const result = await this.querySalesReturnRequestLineToSalesReturnReceiptLine(rowId);
        if (!result.length) {
            return false;
        }
        const resultLine = result.shift();
        if (resultLine?.linkedDocument.creditStatus !== 'credited' && resultLine?.linkedDocument.status !== 'closed') {
            return true;
        }
        return false;
    }

    private async querySalesReturnRequestLineToSalesReturnReceiptLine(rowId: string) {
        return extractEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesReturnRequestLineToSalesReturnReceiptLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            document: {
                                // SalesReturnReceiptLine
                                _id: true,
                            },
                            linkedDocument: {
                                // ReturnRequestLine
                                _id: true,
                                status: true,
                                creditStatus: true,
                            },
                        },
                        {
                            filter: { document: { _id: rowId } },
                        },
                    ),
                )
                .execute(),
        );
    }

    async getSalesShipmentLineFromSalesReturnRequestLine(returnRequestLineId: string): Promise<integer | undefined> {
        const salesShipmentLines = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesShipmentLineToSalesReturnRequestLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            linkedDocument: { _id: true },
                        },
                        {
                            filter: { document: returnRequestLineId },
                            first: 1,
                        },
                    ),
                )
                .execute(),
        );
        // salesShipmentLines should be unique for each SalesReturnRequestLine
        // but the definition of nodes allow several sales shipment lines per sales return receipt line
        // => take the 1st
        return salesShipmentLines[0] ? +salesShipmentLines[0].linkedDocument._id : undefined;
    }

    async getSalesShipmentLineFromSalesReturnReceiptLine(returnReceiptLineId: string): Promise<integer | undefined> {
        const salesShipmentLines = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-sales/SalesShipmentLineToSalesReturnReceiptLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            linkedDocument: { _id: true },
                        },
                        {
                            filter: { document: returnReceiptLineId },
                            first: 1,
                        },
                    ),
                )
                .execute(),
        );
        // salesShipmentLines should be unique for each SalesReturnReceiptLine
        // but the definition of nodes allow several sales shipment lines per sales return receipt line
        // => take the 1st
        return salesShipmentLines[0] ? +salesShipmentLines[0].linkedDocument._id : undefined;
    }

    _setStepSequenceStatusObject(
        stepSequenceValues: SalesReturnReceiptStepSequenceStatus,
    ): Dict<ui.StepSequenceStatus> {
        return {
            [this.returnReceiptStepSequenceCreate]: stepSequenceValues.create,
            [this.returnReceiptStepSequencePost]: stepSequenceValues.post,
        };
    }

    requestReceiptIsTransferHeaderNote() {
        return this.salesReturnRequestLineToSalesReturnReceiptLines.value.some(
            line => line.linkedDocument?.document?.isTransferHeaderNote === true,
        );
    }

    requestReceiptIsTransferLineNote() {
        return this.salesReturnRequestLineToSalesReturnReceiptLines.value.some(
            line => line.linkedDocument?.document?.isTransferLineNote === true,
        );
    }

    linesFromSingleRequestReceipt() {
        const salesReturnRequestNumbers = this.salesReturnRequestLineToSalesReturnReceiptLines.value.map(
            salesReturnRequestLine => salesReturnRequestLine.linkedDocument?.documentNumber,
        );
        return salesReturnRequestNumbers.every(number => number === salesReturnRequestNumbers[0]);
    }

    lineNotesChanged() {
        return this.lines.value.some(line => line.internalNote?.value);
    }

    headerNotesChanged() {
        return this.internalNote.value !== '';
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
