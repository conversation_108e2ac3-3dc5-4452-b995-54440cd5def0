import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import * as actionsFunctionsDistribution from '@sage/xtrem-distribution/build/lib/client-functions/document-actions-functions';
import * as pillColorDocument from '@sage/xtrem-distribution/build/lib/client-functions/pill-color';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Customer,
    DeliveryDetail,
    DeliveryMode,
    Incoterm,
    Item,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as pillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country } from '@sage/xtrem-structure-api';
import type {
    GraphApi,
    StockTransferOrder,
    StockTransferReceipt,
    StockTransferShipmentDisplayStatus,
    StockTransferShipmentLine,
    StockTransferShipmentLineBinding,
    StockTransferShipment as StockTransferShipmentNode,
} from '@sage/xtrem-supply-chain-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableFieldActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import * as pageFunctions from '../client-functions/stock-transfer-shipment';
import * as actionsFunctions from '../client-functions/stock-transfer-shipment-actions-functions';
import * as displayButtons from '../client-functions/stock-transfer-shipment-display-buttons';
import * as displayButtonsManagement from '../client-functions/stock-transfer-shipment-display-buttons-management';
import * as stepSequence from '../client-functions/stock-transfer-shipment-step-sequence';
import { stockTransferMenu } from '../menu-items/stock-transfer';

@ui.decorators.page<StockTransferShipment, StockTransferShipmentNode>({
    title: 'Stock transfer shipment',
    objectTypeSingular: 'Stock transfer shipment',
    objectTypePlural: 'Stock transfer shipments',
    idField() {
        return this.number;
    },
    menuItem: stockTransferMenu,
    module: 'supply-chain',
    node: '@sage/xtrem-supply-chain/StockTransferShipment',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 200,
    headerLabel() {
        return this.displayStatus;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [this.confirm, this.post, this.repost, this.revert],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            quickActions: [this.print],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this.status.value && this.stockTransactionStatus.value) {
            this.shipmentStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
                this.status.value,
                this.stockTransactionStatus.value,
            );
        }
        displayButtonsManagement._manageDisplayApplicativePageActions(this, isDirty);
        if (this.fromNotificationHistory) {
            this.save.isHidden = false;
        }
    },
    async onLoad() {
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (this.status.value && this.stockTransactionStatus.value) {
            this.shipmentStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
                this.status.value,
                this.stockTransactionStatus.value,
            );
        }
        await pageFunctions.initPage(this);
        displayButtonsManagement._manageDisplayApplicativePageActions(this, false);
        if (this.fromNotificationHistory) {
            this.save.isHidden = false;
        }
        this.$.setPageClean();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { date: -1, number: -1 },
        bulkActions: [
            {
                mutation: 'printBulkStockTransferShipmentPackingSlip',
                title: 'Print packing slip',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
            {
                mutation: 'printBulkStockTransferShipmentPickList',
                title: 'Print pick list',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-supply-chain/StockTransferShipment',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id || '' };
                },
            }),
            line2: ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentNode>({
                bind: 'receivingSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Receiving site',
            }),
            line2Right: ui.nestedFields.date({ bind: 'date', title: 'Shipping date', isMandatory: true }),
            site: ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Shipping site',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem--supply-chain/StockTransferShipmentDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => pillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            isPrinted: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData?.isPrinted ? 'tick' : 'none'),
            }),
            shipToCustomer: ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentNode, Customer>({
                bind: 'shipToCustomer',
                node: '@sage/xtrem-master-data/Customer',
                tunnelPage: '@sage/xtrem-master-data/BusinessEntityCustomer',
                tunnelPageIdField: { businessEntity: { _id: true } },
                valueField: { businessEntity: { name: true } },
                title: 'Ship-to customer',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
                isHidden: true,
            }),
            stockSite: ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentNode>({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Stock site',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
            }),
            deliveryMode: ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentNode>({
                bind: 'deliveryMode',
                node: '@sage/xtrem-master-data/DeliveryMode',
                tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
                valueField: 'name',
                title: 'Delivery mode',
            }),
            deliveryLeadTime: ui.nestedFields.numeric({
                bind: 'deliveryLeadTime',
                title: 'Delivery lead time',
                postfix: 'days',
                isHiddenOnMainField: true,
            }),
            deliveryDate: ui.nestedFields.date({
                bind: 'deliveryDate',
                title: 'Delivery date',
                isHiddenOnMainField: true,
            }),
            status: ui.nestedFields.technical({ bind: 'status' }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            date: ui.nestedFields.technical({ bind: 'date' }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: {
                    displayStatus: { _nin: ['shipped', 'received', 'postingInProgress', 'error'] },
                },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Ready to process', graphQLFilter: { displayStatus: { _eq: 'readyToProcess' } } },
            { title: 'Ready to ship', graphQLFilter: { displayStatus: { _eq: 'readyToShip' } } },
            { title: 'Shipped', graphQLFilter: { displayStatus: { _eq: 'shipped' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
            { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
            { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
        ],
        dropdownActions: [
            {
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    if (recordId && this.status.value && rowItem.number) {
                        await actionsFunctionsDistribution.confirmShipmentAction({
                            isCalledFromRecordPage: false,
                            pageInstance: this,
                            number: rowItem.number,
                            mutation: actionsFunctions.runConfirmShipmentMutation({
                                pageInstance: this,
                                number: rowItem.number,
                            }),
                            beforeMutation: actionsFunctions.areAllLinesAllocated(this),
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonConfirmAction({
                            parameters: { status: rowItem.status },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Post stock',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    if (rowItem.number && recordId) {
                        await actionsFunctions.postAction(this, {
                            recordId,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonPostOrRevertAction({
                            parameters: {
                                status: rowItem.status,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            {
                title: 'Revert',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    if (recordId && rowItem.number) {
                        await actionsFunctionsDistribution.revertAction({
                            isCalledFromRecordPage: true,
                            pageInstance: this,
                            number: rowItem.number,
                            mutation: actionsFunctions.runRevertMutation({
                                pageInstance: this,
                                number: rowItem.number,
                            }),
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    return (
                        displayButtons.isHiddenButtonPostOrRevertAction({
                            parameters: {
                                status: rowItem.status,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                icon: 'print',
                refreshesMainList: 'record',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    if (recordId && rowItem.number && rowItem.status) {
                        await actionsFunctions.printShipmentAction(this, {
                            recordNumber: rowItem.number,
                            recordId: rowItem._id,
                            status: rowItem.status,
                        });
                    }
                },
                isDisabled(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    if (recordId && rowItem) {
                        return (
                            displayButtons.isDisabledButtonPrintAction({
                                parameters: { status: rowItem.status },
                                recordId,
                                isDirty: false,
                            }) || false
                        );
                    }
                    return true;
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-supply-chain/StockTransferShipment',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockTransferShipmentNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status, displayStatus: rowItem.displayStatus || '' },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class StockTransferShipment
    extends ui.Page<GraphApi, StockTransferShipmentNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.pageAction<StockTransferShipment>({
        title: 'Confirm',
        isHidden: true,
        async onClick() {
            if (this.$.recordId && this.status.value && this.number.value) {
                await actionsFunctionsDistribution.confirmShipmentAction({
                    isCalledFromRecordPage: true,
                    pageInstance: this,
                    number: this.number.value,
                    mutation: actionsFunctions.runConfirmShipmentMutation({
                        pageInstance: this,
                        number: this.number.value,
                    }),
                    beforeMutation: actionsFunctions.areAllLinesAllocated(this),
                });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        title: 'Revert',
        isHidden: true,
        async onClick() {
            if (this.$.recordId && this.number.value) {
                await actionsFunctionsDistribution.revertAction({
                    isCalledFromRecordPage: true,
                    pageInstance: this,
                    number: this.number.value,
                    mutation: actionsFunctions.runRevertMutation({
                        pageInstance: this,
                        number: this.number.value,
                    }),
                });
            }
        },
    })
    revert: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        title: 'Save',
        isHidden: true,
        access: { bind: '$update' },
        async onClick() {
            await actionsFunctions.saveAction(this);
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'intersiteTransferOrder',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.displayStatus.value !== 'shipped';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        title: 'Print',
        icon: 'print',
        async onClick() {
            if (this.number.value && this._id.value && this.status.value)
                await actionsFunctions.printShipmentAction(this, {
                    recordNumber: this.number.value,
                    recordId: this.$.recordId ?? '',
                    status: this.status.value,
                });
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<StockTransferShipment>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const documentLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-supply-chain/StockTransferShipment')
                .mutations.repost(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    {
                        stockTransferShipment: this._id.value || '',
                        documentLines,
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-supply-chain/pages__stock_transfer_shipment__repost_errors',
                        'Errors while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.textField<StockTransferShipment>({}) _id: ui.fields.Text;

    @ui.decorators.section<StockTransferShipment>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<StockTransferShipment>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<StockTransferShipment>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<StockTransferShipment>({
        parent() {
            return this.headerBlock;
        },
        options() {
            return stepSequence.getStepSequence();
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    shipmentStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<StockTransferShipment, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Shipping site',
        isMandatory: true,
        isReadOnly: true,
        filter: {
            isInventory: true,
        },
        columns: [
            ui.nestedFields.reference<StockTransferShipment, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [ui.nestedFields.technical({ bind: 'doStockPosting' })],
            }),
        ],
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferShipment, Site>({
        title: 'Stock site',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
        filter: {
            isInventory: true,
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferShipment, Customer>({
        title: 'Ship-to customer',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
    })
    shipToCustomer: ui.fields.Reference<Customer>;

    @ui.decorators.referenceField<StockTransferShipment, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Receiving site',
        isMandatory: true,
        isReadOnly: true,
    })
    receivingSite: ui.fields.Reference<Site>;

    @ui.decorators.textField<StockTransferShipment>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<StockTransferShipment>({
        parent() {
            return this.headerBlock;
        },
        title: 'Shipping date',
        isMandatory: true,
        async onChange() {
            await pageFunctions.updateDeliveryDate(this);
        },
    })
    date: ui.fields.Date;

    @ui.decorators.tile<StockTransferShipment>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<StockTransferShipment>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    stockTransferShipmentLineCount: ui.fields.Count;

    @ui.decorators.dateField<StockTransferShipment>({
        parent() {
            return this.tileContainer;
        },
        title: 'Estimated delivery date',
        bind: 'deliveryDate',
        width: 'medium',
    })
    deliveryDateHeader: ui.fields.Date;

    @ui.decorators.referenceField<StockTransferShipment, BusinessEntityAddress>({
        parent() {
            return this.shippingBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Ship-to address',
        lookupDialogTitle: 'Select ship-to address',
        valueField: 'name',
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary ship-to address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<StockTransferShipment, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<StockTransferShipment, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<StockTransferShipment, BusinessEntityAddress, DeliveryDetail>({
                bind: 'deliveryDetail',
                title: 'Ship-to address',
                node: '@sage/xtrem-master-data/DeliveryDetail',
                valueField: '_id',
                columns: [
                    ui.nestedFields.reference<StockTransferShipment, DeliveryDetail, Incoterm>({
                        bind: 'incoterm',
                        title: 'Incoterms® rule',
                        node: '@sage/xtrem-master-data/Incoterm',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.reference<StockTransferShipment, DeliveryDetail, DeliveryMode>({
                        bind: 'mode',
                        title: 'Delivery mode',
                        node: '@sage/xtrem-master-data/DeliveryMode',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name' })],
                    }),
                    ui.nestedFields.numeric({ bind: 'leadTime', title: 'Delivery lead time', postfix: 'day(s)' }),
                    ui.nestedFields.reference<StockTransferShipment, DeliveryDetail, Site>({
                        bind: 'shipmentSite',
                        title: 'Stock site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.reference<StockTransferShipment, Site, Company>({
                                bind: 'legalCompany',
                                title: 'Company',
                                node: '@sage/xtrem-system/Company',
                                tunnelPage: '@sage/xtrem-master-data/Company',
                                valueField: 'name',
                                helperTextField: 'id',
                                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                            }),
                        ],
                    }),
                ],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.shipToCustomer.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: this.shipToCustomer.value?.businessEntity?._id },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
        async onChange() {
            await pageFunctions.cascadeAndFetchDefaultsFromShipToAddress(this);
        },
    })
    shipToCustomerAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.labelField<StockTransferShipment>({
        title: 'Display status',
        optionType: '@sage/xtrem-supply-chain/StockTransferShipmentDisplayStatus',
        style() {
            return pillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<StockTransferShipmentDisplayStatus>;

    @ui.decorators.labelField<StockTransferShipment>({
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-supply-chain/StockTransferShipmentStatus',
        isHidden: true,
    })
    status: ui.fields.Label;

    @ui.decorators.tableField<StockTransferShipment, StockTransferShipmentLine>({
        parent() {
            return this.itemsSection;
        },
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        hasLineNumbers: true,
        pageSize: 10,
        node: '@sage/xtrem-supply-chain/StockTransferShipmentLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical<StockTransferShipment, StockTransferShipmentLine>({
                bind: 'document',
                node: '@sage/xtrem-supply-chain/StockTransferShipment',
                nestedFields: [ui.nestedFields.text({ bind: '_id' })],
            }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to customer document',
                isExcludedFromMainField: true,
                isHidden: true, // not for now
            }),
            ui.nestedFields.label<StockTransferShipment, StockTransferShipmentLine>({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('LineStatus', rowData?.status),
            }),
            ui.nestedFields.label<StockTransferShipment, StockTransferShipmentLine>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value || '');
                },
                async onClick(_id, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isDisabled: true,
                helperTextField: 'id',
                valueField: 'name',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.checkbox({ bind: 'isExpiryManaged', title: 'Expiration managed' }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<StockTransferShipment, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly() {
                    return this.status.value === 'shipped';
                },
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentLine, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isMandatory: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.label({
                title: 'Receiving status',
                bind: 'receivingStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-distribution/ReceivingStatus',
                style: (_id, rowData) =>
                    pillColorDocument.getLabelColorByStatus('ReceivingStatus', rowData?.receivingStatus),
            }),
            ui.nestedFields.numeric({
                title: 'Allocated quantity',
                bind: 'quantityAllocatedInStockUnit',
                isHidden() {
                    return this.status.value === 'shipped' || !this.$.recordId;
                },
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantityToAllocateInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity',
                bind: 'remainingQuantityInStockUnit',
                isReadOnly: true,
                isHidden: true,
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHidden() {
                    return this.status.value === 'shipped' || !this.$.recordId;
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData?.allocationStatus),
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
        ],
        onChange() {
            pageFunctions.disableSomeHeaderPropertiesIfLines(this);
        },
        optionsMenu: [
            {
                title: 'All statuses',
                graphQLFilter: {},
            },
            {
                title: 'Allocation required',
                graphQLFilter: { allocationStatus: { _nin: ['notManaged', 'allocated'] } },
            },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                async onClick(_id, recordValue: ui.PartialCollectionValue<StockTransferShipmentLineBinding>) {
                    await actionsFunctions.allocateStockDialog(this, _id, recordValue);
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                    return ['shipped', 'received'].includes(this.status.value ?? '') || !rowItem.item?.isStockManaged;
                },
                isDisabled() {
                    return this.$.isDirty || ['shipped', 'received'].includes(this.status.value ?? '');
                },
            },
            {
                icon: 'three_boxes',
                title: 'Issued stock',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                    await actionsFunctions.issueStockDialog(this, rowId, rowItem);
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                    return !['shipped', 'received'].includes(this.status.value ?? '') || !rowItem.item?.isStockManaged;
                },
                isDisabled() {
                    return this.$.isDirty;
                },
            },
            {
                icon: 'three_boxes',
                title: 'Manage allocations',
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                    await actionsFunctions.transferAllocationDialog(this, rowId, rowItem);
                },
                isHidden() {
                    return true;
                },
                // isHidden until XT-82150
                // isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                //     return (
                //         ['shipped', 'draft', 'received'].includes(this.status.value ?? '') ||
                //         !rowItem?.item?.isStockManaged
                //     );
                // },
                isDisabled() {
                    return this.$.isDirty || ['shipped', 'received'].includes(this.status.value ?? '');
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_id, recordValue: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: recordValue.status !== 'shipped' || this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions({
                actions: [],
            });
        },
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
        },
        sidebar: {
            title(_id, recordValue) {
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'three_boxes',
                    title: 'Issued stock',
                    async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                        await actionsFunctions.issueStockDialog(this, rowId, rowItem);
                    },
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                        return (
                            !['shipped', 'received'].includes(this.status.value ?? '') || !rowItem.item?.isStockManaged
                        );
                    },
                    isDisabled() {
                        return this.$.isDirty;
                    },
                },
                {
                    icon: 'three_boxes',
                    title: 'Manage allocations',
                    async onClick(rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                        await actionsFunctions.transferAllocationDialog(this, rowId, rowItem);
                    },
                    isHidden() {
                        return true;
                    },
                    // isHidden until XT-82150
                    // isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                    //     return (
                    //         ['shipped', 'draft', 'received'].includes(this.status.value ?? '') ||
                    //         !rowItem?.item?.isStockManaged
                    //     );
                    // },
                    isDisabled(_rowId: string, rowItem: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                        return (
                            this.$.isDirty ||
                            rowItem.allocationStatus === 'allocated' ||
                            ['shipped', 'received'].includes(this.status.value ?? '')
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_id, recordValue: ui.PartialCollectionValue<StockTransferShipmentLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: recordValue.status !== 'shipped' || this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden() {
                        return ['shipped', 'readyToShip'].includes(this.status.value ?? '');
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    this.externalNoteLine.value = recordValue.externalNote ? recordValue.externalNote.value : '';
                    this.isExternalNoteLine.value = recordValue.isExternalNote || false;
                    this.externalNoteLine.isDisabled =
                        !this.isExternalNoteLine.value || this.status.value === 'shipped';

                    if (+recordValue._id > 0) {
                        await pageFunctions.fillStockTransferOrderLines(this, recordValue._id);
                        await pageFunctions.fillStockTransferReceiptLines(this, recordValue._id);
                    } else {
                        recordValue.stockSite = this.stockSite.value as unknown as Site;
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<StockTransferShipmentLineBinding>,
                        );
                    }
                }
            },
            // @ts-ignore:next-line: Type 'void' is not assignable to type 'Promise<void>'
            onRecordConfirmed(_id, recordValue) {
                const record = recordValue as unknown as ExtractEdgesPartial<StockTransferShipmentLineBinding>;
                if (record) {
                    record.internalNote = {
                        value: this.internalNoteLine.value ? this.internalNoteLine.value : '',
                    };
                    record.externalNote = { value: this.externalNoteLine.value ? this.externalNoteLine.value : '' };
                    record.isExternalNote = this.isExternalNoteLine.value || false;
                    this.lines.addOrUpdateRecordValue(record);
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize(
                            '@sage/xtrem-supply-chain/pages_sidebar_tab_title_information',
                            'Information',
                        ),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'itemDescription'],
                            },
                            stockBlock: {
                                isHidden(_rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged || rowItem?.item.type === 'service';
                                },
                                title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit'],
                            },
                        },
                    },
                    stock: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_stock', 'Stock'),
                        isHidden(_rowId, rowItem) {
                            if (!rowItem?.item.isStockManaged) {
                                return true;
                            }
                            return false;
                        },

                        blocks: {
                            allocation: {
                                title: ui.localize(
                                    '@sage/xtrem-supply-chain/pages_sidebar_block_title_allocation',
                                    'Allocation',
                                ),
                                fields: [
                                    'allocationStatus',
                                    'quantityAllocatedInStockUnit',
                                    'remainingQuantityToAllocateInStockUnit',
                                ],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_origin', 'Origin'),
                        blocks: {
                            mainBlock: {
                                fields: [this.linkToStockTransferOrderLine],
                            },
                        },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_progress', 'Progress'),
                        blocks: {
                            shipping: {
                                title: ui.localize(
                                    '@sage/xtrem-supply-chain/pages_sidebar_block_title_receipt',
                                    'Receipt',
                                ),
                                fields: ['receivingStatus', 'quantityInStockUnit', this.linkToStockTransferReceiptLine],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<StockTransferShipmentLine>;

    @ui.decorators.richTextField<StockTransferShipment>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'shipped';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferShipment>({
        title: 'Add notes to customer document',
        isHidden: true, // not for now
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'shipped';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<StockTransferShipment>({
        isFullWidth: true,
        title: 'Customer line notes',
        isHidden: true, // not for now
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'shipped';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.section<StockTransferShipment>({
        title: 'Information',
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<StockTransferShipment>({
        parent() {
            return this.informationSection;
        },
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<StockTransferShipment>({
        parent() {
            return this.informationBlock;
        },
        title: 'Printed',
        isDisabled: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.labelField<StockTransferShipment>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        isHidden: true,
        parent() {
            return this.informationBlock;
        },
        style() {
            return PillColorStock.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.section<StockTransferShipment>({
        title: 'Shipping',
    })
    shippingSection: ui.containers.Section;

    @ui.decorators.block<StockTransferShipment>({
        parent() {
            return this.shippingSection;
        },
        width: 'large',
        title: 'Shipping',
        isTitleHidden: true,
    })
    shippingBlock: ui.containers.Block;

    @ui.decorators.textField<StockTransferShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Tracking number',
        isHidden: true,
    })
    trackingNumber: ui.fields.Text;

    @ui.decorators.referenceField<StockTransferShipment, Incoterm>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Incoterms® rule',
        lookupDialogTitle: 'Select incoterms rule',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isHidden: true,
    })
    incoterm: ui.fields.Reference<Incoterm>;

    @ui.decorators.vitalPodField<StockTransferShipment, Address>({
        parent() {
            return this.shippingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Ship-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.shipToCustomerAddress.value) {
                const { ...values } = { ...this.shipToCustomerAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.shipToCustomerAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'shipped';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.shipToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'shipped' || !this.shipToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.shipToAddress.isReadOnly = true;
                    if (this.shipToAddress.value) {
                        this.shipToAddress.value.concatenatedAddress = getConcatenatedAddress(this.shipToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'shipped' || this.shipToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Ship-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.shipToAddress.value);
                },
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.dropdownList({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.shipToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    shipToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.referenceField<StockTransferShipment, DeliveryMode>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Delivery mode',
        lookupDialogTitle: 'Select delivery mode',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isMandatory: true,
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.numericField<StockTransferShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Work days',
        isReadOnly: true,
        isHidden: true,
    })
    workDays: ui.fields.Numeric;

    @ui.decorators.numericField<StockTransferShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Delivery lead time',
        isMandatory: true,
        async onChange() {
            if (this.date.value) {
                await pageFunctions.updateDeliveryDate(this);
            }
        },
        postfix: 'day(s)',
    })
    deliveryLeadTime: ui.fields.Numeric;

    @ui.decorators.dateField<StockTransferShipment>({
        parent() {
            return this.shippingBlock;
        },
        title: 'Delivery date',
        isMandatory: true,
    })
    deliveryDate: ui.fields.Date;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockTransferShipment>({
        title: 'Posting',
        isHidden() {
            return !(this.site.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockTransferShipment>({
        parent() {
            return this.postingSection;
        },
        isHidden: true,
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<StockTransferShipment, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        node: '@sage/xtrem-finance-data/FinanceTransaction',
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.documentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<StockTransferShipment>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockTransferShipment>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'stockTransferShipment',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.section<StockTransferShipment>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<StockTransferShipment>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<StockTransferShipment>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'shipped';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferShipment>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to customer document',
        isHidden: true, // not for now
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<StockTransferShipment>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        isHidden: true, // not for now
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'shipped';
        },
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferShipment>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isHidden: true, // not for now
        isDisabled() {
            return this.status.value === 'shipped';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<StockTransferShipment>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isHidden: true, // not for now
        isDisabled() {
            return this.status.value === 'shipped';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.switchField<StockTransferShipment>({
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.tableField<StockTransferShipment, StockTransferShipmentLine>({
        title: 'Order lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-supply-chain/StockTransferOrderLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Order number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-supply-chain/StockTransferOrder',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<StockTransferShipment, StockTransferShipmentLine, StockTransferOrder>({
                bind: 'document',
                node: '@sage/xtrem-supply-chain/StockTransferOrder',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Order status',
                bind: 'status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentLine, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                width: 'large',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
        ],
    })
    linkToStockTransferOrderLine: ui.fields.Table<StockTransferShipmentLine>;

    @ui.decorators.tableField<StockTransferShipment, StockTransferShipmentLine>({
        title: 'Receipt lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-supply-chain/StockTransferReceiptLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Receipt number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-supply-chain/StockTransferReceipt',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<StockTransferShipment, StockTransferShipmentLine, StockTransferReceipt>({
                bind: 'document',
                node: '@sage/xtrem-supply-chain/StockTransferReceipt',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Receipt status',
                bind: 'status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            ui.nestedFields.reference<StockTransferShipment, StockTransferShipmentLine, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                width: 'large',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
        ],
    })
    linkToStockTransferReceiptLine: ui.fields.Table<StockTransferShipmentLine>;

    @ui.decorators.pageAction<StockTransferShipment>({
        title: 'Post stock',
        isHidden: true,
        async onClick() {
            if (this.$.recordId && this.number.value && this._id.value) {
                await actionsFunctions.postAction(this, {
                    recordId: this._id.value,
                });
            }
        },
    })
    post: ui.PageAction;

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.shipToAddress._id) < 0) {
            delete values.shipToAddress._id;
        }
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        return values;
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
