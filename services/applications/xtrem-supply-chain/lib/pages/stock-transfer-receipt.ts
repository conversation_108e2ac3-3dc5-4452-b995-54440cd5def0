import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import * as pillColorDocument from '@sage/xtrem-distribution/build/lib/client-functions/pill-color';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Currency,
    Item,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as pillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country } from '@sage/xtrem-structure-api';
import type {
    GraphApi,
    StockTransferReceiptDisplayStatus,
    StockTransferReceiptLine,
    StockTransferReceiptLineBinding,
    StockTransferReceipt as StockTransferReceiptNode,
    StockTransferShipment,
} from '@sage/xtrem-supply-chain-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableFieldActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import * as pageFunctions from '../client-functions/stock-transfer-receipt';
import * as actionsFunctions from '../client-functions/stock-transfer-receipt-actions-functions';
import * as displayButtons from '../client-functions/stock-transfer-receipt-display-buttons';
import * as displayButtonsManagement from '../client-functions/stock-transfer-receipt-display-buttons-management';
import * as stepSequence from '../client-functions/stock-transfer-receipt-step-sequence';
import { stockTransferMenu } from '../menu-items/stock-transfer';

@ui.decorators.page<StockTransferReceipt, StockTransferReceiptNode>({
    title: 'Stock transfer receipt',
    objectTypeSingular: 'Stock transfer receipt',
    objectTypePlural: 'Stock transfer receipts',
    idField() {
        return this.number;
    },
    menuItem: stockTransferMenu,
    module: 'supply-chain',
    node: '@sage/xtrem-supply-chain/StockTransferReceipt',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 300,
    headerLabel() {
        return this.displayStatus;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [this.post, this.repost],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            quickActions: [],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this.status.value && this.stockTransactionStatus.value) {
            this.receiptStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
                this.status.value,
                this.stockTransactionStatus.value,
            );
        }
        displayButtonsManagement._manageDisplayApplicativePageActions(this, isDirty);
        if (this.fromNotificationHistory) {
            this.save.isHidden = true;
        }
    },
    onLoad() {
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (this.status.value && this.stockTransactionStatus.value) {
            this.receiptStepSequence.statuses = stepSequence.getDisplayStatusStepSequence(
                this.status.value,
                this.stockTransactionStatus.value,
            );
        }
        pageFunctions.initPage(this);
        displayButtonsManagement._manageDisplayApplicativePageActions(this, false);
        if (this.fromNotificationHistory) {
            this.save.isHidden = true;
        }
        this.manageDisplayButtonGoToSysNotificationPageAction();
        this.$.setPageClean();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { date: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-supply-chain/StockTransferReceipt',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id || '' };
                },
            }),
            line2: ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Receiving site',
            }),
            line2Right: ui.nestedFields.date({ bind: 'date', title: 'Receipt date', isMandatory: true }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-supply-chain/StockTransferReceiptDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => pillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            supplier: ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptNode, Supplier>({
                bind: 'supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: '@sage/xtrem-master-data/BusinessEntityCustomer',
                tunnelPageIdField: { businessEntity: { _id: true } },
                valueField: { businessEntity: { name: true } },
                title: 'Supplier',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
                isHidden: true,
            }),
            stockSite: ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptNode>({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                title: 'Stock site',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
            }),
            status: ui.nestedFields.technical({ bind: 'status' }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            date: ui.nestedFields.technical({ bind: 'date' }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: {
                    displayStatus: { _nin: ['received', 'postingInProgress', 'error'] },
                },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Ready to process', graphQLFilter: { displayStatus: { _eq: 'readyToProcess' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
            { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
            { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
        ],
        dropdownActions: [
            {
                title: 'Post stock',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferReceiptNode>) {
                    if (rowItem.number && recordId) {
                        await actionsFunctions.postAction(this, {
                            recordId,
                            date: this.date.value,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<StockTransferReceiptNode>) {
                    return (
                        displayButtons.isHiddenButtonPostOrRevertAction({
                            parameters: {
                                status: rowItem.status,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) || false
                    );
                },
            },
        ],
    },
})
export class StockTransferReceipt
    extends ui.Page<GraphApi, StockTransferReceiptNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.pageAction<StockTransferReceipt>({
        title: 'Save',
        isHidden: true,
        access: { bind: '$update' },
        async onClick() {
            await actionsFunctions.saveAction(this);
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<StockTransferReceipt>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'intersiteTransferOrder',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockTransferReceipt>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.displayStatus.value !== 'received';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<StockTransferReceipt>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    @ui.decorators.textField<StockTransferReceipt>({}) _id: ui.fields.Text;

    @ui.decorators.section<StockTransferReceipt>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<StockTransferReceipt>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<StockTransferReceipt>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<StockTransferReceipt>({
        parent() {
            return this.headerBlock;
        },
        options() {
            return stepSequence.getStepSequence();
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    receiptStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<StockTransferReceipt, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Receiving site',
        isMandatory: true,
        isReadOnly: true,
        filter: {
            isInventory: true,
        },
        columns: [
            ui.nestedFields.reference<StockTransferReceipt, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [ui.nestedFields.technical({ bind: 'doStockPosting' })],
            }),
        ],
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferReceipt, Site>({
        title: 'Stock site',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
        filter: {
            isInventory: true,
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTransferReceipt, Supplier>({
        title: 'Supplier',
        bind: 'businessRelation',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<StockTransferReceipt, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Shipping site',
        isMandatory: true,
        isReadOnly: true,
    })
    shippingSite: ui.fields.Reference<Site>;

    @ui.decorators.textField<StockTransferReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<StockTransferReceipt>({
        parent() {
            return this.headerBlock;
        },
        title: 'Receipt date',
        isMandatory: true,
    })
    date: ui.fields.Date;

    @ui.decorators.tile<StockTransferReceipt>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.countField<StockTransferReceipt>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    stockTransferReceiptLineCount: ui.fields.Count;

    @ui.decorators.referenceField<StockTransferReceipt, BusinessEntityAddress>({
        parent() {
            return this.receivingBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Stock site address',
        lookupDialogTitle: 'Stock site address',
        valueField: 'name',
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({
                bind: { deliveryDetail: { isPrimary: true } },
                title: 'Primary address',
            }),
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<StockTransferReceipt, BusinessEntityAddress, Country>({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<StockTransferReceipt, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            // TODO be able to do { _not: { _eq: null } } & then delete the any
            // https://jira.sage.com/browse/XT-67917
            return this.businessEntityAddress.value?.businessEntity?._id
                ? {
                      businessEntity: { _id: this.businessEntityAddress.value?.businessEntity?._id },
                      deliveryDetail: { _not: { _eq: null } } as any,
                  }
                : {};
        },
    })
    businessEntityAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.labelField<StockTransferReceipt>({
        title: 'Display status',
        optionType: '@sage/xtrem-supply-chain/StockTransferReceiptDisplayStatus',
        style() {
            return pillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<StockTransferReceiptDisplayStatus>;

    @ui.decorators.labelField<StockTransferReceipt>({
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-supply-chain/StockTransferReceiptStatus',
        isHidden: true,
    })
    status: ui.fields.Label;

    @ui.decorators.tableField<StockTransferReceipt, StockTransferReceiptLine>({
        parent() {
            return this.itemsSection;
        },
        bind: 'lines',
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        hasLineNumbers: true,
        pageSize: 10,
        node: '@sage/xtrem-supply-chain/StockTransferReceiptLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical<StockTransferReceipt, StockTransferReceiptLine>({
                bind: 'document',
                node: '@sage/xtrem-supply-chain/StockTransferReceipt',
                nestedFields: [ui.nestedFields.text({ bind: '_id' })],
            }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to supplier document',
                isExcludedFromMainField: true,
                isHidden: true, // not for now
            }),
            ui.nestedFields.label<StockTransferReceipt, StockTransferReceiptLine>({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('LineStatus', rowData?.status),
            }),
            ui.nestedFields.label<StockTransferReceipt, StockTransferReceiptLine>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value || '');
                },
                async onClick(_id, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isDisabled: true,
                helperTextField: 'id',
                valueField: 'name',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.checkbox({ bind: 'isExpiryManaged', title: 'Expiration managed' }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<StockTransferReceipt, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly() {
                    return this.status.value === 'received';
                },
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptLine, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isMandatory: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
                isHidden: (_value, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost unit',
                bind: 'stockCostUnit',
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Stock cost amount',
                bind: 'stockCostAmount',
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.label({
                bind: 'stockDetailStatus',
                title: 'Stock details',
                optionType: '@sage/xtrem-stock-data/StockDetailStatus',
                isMandatory: true,
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockDetailStatus', rowData?.stockDetailStatus),
            }),
            ui.nestedFields.numeric({
                title: 'Actual landed cost in company currency',
                bind: 'actualLandedCostInCompanyCurrency',
                width: 'small',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isHidden() {
                    return (
                        this.status.value === 'draft' ||
                        !this.$.isServiceOptionEnabled('landedCostOption') ||
                        !this.$.isServiceOptionEnabled('landedCostStockTransferOption')
                    );
                },
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({
                bind: 'jsonStockDetails',
            }),
        ],
        optionsMenu: [
            {
                title: 'All statuses',
                graphQLFilter: {},
            },
            { title: 'Stock details required', graphQLFilter: { stockDetailStatus: { _eq: 'required' } } },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'three_boxes',
                title: `Stock details`,
                isHidden(_rowId, line) {
                    return !line.item?.isStockManaged;
                },
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockTransferReceiptLine>) {
                    if (await pageFunctions.isStockDetailEntryEnabled(this)) {
                        if (rowItem !== undefined && typeof rowItem._id === 'string' && +(rowItem?._id ?? 0) > 0) {
                            await pageFunctions.fillStockTransferShipmentLines(this, rowItem._id.toString());
                        }
                        await actionsFunctions.editStockDetailsDialog(this, rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_id, recordValue: ui.PartialCollectionValue<StockTransferReceiptLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: recordValue.status !== 'received' || this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'none',
                title: 'Landed costs',
                isHidden(_rowId, rowItem: ui.PartialCollectionValue<StockTransferReceiptLine>) {
                    return (
                        !rowItem.actualLandedCostInCompanyCurrency ||
                        this.status.value === 'draft' ||
                        !this.$.isServiceOptionEnabled('landedCostOption') ||
                        !this.$.isServiceOptionEnabled('landedCostStockTransferOption')
                    );
                },
                async onClick(rowId) {
                    await this.displayLandedCosts(rowId);
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions({
                actions: [],
            });
        },
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
        },
        sidebar: {
            title(_id, recordValue) {
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'three_boxes',
                    title: `Stock details`,
                    isHidden(_rowId, line) {
                        return !line?.item?.isStockManaged;
                    },
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockTransferReceiptLine>) {
                        if (await pageFunctions.isStockDetailEntryEnabled(this)) {
                            if (rowItem !== undefined && typeof rowItem._id === 'string' && +(rowItem?._id ?? 0) > 0) {
                                await pageFunctions.fillStockTransferShipmentLines(this, rowItem._id.toString());
                            }
                            await actionsFunctions.editStockDetailsDialog(this, rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_id, recordValue: ui.PartialCollectionValue<StockTransferReceiptLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable: recordValue.status !== 'received' || this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                {
                    icon: 'none',
                    title: 'Landed costs',
                    isHidden(_rowId, recordValue: ui.PartialCollectionValue<StockTransferReceiptLine>) {
                        return (
                            !recordValue.actualLandedCostInCompanyCurrency ||
                            !this.$.isServiceOptionEnabled('landedCostOption') ||
                            !this.$.isServiceOptionEnabled('landedCostStockTransferOption')
                        );
                    },
                    async onClick(rowId) {
                        await this.displayLandedCosts(rowId);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    this.externalNoteLine.value = recordValue.externalNote ? recordValue.externalNote.value : '';
                    this.isExternalNoteLine.value = recordValue.isExternalNote || false;
                    this.externalNoteLine.isDisabled =
                        !this.isExternalNoteLine.value || this.status.value === 'received';

                    if (+recordValue._id > 0) {
                        await pageFunctions.fillStockTransferShipmentLines(this, recordValue._id);
                    } else {
                        recordValue.stockSite = this.stockSite.value as unknown as Site;
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<StockTransferReceiptLineBinding>,
                        );
                    }
                }
            },
            // @ts-ignore:next-line: Type 'void' is not assignable to type 'Promise<void>'
            onRecordConfirmed(_id, recordValue) {
                const record = recordValue as unknown as ExtractEdgesPartial<StockTransferReceiptLineBinding>;
                if (record) {
                    record.internalNote = {
                        value: this.internalNoteLine.value ? this.internalNoteLine.value : '',
                    };
                    record.externalNote = { value: this.externalNoteLine.value ? this.externalNoteLine.value : '' };
                    record.isExternalNote = this.isExternalNoteLine.value || false;
                    this.lines.addOrUpdateRecordValue(record);
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize(
                            '@sage/xtrem-supply-chain/pages_sidebar_tab_title_information',
                            'Information',
                        ),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'itemDescription'],
                            },
                            stockBlock: {
                                isHidden(_rowId, rowItem) {
                                    return !rowItem?.item.isStockManaged || rowItem?.item.type === 'service';
                                },
                                title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit'],
                            },
                        },
                    },
                    stock: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_stock', 'Stock'),
                        blocks: {
                            stockCost: {
                                fields: ['stockCostUnit', 'stockCostAmount', 'actualLandedCostInCompanyCurrency'],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_origin', 'Origin'),
                        blocks: {
                            mainBlock: {
                                fields: [this.linkToStockTransferShipmentLine],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-supply-chain/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<StockTransferReceiptLine>;

    @ui.decorators.richTextField<StockTransferReceipt>({
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'received';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferReceipt>({
        title: 'Add notes to supplier document',
        isHidden: true, // not for now
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value || false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'received';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<StockTransferReceipt>({
        isFullWidth: true,
        title: 'Supplier line notes',
        isHidden: true, // not for now
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on supplier documents.',
        isDisabled() {
            return !this.isExternalNoteLine.value || this.status.value === 'received';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.section<StockTransferReceipt>({
        title: 'Information',
        isHidden: true,
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<StockTransferReceipt>({
        parent() {
            return this.informationSection;
        },
        title: 'Information',
        isTitleHidden: true,
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.labelField<StockTransferReceipt>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        isHidden: true,
        parent() {
            return this.informationBlock;
        },
        style() {
            return PillColorStock.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.referenceField<StockTransferReceipt, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Transaction currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        isReadOnly: true,
        isHidden: true,
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.section<StockTransferReceipt>({
        title: 'Receiving',
    })
    receivingSection: ui.containers.Section;

    @ui.decorators.block<StockTransferReceipt>({
        parent() {
            return this.receivingSection;
        },
        width: 'large',
        title: 'Receiving',
        isTitleHidden: true,
    })
    receivingBlock: ui.containers.Block;

    @ui.decorators.vitalPodField<StockTransferReceipt, Address>({
        parent() {
            return this.receivingSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Receiving site address',
        width: 'small',
        onAddButtonClick() {
            if (this.businessEntityAddress.value) {
                const { ...values } = { ...this.businessEntityAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.businessEntityAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'received';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.siteAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'received' || !this.siteAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.siteAddress.isReadOnly = true;
                    if (this.siteAddress.value) {
                        this.siteAddress.value.concatenatedAddress = getConcatenatedAddress(this.siteAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'received' || this.siteAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                title: 'Ship-to address',
                isTitleHidden: true,
                bind: 'concatenatedAddress',
                width: 'large',
                isHidden() {
                    return this.siteAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.siteAddress.value);
                },
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.siteAddress.value);
                },
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.dropdownList({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.siteAddress.isReadOnly === true;
                },
            }),
        ],
    })
    siteAddress: ui.fields.VitalPod<Address>;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockTransferReceipt>({
        title: 'Posting',
        isHidden() {
            return !this.site.value?.legalCompany?.doStockPosting || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockTransferReceipt>({
        parent() {
            return this.postingSection;
        },
        isHidden: true,
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<StockTransferReceipt, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.documentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<StockTransferReceipt>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockTransferReceipt>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'stockTransferReceipt',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.section<StockTransferReceipt>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<StockTransferReceipt>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<StockTransferReceipt>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Internal notes',
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'received';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferReceipt>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to supplier document',
        isHidden: true, // not for now
        onChange() {
            this.externalNote.isDisabled = !this.isExternalNote.value;
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'received';
        },
        width: 'large',
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<StockTransferReceipt>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        title: 'Customer notes',
        isHidden: true, // not for now
        capabilities: validCapabilities,
        helperText: 'Notes display on customer documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'received';
        },
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<StockTransferReceipt>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        isHidden: true, // not for now
        isDisabled() {
            return this.status.value === 'received';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<StockTransferReceipt>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        isHidden: true, // not for now
        isDisabled() {
            return this.status.value === 'received';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.switchField<StockTransferReceipt>({
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.tableField<StockTransferReceipt, StockTransferReceiptLine>({
        title: 'Shipment lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-supply-chain/StockTransferShipmentLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                title: 'Shipment number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                width: 'large',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `${rowData.document.number}`;
                },
                page: '@sage/xtrem-supply-chain/StockTransferShipment',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.document._id ?? '',
                    };
                },
            }),
            ui.nestedFields.technical<StockTransferReceipt, StockTransferReceiptLine, StockTransferShipment>({
                bind: 'document',
                node: '@sage/xtrem-supply-chain/StockTransferShipment',
                nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Shipment status',
                bind: 'status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) => pillColorDocument.getLabelColorByStatus('BaseStatus', rowData?.status),
            }),
            ui.nestedFields.reference<StockTransferReceipt, StockTransferReceiptLine, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                width: 'large',
                unit: (_id, rowData) => rowData?.stockUnit,
                unitMode: 'unitOfMeasure',
            }),
        ],
    })
    linkToStockTransferShipmentLine: ui.fields.Table<StockTransferReceiptLine>;

    @ui.decorators.pageAction<StockTransferReceipt>({
        title: 'Post stock',
        async onClick() {
            if (this.$.recordId) {
                if (this.lines.value.some(line => line.stockDetailStatus === 'required')) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-supply-chain/pages__stock_transfer_receipt_post__stock_details_required',
                            'You need to enter stock details for all lines before you can post.',
                        ),
                        { type: 'error' },
                    );
                    return;
                }
                if (this.number.value && this._id.value) {
                    await actionsFunctions.postAction(this, {
                        recordId: this._id.value,
                        date: this.date.value,
                    });
                }
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockTransferReceipt>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const documentLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-supply-chain/StockTransferReceipt')
                .mutations.repost(
                    {
                        wasSuccessful: true,
                        message: true,
                    },
                    {
                        stockTransferReceipt: this._id.value || '',
                        documentLines,
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-supply-chain/pages__stock_transfer_receipt__repost_errors',
                        'Errors while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.section<StockTransferReceipt>({ title: 'Landed costs', isTitleHidden: true })
    landedCostsSection: ui.containers.Section;

    @ui.decorators.block<StockTransferReceipt>({
        parent() {
            return this.landedCostsSection;
        },
        title: 'Total in company currency',
        width: 'large',
    })
    landedCostsSectionBlock: ui.containers.Block;

    @ui.decorators.numericField<StockTransferReceipt>({
        title: 'Actual landed costs',
        isTransient: true,
        parent() {
            return this.landedCostsSectionBlock;
        },
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
    })
    totalActualLandedCostsInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.textField<StockTransferReceipt>({ isHidden: true, bind: 'jsonAggregateLandedCostTypes' })
    jsonAggregateLandedCostTypes: ui.fields.Text;

    @ui.decorators.tableField<StockTransferReceipt, LandedCostInterfaces.LandedCostTypeSummaryLineBinding>({
        parent() {
            return this.landedCostsSection;
        },
        canSelect: false,
        title: 'Summary by landed cost type',
        isFullWidth: true,
        isTransient: true,
        isReadOnly: true,
        pageSize: 10,
        orderBy: { landedCostType: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Type',
                bind: 'landedCostType',
                optionType: '@sage/xtrem-landed-cost/LandedCostType',
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost amount in company currency',
                bind: 'actualCostAmountInCompanyCurrency',
                unit() {
                    return this.currency.value;
                },
            }),
        ],
    })
    landedCosts: ui.fields.Table<LandedCostInterfaces.LandedCostTypeSummaryLineBinding>;

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.siteAddress._id) < 0) {
            delete values.siteAddress._id;
        }
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        return values;
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }

    async displayLandedCosts(documentLineId: string) {
        await this.$.dialog.page(
            '@sage/xtrem-landed-cost/LandedCostSummary',
            {
                args: JSON.stringify({
                    documentLineId,
                    companyCurrency: this.currency.value?._id ?? '',
                    hideAllocatedAmountColumn: true,
                }),
            },
            { size: 'extra-large' },
        );
    }
}
