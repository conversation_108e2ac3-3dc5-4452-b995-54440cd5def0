import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    GraphApi,
    ProductionTrackingLine,
    ProductionTrackingLineBinding,
    ProductionTracking as ProductionTrackingNode,
    WorkOrder,
} from '@sage/xtrem-manufacturing-api';
import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<ProductionTrackingInquiry, ProductionTrackingNode>({
    menuItem: manufacturingInquiries,
    priority: 50,
    title: 'Production receipt',
    objectTypeSingular: 'Production receipt',
    objectTypePlural: 'Production receipts',
    idField() {
        return this.number;
    },
    mode: 'default',
    node: '@sage/xtrem-manufacturing/ProductionTracking',
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    navigationPanel: {
        orderBy: { effectiveDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.reference<ProductionTrackingInquiry, ProductionTrackingNode, WorkOrder>({
                node: '@sage/xtrem-manufacturing/WorkOrder',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
                bind: 'workOrder',
                valueField: 'number',
                title: 'Work order',
            }),
            workOrderName: ui.nestedFields.text({ bind: { workOrder: { name: true } }, title: 'Work order name' }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date' }),
            line3Right: ui.nestedFields.reference<ProductionTrackingInquiry, ProductionTrackingNode, Site>({
                bind: 'site',
                title: 'Site name',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteID: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            line3: ui.nestedFields.reference<ProductionTrackingInquiry, ProductionTrackingNode, Item>({
                bind: { workOrder: { productionItem: { releasedItem: true } } },
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemID: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { id: true } } } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { description: true } } } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.label({
                title: 'Stock status',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                bind: 'stockTransactionStatus',
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', rowData.stockTransactionStatus),
            }),
        },
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockDetail,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.saveStockDetail,
            cancel: this.$standardCancelAction,
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.post, this.saveStockDetail];
    },
})
export class ProductionTrackingInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<ProductionTrackingInquiry>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ProductionTrackingInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<ProductionTrackingInquiry>({ isHidden: true })
    _id: ui.fields.Text;

    @ui.decorators.textField<ProductionTrackingInquiry>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isDisabled: true,
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<ProductionTrackingInquiry, WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Work order',
        node: '@sage/xtrem-manufacturing/WorkOrder',
        lookupDialogTitle: 'Select work order',
        valueField: 'number',
        helperTextField: undefined,
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: { routingCode: { doSerialNumberPreGeneration: true } } }),
            ui.nestedFields.technical({ bind: { productionItem: { _id: true } } }),
        ],
        width: 'medium',
        isDisabled: true,
    })
    workOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<ProductionTrackingInquiry, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
        ],
        filter: { isActive: { _eq: true } },
        width: 'medium',
        isDisabled: true,
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.dateField<ProductionTrackingInquiry>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
        isDisabled: true,
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.referenceField<ProductionTrackingInquiry, Item>({
        parent() {
            return this.mainBlock;
        },
        title: 'Item name',
        bind: { workOrder: { productionItem: { releasedItem: true } } },
        valueField: 'name',
        isDisabled: true,
    })
    releasedItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<ProductionTrackingInquiry, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isLocationManaged' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
        ],
        filter: { isActive: true },
        width: 'medium',
        isDisabled: true,
    })
    financialSite: ui.fields.Reference<Site>;

    get financialSiteCurrency() {
        const currency = this.financialSite.value?.businessEntity?.currency;
        return {
            _id: currency?._id || '',
            id: currency?.id || '',
            decimalDigits: currency?.decimalDigits || 0,
            symbol: currency?.symbol || '',
        };
    }

    @ui.decorators.labelField<ProductionTrackingInquiry>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', this.stockTransactionStatus.value);
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tableField<ProductionTrackingInquiry, ProductionTrackingLineBinding>({
        title: 'Items',
        bind: 'lines',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-manufacturing/ProductionTrackingLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.label<ProductionTrackingInquiry, ProductionTrackingLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', rowData.stockTransactionStatus),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value || '');
                },
                async onClick(_id, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<ProductionTrackingInquiry, ProductionTrackingLineBinding, Item>({
                title: 'Item',
                valueField: 'name',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                width: 'medium',
                isReadOnly: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.reference<ProductionTrackingInquiry, Item, UnitOfMeasure>({
                        title: 'Unit',
                        valueField: 'name',
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits', title: 'Decimal digits' }),
                        ],
                    }),
                    ui.nestedFields.label({
                        bind: 'lotManagement',
                        title: 'Lot management',
                        optionType: '@sage/xtrem-master-data/LotManagement',
                    }),
                    ui.nestedFields.technical({ bind: 'isExpiryManaged' }),
                ],
            }),
            ui.nestedFields.text({ bind: { item: { id: true } }, title: 'Item ID', isReadOnly: true }),
            ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'releasedQuantity',
                scale: (_val, rowData) => rowData?.item?.stockUnit?.decimalDigits || 0,
                postfix: (_val, rowData) => rowData?.item?.stockUnit?.symbol,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Order cost',
                bind: 'orderCost',
                unit() {
                    return this.financialSiteCurrency;
                },
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Valued cost',
                bind: 'valuedCost',
                unit() {
                    return this.financialSiteCurrency;
                },
                isReadOnly: true,
            }),
            ui.nestedFields.checkbox({
                isReadOnly: true,
                bind: 'completed',
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<ProductionTrackingInquiry, ProductionTrackingLineBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: `Stock details`,
                isHidden: (_rowId, rowItem) => !rowItem || !(rowItem.item && rowItem.releasedQuantity),
                async onClick(rowId, rowItem: ui.PartialCollectionValue<ProductionTrackingLineBinding>) {
                    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                    const productionItemId = this.workOrder.value?.productionItem?._id
                        ? +this.workOrder.value.productionItem._id
                        : 0;
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        StockDetailHelper.editStockDetails(this, line as ui.PartialNodeWithId<any>, {
                            movementType: 'receipt',
                            data: {
                                isEditable: ['draft', 'error'].includes(line.stockTransactionStatus),
                                fieldCustomizations: {
                                    supplierLot: {
                                        isHidden: true,
                                    },
                                },
                                effectiveDate: this.effectiveDate.value ?? undefined,
                                jsonStockDetails: line.jsonStockDetails,
                                documentLineSortValue: line._sortValue,
                                documentLine: rowId,
                                trackCheckStock: 'track',
                                item: line.item?._id,
                                stockSite: this.site.value?._id,
                                quantity: +line.releasedQuantity,
                                unit: line.item.stockUnit?._id,
                                stockStatus: this.site.value?.defaultStockStatus?._id,
                                location: this.site.value?.defaultLocation?._id,
                                stockTransactionStatus: line.stockTransactionStatus,
                                preGeneratedSerialNumbers: this.workOrder.value?.routingCode
                                    ?.doSerialNumberPreGeneration
                                    ? true
                                    : undefined,
                                baseDocumentLineSysId: this.workOrder.value?.routingCode?.doSerialNumberPreGeneration
                                    ? productionItemId
                                    : undefined,
                            },
                        }) as Promise<ProductionTrackingLineBinding>,
                    );
                },
            },
            {
                icon: 'view',
                title: 'Dimensions',
                async onClick(_rowId, rowItem) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            { editable: false },
                        ),
                    );
                },
            },
        ],
    })
    lines: ui.fields.Table<ProductionTrackingLine>;

    @ui.decorators.pageAction<ProductionTrackingInquiry>({
        title: 'Save',
        access: { bind: '$update' },
        async onClick() {
            const validation = await this.$.page.validate();

            const values = [...this.$.values.lines];
            values.map((line, index) => {
                line.item = this.lines.value[index].item;
                return line;
            });

            if (!validation.length) {
                StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(values, this.lines, 'receipt');
                await this.$standardSaveAction.execute();
            }
        },
    })
    saveStockDetail: ui.PageAction;

    @ui.decorators.pageAction<ProductionTrackingInquiry>({
        title: 'Post',
        isDisabled() {
            return this.$.isDirty || !this._id.value;
        },
        isHidden() {
            return !['draft', 'error'].includes(this.stockTransactionStatus.value || '');
        },
        async onClick() {
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-manufacturing/ProductionTracking')
                    .mutations.postToStock(true, { documentIds: [this._id.value] })
                    .execute(),
                this,
            );
            await this.$.router.refresh();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<ProductionTrackingInquiry>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.stockTransactionStatus.value !== 'completed';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<ProductionTrackingInquiry>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.stockTransactionStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;
}
