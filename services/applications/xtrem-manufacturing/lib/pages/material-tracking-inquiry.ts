import { DateValue } from '@sage/xtrem-date-time';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    GraphApi,
    MaterialTrackingLine,
    MaterialTrackingLineBinding,
    MaterialTracking as MaterialTrackingNode,
    WorkOrder,
} from '@sage/xtrem-manufacturing-api';
import type { Item, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { manufacturingInquiries } from '../menu-items/manufacturing-inquiries';

@ui.decorators.page<MaterialTrackingInquiry, MaterialTrackingNode>({
    menuItem: manufacturingInquiries,
    priority: 100,
    title: 'Material issue',
    objectTypeSingular: 'Material issue',
    objectTypePlural: 'Material issues',
    idField() {
        return this.number;
    },
    mode: 'default',
    node: '@sage/xtrem-manufacturing/MaterialTracking',
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    navigationPanel: {
        orderBy: { number: -1, effectiveDate: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.reference<MaterialTrackingInquiry, MaterialTrackingNode, WorkOrder>({
                node: '@sage/xtrem-manufacturing/WorkOrder',
                tunnelPage: '@sage/xtrem-manufacturing/WorkOrder',
                bind: 'workOrder',
                valueField: 'number',
                title: 'Work order',
            }),
            workOrderName: ui.nestedFields.text({ bind: { workOrder: { name: true } }, title: 'Work order name' }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date' }),
            line3Right: ui.nestedFields.reference<MaterialTrackingInquiry, MaterialTrackingNode, Site>({
                title: 'Site name',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteID: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Site ID',
                isHiddenOnMainField: true,
            }),
            line3: ui.nestedFields.reference<MaterialTrackingInquiry, MaterialTrackingNode, Item>({
                bind: { workOrder: { productionItem: { releasedItem: true } } },
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemID: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { id: true } } } },
                title: 'Item ID',
            }),
            itemDescription: ui.nestedFields.text({
                bind: { workOrder: { productionItem: { releasedItem: { description: true } } } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.label({
                title: 'Stock status',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                bind: 'stockTransactionStatus',
                style: (_id, rowValue) =>
                    PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', rowValue.stockTransactionStatus),
            }),
        },
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockDetail,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.saveStockDetail,
            cancel: this.$standardCancelAction,
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.post, this.saveStockDetail];
    },
})
export class MaterialTrackingInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<MaterialTrackingInquiry>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<MaterialTrackingInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<MaterialTrackingInquiry>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<MaterialTrackingInquiry>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isDisabled: true,
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<MaterialTrackingInquiry, WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Work order',
        valueField: 'number',
        width: 'medium',
        isDisabled: true,
        helperTextField: undefined,
    })
    workOrder: ui.fields.Reference<WorkOrder>;

    @ui.decorators.referenceField<MaterialTrackingInquiry, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        width: 'medium',
        isDisabled: true,
    })
    site: ui.fields.Reference;

    @ui.decorators.dateField<MaterialTrackingInquiry>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
        isDisabled: true,
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.referenceField<MaterialTrackingInquiry, WorkOrder>({
        parent() {
            return this.mainBlock;
        },
        title: 'Item name',
        bind: { workOrder: { productionItem: { releasedItem: true } } },
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        isDisabled: true,
    })
    releasedItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<MaterialTrackingInquiry, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
        ],
        filter() {
            return { isActive: { _eq: true } };
        },
        placeholder: 'Select ...',
        width: 'medium',
        isDisabled: true,
    })
    financialSite: ui.fields.Reference;

    // TODO: Left here until we implement new stock service
    // @ui.decorators.labelField<MaterialTrackingInquiry>({
    //     title: 'Status',
    //     optionType: '@sage/xtrem-stock/StockDocumentStatus',
    //     parent() {
    //         return this.mainBlock;
    //     },
    //     backgroundColor(status) {
    //         return StockDocumentHelper.stockDocumentTransactionStatusColor(status);
    //     },
    //     borderColor(status) {
    //         return StockDocumentHelper.stockDocumentTransactionStatusColor(status);
    //     },
    // })
    // status: ui.fields.Label;

    @ui.decorators.labelField<MaterialTrackingInquiry>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', this.stockTransactionStatus.value);
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tableField<MaterialTrackingInquiry, MaterialTrackingLineBinding>({
        title: 'Components',
        bind: 'lines',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-manufacturing/MaterialTrackingLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.label<MaterialTrackingInquiry, MaterialTrackingLineBinding>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowValue) =>
                    PillColor.getLabelColorByStatus('StockDocumentTransactionStatus', rowValue.stockTransactionStatus),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value);
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference({
                title: 'Component number',
                bind: 'workOrderLine',
                node: '@sage/xtrem-manufacturing/WorkOrderComponent',
                valueField: 'componentNumber',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<MaterialTrackingInquiry, MaterialTrackingLineBinding, Item>({
                title: 'Item',
                valueField: 'name',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                width: 'medium',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                    }),
                    ui.nestedFields.reference<MaterialTrackingInquiry, Item, UnitOfMeasure>({
                        title: 'Unit',
                        valueField: 'name',
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.label({
                        bind: 'lotManagement',
                        title: 'Lot management',
                        optionType: '@sage/xtrem-master-data/LotManagement',
                    }),
                    ui.nestedFields.checkbox({ bind: 'isExpiryManaged', isHidden: true }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: { item: { description: true } },
                isReadOnly: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantityInStockUnit',
                scale(_val, rowData) {
                    return rowData.item?.stockUnit?.decimalDigits || 0;
                },
                postfix(_val, rowData) {
                    return rowData.item?.stockUnit?.symbol;
                },
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: `Stock details`,
                isHidden(_rowId, rowItem) {
                    return (
                        !rowItem ||
                        !(rowItem.item && rowItem.quantityInStockUnit) ||
                        ['draft', 'error'].includes(rowItem.stockTransactionStatus)
                    );
                },
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<MaterialTrackingLineBinding>) {
                    const line = utils.removeExtractEdgesPartial(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        StockDetailHelper.editStockDetails(this, line, {
                            movementType: 'issue',
                            data: {
                                isEditable: false,
                                effectiveDate: this.effectiveDate.value,
                                documentLineSortValue: line._sortValue,
                                documentLine: rowId,
                                jsonStockDetails: line.jsonStockDetails,
                                item: line.item?._id,
                                stockSite: this.site.value?._id,
                                quantity: +line.quantityInStockUnit,
                                number: this.number.value,
                                unit: line.item.stockUnit?._id,
                                searchCriteria: {
                                    activeQuantityInStockUnit: +line.quantityInStockUnit,
                                    item: line.item?._id,
                                    site: this.site.value?._id,
                                    stockUnit: line.item.stockUnit?._id,
                                    statusList: [],
                                },
                            },
                        }) as Promise<MaterialTrackingLineBinding>,
                    );
                },
            },
            {
                icon: 'edit',
                title: `Allocate stock`,
                isHidden(_rowId, rowItem) {
                    return (
                        !rowItem ||
                        !(rowItem.item && rowItem.quantityInStockUnit) ||
                        !['draft', 'error'].includes(rowItem.stockTransactionStatus)
                    );
                },
                async onClick(rowId: string, rowItem: ui.PartialNodeWithId<MaterialTrackingLineBinding>) {
                    await utils.confirmDialogToBoolean(
                        StockDetailHelper.editStockDetails(this, rowItem, {
                            movementType: 'allocation',
                            data: {
                                isEditable: ['draft', 'error'].includes(rowItem.stockTransactionStatus),
                                needFullAllocation: false,
                                cannotOverAllocate: true,
                                documentLineHasAlreadySomeAllocations: Number(rowItem.quantityAllocated) > 0,
                                documentLineSortValue: rowItem._sortValue,
                                documentLine: rowId,
                                item: rowItem.item?._id,
                                stockSite: this.site.value?._id,
                                quantity: rowItem.quantityInStockUnit ? +rowItem.quantityInStockUnit : 0,
                                unit: rowItem.item!.stockUnit?._id,
                                searchCriteria: {
                                    activeQuantityInStockUnit: rowItem.quantityInStockUnit
                                        ? +rowItem.quantityInStockUnit
                                        : 0,
                                    item: rowItem.item!._id,
                                    site: this.site.value?._id,
                                    stockUnit: rowItem.item!.stockUnit?._id,
                                    statusList: [{ statusType: 'accepted' }],
                                    ignoreExpirationDate: true,
                                },
                            },
                        }),
                    );
                },
            },
            {
                icon: 'view',
                title: 'Dimensions',
                async onClick(_rowId: any, rowItem: any) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: false,
                            },
                        ),
                    );
                },
            },
        ],
    })
    lines: ui.fields.Table<MaterialTrackingLine>;

    @ui.decorators.pageAction<MaterialTrackingInquiry>({
        title: 'Save',
        access: {
            bind: '$update',
        },
        async onClick() {
            const validation = await this.$.page.validate();

            if (!validation.length) {
                StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(
                    this.$.values.lines,
                    this.lines,
                    'issue',
                );
                await this.$standardSaveAction.execute();
            }
        },
    })
    saveStockDetail: ui.PageAction;

    @ui.decorators.pageAction<MaterialTrackingInquiry>({
        title: 'Post',
        isDisabled() {
            return this.$.isDirty || !this._id.value;
        },
        isHidden() {
            return (
                !['draft', 'error'].includes(this.stockTransactionStatus.value ?? '') ||
                this.workOrder.value?.status === 'closed'
            );
        },
        async onClick() {
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-manufacturing/MaterialTracking')
                    .mutations.postToStock(true, { documentIds: [this._id.value] })
                    .execute(),
                this,
            );
            await this.$.router.refresh();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<MaterialTrackingInquiry>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.stockTransactionStatus.value !== 'completed';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<MaterialTrackingInquiry>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.stockTransactionStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;
}
