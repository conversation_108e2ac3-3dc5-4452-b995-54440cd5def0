import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api-partial';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    CurrencyBinding,
    ItemBinding,
    LocationBinding,
    ReasonCode,
    SequenceNumberBinding,
    UnitOfMeasureBinding,
} from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stock } from '@sage/xtrem-master-data/build/lib/menu-items/stock';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type {
    GraphApi,
    StockReceiptDisplayStatus,
    StockReceiptLineBinding,
    StockReceipt as StockReceiptNode,
} from '@sage/xtrem-stock-api';
import type { StockDetailStatus, StockDocumentTransactionStatus, StockStatusBinding } from '@sage/xtrem-stock-data-api';
import * as StockPillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import { getDefaultLocation, getDefaultQuality } from '@sage/xtrem-stock-data/lib/client-functions/utils';
import type { CompanyBinding, SiteBinding } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { deleteDialogWithAcceptButtonText, getSite } from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-stock-receipt';
import type * as StockInterfaces from '../client-functions/interfaces';
import * as actionFunctions from '../client-functions/stock-receipt-action-functions';
import * as StockValuationLib from '../client-functions/stock-valuation-lib';

@ui.decorators.page<StockReceipt, StockReceiptNode>({
    menuItem: stock,
    priority: 150,
    title: 'Stock receipt',
    objectTypeSingular: 'Stock receipt',
    objectTypePlural: 'Stock receipts',
    headerSection() {
        return this.mainSection;
    },
    hasAttachmentsSection: true,
    mode: 'tabs',
    idField() {
        return this.number;
    },
    node: '@sage/xtrem-stock/StockReceipt',
    headerLabel() {
        return this.displayStatus;
    },
    navigationPanel: {
        orderBy: { effectiveDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({ bind: 'number', title: 'Number', isMandatory: true }),
            line2: ui.nestedFields.reference({
                bind: 'stockSite',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date', isMandatory: true }),
            reasonCode: ui.nestedFields.reference({
                bind: 'reasonCode',
                valueField: 'id',
                node: '@sage/xtrem-master-data/ReasonCode',
                title: 'Reason',
                tunnelPage: undefined,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                isMandatory: true,
                optionType: '@sage/xtrem-stock/StockReceiptDisplayStatus',
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            description: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
            isSetDimensionsMainListHidden: ui.nestedFields.technical({ bind: 'isSetDimensionsMainListHidden' }),
            stockTransactionStatus: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
        },
        dropdownActions: [
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<StockReceiptNode>) {
                    const site = await getSite({
                        stockPage: this,
                        siteId: rowItem.stockSite?._id ?? '',
                    });
                    await actionFunctions.setDimensions({
                        stockReceiptPage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        site,
                    });
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<StockReceiptNode>) {
                    return rowItem.isSetDimensionsMainListHidden || false;
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<StockReceiptNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-stock/StockReceipt',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<StockReceiptNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { stockTransactionStatus: rowItem.stockTransactionStatus },
                        recordId,
                    });
                },
            },
        ],
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _ne: 'received' } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Details required', graphQLFilter: { displayStatus: { _eq: 'detailsRequired' } } },
            { title: 'Details entered', graphQLFilter: { displayStatus: { _eq: 'detailsEntered' } } },
            { title: 'Stock posting in progress', graphQLFilter: { displayStatus: { _eq: 'stockPostingInProgress' } } },
            { title: 'Stock posting error', graphQLFilter: { displayStatus: { _eq: 'stockPostingError' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
        ],
    },
    onLoad() {
        this.initPosting();
        this.manageDisplayButtonGoToSysNotificationPageAction();
        this.manageDisplayLinePhantomRow();
        if (this._id.value) {
            this.receiptStepSequence.statuses = actionFunctions.getDisplayStatusStepSequence(
                this._id.value,
                this.displayStatus.value as StockReceiptDisplayStatus,
            );
        }
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message || '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
        return this.init();
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.repost, this.post, this.saveStockReceipt];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this._id.value) {
            this.receiptStepSequence.statuses = actionFunctions.getDisplayStatusStepSequence(
                this._id.value,
                this.displayStatus.value as StockReceiptDisplayStatus,
            );
        }
        setApplicativePageCrudActions({
            page: this,
            isDirty: isDirty && !this.fromNotificationHistory,
            save: this.saveStockReceipt,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
    },
})
export class StockReceipt
    extends ui.Page<GraphApi, StockReceiptNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.section<StockReceipt>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockReceipt>({
        parent() {
            return this.mainSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<StockReceipt>({
        isTitleHidden: true,
        width: 'extra-large',
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<StockReceipt>({
        title: 'Lines',
    })
    linesSection: ui.containers.Section;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockReceipt>({
        title: 'Posting',
        isHidden() {
            return !(this.stockSite.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockReceipt>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
        isHidden: true,
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<StockReceipt, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<StockReceipt>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockReceipt>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'miscellaneousStockReceipt',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.textField<StockReceipt>({})
    _id: ui.fields.Text;

    @ui.decorators.stepSequenceField<StockReceipt>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options: actionFunctions.receiptStepSequenceOptions,
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    receiptStepSequence: ui.fields.StepSequence;

    @ui.decorators.checkboxField<StockReceipt>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    isSetDimensionsMainListHidden: ui.fields.Checkbox;

    @ui.decorators.textField<StockReceipt>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        width: 'small-medium',
        isReadOnly: true,
        maxLength: 24,
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockReceipt, SiteBinding>({
        isMandatory: true,
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        placeholder: 'Select site',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.checkbox({ isHidden: true, bind: 'isLocationManaged' }),
            ui.nestedFields.technical<StockReceipt, SiteBinding, LocationBinding>({
                bind: 'defaultLocation',
                node: '@sage/xtrem-master-data/Location',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<StockReceipt, SiteBinding, LocationBinding>({
                bind: 'defaultStockStatus',
                node: '@sage/xtrem-stock-data/StockStatus',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.reference<StockReceipt, SiteBinding, CurrencyBinding>({
                bind: 'financialCurrency',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Currency',
                isHidden: true,
                columns: [ui.nestedFields.text({ bind: 'symbol' }), ui.nestedFields.numeric({ bind: 'decimalDigits' })],
            }),
            ui.nestedFields.reference<StockReceipt, SiteBinding, CompanyBinding>({
                bind: 'legalCompany',
                valueField: 'name',
                node: '@sage/xtrem-system/Company',
                isHidden: true,
                columns: [
                    ui.nestedFields.checkbox({
                        bind: 'doStockPosting',
                    }),
                    ui.nestedFields.reference<StockReceipt, CompanyBinding, CurrencyBinding>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
        ],
        parent() {
            return this.mainBlock;
        },
        width: 'small-medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        async onChange() {
            this.manageDisplayLinePhantomRow();
            if (this.stockSite.value?.isLocationManaged) {
                this.lines.showColumn('location');
            } else {
                this.lines.hideColumn('location');
            }
            await this.lines.redraw();
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                site: this.stockSite.value,
            });
        },
    })
    stockSite: ui.fields.Reference<SiteBinding>;

    @ui.decorators.dateField<StockReceipt>({
        isMandatory: true,
        title: 'Date',
        maxDate: DateValue.today().toString(),
        parent() {
            return this.mainBlock;
        },
        width: 'small-medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        fetchesDefaults: true,
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.referenceField<StockReceipt>({
        title: 'Reason',
        node: '@sage/xtrem-master-data/ReasonCode',
        lookupDialogTitle: 'Select reason',
        minLookupCharacters: 0,
        placeholder: 'Select reason',
        width: 'small-medium',
        parent() {
            return this.mainBlock;
        },
    })
    reasonCode: ui.fields.Reference<ReasonCode>;

    @ui.decorators.textField<StockReceipt>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        width: 'small-medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    description: ui.fields.Text;

    @ui.decorators.labelField<StockReceipt>({
        title: 'Display status',
        optionType: '@sage/xtrem-stock/StockReceiptDisplayStatus',
        style() {
            return StockPillColor.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<StockReceiptDisplayStatus>;

    @ui.decorators.labelField<StockReceipt>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        style() {
            return StockPillColor.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.tableField<StockReceipt, StockInterfaces.StockReceiptLinePageBinding>({
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        bind: 'lines',
        node: '@sage/xtrem-stock/StockReceiptLine',
        orderBy: {
            _sortValue: +1,
        },
        columns: [
            ui.nestedFields.technical<StockReceipt, StockInterfaces.StockReceiptLinePageBinding>({
                bind: 'stockTransactionStatus',
            }),
            ui.nestedFields.label<StockReceipt, StockInterfaces.StockReceiptLinePageBinding>({
                title: 'Status',
                bind: 'displayStatus',
                optionType: '@sage/xtrem-stock/StockReceiptLineDisplayStatus',
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.reference<StockReceipt, StockInterfaces.StockReceiptLinePageBinding, ItemBinding>({
                isMandatory: true,
                shouldSuggestionsIncludeColumns: true,
                lookupDialogTitle: 'Select item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                bind: 'item',
                valueField: 'name',
                placeholder: 'Select item',
                helperTextField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical<StockReceipt, ItemBinding, UnitOfMeasureBinding>({
                        bind: { stockUnit: { name: true } },
                    }),
                    ui.nestedFields.technical<StockReceipt, ItemBinding, UnitOfMeasureBinding>({
                        bind: { stockUnit: { id: true } },
                    }),
                    ui.nestedFields.technical<StockReceipt, ItemBinding, UnitOfMeasureBinding>({
                        bind: { stockUnit: { symbol: true } },
                    }),
                    ui.nestedFields.technical<StockReceipt, ItemBinding, UnitOfMeasureBinding>({
                        bind: { stockUnit: { decimalDigits: true } },
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.technical({ bind: 'serialNumberManagement' }),
                    ui.nestedFields.checkbox({ isHidden: true, bind: 'isStockManaged' }),
                    ui.nestedFields.checkbox({ isHidden: true, bind: 'isExpiryManaged' }),
                    ui.nestedFields.reference<StockReceipt, ItemBinding, SequenceNumberBinding>({
                        isHidden: true,
                        bind: 'lotSequenceNumber',
                        node: '@sage/xtrem-master-data/SequenceNumber',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.technical({ bind: { image: { value: true } } }),
                ],
                isDisabled(_rowId, rowData: ui.PartialCollectionValue<StockReceiptLineBinding> | undefined) {
                    return Number(rowData?._id) > 0 || Number(rowData?.jsonStockDetails?.length) > 0;
                },
                filter() {
                    return {
                        itemSites: { _atLeast: 1, site: { _id: this.stockSite.value?._id } },
                    };
                },
                orderBy: {
                    name: +1,
                    id: +1,
                },
                async onChange(_id: number, rowData: ui.PartialNodeWithId<StockReceiptLineBinding>) {
                    const { storedAttributes, storedDimensions } =
                        await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                            page: this,
                            _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                            dimensionDefinitionLevel: 'stockDirect',
                            site: this.stockSite.value,
                            item: rowData.item ? rowData.item : undefined,
                        });
                    const defaultLocationId = await StockDataUtils.getDefaultLocation({
                        defaultLocationType: 'inboundDefaultLocation',
                        page: this,
                        itemId: rowData.item?._id || '',
                        site: this.stockSite.value,
                    });
                    const location = defaultLocationId
                        ? await StockDataUtils.getLocationInfo(this, defaultLocationId)
                        : undefined;
                    const defaultQualityId = await StockDataUtils.getDefaultQuality({
                        defaultQualityType: 'inboundDefaultQuality',
                        page: this,
                        itemId: rowData.item?._id || '',
                        site: this.stockSite.value,
                    });
                    const stockStatus = defaultQualityId
                        ? await StockDataUtils.getStatusInfo(this, defaultQualityId)
                        : undefined;
                    rowData.storedAttributes = storedAttributes;
                    rowData.storedDimensions = storedDimensions;
                    rowData.location = location;
                    rowData.stockStatus = stockStatus;
                    rowData.stockDetailStatus = rowData.item?.isStockManaged ? 'required' : 'notRequired';
                    rowData.displayStatus =
                        rowData.stockDetailStatus === 'required' ? 'detailsRequired' : 'detailsEntered';
                    rowData.orderCost = (
                        await StockDataUtils.getLineCost(this, {
                            item: rowData.item?._id,
                            site: this.stockSite.value?._id,
                            dateOfValuation: this.effectiveDate.value,
                        })
                    ).orderCost;
                    rowData.valuedCost = (
                        await StockDataUtils.getLineCost(this, {
                            item: rowData.item?._id,
                            site: this.stockSite.value?._id,
                            dateOfValuation: this.effectiveDate.value,
                        })
                    ).orderCost;
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                width: 'large',
                bind: { item: { description: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                isMandatory: true,
                title: 'Quantity',
                bind: 'quantityInStockUnit',
                isReadOnly(_rowId, rowData) {
                    return StockReceipt.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                unit: (_rowId, rowData) => rowData?.item?.stockUnit,
                unitMode: 'unitOfMeasure',
                validation(val) {
                    if (val <= 0) {
                        return ui.localize(
                            '@sage/xtrem-stock/pages__stock_receipt__quantity_must_be_positive',
                            'You need to enter a value greater than 0.',
                        );
                    }
                    return undefined;
                },
                onChange(_rowId, rowData: ui.PartialNodeWithId<StockReceiptLineBinding>) {
                    rowData.totalCost = StockReceipt.calculateTotalCost(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.reference<StockReceipt, StockReceiptLineBinding, LocationBinding>({
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                bind: 'location',
                valueField: 'name',
                minLookupCharacters: 1,
                lookupDialogTitle: 'Select location',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.reference<StockReceipt, LocationBinding, LocationBinding['locationZone']>({
                        bind: 'locationZone',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: '_id',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.reference<
                                StockReceipt,
                                LocationBinding['locationZone'],
                                LocationBinding['locationZone']['site']
                            >({
                                bind: 'site',
                                node: '@sage/xtrem-system/Site',
                                valueField: '_id',
                            }),
                        ],
                    }),
                ],
                isReadOnly(_rowId, rowData) {
                    return StockReceipt.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                filter() {
                    return {
                        locationZone: { site: { _id: this.stockSite.value?._id } },
                        locationType: { locationCategory: { _ne: 'virtual' } },
                    };
                },
                isHidden() {
                    return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                },
            }),
            ui.nestedFields.reference<StockReceipt, StockReceiptLineBinding, StockStatusBinding>({
                title: 'Quality control',
                bind: 'stockStatus',
                lookupDialogTitle: 'Select quality value',
                minLookupCharacters: 0,
                isReadOnly(_rowId, rowData) {
                    return StockReceipt.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Expected cost',
                bind: 'valuedCost',
                isHiddenOnMainField: true,
                unit() {
                    return this.stockSiteCurrency;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost',
                bind: 'orderCost',
                isHiddenOnMainField: true,
                isReadOnly(_rowId, rowData) {
                    return StockReceipt.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                unit() {
                    return this.stockSiteCurrency;
                },
                validation(val) {
                    if (val < 0) {
                        return ui.localize(
                            '@sage/xtrem-stock/pages__stock_receipt__actual_cost_cannot_be_negative',
                            'You need to enter a value greater than or equal to 0.',
                        );
                    }
                    return undefined;
                },
                onChange(_rowId, rowData: ui.PartialNodeWithId<StockReceiptLineBinding>) {
                    rowData.totalCost = StockReceipt.calculateTotalCost(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.technical<StockReceipt, StockReceiptLineBinding>({ bind: 'stockDetailStatus' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                ],
            }),
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<StockReceipt, StockInterfaces.StockReceiptLinePageBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
            ui.nestedFields.technical({ bind: 'totalCost' }),
        ],
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            {
                title: 'All open statuses',
                id: 'allOpenStatuses',
                graphQLFilter: { displayStatus: { _ne: 'received' } },
            },
            { title: 'Details required', graphQLFilter: { displayStatus: { _eq: 'detailsRequired' } } },
            { title: 'Details entered', graphQLFilter: { displayStatus: { _eq: 'detailsEntered' } } },
            { title: 'Stock posting in progress', graphQLFilter: { displayStatus: { _eq: 'stockPostingInProgress' } } },
            { title: 'Stock posting error', graphQLFilter: { displayStatus: { _eq: 'stockPostingError' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        optionsMenuType: 'dropdown',
        dropdownActions: [
            {
                icon: 'three_boxes',
                title: `Stock details`,
                isHidden(_rowId, rowData) {
                    return rowData && !(rowData.item && rowData.quantityInStockUnit && rowData.item.isStockManaged);
                },
                async onClick(rowId, rowItem: ui.PartialCollectionValue<StockInterfaces.StockReceiptLinePageBinding>) {
                    const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                    try {
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            StockDetailHelper.editStockDetails(this, line, {
                                movementType: 'receipt',
                                data: {
                                    isEditable: !StockReceipt.lineStatusReadonly(line.stockTransactionStatus),
                                    effectiveDate: this.effectiveDate.value || undefined,
                                    baseDocumentLineSysId: Number(line._id),
                                    documentLineSortValue: line._sortValue,
                                    documentLine: rowId,
                                    jsonStockDetails: line.jsonStockDetails,
                                    item: line.item?._id,
                                    stockSite: this.stockSite.value?._id,
                                    quantity: +line.quantityInStockUnit,
                                    unit: line.item.stockUnit?._id,
                                    trackCheckStock: 'track',
                                    stockStatus:
                                        line.stockStatus?._id ||
                                        (await getDefaultQuality({
                                            defaultQualityType: 'inboundDefaultQuality',
                                            page: this,
                                            itemId: line.item?._id || '',
                                            site: this.stockSite.value,
                                        })), // this.stockSite.value?.defaultStockStatus?._id,
                                    location:
                                        line.location?._id ||
                                        (await getDefaultLocation({
                                            defaultLocationType: 'inboundDefaultLocation',
                                            page: this,
                                            itemId: line.item?._id || '',
                                            site: this.stockSite.value,
                                        })), // this.stockSite.value?.defaultLocation?._id,
                                    existingLot: undefined,
                                    lotCreateData: undefined,
                                    orderCost: line.orderCost,
                                    valuedCost: line.valuedCost,
                                    stockTransactionStatus: line.stockTransactionStatus,
                                    fieldCustomizations: {
                                        date: {
                                            isHidden: true,
                                        },
                                    },
                                },
                            }) as Promise<StockInterfaces.StockReceiptLinePageBinding>,
                        );
                    } catch (err) {
                        console.error('Stock details onClick function', err);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockReceiptLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable:
                                    !StockReceipt.lineStatusReadonly(rowData?.stockTransactionStatus) ||
                                    this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowId, rowItem) {
                    return StockReceipt.lineStatusReadonly(rowItem.stockTransactionStatus);
                },
                async onClick(rowId) {
                    await this.deleteLine(rowId);
                },
            },
        ],
        onRowAdded() {
            this.disableHeaderFields();
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-stock/StockReceiptLineDisplayStatus',
                style: (_id, rowData) => StockPillColor.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line2: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Description',
                canFilter: false,
            }),
            line2Right: ui.nestedFields.numeric({
                title: 'Price',
                bind: 'totalCost',
                unit() {
                    return this.stockSiteCurrency;
                },
            }),
            line3Right: ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantityInStockUnit',
                unit: (_rowId, rowData) => rowData?.item?.stockUnit,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (!recordValue || +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-stock/edit-create-line', 'Add new line');
                }
                return `${recordValue.item.name} - ${recordValue.item.id}`;
            },
            headerQuickActions: [],
            headerDropdownActions: [
                {
                    icon: 'three_boxes',
                    title: `Stock details`,
                    isHidden(_rowId, rowData) {
                        return rowData && !(rowData.item && rowData.quantityInStockUnit && rowData.item.isStockManaged);
                    },
                    async onClick(
                        rowId,
                        rowItem: ui.PartialCollectionValue<StockInterfaces.StockReceiptLinePageBinding>,
                    ) {
                        const line = MasterDataUtils.removeExtractEdgesPartial(rowItem);
                        try {
                            await MasterDataUtils.applyPanelToLineIfChanged(
                                this.lines,
                                StockDetailHelper.editStockDetails(this, line, {
                                    movementType: 'receipt',
                                    data: {
                                        isEditable: !StockReceipt.lineStatusReadonly(line.stockTransactionStatus),
                                        effectiveDate: this.effectiveDate.value || undefined,
                                        baseDocumentLineSysId: Number(line._id),
                                        documentLineSortValue: line._sortValue,
                                        documentLine: rowId,
                                        jsonStockDetails: line.jsonStockDetails,
                                        item: line.item?._id,
                                        stockSite: this.stockSite.value?._id,
                                        quantity: +line.quantityInStockUnit,
                                        unit: line.item.stockUnit?._id,
                                        trackCheckStock: 'track',
                                        stockStatus:
                                            line.stockStatus?._id ||
                                            (await getDefaultQuality({
                                                defaultQualityType: 'inboundDefaultQuality',
                                                page: this,
                                                itemId: line.item?._id || '',
                                                site: this.stockSite.value,
                                            })), // this.stockSite.value?.defaultStockStatus?._id,
                                        location:
                                            line.location?._id ||
                                            (await getDefaultLocation({
                                                defaultLocationType: 'inboundDefaultLocation',
                                                page: this,
                                                itemId: line.item?._id || '',
                                                site: this.stockSite.value,
                                            })), // this.stockSite.value?.defaultLocation?._id,
                                        existingLot: undefined,
                                        lotCreateData: undefined,
                                        orderCost: line.orderCost,
                                        valuedCost: line.valuedCost,
                                        stockTransactionStatus: line.stockTransactionStatus,
                                    },
                                }) as Promise<StockInterfaces.StockReceiptLinePageBinding>,
                            );
                        } catch (err) {
                            console.error('Stock details onClick function', err);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockReceiptLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                {
                                    documentLine: rowData,
                                },
                                {
                                    editable:
                                        !StockReceipt.lineStatusReadonly(rowData?.stockTransactionStatus) ||
                                        this.fromNotificationHistory,
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden(_rowId, rowItem) {
                        return StockReceipt.lineStatusReadonly(rowItem.stockTransactionStatus);
                    },
                    async onClick(rowId) {
                        await this.deleteLine(rowId);
                    },
                },
            ],
            onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    if (
                        +recordValue._id < 0 &&
                        (this._defaultDimensionsAttributes.dimensions !== '{}' ||
                            this._defaultDimensionsAttributes.attributes !== '{}')
                    ) {
                        const line = {
                            ...dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                                {
                                    _sortValue: (this.lines.value[this.lines.value.length - 1]?._sortValue || 0) + 10,
                                } as any,
                                this._defaultDimensionsAttributes,
                            ),
                            stockTransactionStatus: 'draft' as StockDocumentTransactionStatus,
                            stockDetailStatus: 'required' as StockDetailStatus,
                        };
                        recordValue.storedAttributes = line.storedAttributes ?? '';
                        recordValue.storedDimensions = line.storedDimensions ?? '';
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<StockReceiptLineBinding>,
                        );
                    }
                }
                return Promise.resolve();
            },
            onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<StockReceiptLineBinding>,
                    );
                }
                return Promise.resolve();
            },
            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-stock/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'quantityInStockUnit'],
                            },
                            descriptionBlock: {
                                fields: [{ item: { description: true } }],
                            },
                            costBlock: {
                                fields: ['valuedCost', 'orderCost'],
                            },
                            locationBlock: {
                                fields: ['location', 'stockStatus'],
                            },
                        },
                    },
                };
            },
        },
        mapServerRecord(record) {
            return {
                ...record,
            };
        },
        parent() {
            return this.linesSection;
        },
    })
    lines: ui.fields.Table<StockInterfaces.StockReceiptLinePageBinding>;

    @ui.decorators.pageAction<StockReceipt>({
        icon: 'none',
        title: 'Set dimensions',
        isHidden() {
            return this.isSetDimensionsMainListHidden.value === true;
        },
        isDisabled() {
            return !this.stockSite.value;
        },
        async onClick() {
            const filter = (line: ui.PartialNodeWithId<StockReceiptLineBinding>) =>
                !StockReceipt.lineStatusReadonly(line.stockTransactionStatus);
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                filter,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockReceipt>({
        title: 'Save',
        buttonType: 'primary',
        access: {
            bind: '$update',
        },
        async onClick() {
            const validateResult = await this.validate(true);
            if (validateResult.length !== 0) {
                return;
            }
            if (this.lines.value.length > 0) {
                StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(
                    this.lines.value,
                    this.lines,
                    'receipt',
                );
            }
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            await this.$.refreshNavigationPanel();
        },
    })
    saveStockReceipt: ui.PageAction;

    @ui.decorators.pageAction<StockReceipt>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.displayStatus.value !== 'received';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                {
                    origin: 'header',
                    tableField: this.lines,
                },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<StockReceipt>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'stockPostingError';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    async init() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockReceipt,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        if (this.$.detailPanel) this.$.detailPanel.isHidden = true;
        this.setDeleteAction();
        if (!this.$.recordId) {
            this.effectiveDate.value = DateValue.today().toString();
        }
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.stockSite.value,
        });
        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        await this.$.refreshNavigationPanel();
        this.$.setPageClean();
    }

    setDeleteAction() {
        if (this.stockTransactionStatus.value) {
            this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
                parameters: { stockTransactionStatus: this.stockTransactionStatus.value },
                recordId: this.$.recordId,
            });
        }
    }

    private static calculateTotalCost(rowItem: ui.PartialCollectionValue<StockReceiptLineBinding>) {
        return (Number(rowItem.quantityInStockUnit) * Number(rowItem.orderCost)).toString();
    }

    @ui.decorators.pageAction<StockReceipt>({
        title: 'Post stock',
        buttonType: 'primary',
        isHidden() {
            return (
                !['draft', 'error'].includes(this.stockTransactionStatus.value || '') ||
                !this._id.value ||
                this.$.isDirty
            );
        },
        async onClick() {
            if (this.lines.value.some(line => line.stockDetailStatus === 'required')) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-stock/pages__stock_receipt_post__stock_details_required',
                        'You need to enter stock details for all lines before you can post.',
                    ),
                    { type: 'error' },
                );
                return;
            }
            this.post.isDisabled = true;
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-stock/StockReceipt')
                    .mutations.postToStock(true, { documentIds: [this.$.recordId ?? ''] })
                    .execute(),
                this,
            );
            if (this._id.value) {
                await actionFunctions.checkForUpdate({
                    stockReceiptPage: this,
                    recordId: this._id.value,
                });
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockReceipt>({
        title: 'Repost',
        isHidden() {
            return !(this.fromNotificationHistory && (this.stockSite?.value?.legalCompany?.doStockPosting || false));
        },
        isDisabled() {
            return !this.$.isDirty;
        },
        async onClick() {
            StockDocumentHelper.catchPostingError((await this.receiptRepost()).message, this);
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    async receiptRepost() {
        const documentLines = this.lines.value
            .filter(line => Number(line._id) > 0)
            .map(line => ({
                baseDocumentLineSysId: line._id,
                storedAttributes: line.storedAttributes,
                storedDimensions: line.storedDimensions,
            }));

        this.$.loader.isHidden = false;

        const postResult = await this.$.graph
            .node('@sage/xtrem-stock/StockReceipt')
            .mutations.repost(
                {
                    wasSuccessful: true,
                    message: true,
                },
                { stockReceipt: this._id.value || '', documentLines },
            )
            .execute();

        this.$.loader.isHidden = true;
        if (!postResult.wasSuccessful) {
            this.$.showToast(
                `**${ui.localize(
                    '@sage/xtrem-stock/pages__stock_receipt__repost_errors',
                    'Errors while reposting:',
                )}**\n${postResult.message}`,
                { type: 'error', timeout: 20000 },
            );
        } else {
            this.$.showToast(postResult.message, { type: 'success' });
        }

        return postResult;
    }

    /**
     * Validates the page (including mandatory stock fields)
     * @returns string array (promise of) with all the error messages
     */
    private validate(notify = false): Promise<string[]> | string[] {
        // check if the serial numbers on all new (or updated) lines are unique
        if (this.$.isServiceOptionEnabled('serialNumberOption')) {
            const validationMessages = StockValuationLib.validateSerialNumbers(this);
            if (validationMessages && !!validationMessages.length) {
                if (notify) {
                    this.$.showToast(validationMessages.join('\n\n'), { type: 'error' });
                }
                return validationMessages;
            }
        }
        return this.$.page.validate();
    }

    private static lineStatusReadonly(stockTransactionStatus: StockDocumentTransactionStatus) {
        return ['inProgress', 'completed'].includes(stockTransactionStatus);
    }

    readOnlyHeaderFields() {
        return (this.lines.value?.length || 0) !== 0;
    }

    disableHeaderFields() {
        // header fields (including stockSite) are disabled for an existing loaded record
        // so here just manage case where we are in creation of a new record
        if (!this.$.recordId) {
            this.stockSite.isDisabled = this.lines.value.length > 0;
        }
    }

    get stockSiteCurrency() {
        const currency = this.stockSite.value?.legalCompany?.currency;
        return {
            _id: currency?._id ?? '',
            decimalDigits: currency?.decimalDigits ?? 2,
            symbol: currency?.symbol ?? '',
        };
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value.at(0)?.message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: {
                displayStatus: this.displayStatus.value,
                stockSite: this.stockSite.value,
            },
        });
    }

    async deleteLine(rowId: string) {
        if (+rowId < 0) {
            this.lines.removeRecord(rowId);
            return;
        }
        if (
            await deleteDialogWithAcceptButtonText(
                this,
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_title',
                    'Confirm delete',
                ),
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_receipt__line_delete_action_dialog_content',
                    'You are about to delete this stock receipt line.',
                ),
                ui.localize('@sage/xtrem-stock/pages-confirm-delete', 'Delete'),
            )
        ) {
            this.lines.removeRecord(rowId);
            this.disableHeaderFields();
        }
    }
}
