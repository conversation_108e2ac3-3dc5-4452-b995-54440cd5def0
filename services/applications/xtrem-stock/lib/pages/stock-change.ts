import { asyncArray } from '@sage/xtrem-async-helper';
import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { Item, Location, LocationZone, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stock } from '@sage/xtrem-master-data/build/lib/menu-items/stock';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type { DeepPartial, integer } from '@sage/xtrem-shared';
import type { GraphApi, StockChangeLineBinding, StockChange as StockChangeNode } from '@sage/xtrem-stock-api';
import type {
    Lot,
    SerialNumber,
    Stock,
    StockChangeDetail,
    StockChangeDetailBinding,
    StockDetailSerialNumber,
    StockDocumentStatus,
    StockDocumentTransactionStatus,
    StockStatus,
} from '@sage/xtrem-stock-data-api';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import type * as StockInterfaces from '../client-functions/interfaces';

@ui.decorators.page<StockChange, StockChangeNode>({
    menuItem: stock,
    priority: 220,
    title: 'Stock change',
    objectTypeSingular: 'Stock change',
    objectTypePlural: 'Stock changes',
    idField() {
        return this.number;
    },
    hasAttachmentsSection: true,
    mode: 'tabs',
    node: '@sage/xtrem-stock/StockChange',
    navigationPanel: {
        orderBy: { number: -1, effectiveDate: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            line2: ui.nestedFields.reference({
                bind: 'stockSite',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
                title: 'Site name',
                tunnelPage: undefined,
            }),
            siteID: ui.nestedFields.text({
                title: 'Site ID',
                bind: { stockSite: { id: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date' }),
            line3: ui.nestedFields.reference({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                title: 'Item',
                tunnelPage: undefined,
            }),
            itemId: ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            itemDescription: ui.nestedFields.text({
                title: 'Item description',
                bind: { item: { description: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            description: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                optionType: '@sage/xtrem-stock-data/StockDocumentStatus',
                backgroundColor: status => StockDocumentHelper.stockDocumentTransactionStatusColor(status),
                borderColor: status => StockDocumentHelper.stockDocumentTransactionStatusColor(status),
                color: status => StockDocumentHelper.stockDocumentTransactionStatusColor(status, true),
            }),
        },
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockChange,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });

        this.setDeleteAction();

        if (!this.$.recordId) {
            this.effectiveDate.value = DateValue.today().toString();
            this.location.isDisabled = true;
            this.lot.isDisabled = true;
            await this.fetchStock();
        } else {
            const stockResult = await this.stockRecord.fetchSuggestions('');
            if (stockResult.length === 1) {
                this.stockRecord.value = stockResult.at(0) ?? null;
                this.setAvailableQuantity();
                this.availableQuantity.postfix = this.item.value?.stockUnit?.symbol;
                this.availableQuantity.scale = this.item.value?.stockUnit?.decimalDigits;
            }
            // Today, stock allocations information are not changed by stock engine
            this.totalQuantity.postfix = this.item.value?.stockUnit?.symbol;
            this.totalQuantity.scale = this.item.value?.stockUnit?.decimalDigits;
            this.lot.isDisabled = this.item.value?.lotManagement === 'notManaged';
            if (this.stockTransactionStatus.value) {
                this.addLine.isDisabled = StockChange.lineStatusReadonly(this.stockTransactionStatus.value);
            }
            this.calculateTotalQuantity();
            await this.loadExistingDetails();
            this.item.value = await StockDataUtils.getItemInfo(
                this,
                this.item.value?._id || this.stockRecord.value?.item._id || '',
            );
            this.convertSerialNumberData();
            this.$.setPageClean();
            await this.showHideColumns();
        }
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.post, this.saveStockChange];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.saveStockChange,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.setDeleteAction();
    },
})
export class StockChange extends ui.Page<GraphApi, StockChangeNode> {
    sequenceNumberStart: integer;

    sequenceNumberLength: integer;

    getSerializedValues() {
        if (this.lines.value.length) {
            StockDetailHelper.prepareDetailsToBeSavedInTheDocumentTransaction(this.lines.value, this.lines, 'change');
        }
        const { values } = this.$;
        return values;
    }

    @ui.decorators.section<StockChange>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockChange>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<StockChange>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.block<StockChange>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Initial stock',
    })
    initialStockBlock: ui.containers.Block;

    @ui.decorators.textField<StockChange>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        width: 'medium',
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockChange, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        helperTextField: 'id',
        lookupDialogTitle: 'Select site',
        minLookupCharacters: 0,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ isHidden: true, bind: 'isLocationManaged' }),
        ],
        width: 'medium',
        isMandatory: true,
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        async onChange() {
            await this.fetchStock();
            this.location.isDisabled = !this.stockSite.value;
            this.location.isHidden = !this.stockSite.value?.isLocationManaged;
            if (this.location.isHidden || this.location.isDisabled) {
                this.location.value = null;
            }
            // to potentially remove/add column for location
            await this.showHideColumns();
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<StockChange>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        maxDate: DateValue.today().toString(),
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.textField<StockChange>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
    })
    description: ui.fields.Text;

    @ui.decorators.labelField<StockChange>({
        title: 'Status',
        optionType: '@sage/xtrem-stock-data/StockDocumentStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return {
                backgroundColor: StockDocumentHelper.stockDocumentTransactionStatusColor(this.status.value),
                borderColor: StockDocumentHelper.stockDocumentTransactionStatusColor(this.status.value),
                color: StockDocumentHelper.stockDocumentTransactionStatusColor(this.status.value, true),
            };
        },
    })
    status: ui.fields.Label<StockDocumentStatus>;

    @ui.decorators.labelField<StockChange>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return {
                backgroundColor: StockDocumentHelper.stockDocumentTransactionStatusColor(
                    this.stockTransactionStatus.value,
                ),
                borderColor: StockDocumentHelper.stockDocumentTransactionStatusColor(this.stockTransactionStatus.value),
                color: StockDocumentHelper.stockDocumentTransactionStatusColor(this.stockTransactionStatus.value, true),
            };
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.referenceField<StockChange, Item>({
        parent() {
            return this.initialStockBlock;
        },
        title: 'Item',
        lookupDialogTitle: 'Select item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.reference<StockChange, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                valueField: 'symbol',
                helperTextField: 'name',
                isHidden: true,
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'lotManagement' }),
        ],
        orderBy: {
            name: +1,
            id: +1,
        },
        minLookupCharacters: 1,
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        async onChange() {
            await this.fetchStock();
            if (this.item.value) {
                this.totalQuantity.postfix = this.item.value.stockUnit?.symbol;
                this.totalQuantity.scale = this.item.value.stockUnit?.decimalDigits;
                this.availableQuantity.postfix = this.totalQuantity.postfix;
                this.availableQuantity.scale = this.totalQuantity.scale;
                this.lot.isDisabled = this.item.value.lotManagement === 'notManaged';
            } else {
                this.lot.isDisabled = true;
            }
            if (this.lot.isDisabled) {
                this.lot.value = null;
            }
            await this.showHideColumns();
        },
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockChange, Location>({
        parent() {
            return this.initialStockBlock;
        },
        title: 'Location',
        node: '@sage/xtrem-master-data/Location',
        valueField: 'name',
        helperTextField: 'id',
        bind: 'location',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            ui.nestedFields.reference<StockChange, Location, LocationZone>({
                bind: 'locationZone',
                node: '@sage/xtrem-master-data/LocationZone',
                valueField: 'name',
                isHidden: true,
            }),
        ],
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        filter() {
            return {
                locationType: { locationCategory: { _ne: 'virtual' } },
            };
        },
        onChange() {
            return this.fetchStock();
        },
    })
    location: ui.fields.Reference<Location>;

    @ui.decorators.referenceField<StockChange, StockStatus>({
        parent() {
            return this.initialStockBlock;
        },
        title: 'Quality control',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select quality value',
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        onChange() {
            return this.fetchStock();
        },
    })
    stockStatus: ui.fields.Reference<StockStatus>;

    @ui.decorators.referenceField<StockChange, Lot>({
        parent() {
            return this.initialStockBlock;
        },
        title: 'Lot',
        node: '@sage/xtrem-stock-data/Lot',
        valueField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'expirationDate', title: 'Expiration date' }),
        ],
        minLookupCharacters: 1,
        width: 'medium',
        isReadOnly() {
            return this.item.value?.lotManagement === 'notManaged' || this.readOnlyHeaderFields();
        },
        onChange() {
            return this.fetchStock();
        },
    })
    lot: ui.fields.Reference<Lot>;

    @ui.decorators.textField<StockChange>({
        parent() {
            return this.initialStockBlock;
        },
        title: 'Owner',
        width: 'medium',
        isReadOnly() {
            return this.readOnlyHeaderFields();
        },
        onChange() {
            return this.fetchStock();
        },
    })
    owner: ui.fields.Text;

    @ui.decorators.referenceField<StockChange, Stock>({
        isTransient: true,
        isHidden: true,
        title: 'Stock',
        parent() {
            return this.initialStockBlock;
        },
        node: '@sage/xtrem-stock-data/Stock',
        valueField: 'quantityInStockUnit',
        columns: [
            ui.nestedFields.reference<StockChange, Stock, Site>({
                bind: 'site',
                title: 'Site',
                valueField: 'name',
                helperTextField: 'id',
                node: '@sage/xtrem-system/Site',
                isHidden() {
                    return !!this.stockSite.value;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.checkbox({
                        isHidden: true,
                        bind: 'isLocationManaged',
                        title: 'Location management',
                    }),
                ],
            }),
            ui.nestedFields.reference<StockChange, Stock, Item>({
                bind: 'item',
                title: 'Item',
                valueField: 'name',
                helperTextField: 'id',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isHidden() {
                    return !!this.item.value;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<StockChange, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        helperTextField: 'name',
                        isHidden: true,
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.text({
                        bind: 'lotManagement',
                        isHidden: true,
                    }),
                ],
            }),
            ui.nestedFields.reference<StockChange, Stock, Lot>({
                bind: 'lot',
                title: 'Lot',
                valueField: 'id',
                node: '@sage/xtrem-stock-data/Lot',
                isHidden() {
                    return !this.item.value || this.item.value.lotManagement === 'notManaged';
                },
            }),
            ui.nestedFields.reference<StockChange, Stock, Location>({
                bind: 'location',
                title: 'Location',
                valueField: 'name',
                helperTextField: 'id',
                node: '@sage/xtrem-master-data/Location',
                columns: [
                    ui.nestedFields.reference<StockChange, Location, LocationZone>({
                        bind: 'locationZone',
                        title: 'Zone',
                        valueField: 'name',
                        node: '@sage/xtrem-master-data/LocationZone',
                    }),
                ],
            }),
            ui.nestedFields.reference<StockChange, Stock, StockStatus>({
                bind: 'status',
            }),
            ui.nestedFields.text({
                bind: 'owner',
                title: 'Owner',
            }),
            ui.nestedFields.numeric({ bind: 'quantityInStockUnit', title: 'Quantity' }),
            ui.nestedFields.numeric({ bind: 'totalAllocated', title: 'Allocated' }),
        ],
        filter() {
            const filter: Filter<Stock> = {
                isInTransit: false,
            };

            if (this.stockSite.value) {
                filter.site = { _id: this.stockSite.value._id };
                if (this.location.value) {
                    filter.location = { _id: this.location.value._id };
                }
            }
            if (this.item.value) {
                filter.item = { _id: this.item.value._id };
                if (this.lot.value) {
                    filter.lot = { _id: this.lot.value._id };
                }
            }
            if (this.stockStatus.value) {
                filter.status = { _id: this.stockStatus.value._id };
            }
            if (this.owner.value) {
                filter.owner = this.owner.value;
            }

            return filter;
        },
        async onChange() {
            if (this.stockRecord.value) {
                await this.setStockValues();
                this.initSequenceNumberData();
                this.stockSearch.value = '1';
            }
            this.addLine.isDisabled = !this.stockRecord.value;
        },
    })
    stockRecord: ui.fields.Reference<Stock>;

    async fetchStock() {
        if (!this.stockSearch.isHidden) {
            const stockResult = await this.stockRecord.fetchSuggestions('');
            const nb = stockResult.length;
            this.stockSearch.value = String(nb);
            if (nb === 1) {
                this.stockRecord.value = stockResult.at(0) ?? null;
                this.setAvailableQuantity();
            } else {
                this.availableQuantity.value = null;
            }
            this.addLine.isDisabled = nb !== 1;
        }
    }

    private async setStockValues() {
        this.item.value = await StockDataUtils.getItemInfo(
            this,
            this.item.value?._id || this.stockRecord.value?.item?._id || '',
        );
        this.location.value = this.stockRecord.value?.location ?? null;
        this.location.isDisabled = false;
        this.lot.value = this.stockRecord.value?.lot ?? null;
        this.lot.isDisabled = this.item.value?.lotManagement === 'notManaged';
        this.stockStatus.value = this.stockRecord.value?.status ?? null;
        this.owner.value = this.stockRecord.value?.owner ?? '';
        this.stockSite.value = this.stockRecord.value?.site ?? null;
        this.setAvailableQuantity();
        this.availableQuantity.postfix = this.item.value?.stockUnit?.symbol;
        this.availableQuantity.scale = this.item.value?.stockUnit?.decimalDigits;
        this.totalQuantity.value = 0;
        this.totalQuantity.postfix = this.availableQuantity.postfix;
        this.totalQuantity.scale = this.availableQuantity.scale;
        await this.showHideColumns();
    }

    @ui.decorators.buttonField<StockChange>({
        isTransient: true,
        isHidden() {
            return !!this.$.recordId || this.readOnlyHeaderFields();
        },
        width: 'medium',
        parent() {
            return this.initialStockBlock;
        },
        map(val) {
            return ui.localize('@sage/xtrem-stock/search-count', 'Search ({{#if plus}}+{{/if}}{{nb}})', {
                nb: val > 19 ? 19 : val,
                plus: val > 19,
            });
        },
        onClick() {
            this.stockRecord.openDialog();
        },
    })
    stockSearch: ui.fields.Button;

    @ui.decorators.numericField<StockChange>({
        isTransient: true,
        isReadOnly: true,
        parent() {
            return this.initialStockBlock;
        },
        title: 'Available quantity',
        width: 'medium',
        scale: 2,
    })
    availableQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<StockChange>({
        isTransient: true,
        isReadOnly: true,
        parent() {
            return this.initialStockBlock;
        },
        title: 'Total quantity',
        width: 'medium',
        scale: 2,
    })
    totalQuantity: ui.fields.Numeric;

    private calculateTotalQuantity() {
        this.totalQuantity.value = this.lines.value.reduce((prev, line) => {
            if (line.quantityInStockUnit) {
                return prev + line.quantityInStockUnit;
            }
            return prev;
        }, 0);
    }

    @ui.decorators.tableField<StockChange, StockInterfaces.StockChangeLinePageBinding>({
        title: 'Changes',
        bind: 'lines',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-stock/StockChangeLine',
        parent() {
            return this.mainSection;
        },
        orderBy: {
            _sortValue: +1,
        },
        mobileCard: undefined,
        columns: [
            ui.nestedFields.label({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                backgroundColor: status => StockDocumentHelper.stockDocumentTransactionStatusColor(status),
                borderColor: status => StockDocumentHelper.stockDocumentTransactionStatusColor(status),
                color: status => StockDocumentHelper.stockDocumentTransactionStatusColor(status, true),
                isHidden() {
                    if (this.stockTransactionStatus.value) {
                        return ['draft', 'completed'].includes(this.stockTransactionStatus.value);
                    }
                    return false;
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<StockChange, StockInterfaces.StockChangeLinePageBinding, SerialNumber>({
                title: 'From serial number',
                bind: 'startingSerialNumber',
                node: '@sage/xtrem-stock-data/SerialNumber',
                valueField: 'id',
                isTransient: true,
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select From serial number',
                isReadOnly(rowId: any, rowData: any) {
                    return StockChange.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                isHidden() {
                    return this.item.value
                        ? !(
                              this.item.value.serialNumberManagement === 'managed' &&
                              this.item.value.serialNumberUsage === 'issueAndReceipt'
                          )
                        : true;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'supplierSerialNumber', title: 'Supplier serial number' }),
                    ui.nestedFields.reference({
                        bind: 'supplier',
                        title: 'Supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
                filter() {
                    return {
                        ...(this.stockRecord.value && {
                            stockRecord: { _id: { _eq: `${this.stockRecord.value._id}` } },
                        }),
                        isUsable: true,
                        isInStock: true,
                        isAllocated: false,
                    };
                },
                validation(_val, rowData: StockInterfaces.StockChangeLinePageBinding) {
                    if (this.initSerialNumberRowData(rowData)) {
                        if (!this.checkUniquenessOfSerialNumberRange(rowData))
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_change_duplicate_error',
                                'The serial number is already included in another range.',
                            );
                    }
                    // enforce setRecordValue if startingSerialNumber really changed. Reason:
                    // 'validation' for quantity property is triggered before onChange
                    // of startingSerialNumber property. Therefore the quantity is in this case the old one before
                    // change of startingSerialNumber and we enforce the new quantity here (didn't find another solution).
                    if (rowData.originalStartId !== rowData.startingSerialNumber?.id)
                        this.lines.setRecordValue(rowData);
                    return undefined;
                },
                onChange(_id: number, rowData: StockInterfaces.StockChangeLinePageBinding) {
                    let updatedRowData: StockInterfaces.StockChangeLinePageBinding = { ...rowData };
                    if (this.initSerialNumberRowData(updatedRowData)) {
                        updatedRowData.originalStartId = updatedRowData.startingSerialNumber?.id ?? '';
                        if (updatedRowData.stockDetailId) {
                            updatedRowData = StockChange.updateStockDetailData(updatedRowData, {
                                quantityInStockUnit: updatedRowData.quantityInStockUnit,
                            });
                        } else {
                            updatedRowData = this.createStockDetailData(updatedRowData);
                        }
                        updatedRowData.jsonStockDetailSerialNumberRange = StockChange.createJsonRange(updatedRowData);
                        this.lines.setRecordValue(updatedRowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'quantityInStockUnit',
                isMandatory: true,
                scale() {
                    return this.totalQuantity.scale ?? 0;
                },
                postfix() {
                    return this.totalQuantity.postfix ?? '';
                },
                isReadOnly(rowId: any, rowData: any) {
                    return StockChange.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                async validation(val: number, rowData: StockInterfaces.StockChangeLinePageBinding) {
                    // Prevent a quantity of zero
                    if (val <= 0)
                        return ui.localize(
                            '@sage/xtrem-stock/pages__stock_change_zero_quantity_error',
                            'Enter a quantity greater than 0.',
                        );
                    if (
                        this.item.value?.serialNumberManagement === 'managed' &&
                        this.item.value.serialNumberUsage === 'issueAndReceipt'
                    ) {
                        if (!rowData.startingSerialNumber)
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_change_missing_from_serial_number',
                                'You need to enter a From serial number.',
                            );
                        if (this.prepareSerialNumberRowData(rowData)) {
                            // check in the database if all serial numbers within the calculated range are available
                            const filter: Filter<SerialNumber> = {
                                stockRecord: { _id: { _eq: `${this.stockRecord.value?._id}` } },
                                isUsable: true,
                                isInStock: true,
                                isAllocated: false,
                                _and: [
                                    { id: { _gte: rowData.startingSerialNumber.id } },
                                    { id: { _lte: rowData.endingSerialNumber?.id } },
                                ],
                            };

                            const searchRange = extractEdges(
                                await this.$.graph
                                    .node('@sage/xtrem-stock-data/SerialNumber')
                                    .query(
                                        ui.queryUtils.edgesSelector(
                                            { _id: true, id: true },
                                            {
                                                filter,
                                                orderBy: {
                                                    id: 1,
                                                },
                                                first: 1000,
                                            },
                                        ),
                                    )
                                    .execute(),
                            );

                            // enforce re-reading line from grid. Reason:
                            // 'validation' is triggered when startingSerialNumber has been changed before onChange
                            // of startingSerialNumber. Therefore the quantity is in this case the old one before
                            // change of startingSerialNumber. In 'validation' of startingSerialNumber we therefore
                            // already update the quantity to 1 (as no other solution found) in the grid
                            const line = this.lines.getRecordValue(rowData._id);

                            // check if the last serial number of the range is the same as the calculated last one
                            // (without gaps) of the range. If not > calculate available range
                            if (
                                searchRange.length > 0 &&
                                (searchRange.length !== line?.quantityInStockUnit ||
                                    +searchRange[searchRange.length - 1].id.substring(
                                        this.sequenceNumberStart,
                                        this.sequenceNumberStart + this.sequenceNumberLength,
                                    ) !== rowData.numericEnd)
                            ) {
                                let gapFound = false;
                                let rangeLength: integer = 0;
                                let actNumeric: integer = 0;
                                // find the first gap
                                searchRange.forEach(serialNumber => {
                                    if (
                                        !gapFound &&
                                        (rangeLength === 0 ||
                                            actNumeric ===
                                                +serialNumber.id.substring(
                                                    this.sequenceNumberStart,
                                                    this.sequenceNumberStart + this.sequenceNumberLength,
                                                ) -
                                                    1)
                                    ) {
                                        rangeLength += 1;
                                    } else gapFound = true;
                                    actNumeric = +serialNumber.id.substring(
                                        this.sequenceNumberStart,
                                        this.sequenceNumberStart + this.sequenceNumberLength,
                                    );
                                });
                                if (!gapFound && rangeLength !== line?.quantityInStockUnit) {
                                    gapFound = true;
                                }

                                // if not all serial numbers of range are avaliable -> error
                                if (gapFound) {
                                    return ui.localize(
                                        '@sage/xtrem-stock/pages__stock_change_available_error',
                                        'Serial numbers available in this range: {{available}}',
                                        { available: rangeLength },
                                    );
                                }
                            }
                            // check the uniqueness of the calculated range for the stock detail
                            if (!this.checkUniquenessOfSerialNumberRange(rowData)) {
                                return ui.localize(
                                    '@sage/xtrem-stock/pages__stock_change_duplicate_range_error',
                                    'One of the serial numbers is already included in another range.',
                                );
                            }
                        }
                    }
                    return undefined;
                },
                onChange(_id: number, rowData: StockInterfaces.StockChangeLinePageBinding) {
                    let updatedRowData: StockInterfaces.StockChangeLinePageBinding = { ...rowData };
                    if (updatedRowData.stockDetailId) {
                        updatedRowData = StockChange.updateStockDetailData(updatedRowData, {
                            quantityInStockUnit: updatedRowData.quantityInStockUnit,
                        });
                    } else {
                        updatedRowData = this.createStockDetailData(updatedRowData);
                    }
                    if (
                        this.item.value?.serialNumberManagement === 'managed' ||
                        this.item.value?.serialNumberUsage === 'issueAndReceipt'
                    ) {
                        if (updatedRowData.startingSerialNumber && this.prepareSerialNumberRowData(updatedRowData)) {
                            updatedRowData.jsonStockDetailSerialNumberRange =
                                StockChange.createJsonRange(updatedRowData);
                        }
                    }
                    this.lines.setRecordValue(updatedRowData);
                },
            }),
            ui.nestedFields.reference({
                title: 'To serial number',
                bind: 'endingSerialNumber',
                node: '@sage/xtrem-stock-data/SerialNumber',
                valueField: 'id',
                isReadOnly: true,
                isTransient: true,
                isHidden() {
                    return this.item.value
                        ? !(
                              this.item.value.serialNumberManagement === 'managed' &&
                              this.item.value.serialNumberUsage === 'issueAndReceipt'
                          )
                        : true;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'supplierSerialNumber', title: 'Supplier serial number' }),
                    ui.nestedFields.reference({
                        bind: 'supplier',
                        title: 'Supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        valueField: 'name',
                        helperTextField: 'id',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
            }),
            ui.nestedFields.reference<StockChange, StockChangeLineBinding, Location>({
                title: 'Location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                bind: 'location',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
                    ui.nestedFields.reference<StockChange, Location, Location['locationZone']>({
                        bind: 'locationZone',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: '_id',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.reference<
                                StockChange,
                                Location['locationZone'],
                                Location['locationZone']['site']
                            >({
                                bind: 'site',
                                node: '@sage/xtrem-system/Site',
                                valueField: '_id',
                            }),
                        ],
                    }),
                ],
                minLookupCharacters: 1,
                filter() {
                    return {
                        locationZone: { site: { _id: this.stockSite.value?._id || '' } },
                        isActive: true,
                        locationType: { locationCategory: { _ne: 'virtual' } },
                    };
                },
                isHidden() {
                    return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                },
                isReadOnly(rowId: any, rowData: any) {
                    return StockChange.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                onChange(_id: number, rowData: StockInterfaces.StockChangeLinePageBinding) {
                    let updatedRowData: StockInterfaces.StockChangeLinePageBinding = { ...rowData };
                    if (updatedRowData.stockDetailId) {
                        updatedRowData = StockChange.updateStockDetailData(updatedRowData, {
                            location: updatedRowData.location,
                        });
                    } else {
                        updatedRowData = this.createStockDetailData(updatedRowData);
                    }
                    this.lines.setRecordValue(updatedRowData);
                },
            }),
            ui.nestedFields.reference<StockChange, StockChangeLineBinding, StockStatus>({
                title: 'Quality control',
                bind: 'stockStatus',
                minLookupCharacters: 1,
                lookupDialogTitle: 'Select quality value',
                isReadOnly(rowId: any, rowData: any) {
                    return StockChange.lineStatusReadonly(rowData.stockTransactionStatus);
                },
                onChange(_id: number, rowData: StockInterfaces.StockChangeLinePageBinding) {
                    let updatedRowData: StockInterfaces.StockChangeLinePageBinding = { ...rowData };
                    if (updatedRowData.stockDetailId) {
                        updatedRowData = StockChange.updateStockDetailData(updatedRowData, {
                            status: updatedRowData.stockStatus,
                        });
                    } else {
                        updatedRowData = this.createStockDetailData(updatedRowData);
                    }
                    this.lines.setRecordValue(updatedRowData);
                },
            }),
            ui.nestedFields.technical<StockChange, StockInterfaces.StockChangeLinePageBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetails',
            }),
            ui.nestedFields.technical<StockChange, StockInterfaces.StockChangeLinePageBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetailSerialNumbers',
            }),
            ui.nestedFields.technical<StockChange, StockInterfaces.StockChangeLinePageBinding>({
                isTransientInput: true,
                bind: 'jsonStockDetailSerialNumberRange',
            }),
            ui.nestedFields.technical<StockChange, StockInterfaces.StockChangeLinePageBinding>({
                isTransientInput: true,
                bind: 'stockDetailId',
            }),
            ui.nestedFields.technical({ isTransient: true, bind: 'numericStart' }),
            ui.nestedFields.technical({ isTransient: true, bind: 'numericEnd' }),
            ui.nestedFields.technical({ isTransient: true, bind: 'originalStartId' }),
        ],
        onChange() {
            this.calculateTotalQuantity();
        },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(rowID: any, rowItem: any) {
                    return StockChange.lineStatusReadonly(rowItem.stockTransactionStatus);
                },
                onClick(rowID: any) {
                    this.lines.removeRecord(rowID);
                    this.calculateTotalQuantity();
                    this.disableHeaderFields();
                },
            },
        ],
        fieldActions() {
            return [this.addLine];
        },
    })
    lines: ui.fields.Table;

    @ui.decorators.pageAction<StockChange>({
        icon: 'add',
        title: 'Add line',
        isDisabled: true,
        async onClick() {
            if (this.stockSearch.value === '1') await this.setStockValues();
            this.lines.addRecord({});
            this.disableHeaderFields();
        },
    })
    addLine: ui.PageAction;

    setDeleteAction() {
        if (
            !this.$standardDeleteAction.isDisabled &&
            this.lines.value.some((line: any) => StockChange.lineStatusReadonly(line.stockTransactionStatus))
        ) {
            this.$standardDeleteAction.isDisabled = true;
        }
    }

    @ui.decorators.pageAction<StockChange>({
        title: 'Save',
        access: {
            bind: '$update',
        },
        async onClick() {
            const validation = await this.$.page.validate();

            if (!validation.length) {
                await this.manageSerialNumberData();
                const isDialog = this.$.isInDialog;
                await this.$standardSaveAction.execute(true);
                if (isDialog) return;
                await this.$.router.refresh(true);
                this.$.setPageClean();
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    saveStockChange: ui.PageAction;

    @ui.decorators.pageAction<StockChange>({
        title: 'Post',
        isDisabled() {
            return this.$.isDirty || !this._id.value;
        },
        isHidden() {
            if (this.stockTransactionStatus.value) {
                return !['draft', 'error'].includes(this.stockTransactionStatus.value);
            }
            return false;
        },
        onError(error: string | (Error & { errors: Array<any> })) {
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-stock/StockChange')
                    .mutations.postToStock(true, { documentIds: [this.$.recordId ?? ''] })
                    .execute(),
                this,
            );
            if (this.$.isInTunnel) {
                this.$.setPageClean();
                this.$.loader.isHidden = true;
                this.$.finish({ _id: this.$.queryParameters._id, [ui.SHOULD_REFRESH_DIALOG_RESULT]: true }); // return ID
                return;
            }
            await this.$.router.refresh();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockChange>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.stockTransactionStatus.value !== 'completed';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                {
                    origin: 'header',
                    tableField: this.lines,
                },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<StockChange>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.stockTransactionStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    // updates the existing stockDetail json string on the rowData
    private static updateStockDetailData(
        rowData: StockInterfaces.StockChangeLinePageBinding,
        updates: Partial<StockChangeDetail>,
    ): StockInterfaces.StockChangeLinePageBinding {
        rowData.jsonStockDetails = JSON.stringify([
            {
                ...JSON.parse(rowData.jsonStockDetails || '[{}]')[0],
                _id: rowData.stockDetailId,
                jsonStockDetailSerialNumbers: rowData.jsonStockDetailSerialNumbers,
                ...updates,
            },
        ]);
        return rowData;
    }

    // creates a new stockDetail and adds it as json string to the rowData
    private createStockDetailData(
        rowData: StockInterfaces.StockChangeLinePageBinding,
    ): StockInterfaces.StockChangeLinePageBinding {
        rowData.jsonStockDetails = JSON.stringify([
            {
                stockRecord: this.stockRecord.value!._id,
                effectiveDate: this.effectiveDate.value,
                location: rowData.location,
                status: rowData.stockStatus,
                quantityInStockUnit: rowData.quantityInStockUnit,
                documentLine: rowData._id,
            },
        ]);
        return rowData;
    }

    // convert the json serial number range array to json single serial number array
    // the range from serial number SN0001 to serial number SN0003 (quantity 3) is converted to
    // SN0001, SN0002, SN0003
    private async manageSerialNumberData() {
        if (
            this.item.value?.serialNumberManagement === 'managed' &&
            this.item.value?.serialNumberUsage === 'issueAndReceipt'
        ) {
            const lines: ui.PartialNodeWithId<StockInterfaces.StockChangeLinePageBinding>[] = this.lines.value.filter(
                line => +line.quantityInStockUnit > 0 && line.jsonStockDetails && line.startingSerialNumber,
            );

            await asyncArray(lines).forEach(async line => {
                const serialNumbers: DeepPartial<StockDetailSerialNumber>[] = [];

                if (line.jsonStockDetailSerialNumberRange !== '[]') {
                    const serialNumberRange: StockInterfaces.StockChangeDetailSerialNumberPageBinding = JSON.parse(
                        line.jsonStockDetailSerialNumberRange as string,
                    );
                    await this.$.graph
                        .node('@sage/xtrem-stock-data/SerialNumber')
                        .queries.queryRange(
                            {
                                _id: true,
                                id: true,
                                supplierSerialNumber: true,
                            },
                            {
                                stockRecordId: this.stockRecord.value?._id,
                                isUsable: true,
                                isInStock: true,
                                isAllocated: false,
                                startingSerialNumber: serialNumberRange.startingSerialNumber?.id,
                                endingSerialNumber: serialNumberRange.endingSerialNumber?.id,
                            },
                        )
                        .execute()
                        .then(searchRange => {
                            if (searchRange.length === serialNumberRange.quantityInStockUnit) {
                                searchRange.forEach(singleSerial =>
                                    serialNumbers.push({
                                        serialNumber: singleSerial,
                                        supplierSerialNumber: singleSerial.supplierSerialNumber,
                                    }),
                                );
                            } else {
                                // when the number of found serial numbers is different from the quantity of the range
                                // there must be gaps. We only take over the serial numbers until the first gap
                                let gapFound = false;
                                let rangeLength = 0;
                                let actNumeric = 0;
                                searchRange.forEach(singleSerial => {
                                    if (
                                        !gapFound &&
                                        (rangeLength === 0 ||
                                            actNumeric ===
                                                +singleSerial.id.substring(
                                                    this.sequenceNumberStart,
                                                    this.sequenceNumberStart + this.sequenceNumberLength,
                                                ) -
                                                    1)
                                    ) {
                                        rangeLength += 1;
                                        serialNumbers.push({
                                            serialNumber: singleSerial,
                                            supplierSerialNumber: singleSerial.supplierSerialNumber,
                                        });
                                    } else gapFound = true;
                                    actNumeric = +singleSerial.id.substring(
                                        this.sequenceNumberStart,
                                        this.sequenceNumberStart + this.sequenceNumberLength,
                                    );
                                });
                            }
                            line.jsonStockDetailSerialNumbers = JSON.stringify(serialNumbers);
                            const stockDetails = JSON.parse(line.jsonStockDetails);
                            stockDetails[0].jsonStockDetailSerialNumbers = line.jsonStockDetailSerialNumbers;
                            line.jsonStockDetails = JSON.stringify(stockDetails);
                            this.lines.setRecordValue(line);
                        });
                }
            });
        }
    }

    // convert the json single serial number array to json serial number range array
    // the [SN0001, SN0002, SN0003] array is converted into [from SN0001 to SN0003] array
    private convertSerialNumberData() {
        // make sure serial number settings for item are available
        this.initSequenceNumberData();

        if (
            this.item.value?.serialNumberManagement === 'managed' &&
            this.item.value?.serialNumberUsage === 'issueAndReceipt'
        ) {
            this.lines.value = this.lines.value.map(line => {
                if (line.jsonStockDetailSerialNumbers) {
                    const serialNumbers: StockDetailSerialNumber[] = JSON.parse(
                        line.jsonStockDetailSerialNumbers as string,
                    );
                    line.startingSerialNumber = serialNumbers[0].serialNumber;
                    line.endingSerialNumber = serialNumbers[serialNumbers.length - 1].serialNumber;
                    line.numericStart = line.startingSerialNumber
                        ? +line.startingSerialNumber.id.substring(
                              this.sequenceNumberStart,
                              this.sequenceNumberStart + this.sequenceNumberLength,
                          )
                        : 0;
                    line.numericEnd = line.endingSerialNumber
                        ? +line.endingSerialNumber.id.substring(
                              this.sequenceNumberStart,
                              this.sequenceNumberStart + this.sequenceNumberLength,
                          )
                        : 0;
                    line.originalStartId = line.startingSerialNumber.id;
                    line.jsonStockDetailSerialNumberRange = StockChange.createJsonRange(
                        line as StockInterfaces.StockChangeLinePageBinding,
                    );
                }
                return line;
            });
        }
    }

    // Init properties of a row after selecting startingSerialNumber in the same row (same endingSerialNumber, quantity 1)
    private initSerialNumberRowData(rowData: StockInterfaces.StockChangeLinePageBinding): boolean {
        if (this.sequenceNumberLength > 0) {
            rowData.quantityInStockUnit = '1';
            rowData.endingSerialNumber = rowData.startingSerialNumber;
            rowData.numericStart = +(
                rowData.startingSerialNumber?.id.substring(
                    this.sequenceNumberStart,
                    this.sequenceNumberStart + this.sequenceNumberLength,
                ) ?? 0
            );
            rowData.numericEnd = rowData.numericStart;
            return true;
        }
        return false;
    }

    // Checks for the range of a changed row in the stock change grid if there are overlappings with another row of the grid
    private checkUniquenessOfSerialNumberRange(rowData: StockInterfaces.StockChangeLinePageBinding): boolean {
        return !this.lines.value.some(
            line =>
                rowData._id !== line._id &&
                ((rowData.numericStart >= line.numericStart && rowData.numericStart <= line.numericEnd) ||
                    (rowData.numericEnd >= line.numericStart && rowData.numericEnd <= line.numericEnd) ||
                    (line.numericStart >= rowData.numericStart && line.numericStart <= rowData.numericEnd) ||
                    (line.numericEnd >= rowData.numericStart && line.numericEnd <= rowData.numericEnd)),
        );
    }

    // Create endingSerialNumber of a row from the selected startingSerialNumber in the same row
    private prepareSerialNumberRowData(rowData: StockInterfaces.StockChangeLinePageBinding): boolean {
        if (
            rowData.startingSerialNumber?.id &&
            rowData.endingSerialNumber?.id &&
            this.sequenceNumberLength > 0 &&
            +rowData.quantityInStockUnit > 0
        ) {
            // extract sequence number from startingSerialNumber and make it numeric
            rowData.numericStart = +rowData.startingSerialNumber.id.substring(
                this.sequenceNumberStart,
                this.sequenceNumberStart + this.sequenceNumberLength,
            );
            rowData.numericEnd = rowData.numericStart + +rowData.quantityInStockUnit - 1;
            const prefix = rowData.startingSerialNumber.id.substring(0, this.sequenceNumberStart); // remember prefix
            // create ending sequence number by adding quantity to sequence number and leading zeros
            rowData.endingSerialNumber.id = `${prefix}${String(rowData.numericEnd).padStart(
                this.sequenceNumberLength,
                '0',
            )}`;
            return true;
        }
        return false;
    }

    // creates a string representation of the serial number range data of a row
    private static createJsonRange(rowData: StockInterfaces.StockChangeLinePageBinding): string {
        const rangeData: StockInterfaces.StockChangeDetailSerialNumberPageBinding = {
            startingSerialNumber: rowData.startingSerialNumber,
            quantityInStockUnit: +rowData.quantityInStockUnit,
            endingSerialNumber: rowData.endingSerialNumber,
            numericStart: rowData.numericStart,
            numericEnd: rowData.numericEnd,
            originalStartId: rowData.originalStartId,
        };
        return JSON.stringify(rangeData);
    }

    // extracts starting position and length of "sequenceNumber" component of item's serialNumberSequenceNumber
    // and stores it into this.sequenceNumberStart and this.sequenceNumberLength
    private initSequenceNumberData() {
        if (
            this.item.value?.serialNumberManagement === 'managed' &&
            this.item.value?.serialNumberUsage === 'issueAndReceipt'
        ) {
            this.sequenceNumberStart = 0;
            this.sequenceNumberLength = 0;
            let found: boolean = false;
            const components = this.item.value?.serialNumberSequenceNumber?.components
                ? this.item.value.serialNumberSequenceNumber.components
                : [];
            components.forEach(component => {
                if (!found) {
                    this.sequenceNumberStart =
                        component.type === 'sequenceNumber'
                            ? this.sequenceNumberStart
                            : this.sequenceNumberStart + (component.length ?? 0);
                    if (component.type === 'sequenceNumber') {
                        found = true;
                        this.sequenceNumberLength = component.length ?? 0;
                    }
                }
            });
        }
    }

    setAvailableQuantity() {
        this.availableQuantity.value =
            Number(this.stockRecord.value!.quantityInStockUnit) - Number(this.stockRecord.value!.totalAllocated);
    }

    disableHeaderFields() {
        if (!this.$.recordId) {
            const gridWithLines = this.lines.value.length > 0;
            this.stockSite.isReadOnly = gridWithLines;
            this.item.isReadOnly = gridWithLines;
            this.location.isReadOnly = gridWithLines;
            this.stockStatus.isReadOnly = gridWithLines;
            this.lot.isReadOnly = gridWithLines;
            this.owner.isReadOnly = gridWithLines;
        }
    }

    private static lineStatusReadonly(stockTransactionStatus: string) {
        return ['inProgress', 'completed'].includes(stockTransactionStatus);
    }

    readOnlyHeaderFields() {
        return (this.lines.value?.length || 0) !== 0;
    }

    private async showHideColumns() {
        if (
            this.item.value?.serialNumberManagement === 'managed' &&
            this.item.value?.serialNumberUsage === 'issueAndReceipt'
        ) {
            this.lines.showColumn('startingSerialNumber');
            this.lines.showColumn('endingSerialNumber');
        } else {
            this.lines.hideColumn('startingSerialNumber');
            this.lines.hideColumn('endingSerialNumber');
        }
        if (this.stockSite.value && !this.stockSite.value.isLocationManaged) {
            this.lines.hideColumn('location');
        } else {
            this.lines.showColumn('location');
        }
        await this.lines.redraw();
    }

    private async loadExistingDetails() {
        let lines = this.lines.value;
        const lineIds: string[] = this.lines.value.map(line => line._id);
        let details: Array<ui.PartialNodeWithId<StockChangeDetailBinding>>;
        const edgesSelector = ui.queryUtils.edgesSelector(
            {
                _id: true,
                effectiveDate: true,
                quantityInStockUnit: true,
                documentLine: {
                    _id: true,
                },
                orderCost: true,
                valuedCost: true,
                stockUnit: {
                    id: true,
                    name: true,
                    decimalDigits: true,
                    symbol: true,
                },
                stockRecord: {
                    _id: true,
                    lot: {
                        id: true,
                        expirationDate: true,
                    },
                    location: {
                        id: true,
                    },
                    status: {
                        name: true,
                    },
                    owner: true,
                },
                jsonStockDetailSerialNumbers: true,
                stockDetailSerialNumberStatus: true,
            },
            {
                filter: { documentLine: { _id: { _in: lineIds } } },
            },
        );
        await this.$.graph
            .node('@sage/xtrem-stock-data/StockChangeDetail')
            .query(edgesSelector)
            .execute()
            .then(result => {
                details = extractEdges(result) as Array<ui.PartialNodeWithId<StockChangeDetailBinding>>;
                lines = lines.map(line => {
                    if (details && details.length > 0) {
                        const stockDetail = details.find(detail => detail.documentLine?._id === line._id);
                        line.stockDetailId = stockDetail?._id;
                        line.jsonStockDetailSerialNumbers = stockDetail?.jsonStockDetailSerialNumbers;
                    } else {
                        line.jsonStockDetailSerialNumbers = '[]';
                    }
                    return line;
                });
            });
        this.lines.value = lines;
    }
}
