import type { Filter, WithoutEdges, integer } from '@sage/xtrem-client';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    Item,
    ItemBinding,
    ItemCategory,
    Location,
    LocationType,
    LocationZone,
    ReasonCode,
    SequenceNumber,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import { toInteger } from '@sage/xtrem-shared';
import type { GraphApi, StockCountLine, StockCountStatus } from '@sage/xtrem-stock-api';
import type { LotBinding, StockDocumentTransactionStatus, StockStatus } from '@sage/xtrem-stock-data-api';
import * as StockPillColor from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDocumentHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-document-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import { StockCountMenu } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-count-menu';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import type * as StockInterfaces from '../client-functions/interfaces';
import * as PillColor from '../client-functions/pill-color';

@ui.decorators.page<StockCount>({
    menuItem: StockCountMenu,
    priority: 150,
    title: 'Stock count',
    objectTypeSingular: 'Stock count',
    objectTypePlural: 'Stock counts',
    idField() {
        return this.number;
    },
    node: '@sage/xtrem-stock/StockCount',
    hasAttachmentsSection: true,
    mode: 'tabs',
    navigationPanel: {
        orderBy: { effectiveDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.text({ bind: 'number', title: 'Number' }),
            stockSite: ui.nestedFields.reference({
                bind: 'stockSite',
                valueField: 'id',
                node: '@sage/xtrem-system/Site',
                title: 'Site',
                tunnelPage: undefined,
            }),
            line2Right: ui.nestedFields.date({ bind: 'effectiveDate', title: 'Date' }),
            titleRight: ui.nestedFields.label({
                title: 'Status',
                optionType: '@sage/xtrem-stock/StockCountStatus',
                bind: 'status',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('StockCountStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isHiddenOnMainField: true,
            }),
            counter: ui.nestedFields.text({
                bind: 'counter',
                title: 'Counter',
                isHiddenOnMainField: true,
            }),
            stockTransactionStatus: ui.nestedFields.label({
                title: 'Stock status',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                bind: 'stockTransactionStatus',
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData.stockTransactionStatus,
                    ),
                isHiddenOnMainField: true,
            }),
        },
        optionsMenu: [{ title: '', graphQLFilter: { status: { _ne: 'draft' } } }],
    },
    async onLoad() {
        await this.init();
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.confirmZeroQuantity,
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.mainSection;
    },
    businessActions() {
        return [this.$standardCancelAction, this.start, this.post, this.saveStockCount, this.repost];
    },
    headerQuickActions() {
        return [this.print];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty: isDirty && !this.fromNotificationHistory,
            save: this.saveStockCount,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.managePageActions(isDirty);
    },
})
export class StockCount extends ui.Page<GraphApi> implements financeInterfaces.PageWithDefaultDimensions {
    isSaving: boolean;

    reasonCodeIncrease: Partial<ReasonCode>;

    reasonCodeDecrease: Partial<ReasonCode>;

    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    fromNotificationHistory: boolean;

    @ui.decorators.section<StockCount>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<StockCount>({ title: 'Lines' })
    linesSection: ui.containers.Section;

    @ui.decorators.block<StockCount>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<StockCount>({
        title: 'Posting',
        isHidden() {
            return !(this.stockSite.value?.legalCompany?.doStockPosting || false) || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<StockCount>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.textField<StockCount>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    // hasLotInLines, hasSublotInLines, hasExpiryManagementInLines are used to determine
    // if the lot, sublot, and expiration date fields should be displayed in the lines
    @ui.decorators.switchField<StockCount>({
        isHidden: true,
    })
    hasLotInLines: ui.fields.Switch;

    @ui.decorators.switchField<StockCount>({
        isHidden: true,
    })
    hasSublotInLines: ui.fields.Switch;

    @ui.decorators.switchField<StockCount>({
        isHidden: true,
    })
    hasExpiryManagementInLines: ui.fields.Switch;

    @ui.decorators.block<StockCount>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Selection criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.textField<StockCount>({
        title: 'Number',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        width: 'medium',
    })
    number: ui.fields.Text;

    @ui.decorators.referenceField<StockCount, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        helperTextField: 'id',
        width: 'medium',
        isMandatory: true,
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.checkbox({ bind: 'isLocationManaged', isHidden: true }),
            ui.nestedFields.technical<StockCount, Site, Company>({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [
                    ui.nestedFields.technical({
                        bind: 'doStockPosting',
                    }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                ],
            }),
        ],
        async onChange() {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                site: this.stockSite.value,
            });
        },
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<StockCount>({
        title: 'Date',
        parent() {
            return this.mainBlock;
        },
        width: 'small',
        isMandatory: true,
        isReadOnly: true,
    })
    effectiveDate: ui.fields.Date;

    @ui.decorators.textField<StockCount>({
        title: 'Description',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isMandatory: true,
        isReadOnly() {
            return this.status.value === 'closed';
        },
    })
    description: ui.fields.Text;

    @ui.decorators.textField<StockCount>({
        title: 'Counter',
        parent() {
            return this.mainBlock;
        },
        width: 'medium',
        isReadOnly() {
            return this.status.value === 'closed';
        },
    })
    counter: ui.fields.Text;

    @ui.decorators.labelField<StockCount>({
        title: 'Status',
        optionType: '@sage/xtrem-stock/StockCountStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('StockCountStatus', this.status.value);
        },
    })
    status: ui.fields.Label<StockCountStatus>;

    @ui.decorators.labelField<StockCount>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.mainBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('StockCountStatus', this.stockTransactionStatus.value);
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.referenceField<StockCount, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        width: 'medium',
        isReadOnly: true,
    })
    fromItem: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<StockCount, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        width: 'medium',
        isReadOnly: true,
    })
    toItem: ui.fields.Reference<Item>;

    @ui.decorators.multiReferenceField<StockCount, ItemCategory>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Category',
        node: '@sage/xtrem-master-data/ItemCategory',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly: true,
    })
    categories: ui.fields.MultiReference<ItemCategory>;

    @ui.decorators.switchField<StockCount>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'In stock only',
        isReadOnly: true,
    })
    hasStockRecords: ui.fields.Switch;

    @ui.decorators.multiReferenceField<StockCount, LocationZone>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Zone',
        node: '@sage/xtrem-master-data/LocationZone',
        valueField: 'name',
        helperTextField: 'id',
        width: 'medium',
        isReadOnly: true,
        isHidden() {
            return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
        },
    })
    zones: ui.fields.MultiReference<LocationZone>;

    @ui.decorators.multiReferenceField<StockCount, Location>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Location',
        node: '@sage/xtrem-master-data/Location',
        valueField: 'name',
        helperTextField: 'id',
        width: 'medium',
        isReadOnly: true,
        isHidden() {
            return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
        },
    })
    locations: ui.fields.MultiReference<Location>;

    @ui.decorators.dateField<StockCount>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Items not counted as of',
        width: 'small',
        isReadOnly: true,
    })
    lastCountDate: ui.fields.Date;

    @ui.decorators.aggregateField<StockCount, StockCountLine>({
        bind: 'lines',
        aggregateOn: '_id',
        aggregationMethod: 'distinctCount',
        isHidden: true,
        parent() {
            return this.criteriaBlock;
        },
        filter: {
            shouldEnterOrderCost: { _eq: true },
        },
    })
    costWarningNumber: ui.fields.Aggregate;

    @ui.decorators.tableField<StockCount, StockInterfaces.StockCountPageLine>({
        title: 'Document lines',
        canSelect: false,
        canExport: true,
        bind: 'lines',
        node: '@sage/xtrem-stock/StockCountLine',
        orderBy: {
            _sortValue: +1,
        },
        canAddNewLine: true,
        hasLineNumbers: true,
        canResizeColumns: true,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.linesSection;
        },
        mapServerRecord(record: ui.PartialNode<StockInterfaces.StockCountPageLine>) {
            // Need to set the transient lot fields when loading the page

            // test isNonStockItem to avoid setting lot fields when entering new values
            if (toInteger(record?._id ?? 0) > 0 && (!this.isSaving || record.isModified) && !record.isNonStockItem) {
                const stockDetail = JSON.parse(record.jsonStockDetails ?? '[{}]')[0];
                const stockDetailLot = stockDetail?.stockDetailLot ?? '{}';

                record.stockDetail = {
                    ...record.stockDetail,
                    stockDetailLot: {
                        ...record.stockDetail?.stockDetailLot,
                        lotId: stockDetailLot?.lot?.id ?? stockDetailLot?.lotNumber ?? '',
                    },
                };
                record.lot = stockDetailLot?.lot;
                record.sublot = record.lot?.sublot ?? stockDetailLot?.sublot;
                record.supplierLot = record.lot?.supplierLot ?? stockDetailLot?.supplierLot;
                record.expirationDate = record.lot?.expirationDate ?? stockDetailLot?.expirationDate;
            }
            return {
                ...record,
            };
        },
        warningMessage() {
            return toInteger(this.costWarningNumber.value ?? 0) > 0 ? StockCount.warningMessageForOrderCost() : '';
        },
        columns: [
            ui.nestedFields.icon<StockCount, StockInterfaces.StockCountPageLine>({
                bind: '_id', // link to a non-transient field
                title: '',
                size: 'small',
                color: ui.tokens.colorsSemanticCaution500,
                isTitleHidden: true,
                map(_value?, rowValue?) {
                    return rowValue.shouldEnterOrderCost ? 'warning' : '';
                },
                isHidden() {
                    return toInteger(this.costWarningNumber.value ?? 0) === 0;
                },
            }),
            ui.nestedFields.label<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Count status',
                bind: 'status',
                optionType: '@sage/xtrem-stock/StockCountLineStatus',
                style: (_id, rowData) => PillColor.getLabelColorByStatus('StockCountLineStatus', rowData?.status),
            }),
            ui.nestedFields.label<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    StockPillColor.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData.stockTransactionStatus,
                    ),
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value || '');
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.reference<StockCount, StockInterfaces.StockCountPageLine, Item>({
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                title: 'Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.reference<StockCount, Item, UnitOfMeasure>({
                        valueField: 'name',
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.technical({ bind: 'isExpiryManaged' }),
                    ui.nestedFields.reference<StockCount, Item, SequenceNumber>({
                        bind: 'lotSequenceNumber',
                        node: '@sage/xtrem-master-data/SequenceNumber',
                        valueField: 'id',
                        isHidden: true,
                    }),
                    ui.nestedFields.technical({ bind: 'serialNumberManagement' }),
                    ui.nestedFields.technical({ bind: 'serialNumberUsage' }),
                    ui.nestedFields.technical({ bind: 'isPhantom' }),
                ],
                isReadOnly(_value, rowData) {
                    return Number(rowData?._id ?? 0) > 0;
                },
                filter() {
                    const returnedObject = {
                        itemSites: { _atLeast: 1, site: { _id: this.stockSite.value?._id || '' } },
                    };
                    return this.$.isServiceOptionEnabled('serialNumberOption')
                        ? {
                              ...returnedObject,
                              serialNumberManagement: 'notManaged',
                          }
                        : returnedObject;
                },
                validation(value, rowValue) {
                    if (
                        toInteger(this.$.recordId ?? 0) < 0 &&
                        this.$.isServiceOptionEnabled('serialNumberOption') &&
                        value.serialNumberManagement === 'managed'
                    ) {
                        return ui.localize(
                            '@sage/xtrem-stock/page__stock_count__forbid_addition_serialized_managed_item',
                            'A stock line for a serialized item cannot be added to an existing stock count.',
                        );
                    }

                    if (value.lotManagement !== 'notManaged') {
                        if (!rowValue.stockDetail?.stockDetailLot?.lotId && !value.lotSequenceNumber) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_lot_is_mandatory',
                                'The lot is mandatory.',
                            );
                        }
                    }

                    if (value.isExpiryManaged) {
                        if (!rowValue.expirationDate) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_receipt_details_panel__notification__expiration_date_is_mandatory',
                                'The expiration date is mandatory.',
                            );
                        }
                    }

                    if (value.lotManagement === 'lotSublotManagement') {
                        if (!rowValue.sublot) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_sublot_is_mandatory',
                                'The sublot is mandatory.',
                            );
                        }
                    }

                    return undefined;
                },
                async onChange(_columnName, rowData) {
                    if (!rowData.item) return;

                    await this.updateColumnsDisplay(rowData.item);

                    const { storedAttributes, storedDimensions } =
                        await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                            page: this,
                            _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                            dimensionDefinitionLevel: 'stockDirect',
                            site: this.stockSite.value,
                            item: rowData.item,
                        });
                    const newLineOrderCost = (
                        await StockDataUtils.getLineCost(this, {
                            item: rowData.item?._id,
                            site: this.stockSite.value?._id,
                            dateOfValuation: this.effectiveDate.value,
                        })
                    ).orderCost;
                    const shouldEnterOrderCost = StockCount.calculateShouldEnterOrderCost({
                        item: rowData.item,
                        newLineOrderCost,
                        canEnterOrderCost: rowData.canEnterOrderCost,
                    });

                    this.lines.addOrUpdateRecordValue({
                        _id: rowData._id,
                        stockUnit: rowData.item ? rowData.item.stockUnit : null,
                        newLineOrderCost,
                        shouldEnterOrderCost,
                        storedAttributes,
                        storedDimensions,
                        // If lot characteristics are already set, we keep them if the lot management has not changed
                        // considering that the user only made a mistake on the item id
                        stockDetail: {
                            stockDetailLot: {
                                lotId:
                                    rowData.item.lotManagement === 'notManaged'
                                        ? ''
                                        : rowData.stockDetail?.stockDetailLot?.lotId,
                            },
                        },
                        supplierLot: rowData.item.lotManagement === 'notManaged' ? '' : rowData.supplierLot,
                        sublot: rowData.item.lotManagement !== 'lotSublotManagement' ? '' : rowData.sublot,
                        expirationDate: !rowData.item.isExpiryManaged ? '' : rowData.expirationDate,
                    });
                },
            }),
            ui.nestedFields.text({
                isReadOnly: true,
                title: 'Item ID',
                bind: { item: { id: true } },
            }),
            ui.nestedFields.text({
                isReadOnly: true,
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<StockCount, StockInterfaces.StockCountPageLine, UnitOfMeasure>({
                isHidden: true,
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.reference<StockCount, StockInterfaces.StockCountPageLine, Location>({
                title: 'Location',
                bind: 'location',
                node: '@sage/xtrem-master-data/Location',
                valueField: 'name',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                    }),
                    ui.nestedFields.reference<StockCount, Location, LocationType>({
                        bind: 'locationType',
                        title: 'Type',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<StockCount, Location, Site>({
                        isHidden: true,
                        bind: 'site',
                        node: '@sage/xtrem-system/Site',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({
                                bind: 'name',
                            }),
                            ui.nestedFields.text({
                                bind: 'id',
                            }),
                        ],
                    }),
                    ui.nestedFields.reference<StockCount, Location, LocationZone>({
                        isHidden: true,
                        bind: 'locationZone',
                        node: '@sage/xtrem-master-data/LocationZone',
                        valueField: 'name',
                    }),
                ],
                isDisabled() {
                    return this.lineStatusReadonly();
                },
                isMandatory() {
                    return !!this.stockSite.value?.isLocationManaged;
                },
                isReadOnly(_value, rowData) {
                    return (
                        !(Number(rowData?._id ?? 0) < 0 || rowData?.isNonStockItem) || rowData?.status === 'excluded'
                    );
                },
                isHidden() {
                    return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                },
                filter() {
                    return {
                        locationZone: { site: { _id: this.stockSite.value?._id || '' } },
                    };
                },
                validation(value, rowValue) {
                    if (rowValue.location?._id) {
                        if (value.site.isLocationManaged) {
                            return ui.localize(
                                '@sage/xtrem-stock/events/control__location_not_managed',
                                'Locations are not managed for the {{site}} site.',
                                { site: value.site.name },
                            );
                        }
                        if (rowValue.location.site._id !== value.site._id) {
                            return ui.localize(
                                '@sage/xtrem-stock/events/control__location_for_wrong_site',
                                'The location should be at the {{site}} site.',
                                { site: value.site.name },
                            );
                        }
                    } else if (!value.site.isLocationManaged) {
                        return ui.localize(
                            '@sage/xtrem-stock/events/control__location_missing',
                            'The location is mandatory.',
                        );
                    }

                    return undefined;
                },
                onChange(_columnName, rowData) {
                    if (Number(rowData._id) < 0 || rowData.isNonStockItem) this.updateJsonStockDetails(rowData);
                },
            }),
            ui.nestedFields.reference<StockCount, StockInterfaces.StockCountPageLine, Location>({
                isReadOnly: true,
                title: 'Zone',
                bind: 'location',
                node: '@sage/xtrem-master-data/Location',
                valueField: { locationZone: { name: true } },
                isHidden() {
                    return this.stockSite.value ? !this.stockSite.value.isLocationManaged : false;
                },
            }),
            ui.nestedFields.reference<StockCount, StockInterfaces.StockCountPageLine, StockStatus>({
                isMandatory: true,
                title: 'Quality control',
                bind: 'stockStatus',
                node: '@sage/xtrem-stock-data/StockStatus',
                tunnelPage: '@sage/xtrem-stock-data/StockStatus',
                valueField: 'name',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                ],
                isDisabled() {
                    return this.lineStatusReadonly();
                },
                isReadOnly(_stockStatus, rowData) {
                    return (
                        !(Number(rowData?._id ?? 0) < 0 || rowData?.isNonStockItem) || rowData?.status === 'excluded'
                    );
                },
                validation(_value, rowValue) {
                    if (!rowValue.status) {
                        return ui.localize(
                            '@sage/xtrem-stock/pages__stock_receipt_detail_panel__notification__The_status_is_mandatory',
                            'The status is mandatory.',
                        );
                    }
                    return undefined;
                },
                onChange(_columnName, rowData) {
                    if (Number(rowData._id) < 0 || rowData.isNonStockItem) this.updateJsonStockDetails(rowData);
                },
            }),
            // lot management refactoring: lot filter select field: just for entering an existing lot or new lot number to feed
            // the other hidden properties 'lot' if existing lot was selected or 'lotNumber' if new lot number was entered
            // (depends on temporary service option lotManagementOption: active for new behavior, inactive for old behavior)
            ui.nestedFields.filterSelect<StockCount, StockInterfaces.StockCountPageLine, LotBinding>({
                title: 'Lot',
                lookupDialogTitle: 'Select lot',
                node: '@sage/xtrem-stock-data/Lot',
                bind: { stockDetail: { stockDetailLot: { lotId: true } } },
                valueField: 'id',
                columns: [
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.text({ bind: 'sublot', title: 'Sublot' }),
                    ui.nestedFields.date({ bind: 'expirationDate', title: 'Expiration date' }),
                    ui.nestedFields.text({ bind: 'supplierLot', title: 'Supplier lot number' }),
                    ui.nestedFields.reference<StockCount, LotBinding, ItemBinding>({
                        bind: 'item',
                        title: 'Item',
                        node: '@sage/xtrem-master-data/Item',
                        tunnelPage: '@sage/xtrem-master-data/Item',
                        valueField: 'name',
                        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                    }),
                ],
                isDisabled(_value, rowData) {
                    return rowData?.item?.lotManagement === 'notManaged' || this.lineStatusReadonly();
                },
                isReadOnly(_value, rowData) {
                    return (
                        !(Number(rowData?._id ?? 0) < 0 || rowData?.isNonStockItem) || rowData?.status === 'excluded'
                    );
                },
                isHidden(_value, rowData) {
                    // rowData is null when loading the page
                    if (!rowData || this.hasLotInLines.value) return !this.hasLotInLines.value;

                    // rowData is set when in sidePanel
                    return rowData?.item?.lotManagement === 'notManaged';
                },
                filter(rowData) {
                    return { item: { _id: rowData?.item?._id } };
                },
                async onChange(_columnName, rowData) {
                    const lotCharacteristics = await this.getLotCharacteristics(
                        rowData.stockDetail?.stockDetailLot?.lotId ?? '',
                        rowData.item?._id,
                    );

                    const updatedRowData = {
                        ...rowData,
                        ...(!rowData.stockDetail ? { stockDetail: { _id: -1, stockDetailLot: {} } } : {}),
                        ...(rowData.lot && Object.keys(lotCharacteristics).length === 0
                            ? // when the lot was an existing one and the user wants to change it to a new one
                              // we have to remove the lot properties
                              { lot: undefined, supplierLot: '', expirationDate: '', sublot: '' }
                            : lotCharacteristics),
                    };
                    if (Number(updatedRowData._id) < 0 || updatedRowData.isNonStockItem)
                        this.updateJsonStockDetails(updatedRowData);
                    else this.lines.addOrUpdateRecordValue(updatedRowData);
                },
            }),
            ui.nestedFields.technical<StockCount, StockInterfaces.StockCountPageLine>({
                bind: 'lot',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.text<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Sublot number',
                bind: 'sublot',
                isTransient: true,
                isMandatory(rowData) {
                    return rowData?.item?.lotManagement === 'lotSublotManagement';
                },
                isDisabled(_value, rowData) {
                    return rowData?.item?.lotManagement !== 'lotSublotManagement' || this.lineStatusReadonly();
                },
                isHidden(_value, rowData) {
                    if (!rowData || this.hasSublotInLines.value) return !this.hasSublotInLines.value;
                    return rowData?.item?.lotManagement !== 'lotSublotManagement';
                },
                isReadOnly(_value, rowData) {
                    return !(
                        Number(rowData?._id ?? 0) < 0 ||
                        rowData?.isNonStockItem ||
                        rowData?.status === 'excluded'
                    );
                },
                async onChange(_columnName, rowData) {
                    if (rowData.lot) {
                        // if a lot has been selected previously and the user changed the sublot >
                        // we try to find a lot with the same lotNumber and sublot. If we find one, we link the lot of the line
                        // to the found one otherwise we have to create a new lot
                        const [lot] = await this.readLots({
                            id: rowData?.lotNumber,
                            item: rowData?.item?._id,
                            sublot: rowData?.sublot,
                        });
                        if (lot) {
                            // if an existing lot was found > take the data from it
                            rowData.lot = lot;
                            rowData.lotNumber = lot.id;
                            rowData.expirationDate = lot.expirationDate;
                            rowData.sublot = lot.sublot;
                            rowData.supplierLot = lot.supplierLot;
                            // no existing lot was found > decide if a new lot has to be created
                        } else {
                            // when a new lot has to be created because of a different sublot > remove link to lot
                            rowData.lot = undefined;
                        }

                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                    if (Number(rowData._id) < 0 || rowData.isNonStockItem) this.updateJsonStockDetails(rowData);
                },
            }),
            ui.nestedFields.date<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Expiration date',
                bind: 'expirationDate',
                isTransient: true,
                isMandatory(rowData) {
                    return rowData?.item.isExpiryManaged;
                },
                isDisabled(_value, rowData) {
                    return !rowData?.item.isExpiryManaged || this.lineStatusReadonly();
                },
                isHidden(_value, rowData) {
                    if (!rowData || this.hasExpiryManagementInLines.value)
                        return !this.hasExpiryManagementInLines.value;

                    return !rowData?.item.isExpiryManaged;
                },
                isReadOnly(_value, rowData) {
                    return (
                        ((!!rowData?.lot || Number(rowData?._id ?? 0) > 0) && !rowData?.isNonStockItem) ||
                        rowData?.status === 'excluded'
                    );
                },
                onChange(_columnName, rowData: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    if (Number(rowData._id) < 0 || rowData.isNonStockItem) this.updateJsonStockDetails(rowData);
                },
            }),
            ui.nestedFields.text<StockCount, StockInterfaces.StockCountPageLine>({
                isHiddenOnMainField: true,
                title: 'Supplier lot number',
                bind: 'supplierLot',
                isTransient: true,
                isDisabled(_value, rowData) {
                    return (
                        rowData?.item.lotManagement === 'notManaged' ||
                        this.lineStatusReadonly() ||
                        rowData?.stockDetail?.stockDetailLot?.lot // is an existing lot selected ?
                    );
                },
                isHidden(_value, rowData) {
                    if (!rowData) return !this.lines.value.some(line => line.item?.lotManagement !== 'notManaged');

                    return rowData?.item.lotManagement === 'notManaged';
                },
                isReadOnly(_value, rowData) {
                    if (rowData?.status === 'excluded') return true;
                    if (Number(rowData?._id ?? 0) > 0 && !rowData.isNonStockItem) return true;
                    return false;
                },
                onChange(_columnName, rowData: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    if (Number(rowData._id) < 0 || rowData.isNonStockItem) this.updateJsonStockDetails(rowData);
                },
            }),
            ui.nestedFields.text<StockCount, StockInterfaces.StockCountPageLine>({
                isHiddenOnMainField: true,
                title: 'Owner',
                bind: 'owner',
                isReadOnly(_value, rowData) {
                    return Number(rowData?._id ?? 0) > 0;
                },
            }),
            ui.nestedFields.numeric<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Stock quantity',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                scale(_rowId, rowData) {
                    return StockCount.quantityScale(rowData?.stockUnit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol || '';
                },
            }),
            ui.nestedFields.numeric<StockCount, StockInterfaces.StockCountPageLine>({
                // isMandatory: true,
                title: 'Counted quantity',
                bind: 'countedQuantityInStockUnit',
                min: 0,
                scale(_rowId, rowData) {
                    return StockCount.quantityScale(rowData?.stockUnit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol || '';
                },
                isDisabled() {
                    return this.lineStatusReadonly();
                },
                isReadOnly(_rowId, rowData) {
                    return rowData?.status === 'excluded';
                },
                async onChange(_columnName, rowData: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    if (Number(rowData._id) < 0 || rowData.isNonStockItem) this.updateJsonStockDetails(rowData);
                    this.updateSerialNumberQuantityPercentage(rowData);
                    await this.updateRowStatus(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                validation(value, rowValue) {
                    if (
                        rowValue.item?.serialNumberManagement === 'managed' &&
                        rowValue.item?.serialNumberUsage === 'issueAndReceipt'
                    ) {
                        if (value > Number(rowValue?.quantityInStockUnit)) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_count__counted_quantity_can_not_be_greater_than_quantity_in_stock',
                                'The counted quantity cannot exceed the stock quantity for a serialized item.',
                            );
                        }
                        const selectedQuantity = StockCount.quantityOfSelectedSerialNumbers(rowValue.jsonSerialNumbers);
                        if (value < selectedQuantity) {
                            return ui.localize(
                                '@sage/xtrem-stock/pages__stock_count__selected_serial_number_quantity_too_high',
                                'The number of selected serial numbers ({{selectedQuantity}}) cannot exceed the counted quantity ({{countedQuantity}}).',
                                { countedQuantity: value, selectedQuantity },
                            );
                        }
                    }

                    return undefined;
                },
            }),
            ui.nestedFields.numeric<StockCount, StockInterfaces.StockCountPageLine>({
                isReadOnly: true,
                title: 'Quantity variance',
                bind: 'quantityVariance',
                scale(_rowId, rowData) {
                    return StockCount.quantityScale(rowData?.stockUnit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol || '';
                },
            }),
            ui.nestedFields.progress<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Serial number',
                bind: 'countedSerialNumberPercentage',
                isHidden() {
                    return !this.lines.value.some(line => line.item?.serialNumberManagement === 'managed');
                },
            }),
            ui.nestedFields.numeric<StockCount, StockInterfaces.StockCountPageLine>({
                isReadOnly: true,
                title: 'Quantity variance %',
                bind: 'quantityVariancePercentage',
                postfix: '%',
                scale: 2,
            }),
            ui.nestedFields.numeric<StockCount, StockInterfaces.StockCountPageLine>({
                title: 'Unit cost',
                bind: 'newLineOrderCost',
                min: 0,
                warningMessage(_value, rowValue: any) {
                    return rowValue.shouldEnterOrderCost ? StockCount.warningMessageForOrderCost() : '';
                },
                isDisabled(_rowId, rowData: any) {
                    return !rowData || !rowData.canEnterOrderCost;
                },
                prefix() {
                    return this.costSymbol();
                },
                scale() {
                    return this.costScale();
                },
                onChange(_columnName, rowData) {
                    const shouldEnterOrderCost = StockCount.calculateShouldEnterOrderCost(rowData);
                    if (rowData.shouldEnterOrderCost !== shouldEnterOrderCost) {
                        this.costWarningNumber.value =
                            (this.costWarningNumber.value ?? 0) + (shouldEnterOrderCost ? 1 : -1);

                        this.lines.addOrUpdateRecordValue({
                            _id: rowData._id,
                            shouldEnterOrderCost,
                        });
                    }
                },
            }),
            ui.nestedFields.technical<StockCount, StockInterfaces.StockCountPageLine>({ bind: 'jsonStockDetails' }),
            ui.nestedFields.technical<StockCount, StockInterfaces.StockCountPageLine>({ bind: 'jsonSerialNumbers' }),
            ui.nestedFields.technical({ bind: 'computedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'isAddedDuringCount' }),
            ui.nestedFields.technical({ bind: 'canBeDeleted' }),
            ui.nestedFields.technical({ bind: 'isNonStockItem' }),
            ui.nestedFields.technical({ bind: 'shouldEnterOrderCost' }),
            ui.nestedFields.technical({ bind: 'canEnterOrderCost' }),
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Count',
                isHidden(_recordId, rowItem) {
                    return (
                        rowItem.status === 'toBeCounted' ||
                        this.status.value === 'closed' ||
                        // TODO: A line that has been excluded because it had no stock at creation time but had stock at start time
                        // cannot be counted anymore (XT-83850). For this case, the 'excluded' status should be replaced
                        // by a new status (e.g. 'removed') to allow the user to count the other lines
                        // that were excluded with the 'Exclude' action.
                        (rowItem.status === 'excluded' && Number(rowItem.quantityInStockUnit) === 0)
                    );
                },
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    await this.changeRowStatus(rowItem, 'toBeCounted');
                },
            },
            {
                icon: 'minus',
                title: 'Exclude',
                isHidden(_recordId, rowItem) {
                    return (
                        rowItem.canBeDeleted ||
                        rowItem.status === 'excluded' ||
                        ['inProgress', 'completed'].includes(rowItem.stockTransactionStatus)
                    );
                },
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    await this.changeRowStatus(rowItem, 'excluded');
                },
            },
            {
                icon: 'tick',
                title: 'Confirm zero quantity',
                isHidden(_recordId, rowItem) {
                    return !(
                        Number(rowItem.countedQuantityInStockUnit ?? 0) === 0 &&
                        rowItem.status === 'toBeCounted' &&
                        this.status.value === 'countInProgress'
                    );
                },
                async onClick(_rowId: string, rowItem: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    this.updateSerialNumberQuantityPercentage(rowItem);
                    await this.changeRowStatus(rowItem, 'counted');
                },
            },
            {
                icon: 'edit',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<StockInterfaces.StockCountPageLine>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            {
                                documentLine: rowData,
                            },
                            {
                                editable: !this.lineStatusReadonly() || this.fromNotificationHistory,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'edit',
                title: 'Serial numbers',
                isHidden(_recordId, rowData) {
                    return (
                        rowData.item?.serialNumberManagement !== 'managed' ||
                        Number(rowData.countedQuantityInStockUnit ?? 0) === 0
                    );
                },
                async onClick(rowId, rowData: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
                    const serialNumbersPanelResult = await this.$.dialog.page(
                        '@sage/xtrem-stock/StockCountSerialNumbersPanel',
                        {
                            args: JSON.stringify({
                                isEditable: !this.lineStatusReadonly(),
                                stockCountLineId: rowId,
                                quantityInStockUnit: Number(rowData.quantityInStockUnit ?? 0),
                                countedQuantity: Number(rowData.countedQuantityInStockUnit ?? 0),
                                item: rowData.item?._id,
                                jsonSerialNumbers: rowData.jsonSerialNumbers,
                                stockSite: this.stockSite.value?._id,
                                unit: rowData.item?.stockUnit?._id,
                            } as StockInterfaces.StockCountSerialNumberPanelParameters),
                        },
                        {
                            resolveOnCancel: true,
                            rightAligned: true,
                            size: 'large',
                        },
                    );
                    if (serialNumbersPanelResult.output) {
                        serialNumbersPanelResult.output._id = rowId;
                        this.lines.addOrUpdateRecordValue(serialNumbersPanelResult.output);
                        const rowItem = this.lines.getRecordValue(rowId);
                        if (!rowItem) return;
                        await this.updateRowStatus(rowItem);
                        this.updateSerialNumberQuantityPercentage(rowItem);
                        await this.lines.validate(); // to remove error if it has been fixed into the panel
                    }
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_recordId, rowItem) {
                    return !rowItem.canBeDeleted;
                },
                onClick(recordId) {
                    if (this.lines.getRecordValue(recordId)?.shouldEnterOrderCost) {
                        this.costWarningNumber.value = (this.costWarningNumber.value ?? 0) - 1;
                    }
                    this.lines.removeRecord(recordId);
                },
            },
        ],
        fieldActions() {
            return [this.defaultDimension];
        },
        sidebar: {
            title(_id, recordValue) {
                if (+(recordValue?._id ?? '') < 0) {
                    return ui.localize('@sage/xtrem-stock/page__stock_count__add_new_line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            layout() {
                return {
                    general: {
                        title: ui.localize(
                            '@sage/xtrem-stock/pages__stock_count__sidebar_tab_title_information',
                            'Information',
                        ),
                        blocks: {
                            mainBlock: {
                                fields: ['item', 'location', 'stockStatus'],
                            },
                            lotBlock: {
                                fields: [
                                    { stockDetail: { stockDetailLot: { lotId: true } } },
                                    'expirationDate',
                                    'supplierLot',
                                    'sublot',
                                ],
                            },
                            quantityBlock: {
                                fields: ['countedQuantityInStockUnit', 'newLineOrderCost'],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<StockInterfaces.StockCountPageLine>;

    @ui.decorators.tableField<StockCount, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'targetDocumentSysId' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.postingMessages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.postingMessages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.pageAction<StockCount>({
        title: 'Print',
        icon: 'print',
        async onClick() {
            this.printSelectBlock.isHidden = false;
            await this.$.dialog.custom('info', this.printSelectBlock, {
                resolveOnCancel: true,
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
            });

            this.$.setPageClean();
        },
    })
    print: ui.PageAction;

    @ui.decorators.textAreaField<StockCount>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    postingMessages: ui.fields.TextArea;

    @ui.decorators.pageAction<StockCount>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'stockCount',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.status ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.block<StockCount>({
        width: 'small',
        isTitleHidden: true,
        isHidden: true,
    })
    printSelectBlock: ui.containers.Block;

    @ui.decorators.switchField<StockCount>({
        title: 'Show quantity in stock',
        isTransient: true,
        parent() {
            return this.printSelectBlock;
        },
    })
    showQuantityInStock: ui.fields.Switch;

    @ui.decorators.buttonField<StockCount>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.printSelectBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/stock_count_pages__stock_count_print', 'Print');
        },
        async onClick() {
            this.printSelectBlock.isHidden = true;
            this.printSelectBlock.isHidden = true;
            await this.$.dialog.page(
                '@sage/xtrem-reporting/PrintDocument',
                {
                    reportName: 'stockCount',
                    stockCount: this.$.recordId ?? '',
                    showQuantityInStock: this.showQuantityInStock.value ?? true,
                },
                { size: 'extra-large' },
            );
        },
        isTitleHidden: true,
    })
    printDocument: ui.fields.Button;

    costScale() {
        return MasterDataUtils.getScaleValue(2, this.stockSite.value?.financialCurrency?.decimalDigits);
    }

    costSymbol() {
        return this.stockSite.value?.financialCurrency?.symbol || '';
    }

    static quantityScale(decimalDigits: number | undefined) {
        return MasterDataUtils.getScaleValue(0, decimalDigits);
    }

    private lineStatusReadonly() {
        return ['closed', 'toBeCounted'].includes(this.status.value ?? '');
    }

    private async getReasonCode(isQuantityVariancePositive: boolean): Promise<Partial<ReasonCode>> {
        return extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/ReasonCode')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                        },
                        {
                            filter: {
                                quantityDecreaseAdjustment: !isQuantityVariancePositive,
                                quantityIncreaseAdjustment: isQuantityVariancePositive,
                                isDefault: true,
                            },
                        },
                    ),
                )
                .execute(),
        )[0];
    }

    private async getLotCharacteristics(lotNumber: string, itemId: string) {
        // first try to read lot from database to find out if it exists
        const existingLots = await this.readLots({ id: lotNumber, item: itemId });
        if (existingLots.length > 0) {
            // if user selected an existing lot
            const [existingLot] = existingLots;
            if (existingLots.length === 1) {
                // exactly 1 found > feed 'lot' property and do onChange for 'lot'

                // if a lot has been selected > take the data from it
                return {
                    lot: existingLot,
                    expirationDate: existingLot.expirationDate,
                    sublot: existingLot.sublot,
                    supplierLot: existingLot.supplierLot,
                };
            }
            return {
                // more than 1 found > feed 'lot' and 'lotNumber' properties but empty all other lot fields
                expirationDate: '',
                sublot: '',
                supplierLot: '',
            };
        }

        return {};
    }

    /**
     * @returns the quantity of selected serial number stored in the jsonSerialNumbers property
     */
    private static quantityOfSelectedSerialNumbers(jsonSerialNumbers: string) {
        const ranges: Array<StockInterfaces.StockCountLineSerialNumberNumericRange> = jsonSerialNumbers
            ? (MasterDataUtils.tryParseJSON<{
                  ranges: Array<StockInterfaces.StockCountLineSerialNumberNumericRange>;
              }>(jsonSerialNumbers)?.ranges ?? [])
            : [];
        return ranges.reduce((sum: integer, range) => sum + range.quantity, 0);
    }

    updateSerialNumberQuantityPercentage(rowData: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
        if (
            !(
                rowData.item?.serialNumberManagement === 'managed' &&
                rowData.item?.serialNumberUsage === 'issueAndReceipt'
            )
        )
            return;
        const qty = Number(rowData.countedQuantityInStockUnit ?? 0);
        rowData.countedSerialNumberPercentage = (
            qty > 0 ? (StockCount.quantityOfSelectedSerialNumbers(rowData.jsonSerialNumbers ?? '') * 100) / qty : 0
        ).toString();
        this.lines.setRecordValue(rowData);
    }

    async updateRowStatus(rowData: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
        let newStatus: StockInterfaces.StockCountPageLine['status'] =
            Number(rowData.countedQuantityInStockUnit ?? 0) > 0 ? 'counted' : 'toBeCounted';
        if (
            rowData.item?.serialNumberManagement === 'managed' &&
            rowData.item?.serialNumberUsage === 'issueAndReceipt'
        ) {
            const quantitySelected = StockCount.quantityOfSelectedSerialNumbers(rowData.jsonSerialNumbers ?? '');
            const noOfQuantitySelected =
                quantitySelected === Number(rowData.countedQuantityInStockUnit) ? 'counted' : 'countInProgress';
            newStatus = quantitySelected === 0 ? 'toBeCounted' : noOfQuantitySelected;
        }
        await this.changeRowStatus(rowData, newStatus);
    }

    async changeRowStatus(
        rowItem: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>,
        newStatus: StockInterfaces.StockCountPageLine['status'],
    ) {
        if (rowItem.status === 'excluded' && !rowItem.isNonStockItem) {
            if (Number(rowItem._id) > 0) {
                this.$.loader.isHidden = false;
                rowItem.quantityInStockUnit = String(
                    await this.$.graph
                        .node('@sage/xtrem-stock/StockCountLine')
                        .queries.getStockQuantity(true, { stockCountLine: `${rowItem._id}` })
                        .execute(),
                );
                this.$.loader.isHidden = true;
            }
        }
        if (newStatus === 'excluded') {
            if (rowItem.isNonStockItem && JSON.parse(rowItem.jsonStockDetails)) {
                rowItem.jsonStockDetails = '';
            }
            rowItem.countedQuantityInStockUnit = '0';
            rowItem.jsonSerialNumbers = JSON.stringify({ ranges: [] });
            this.updateSerialNumberQuantityPercentage(rowItem);
        }

        if (rowItem.shouldEnterOrderCost) {
            this.costWarningNumber.value = (this.costWarningNumber.value ?? 0) + (newStatus === 'excluded' ? -1 : +1);
        }

        rowItem.status = newStatus;
        rowItem.isModified = true;
        this.lines.setRecordValue(rowItem);
    }

    @ui.decorators.pageAction<StockCount>({
        icon: 'arrow_right',
        title: 'Set default dimensions',
        isDisabled() {
            return (
                !this.$.recordId ||
                !dimensionPanelHelpers.isDefaultDimensionActionActive(this.$.recordId, this.$.isDirty) ||
                this.lineStatusReadonly()
            );
        },
        async onClick() {
            const filter = () => !this.lineStatusReadonly();
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'stockDirect',
                companyId: Number(this.stockSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                filter,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        title: 'Confirm zero quantities',
        isDisabled() {
            return !this.$.recordId || this.$.isDirty || this.lineStatusReadonly();
        },
        isHidden() {
            return !this.$.recordId || this.lineStatusReadonly();
        },
        onError(error) {
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            this.$.loader.isHidden = false;
            const recordsUpdated = await this.$.graph
                .node('@sage/xtrem-stock/StockCount')
                .asyncOperations.confirmZeroQuantity.runToCompletion(true, {
                    number: this.number.value ?? '',
                })
                .execute();
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-stock/pages__stock_count__confirm_zero_quantity_result',
                    'Lines updated: {{recordsUpdated}}.',
                    { recordsUpdated },
                ),
                { type: 'success', timeout: 5000 },
            );
            if (recordsUpdated) {
                await this.$.router.refresh();
                await this.$.refreshNavigationPanel();
            }

            this.$.loader.isHidden = true;
        },
    })
    confirmZeroQuantity: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        title: 'Start',
        onError(e) {
            return MasterDataUtils.formatError(this, e);
        },
        async onClick() {
            await this.startCount();
        },
    })
    start: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        title: 'Post stock',
        isHidden() {
            return (
                !['draft', 'error'].includes(this.stockTransactionStatus.value || '') ||
                !this._id.value ||
                this.$.isDirty
            );
        },
        onError(e) {
            return MasterDataUtils.formatError(this, e);
        },
        async onClick() {
            this.post.isDisabled = true;
            await this.postCount();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        title: 'Repost',
        isHidden() {
            return !(this.fromNotificationHistory && (this.stockSite?.value?.legalCompany?.doStockPosting || false));
        },
        isDisabled() {
            return !this.$.isDirty;
        },
        async onClick() {
            StockDocumentHelper.catchPostingError((await this.stockCountRepost()).message, this);
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        icon: 'save',
        title: 'Save',
        access: {
            bind: '$update',
        },
        onError(e) {
            this.isSaving = false;
            this.$.loader.isHidden = true;
            return MasterDataUtils.formatError(this, e);
        },
        async onClick() {
            this.$.loader.isHidden = false;
            this.isSaving = true;

            await this.$standardSaveAction.execute(false);
            this.isSaving = false;
            this.$.loader.isHidden = true;

            await this.$.router.refresh();
            await this.$.refreshNavigationPanel();
        },
    })
    saveStockCount: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.stockTransactionStatus.value !== 'completed';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                {
                    origin: 'header',
                    tableField: this.lines,
                },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<StockCount>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.stockTransactionStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    getSerializedValues() {
        const { ...values } = this.$.values;
        // criteria are frozen
        delete values.fromItem;
        delete values.toItem;
        delete values.locations;
        delete values.zones;
        delete values.categories;
        delete values.hasStockRecords;
        delete values.lastCountDate;

        if (values.lines) {
            values.lines.forEach((recordedLine: ui.PartialCollectionValue<StockInterfaces.StockCountPageLine>) => {
                const isNonStockItem = this.lines.value.find(line => line._id === recordedLine._id)?.isNonStockItem;
                if (Number(recordedLine._id ?? 0) > 0 && !isNonStockItem) {
                    const quantityVariance =
                        Number(recordedLine.countedQuantityInStockUnit ?? 0) -
                        Number(recordedLine.quantityInStockUnit ?? 0);
                    const commonUpdate = {
                        quantityInStockUnit: quantityVariance,
                        // the order cost must be sent if it has been entered
                        ...(recordedLine.newLineOrderCost || Number(recordedLine.newLineOrderCost ?? -1) === 0
                            ? { orderCost: recordedLine.newLineOrderCost }
                            : {}),
                        reasonCode: quantityVariance > 0 ? this.reasonCodeIncrease : this.reasonCodeDecrease,
                    };
                    recordedLine.jsonStockDetails = JSON.stringify([
                        {
                            _id: JSON.parse(recordedLine.jsonStockDetails || '[{}]')[0]?._id,
                            ...commonUpdate,
                        },
                    ]);
                }

                delete recordedLine.quantityInStockUnit;
                if (!this.$.isServiceOptionEnabled('serialNumberOption')) {
                    delete recordedLine.jsonSerialNumbers;
                }
            });
        }
        return values;
    }

    async init() {
        this.isSaving = false;
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.saveStockCount,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [this.$standardOpenCustomizationPageWizardAction],
        });
        this.managePageActions();
        this._defaultDimensionsAttributes = await initDefaultDimensions({
            page: this,
            dimensionDefinitionLevel: 'stockDirect',
            site: this.stockSite.value,
        });

        this.fromNotificationHistory = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;

        if (this.lineStatusReadonly()) {
            this.lines.isPhantomRowDisabled = true;
        }

        if (this.postingDetails.value.length === 1) {
            this.postingMessages.value = this.postingDetails.value[0].message || '';
            this.postingMessageBlock.isHidden = this.postingMessages.value === '';
        }

        this.reasonCodeIncrease = await this.getReasonCode(true);
        this.reasonCodeDecrease = await this.getReasonCode(false);
        this.showQuantityInStock.value = true;
        this.$.setPageClean();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    }

    async startCount() {
        this.$.loader.isHidden = false;
        if (this._id.value) {
            const started = await this.$.graph
                .node('@sage/xtrem-stock/StockCount')
                .mutations.start(true, { document: this._id.value })
                .execute();
            if (started) {
                this.managePageActions();
            }
        }
        this.$.loader.isHidden = true;
        await this.$.router.refresh();
        await this.$.refreshNavigationPanel();
    }

    managePageActions(isDirty: boolean = this.$.isDirty) {
        this.setDeleteAction();
        this.setStartAction(isDirty);
        this.setPostAction(isDirty);
    }

    setDeleteAction() {
        if (!this.$standardDeleteAction.isDisabled) {
            this.$standardDeleteAction.isDisabled = this.status.value !== 'toBeCounted' && this.lines.value.length > 0;
        }
    }

    setStartAction(isDirty: boolean) {
        this.start.isDisabled = isDirty || this.status.value !== 'toBeCounted';
        this.start.isHidden = this.start.isDisabled;
    }

    setPostAction(isDirty: boolean) {
        this.post.isDisabled =
            isDirty ||
            this.status.value !== 'counted' ||
            ['posted', 'inProgress'].includes(this.stockTransactionStatus.value ?? '');
        this.post.isHidden = this.post.isDisabled;
    }

    async postCount() {
        this.$.loader.isHidden = false;
        if (this._id.value) {
            StockDocumentHelper.catchPostingError(
                await this.$.graph
                    .node('@sage/xtrem-stock/StockCount')
                    .mutations.postToStock(true, { documentIds: [this.$.recordId ?? ''] })
                    .execute(),
            );
        }
        await this.$.router.refresh(true);
        await this.$.refreshNavigationPanel();

        this.$.loader.isHidden = true;
    }

    /**
     * Indicates if the price should be entered
     * @param line
     * @returns true if the price entered is 0 and the price can be entered
     */
    static calculateShouldEnterOrderCost(
        args: Pick<ui.PartialNode<StockCountLine>, 'item' | 'newLineOrderCost' | 'canEnterOrderCost'>,
    ): boolean {
        return (
            !!args.item &&
            args.item.id !== '' &&
            (!args.newLineOrderCost || Number(args.newLineOrderCost) === 0) &&
            !!args.canEnterOrderCost
        );
    }

    static warningMessageForOrderCost(): string {
        return ui.localize(
            '@sage/xtrem-stock/pages__stock_count__order_cost_should_be_entered',
            'Check your price. A value of 0 can affect the stock value.',
        );
    }

    async stockCountRepost() {
        this.$.loader.isHidden = false;
        const documentLines = this.lines.value
            .filter(line => Number(line._id) > 0)
            .map(line => ({
                baseDocumentLineSysId: line._id,
                storedAttributes: line.storedAttributes,
                storedDimensions: line.storedDimensions,
            }));

        const postResult = await this.$.graph
            .node('@sage/xtrem-stock/StockCount')
            .mutations.repost(
                {
                    wasSuccessful: true,
                    message: true,
                },
                { stockCount: this._id.value || '', documentLines },
            )
            .execute();

        this.$.loader.isHidden = true;
        if (!postResult.wasSuccessful) {
            this.$.showToast(
                `**${ui.localize(
                    '@sage/xtrem-stock/pages__stock_count__repost_errors',
                    'Errors while reposting:',
                )}**\n${postResult.message}`,
                { type: 'error', timeout: 20000 },
            );
        } else {
            this.$.showToast(postResult.message, { type: 'success' });
        }

        return postResult;
    }

    private updateJsonStockDetails(line: ui.PartialNodeWithId<StockInterfaces.StockCountPageLine>) {
        // For new line, calculate its jsonStockDetails

        const quantityVariance = Number(line.countedQuantityInStockUnit ?? 0) - Number(line.quantityInStockUnit ?? 0);
        const commonUpdate = {
            quantityInStockUnit: quantityVariance,
            reasonCode: quantityVariance > 0 ? this.reasonCodeIncrease : this.reasonCodeDecrease,
        };

        const lotNumber = line.stockDetail?.stockDetailLot?.lotId ?? '';
        line.jsonStockDetails = JSON.stringify([
            {
                stockRecord: null,
                effectiveDate: this.effectiveDate.value,
                site: this.stockSite.value,
                owner: this.stockSite.value?.id,
                item: line.item,
                location: line.location,
                status: line.stockStatus,
                stockUnit: line.stockUnit,
                existingLot: null,
                lotCreateData: null,
                ...(line.item?.lotManagement === 'notManaged'
                    ? {}
                    : {
                          jsonStockDetailLot: JSON.stringify(
                              line.lot
                                  ? { lot: line.lot }
                                  : {
                                        lotNumber,
                                        expirationDate: line.expirationDate,
                                        supplierLot: line.supplierLot,
                                        sublot: line.sublot,
                                    },
                          ),
                      }),
                ...commonUpdate,
            },
        ]);

        this.lines.addOrUpdateRecordValue(line);
    }

    private async readLots(filter: Filter<LotBinding>): Promise<
        WithoutEdges<{
            id: string;
            _id: string;
            sublot: string;
            supplierLot: string;
            expirationDate: string;
        }>[]
    > {
        return withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-stock-data/Lot')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            sublot: true,
                            expirationDate: true,
                            supplierLot: true,
                            id: true,
                            _id: true,
                        },
                        {
                            filter,
                        },
                    ),
                )
                .execute(),
        );
    }

    // if a lot managed item is added and the lot column was not displayed => show it
    // idem for sublot and expiration date
    async updateColumnsDisplay(item: Item) {
        let mustRedraw = false;
        if (!this.hasLotInLines.value && item.lotManagement !== 'notManaged') {
            this.hasLotInLines.value = true;
            this.lines.showColumn('stockDetail.stockDetailLot.lotId');
            mustRedraw = true;
        }

        if (!this.hasSublotInLines.value && item.lotManagement === 'lotSublotManagement') {
            this.hasSublotInLines.value = true;
            this.lines.showColumn('sublot');
            mustRedraw = true;
        }
        if (!this.hasExpiryManagementInLines.value && item.isExpiryManaged) {
            this.hasExpiryManagementInLines.value = true;
            this.lines.showColumn('expirationDate');
            mustRedraw = true;
        }
        if (mustRedraw) {
            await this.lines.redraw();
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
